/// Flutter icons ThePunchIcons
/// Copyright (C) 2024 by original authors @ fluttericon.com, fontello.com
/// This font was generated by FlutterIcon.com, which is derived from Fontello.
///
/// To use this font, place it in your fonts/ directory and include the
/// following in your pubspec.yaml
///
/// flutter:
///   fonts:
///    - family:  ThePunchIcons
///      fonts:
///       - asset: fonts/ThePunchIcons.ttf
///
/// 
///
import 'package:flutter/widgets.dart';

class ThePunchIcons {
  ThePunchIcons._();

  static const _kFontFam = 'ThePunchIcons';

  static const IconData callLog = IconData(0xe800, fontFamily: _kFontFam);
  static const IconData eventAvailable = IconData(0xe801, fontFamily: _kFontFam);
  static const IconData face = IconData(0xe802, fontFamily: _kFontFam);
  static const IconData frameInspect = IconData(0xe803, fontFamily: _kFontFam);
  static const IconData home = IconData(0xe804, fontFamily: _kFontFam);
  static const IconData personPinCircle = IconData(0xe805, fontFamily: _kFontFam);
  static const IconData pieChart = IconData(0xe806, fontFamily: _kFontFam);
  static const IconData punchClock = IconData(0xe807, fontFamily: _kFontFam);
}
