import 'package:json_annotation/json_annotation.dart';

import '../../dataModel/data/travel_pings.dart';
import '../../misc/json_conversion.dart';
import 'system.dart';

part 'send_travel_ping_request.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class SendTravelPingRequest extends SystemRequest {
  final TravelPing ping;

  /// Optional field to identify which "piece" of code is sending this request
  final String? pingSource;

  SendTravelPingRequest({
    required this.ping,
    this.pingSource,
    required String serverIP,
    required String databaseName,
    required String sessionId,
  }) : super(serverIP: serverIP, databaseName: databaseName, sessionId: sessionId);

  /// Create a request automatically using the current SystemRequest config
  static Future<SendTravelPingRequest> create(
    TravelPing ping, {
    String? pingSource,
  }) async {
    final systemRequest = await SystemRequest.create();
    return SendTravelPingRequest(
      ping: ping,
      pingSource: pingSource,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory SendTravelPingRequest.fromJson(Map<String, dynamic> json) =>
      _$SendTravelPingRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$SendTravelPingRequestToJson(this);
}

// send_travel_ping_response.dart
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class SendTravelPingResponse extends SystemResponse {
  final bool success;

  SendTravelPingResponse({
    required this.success,
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory SendTravelPingResponse.fromJson(Map<String, dynamic> json) =>
      _$SendTravelPingResponseFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$SendTravelPingResponseToJson(this);
}
