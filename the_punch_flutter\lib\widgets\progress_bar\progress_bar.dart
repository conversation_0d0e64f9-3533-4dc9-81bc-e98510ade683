import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../dataModel/data/punch_card.dart';
import '../../dataModel/models/punch_card_model.dart';
import '../../helpers/color_helper.dart';
import 'dart:math';
import '../../misc/extensions.dart';
import '../../state/punch_state.dart';
import '../../helpers/screen_helper.dart';
import '../../dataModel/models/punch_view_model.dart';


// Add this import

class ProgressBar extends StatefulWidget {
  final Duration duration;
  final Widget durationWidget;
  final String locationId;
  final Widget jobTypeWidget;
  const ProgressBar(
      {Key? key,
      required this.duration,
      required this.durationWidget,
      this.jobTypeWidget = const Text(''),
      this.locationId = ''})
      : super(key: key);

  @override
  State<ProgressBar> createState() => _ProgressBarState();
}

class _ProgressBarState extends State<ProgressBar>
    with TickerProviderStateMixin {
  // State variables go here.  For example:
  final _totalDurationStreamController = StreamController<Duration>.broadcast();
  Stream<Duration> get totalDurationStream =>
      _totalDurationStreamController.stream;
  Duration _totalDuration = Duration.zero;

  late AnimationController _controller;
  PunchCard? _firstPunchCard;
  List<PunchCard> _relatedPunchCards = [];
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration, // Use the provided duration
    )..forward(); // Start the animation right away
    _initializeDurations();
    unawaited( _startAllDurationStream());
  }
  

  Future<void> _initializeDurations() async {
    // Fetch the latest punch card
    _firstPunchCard = await _getFirstPunchCard();

    // Fetch all related punch cards
    _relatedPunchCards = await _fetchRelatedPunchCards();

    // Calculate initial Total Time
    _totalDuration = _calculateTotalDuration();
    // Calculate initial Task Time (from the first punch card)
    if (_firstPunchCard != null) {
    }
  }

  Future<void> _startAllDurationStream() async {
    
final allDuration = (_totalDuration.isNegative?Duration.zero:_totalDuration) + (PunchState().duration?? Duration.zero);
          _totalDurationStreamController.add(
            allDuration,
          );
        
        await Future.delayed(const Duration(seconds: 1));
    
  }
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => SizedBox(
        //       // Or any other appropriate parent widget
        //       // Add your widget's layout and functionality here.
        //       // This is just a very basic example:
        //       //padding: const EdgeInsets.all(16.0),
        width: 250,
        height: 250,

        child: Stack(
          alignment: Alignment.center,
          children: [
            StreamBuilder<String>(
                stream: PunchState().durationStream,
                initialData: PunchState().duration.toString(),
                builder: (context, snapshot) => AnimatedBuilder(
                    animation: _controller,
                    builder: (context, child) => CustomPaint(
                      size: const Size(275, 275),
                      painter: ProgressPainter(
                          totalDuration: _totalDuration),
                    ),
                  ),
            ),
 
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(height: ScreenHelper.screenHeightPercentage(context, 3),),
                if (PunchState().isTaskActive || PunchState().isPunchedIn)
                  widget.jobTypeWidget,
             
            SizedBox(height: ScreenHelper.screenHeightPercentage(context, 10),),
          
              ],
            )
          ],
        ),
      );

  Future<List<PunchCard>> _fetchRelatedPunchCards() async {
    final prefs = await SharedPreferences.getInstance();
    final linkId = prefs.getString('LinkId') ?? '';

    // Fetch punch cards from the view model or API
    final punchCardModel = PunchCardModel();
    final allPunchCards =
        (await punchCardModel.getAllByLinkId(linkId)).toList();

    // Sort the punch cards by ClockedIn date (descending order)
    allPunchCards.sort((a, b) => b.clockedIn.compareTo(a.clockedIn));

    return allPunchCards;
  }

  Future<PunchCard?> _getFirstPunchCard() async {
    final prefs = await SharedPreferences.getInstance();
    final linkId = prefs.getString('LinkId') ?? '';

    final punchCardModel = PunchCardModel();
    final allPunchCards =
        (await punchCardModel.getAllByLinkId(linkId)).toList();

    if (allPunchCards.isNotEmpty) {
      // **Sort in descending order to get the latest punch card**
      allPunchCards.sort((a, b) => b.clockedIn.compareTo(a.clockedIn));
      return allPunchCards.first;
    }

    return null;
  }

  Duration _calculateTotalDuration() {
    Duration total = Duration.zero;
    for (var card in _relatedPunchCards) {
      // ignore: unnecessary_null_comparison
      if (card.clockedIn != null && card.clockedOut != null) {
        total += card.clockedOut!.difference(card.clockedIn!);
      } else if (card.clockedIn != null && card.clockedOut == null) {
        // If the punch card is active, add the duration till now
        total += DateTime.now().difference(card.clockedIn!);
      }
    }
    return total;
  }
}

class ProgressPainter extends CustomPainter {

  final Duration totalDuration; // Calculated total duration

  ProgressPainter(
      {
      // required this.punchStateDuration,
      required this.totalDuration});

  @override
  Future<void> paint(Canvas canvas, Size size) async {
   
    final center = size.center(Offset.zero);
    final radius = size.width / 2;

   final allTime = (totalDuration.isNegative?Duration.zero:totalDuration)+(PunchState().duration??Duration.zero);

      final Gradient gradient = RadialGradient(
        radius: 1.0,
        colors: [
          Colors.black.withOpacity(.2),  // Soft shadow start
          const Color.fromARGB(255, 236, 236, 236).withOpacity(.5),   // Brighter middle band
          const Color.fromARGB(255, 236, 236, 236).withOpacity(.5),   // Brighter middle band
          //Colors.white.withOpacity(.6),   // Brighter middle band
          Colors.black.withOpacity(.3),  // Shadow edge
        ],
        stops: const [
          0.38,  // Start of the white band closer to center
          0.48,
           .54, // Wider white band — adjust this to make it feel more lifted
          .66,  // Edge fade
        ],
      );
    // Background Circle
    final bgPaint = Paint()
      ..color = Colors.white.withOpacity(.3)
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke
      ..strokeWidth = 35;
    final numArcs =
        PunchState().isPunchedIn ? PunchState().duration!.inMinutes / (720) : 0;
    final paint2 = Paint()
      ..color = PunchState().isTaskActive ?ColorHelper.thePunchPink().withOpacity(.6):ColorHelper.thePunchPink()
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke
      ..strokeWidth = 30;
    if(PunchState().isPunchedIn) {
      canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -pi / 2,
      2 * pi * allTime.inSeconds / (720*60), // Use progressForArc here
      false,
      paint2, // Use the NEW paint object
    );
    }
    //final taskProgress = PunchState().
    // Actual Progress Circle
    for (int i = 0; i <= numArcs; i++) {
      final denominator = numArcs == 0 ? 1 : numArcs; // Simplified denominator
      final opacity =
          0.2 + (0.6 * i / denominator); // Corrected opacity calculation
      final stroke = 24.0;
      // Create a NEW Paint object inside the loop:
      final paint = Paint()
        ..color = ColorHelper.thePunchBlue().withOpacity(.8)
        ..strokeCap = StrokeCap.round
        ..style = PaintingStyle.stroke
        ..strokeWidth = stroke;

      final progressForArc = i == numArcs.floor()
          ? numArcs.remainder(1)
          : 1.0; // Use progress variable
      if(PunchState().isTaskActive) {
        canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        -pi / 2,
        2 * pi * progressForArc, // Use progressForArc here
        false,
        paint, // Use the NEW paint object
      );
      }
    }
    final textSpan = TextSpan(
      text: PunchState().isTaskActive
          ? DurationExtension(PunchState().duration??Duration.zero).toFormattedWithSeconds
          : PunchState().isPunchedIn
            ?  DurationExtension(allTime).toFormattedWithSeconds : "0:00", // Replace with your desired string
      style:  TextStyle().copyWith(
                        fontSize: 45,
                        fontWeight: FontWeight.w900,
                        //fontFamily: GoogleFonts.bigShouldersInlineText().fontFamily,
                        color: PunchState().isPunchedIn
                            ? Colors.black
                            : Colors.black54)
    );
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout(
      minWidth: 0,
      maxWidth: size.width,
    );

    final offset = Offset(
      size.width / 2 - textPainter.width / 2, // Center horizontally
      size.height / 2 - textPainter.height / 2, // Center vertically
    );

    textPainter.paint(canvas, offset);


    bgPaint.shader = gradient.createShader(
      Rect.fromCircle(center: center, radius: radius),
    );
    canvas.drawCircle(center, radius, bgPaint);

    // Draw the dot ring

    final dotRadius = 1.0; // Radius of each dot
    final dotSpacing = 15.0 * pi / 180; // 15 degrees in radians

    final dotPaint = Paint()
      ..color = Colors.black.withOpacity(.1) // Color of the dots
      ..style = PaintingStyle.fill;

    // Calculate the radius for the dots ring (slightly inside the main radius)
    final dotsRingRadius = radius - 23; // Adjust 20 to control the inset

    for (double i = 0; i < 360; i += 15) {
      // Iterate every 15 degrees
      final angle = i * pi / 180; // Convert degrees to radians
      final x = center.dx + dotsRingRadius * cos(angle);
      final y = center.dy + dotsRingRadius * sin(angle);

      canvas.drawCircle(Offset(x, y), dotRadius, dotPaint);
    }
  }

  @override
  // ignore: prefer_expression_function_bodies
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true; //Always repaint
  }
}

class _Location extends StatelessWidget {
  final String locationId;

  const _Location({required this.locationId});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final body1OnSecondary = theme.textTheme.bodyMedium?.copyWith(
        color: ColorHelper.thePunchLightGray(), fontWeight: FontWeight.w800);
    final body1OnSurface = theme.textTheme.bodyMedium?.copyWith(
      color: ColorHelper.thePunchLightGray(),
      fontSize: ScreenHelper.screenHeightPercentage(context, 1.2),
    );

    return Consumer<PunchViewModel>(builder: (context, viewModel, child) {
      if (viewModel.location == null) return Container();
      final location = viewModel.location!;
      return Column(
        children: [
          Text(location.name,
              style: body1OnSecondary, textAlign: TextAlign.center),
          if (location.address1.isNotEmpty)
            Text(location.address1,
                style: body1OnSurface, textAlign: TextAlign.center),
          if (location.address2.isNotEmpty)
            Text(location.address2,
                style: body1OnSurface, textAlign: TextAlign.center),
          if (location.address3.isNotEmpty)
            Text(location.address3,
                style: body1OnSurface, textAlign: TextAlign.center),
        ],
      );
    });
  }
}
