// lib/pages/web/contacts/new_contacts.dart

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../../dataModel/data/location.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data_model.dart';
import '../../../misc/app_localization.dart';
import '../../../misc/extensions.dart';
import '../../view_model_mixin.dart';
import '../../../state/permissions_state.dart';
import '../../../widgets/search_text_field.dart';
import '../../../widgets/value_listenable_builder.dart';
import '../my_scaffold.dart';
import 'contact_mixin.dart';

// THIS IS THE FILE WE ARE GOING TO USE TO REMAKE THIS WITH CARDS.
class NewContactsPage extends StatelessWidget {
  const NewContactsPage({super.key});

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
      create: (context) => _ViewModel(),
      child: MyScaffold(
        title: 'New Contacts Page with Cards',
        body: _Body(),
      ));
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1300),
          child: Column(children: [
            _TableHeader(),
            Flexible(child: _Table()),
          ]),
        ),
      );
}

class _TableHeader extends StatefulWidget {
  @override
  _TableHeaderState createState() => _TableHeaderState();
}

class _TableHeaderState extends State<_TableHeader> {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
      builder: (context, viewModel, child) => Card(
            child: Flex(
              direction: Axis.horizontal,
              children: [
                Flexible(
                  child: SearchTextField(notifier: viewModel.searchNotifier),
                ),
                // Removed the ActiveToggle widget
                if (PermissionsState().editContacts)
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: ElevatedButton.icon(
                      onPressed: () => context.go('/contacts/edit'),
                      icon: const Icon(Icons.add),
                      label: Text(AppLocalization.of(context).addContact),
                    ),
                  ),
              ],
            ),
          ));
}

class _Table extends StatefulWidget {
  @override
  _TableState createState() => _TableState();
}

class _TableState extends State<_Table> {
  int _sortColumn = 0;
  bool _isAscending = false;

  @override
  Widget build(BuildContext context) => LayoutBuilder(builder: (context, layout) {
        final theme = Theme.of(context);
        final highlightColor = theme.highlightColor;
        return FocusScope(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal, // Allow horizontal scrolling
            child: Card(
              elevation: 0,
              child: Consumer<_ViewModel>(
                  builder: (context, viewModel, child) => ValueListenableBuilder<String>(
                        valueListenable: viewModel.searchNotifier,
                        builder: (context, search, child) => SizedBox(
                              width: layout.maxWidth,
                              child: DataTable(
                                showCheckboxColumn: false,
                                dataRowHeight: 75,
                                showBottomBorder: true,
                                dividerThickness: 3,
                                headingRowColor: MaterialStateColor.resolveWith(
                                    (states) => highlightColor),
                                sortColumnIndex: _sortColumn,
                                sortAscending: _isAscending,
                                columns: getColumns(viewModel),
                                rows: getRows(viewModel, search),
                              ),
                            ),
                      )),
            ),
          ),
        );
      });

  List<DataColumn> getColumns(_ViewModel viewModel) => [
        DataColumn(
            onSort: onSort,
            label: Flexible(
                child: SizedBox(
                    width: 100,
                    child: Text(AppLocalization.of(context).contactName)))),
        DataColumn(
            onSort: onSort,
            label: Flexible(
                child: SizedBox(
                    width: 100,
                    child: Text(AppLocalization.of(context).location)))),
        DataColumn(
            onSort: onSort,
            label: Flexible(
                child: SizedBox(
                    width: 100,
                    child: Text(AppLocalization.of(context).phone)))),
        DataColumn(
            onSort: onSort,
            label: Flexible(
                child: SizedBox(
                    width: 120,
                    child: Text(AppLocalization.of(context).emailAddress)))),
        DataColumn(
            onSort: onSort,
            label: Flexible(
                child: SizedBox(
                    width: 42,
                    child: Text(AppLocalization.of(context).active)))),
        DataColumn(
            label: Flexible(child: SizedBox(width: 5, child: Container()))),
      ];

  List<DataRow> getRows(_ViewModel viewModel, String search) {
    if (viewModel.contacts.isEmpty) return [];

    Iterable<User> contacts = viewModel.contacts;

    // Removed active toggle filtering
    /*
    switch (viewModel.activeNotifier.value) {
      case ActiveToggleState.active:
        contacts = contacts.where((e) => e.isActive);
        break;
      case ActiveToggleState.inactive:
        contacts = contacts.where((e) => !e.isActive);
        break;
      default:
        break;
    }
    */

    if (search.isNotEmpty) {
      contacts = contacts.where((e) {
        if (e.name.toLowerCase().contains(search.toLowerCase())) return true;
        if (e.phone != null && e.phone!.contains(search)) return true;
        if (e.emailAddress != null &&
            e.emailAddress!.toLowerCase().contains(search.toLowerCase()))
          return true;
        return false;
      });
    }

    final visibleContacts = contacts.toList();

    if (_isAscending) {
      switch (_sortColumn) {
        case 0:
          visibleContacts.sort((a, b) => a.name.compareTo(b.name));
          break;
        case 1:
          visibleContacts.sort((a, b) =>
              (viewModel.locationsForContact(a.id) ?? '')
                  .compareTo(viewModel.locationsForContact(b.id) ?? ''));
          break;
        case 2:
          visibleContacts.sort((a, b) =>
              (a.phone ?? '').compareTo(b.phone ?? ''));
          break;
        case 3:
          visibleContacts.sort((a, b) =>
              (a.emailAddress ?? '').compareTo(b.emailAddress ?? ''));
          break;
        case 4:
          visibleContacts.sort((a, b) => a.isActive
              .toActive(context)
              .compareTo(b.isActive.toActive(context)));
          break;
      }
    } else {
      switch (_sortColumn) {
        case 0:
          visibleContacts.sort((b, a) => a.name.compareTo(b.name));
          break;
        case 1:
          visibleContacts.sort((b, a) =>
              (viewModel.locationsForContact(a.id) ?? '')
                  .compareTo(viewModel.locationsForContact(b.id) ?? ''));
          break;
        case 2:
          visibleContacts.sort((b, a) =>
              (a.phone ?? '').compareTo(b.phone ?? ''));
          break;
        case 3:
          visibleContacts.sort((b, a) =>
              (a.emailAddress ?? '').compareTo(b.emailAddress ?? ''));
          break;
        case 4:
          visibleContacts.sort((b, a) => a.isActive
              .toActive(context)
              .compareTo(b.isActive.toActive(context)));
          break;
      }
    }

    return visibleContacts
        .map((e) => DataRow(
              onSelectChanged: (value) async => context.pushNamed(
                  '/contacts/view',
                  queryParameters: {'id': e.id}),
              cells: [
                DataCell(
                    Flexible(child: SizedBox(width: 100, child: Text(e.name)))),
                DataCell(Flexible(
                    child: SizedBox(
                        width: 140,
                        child:
                            Text(viewModel.locationsForContact(e.id) ?? '')))),
                DataCell(Flexible(
                    child: SizedBox(width: 100, child: Text(e.phone ?? '')))),
                DataCell(Flexible(
                    child: SizedBox(
                        width: 120, child: Text(e.emailAddress ?? '')))),
                DataCell(Flexible(
                    child: SizedBox(
                        width: 50, child: Text(e.isActive.toActive(context))))),
                if (PermissionsState().editContacts)
                  DataCell(Flexible(
                      child: SizedBox(
                          width: 2,
                          child: PopupMenuButton<int>(
                            onSelected: (value) async {
                              switch (value) {
                                case 1:
                                  await context.pushNamed('/contacts/edit',
                                      queryParameters: {'id': e.id});
                                  break;
                                case 2:
                                  unawaited(showDialog(
                                      context: context,
                                      builder: (context) => DeactivateDialog(
                                          selected: () =>
                                              viewModel.setActive(e, false))));
                                  break;
                                case 3:
                                  unawaited(viewModel.setActive(e, true));
                                  break;
                              }
                            },
                            itemBuilder: (context) => [
                              PopupMenuItem<int>(
                                  value: 1,
                                  child: Text(AppLocalization.of(context).edit)),
                              if (e.isActive)
                                PopupMenuItem<int>(
                                    value: 2,
                                    child: Text(AppLocalization.of(context)
                                        .deactivate)),
                              if (!e.isActive)
                                PopupMenuItem<int>(
                                    value: 3,
                                    child: Text(
                                        AppLocalization.of(context).activate)),
                            ],
                          ))))
                else
                  DataCell(
                      Flexible(child: SizedBox(width: 10, child: Container()))),
              ],
            ))
        .toList();
  }

  void onSort(int columnIndex, bool ascending) {
    setState(() {
      _sortColumn = columnIndex;
      _isAscending = ascending;
    });
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin, ContactMixin {
  var contacts = <User>[];
  var contactLocationMap = <String, Iterable<Location>>{};
  // Removed activeNotifier
  var searchNotifier = ValueNotifier('');

  _ViewModel() {
    addListenables([
      DataModel().userModel,
      DataModel().locationModel,
      DataModel().locationContactModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    contacts = (await DataModel().userModel.allContacts).toList();
    final userIds = contacts.map((e) => e.id);
    final locationContacts =
        await DataModel().locationContactModel.getByUserIds(userIds);
    final locationIds = locationContacts.map((e) => e.locationId).toSet();
    final locations =
        (await DataModel().locationModel.getByIds(locationIds)).toList();
    contactLocationMap = {
      for (final e in userIds)
        e: locationContacts
            .where((locationContact) => locationContact.userId == e)
            .map((locationContact) =>
                locations.firstWhere((e) => locationContact.locationId == e.id))
    };
    notifyListeners();
  }

  String? locationsForContact(String id) =>
      contactLocationMap[id]?.map((e) => e.name).join('\n');

  Future<void> setActive(User user, bool isActive) async {
    // Implement the logic to set the user's active status
    // For example:
    // await DataModel().userModel.updateUserStatus(user.id, isActive);
    // Then refresh the contacts list
    await refresh();
  }
}

// Removed the import for active_toggle.dart as it's no longer used
// import '../../../widgets/active_toggle.dart';

// Removed the CustomDropdownButton class as it's no longer needed

class DeactivateDialog extends StatelessWidget {
  final VoidCallback selected;

  const DeactivateDialog({required this.selected, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) => AlertDialog(
        title: Text(AppLocalization.of(context).activate),
        content: Text(AppLocalization.of(context).deactivate),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocalization.of(context).cancel),
          ),
          ElevatedButton(
            onPressed: () {
              selected();
              Navigator.of(context).pop();
            },
            child: Text(AppLocalization.of(context).confirm),
          ),
        ],
      );
}
