import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';
import '../base_data.dart';
import '../../misc/json_conversion.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import 'package:uuid/uuid.dart';

part 'punch_cards_history.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class PunchCardsHistory extends BaseData {
  @override
  @JsonKey(fromJson: idFromJson)
  String id;  // Unique identifier for this history entry

  @JsonKey(fromJson: idFromJson)
  String punchCardId;  // Reference to the original PunchCard

  // Nullable fields to store the previous values
  @JsonKey(fromJson: nullableIdFromJson)
  String? userId;

  @JsonKey(fromJson: nullableIdFromJson)
  String? locationId;

  @JsonKey(fromJson: nullableIdFromJson)
  String? jobTypeId;

  @JsonKey(fromJson: nullableDateTimeFromJson, toJson: nullableDateTimeToJson)
  DateTime? clockedIn;

  @JsonKey(fromJson: nullableDateTimeFromJson, toJson: nullableDateTimeToJson)
  DateTime? clockedOut;

  @JsonKey()
  double? duration;

  // Information about who made the changes and when
  @JsonKey(fromJson: idFromJson)
  String changedByUserId;

  @JsonKey(fromJson: dateTimeFromJson, toJson: dateTimeToJson)
  DateTime changeDate;

  PunchCardsHistory({
    required this.id,
    required this.punchCardId,
    this.userId,
    this.locationId,
    this.jobTypeId,
    this.clockedIn,
    this.clockedOut,
    this.duration,
    required this.changedByUserId,
    required this.changeDate,
    super.isActive,
    super.isDirty,
    required super.createdOn,
    required super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
  });

  factory PunchCardsHistory.fromJson(Map<String, dynamic> json) {
    try {
      return _$PunchCardsHistoryFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() => _$PunchCardsHistoryToJson(this);

  factory PunchCardsHistory.create(String punchCardId) => PunchCardsHistory(
        id: const Uuid().v4(),  // Create a new unique ID
        punchCardId: punchCardId,
        changedByUserId: LoginState.userId,
        changeDate: ServerTimeState().utcTime,
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
      );
}
