import 'package:flutter/material.dart';

class PopupMenuLabel extends PopupMenuEntry<Never> {
  const PopupMenuLabel({super.key, required this.child});

  final Widget child;

  @override
  State<PopupMenuLabel> createState() => _PopupMenuLabelState();

  @override
  double get height => 16;

  @override
  bool represents(void value) => false;
}

class _PopupMenuLabelState extends State<PopupMenuLabel> {
  @override
  Widget build(BuildContext context) => Container(
      alignment: AlignmentDirectional.centerStart,
      constraints: BoxConstraints(minHeight: widget.height),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: widget.child,
    );
}
