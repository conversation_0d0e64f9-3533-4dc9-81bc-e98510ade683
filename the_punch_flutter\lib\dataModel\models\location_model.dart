import '../data/location.dart';
import '../hive_db.dart';
import '../base_data.dart';

class LocationModel extends BaseDataModel<Location> {
  @override
  Future<Iterable<Location>> get all async =>
      (await HiveDb.database).locations.values;

  @override
  Future<void> save(Iterable<Location> t) async {
    await (await HiveDb.database).locations.putAll({for (final e in t) e.id: e});
    notifyListeners();
  }

  Future<bool> isAnyContactsByLocationId(String id) async =>
      (await HiveDb.database).locationContacts.values.any((e) => e.locationId == id);

  Future<Iterable<Location>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }

  // Function to get a location by ID
  Future<Location?> getLocationById(String id) async {
    var locations = await (await HiveDb.database).locations;
    return locations.get(id);
  }

  Future<String?> getLocationNameById(String id) async {
    final location = await getLocationById(id);
    return location?.name; // Adjust the field name if it's different
  }
}
