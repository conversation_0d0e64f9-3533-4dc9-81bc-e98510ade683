import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../dataModel/data/schedule.dart';
import '../../misc/json_conversion.dart';
import 'system.dart';

part 'get_schedules_for_month.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class GetSchedulesForMonthRequest extends SystemRequest {
  int month;
  int year;

  GetSchedulesForMonthRequest({
    required this.month,
    required this.year,
    required super.serverIP,
    required super.databaseName,
    required super.sessionId,
  });

  static Future<GetSchedulesForMonthRequest> create(int month, int year) async {
    final systemRequest = await SystemRequest.create();

    return GetSchedulesForMonthRequest(
      month: month,
      year: year,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory GetSchedulesForMonthRequest.fromJson(Map<String, dynamic> json) {
    try {
      return _$GetSchedulesForMonthRequestFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }

  @override
  Map<String, dynamic> toJson() => _$GetSchedulesForMonthRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class GetSchedulesForMonthResponse extends SystemResponse {
  @JsonKey(defaultValue: [])
  List<Schedule> schedules;

  GetSchedulesForMonthResponse({
    this.schedules = const [],
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory GetSchedulesForMonthResponse.fromJson(Map<String, dynamic> json) => _$GetSchedulesForMonthResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetSchedulesForMonthResponseToJson(this);
}