import 'package:json_annotation/json_annotation.dart';
import '../../dataModel/data/organization.dart';

part 'get_organizations.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class GetOrganizationsResponse {
  final List<Organization> organizations;

  GetOrganizationsResponse({required this.organizations});

  factory GetOrganizationsResponse.fromJson(Map<String, dynamic> json) => _$GetOrganizationsResponseFromJson(json);
  Map<String, dynamic> toJson() => _$GetOrganizationsResponseToJson(this);
}
