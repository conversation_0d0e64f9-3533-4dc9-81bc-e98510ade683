import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../dataModel/data/alert.dart';
import '../dataModel/data/location.dart';
import '../dataModel/data/user.dart';
import '../dataModel/data_model.dart';
import '../misc/app_localization.dart';
import '../misc/extensions.dart';
import '../pages/view_model_mixin.dart';

class AlertsWidget extends StatelessWidget {
  final String? userId;
  final String? locationId;

  AlertsWidget({super.key, this.userId, this.locationId}) {
    if (userId == null && locationId == null) throw UnimplementedError();
  }

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
        create: (context) => _ViewModel(userId: userId, locationId: locationId),
        builder: (context, child) => _Body(),
      );
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final highlightColor = theme.highlightColor;
    return Consumer<_ViewModel>(builder: (context, viewModel, _) {
      if (viewModel.alerts.isEmpty) {
        return const Padding(
          padding: EdgeInsets.all(30),
          child: Text('No alerts yet.'),
        );
      }
      return Card(
        elevation: 0,
        child: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: DataTable(
              dataRowHeight: 50,
              showBottomBorder: true,
              dividerThickness: 3,
              headingRowColor:
                  MaterialStateColor.resolveWith((states) => highlightColor),
              columns: getColumns(context),
              rows: getRows(viewModel, context),
            ),
          ),
        ),
      );
    });
  }

  List<DataColumn> getColumns(BuildContext context) => [
        DataColumn(label: Text(AppLocalization.of(context).alerts)),
        DataColumn(label: Text(AppLocalization.of(context).on)),
        DataColumn(label: Text(AppLocalization.of(context).employee)),
        DataColumn(label: Text(AppLocalization.of(context).location)),
      ];

  List<DataRow> getRows(_ViewModel viewModel, BuildContext context) {
    final locale = Localizations.localeOf(context);

    return viewModel.alerts
        .map((e) => DataRow(
              cells: [
                DataCell(Text(e.name(context))),
                DataCell(Text(e.alertOn.toFormattedDateTime(locale))),
                DataCell(Text(viewModel.employeeMap[e.userId]?.name ?? '')),
                DataCell(Text(viewModel.locationMap[e.locationId]?.name ?? '')),
              ],
            ))
        .toList();
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String? userId;
  final String? locationId;
  List<Alert> alerts = [];
  Map<String, User> employeeMap = {};
  Map<String, Location> locationMap = {};

  _ViewModel({this.userId, this.locationId}) {
    addListenables([
      DataModel().alertModel,
      DataModel().userModel,
      DataModel().locationModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    if (userId != null) {
      alerts =
          (await DataModel().alertModel.getActiveByUserId(userId!)).toList();
    }
    if (locationId != null) {
      alerts = (await DataModel().alertModel.getActiveByLocationId(locationId!))
          .toList();
    }

    alerts.sort((a, b) => b.alertOn.compareTo(a.alertOn));

    final employeeIds = alerts.map((e) => e.userId).toSet();
    final employees = await DataModel().userModel.getByIds(employeeIds);
    employeeMap = {for (final e in employees) e.id: e};

    final locationIds = alerts
        .where((e) => e.locationId != null)
        .map((e) => e.locationId!)
        .toSet();
    final locations = await DataModel().locationModel.getByIds(locationIds);
    locationMap = {for (final location in locations) location.id: location};

    notifyListeners();
  }
}
