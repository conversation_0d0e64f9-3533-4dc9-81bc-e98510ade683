import 'dart:async';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../helpers/color_helper.dart';

class WeekScroller extends StatefulWidget {
  // final Map<DateTime, int>? pipsByDate;
  // final DateTime initialDate;
  // final ValueNotifier<DateTime> selectedDate;
  final PageController pageController ;

  const WeekScroller({super.key, required this.pageController});

  @override
  _WeekScrollerState createState() => _WeekScrollerState();
}

class _WeekScrollerState extends State<WeekScroller> {
  final List<DateTime> _dates =
      List.generate(12, (index) => DateTime(DateTime.now().year, index + 1));
final List<DateTime> _mondays = List.generate(
  20,
  (index) {
    DateTime now = DateTime.now();
    DateTime startMonday = now.subtract(Duration(days: (now.weekday - DateTime.monday) % 7));
    return startMonday.subtract(Duration(days: index * 7));
  },
);
  late int _selectedIndex ; // Initially select the current month
  final ScrollController _scrollController = ScrollController();
  final Map<int, GlobalKey> _itemKeys = {};
  final Map<int, double> _itemPositions = {}; // Store item positions dynamically
static const initialPage = 1073741824;
  @override
  void initState() {
    super.initState();
    _selectedIndex =  0;
    print('LIST OF WEEKS: ${_mondays.toString()}');
    for (int i = 0; i < _mondays.length; i++) {
      _itemKeys[i] = GlobalKey();
    }
    _scrollController.addListener(_updateItemPositions);

      WidgetsBinding.instance.addPostFrameCallback((_) {
    _scrollToSelectedIndex();
  });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_updateItemPositions);
    _scrollController.dispose();
    super.dispose();
  }

  void _updateItemPositions() {
    if (!mounted) return;
    setState(() {
      for (int index in _itemKeys.keys) {
        final key = _itemKeys[index];
        if (key == null) continue;

        final renderBox = key.currentContext?.findRenderObject() as RenderBox?;
        if (renderBox != null) {
          final position = renderBox.localToGlobal(Offset.zero);
          _itemPositions[index] = position.dx;
        }
      }
    });
  }

  bool _isPartiallyOffscreen(int index) {
    if (!_itemPositions.containsKey(index)) return false;

    double dx = _itemPositions[index]!;
    double screenWidth = MediaQuery.of(context).size.width;

    return dx < 0 || dx > screenWidth - 50; // 50 is the assumed item width
  }
void _scrollToSelectedIndex() {
  
  double offset = 0;
  print('SCROLLLLLL $offset    ${_itemPositions.length}');
  _scrollController.animateTo(
    offset,
    duration: Duration(milliseconds: 300),
    curve: Curves.easeInOut,
  );
}
  @override
  Widget build(BuildContext context) => Column(
        children: [
          // Text(
          //   DateFormat.MMMM().format(DateTime.now())+' '+ DateFormat.y().format(DateTime.now()), // Displays current year
          //   style:  TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: ColorHelper.thePunchBlueGray()),
          // ),
          
          Container(
            height: 90,
            child: ListView.builder(
              reverse: true,
              controller: _scrollController,
              scrollDirection: Axis.horizontal,
              itemCount: _mondays.length,
              itemBuilder: (context, index) {
                bool isSelected = index == _selectedIndex;
                bool isOffscreen = _isPartiallyOffscreen(index);
                  int firstDate = _mondays[index].day;
                  int secondDate = _mondays[index].add(Duration(days: 6)).day;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedIndex = index;
                      print('INDEX $index');
                  widget.pageController.jumpToPage(initialPage - index);//
                    });
                  },            
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Text(
                      //   DateFormat.MMM().format(_dates[index]), // Week abbreviation (Jan, Feb, etc.)
                      //   style: TextStyle(
                      //     fontSize: 12,
                      //     fontWeight: FontWeight.bold,
                      //     color: isSelected ? ColorHelper.thePunchBlueGray() : Colors.grey,
                      //   ),
                      // ),
                      // const SizedBox(height: 5),
                      Container(
                        key: _itemKeys[index], // Assign key to track position
                        width: isSelected ? 65 : 55,
                        height: isSelected ? 65 : 55,
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        decoration: BoxDecoration(
                          color: isSelected ? ColorHelper.thePunchBlueGray() : Colors.white,
                          borderRadius: BorderRadius.circular(35),
                          border: Border.all(
                            color: isSelected
                                ? ColorHelper.thePunchBlueGray()
                                : ColorHelper.thePunchLighterGray(),
                          ),
                        ),
                        alignment: Alignment.center,
                        
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children:[
                            Text(
                          DateFormat.MMM().format(_mondays[index]), // Day of the month (e.g., "1", "2")
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: isSelected ? Colors.white : isOffscreen ? Colors.black54 : ColorHelper.thePunchBlueGray(),
                          ),
                        ),
                        Text('$firstDate - $secondDate',
                        style: TextStyle(
                          color: isSelected ? Colors.white : ColorHelper.thePunchDarkBlue()  
                        ),
                        )
                         ]
                        )
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      );
}
