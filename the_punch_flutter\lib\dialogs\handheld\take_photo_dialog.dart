import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:camera/camera.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter/material.dart';
import '../../state/app_state.dart';
import 'package:uuid/uuid.dart';

class TakePhotoDialog extends StatefulWidget {
  final Function(String id) photoTaken;

  const TakePhotoDialog({super.key, required this.photoTaken});

  @override
  State<TakePhotoDialog> createState() => _TakePhotoDialogState();
}

class _TakePhotoDialogState extends State<TakePhotoDialog> {
  CameraDescription? camera;
  late CameraController _controller;

  @override
  void initState() {
    super.initState();
  }

  Future<bool> _initialize() async {
    try {
      final cameras = await availableCameras();
      if (cameras.isEmpty) return false;
      camera = cameras.first;
      _controller = CameraController(camera!, ResolutionPreset.ultraHigh, enableAudio: false);
      await _controller.initialize();
      return true;
    } catch (e) {
      if (kDebugMode) print('falied to initialize the camera $e');
      return false;
    }
  }

  @override
  void dispose() {
    unawaited(_controller.dispose());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => FutureBuilder<bool>(
        future: Future(() async => await _initialize()),
        builder: (context, snapshot) {
          if (!snapshot.hasData) return const Center(child: CircularProgressIndicator());
          if (snapshot.data == false) return const Placeholder();

          // IconData flashIcon;
          // switch (_controller.value.flashMode) {
          //   case FlashMode.auto:
          //     flashIcon = Icons.flash_auto;
          //     break;
          //   case FlashMode.always:
          //     flashIcon = Icons.flash_on;
          //     break;
          //   case FlashMode.off:
          //   default:
          //     flashIcon = Icons.flash_off;
          //     break;
          // }

          return CameraPreview(_controller,
              child: Stack(
                children: [
                  Align(
                      alignment: Alignment.bottomCenter,
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 24),
                        child: ElevatedButton.icon(
                          icon: const Icon(Icons.camera),
                          label: Text(AppLocalizations.of(context)!.takePhoto),
                          onPressed: () async => await takePicture(context),
                        ),
                      )),
                  // Align(
                  //     alignment: Alignment.topRight,
                  //     child: ElevatedButton.icon(
                  //       icon: Icon(flashIcon),
                  //       label: Text(''),
                  //       onPressed: () {
                  //         switch (_controller.value.flashMode) {
                  //           case FlashMode.auto:
                  //             _controller.setFlashMode(FlashMode.off);
                  //             break;
                  //           case FlashMode.off:
                  //             _controller.setFlashMode(FlashMode.always);
                  //             break;
                  //           case FlashMode.always:
                  //           default:
                  //             _controller.setFlashMode(FlashMode.auto);
                  //             break;
                  //         }
                  //       },
                  //     )),
                ],
              ));
        },
      );

  Future<void> takePicture(BuildContext context) async {
    try {
      final image = await _controller.takePicture();

      if (!mounted) return;
      Navigator.pop(context);

      final id = const Uuid().v4();
      final start = DateTime.now();
      await FlutterImageCompress.compressAndGetFile(
        image.path,
        '${AppState().applicationSupportDirectory.path}/$id.jpg',
        quality: 93,
      );
      if (kDebugMode) print('jpg took ${DateTime.now().difference(start)}');
      widget.photoTaken(id);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
    }
  }
}
