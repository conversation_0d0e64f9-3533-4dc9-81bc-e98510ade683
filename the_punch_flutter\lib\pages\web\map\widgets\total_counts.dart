import 'package:flutter/material.dart';
import '../map.dart';

class TotalCounts extends StatelessWidget {
  final String month;
  final int year;
  final int total;
  final String time;

  final dynamic viewModel;

  const TotalCounts({
    Key? key,
    this.month = 'April',
    this.year = 2024,
    this.total = 229,
    this.time = '603h 36m',
    required this.viewModel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: const Color(0xFFEBF6FF),
        borderRadius: BorderRadius.circular(32),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Total Counts',
            style: TextStyle(
              fontFamily: 'Poppins',
              fontSize: 14,
              fontWeight: FontWeight.bold,
              letterSpacing: -0.14,
              color: Color(0xFF091F30),
            ),
          ),
          const SizedBox(height: 14),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Row(
                children: [
                    _buildDropdown(this.month, 100, viewModel.months),
                  const SizedBox(width: 7),
                  _buildDropdown(this.year, 100,viewModel.years),
                ],
              ),
              const SizedBox(width: 16),
              Row(
                children: [
                  _buildMetric('Total', this.viewModel.totalCount.toString()),
                  const SizedBox(width: 16),
                  _buildMetric('Time', this.viewModel.formattedTotalHours),
                ],
              ),
            ],
          ),
        ],
      ),
    );

  Widget _buildDropdown(dynamic selectedValue, double width, List<dynamic> items) => Container(
        width: width,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: const Color(0xFF091F30),
          borderRadius: BorderRadius.circular(8),
        ),
        child: DropdownButton<dynamic>(
          value: selectedValue,
          alignment: Alignment.center,
          icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
          style: const TextStyle(
            fontFamily: 'Poppins',
            fontSize: 14,
            fontWeight: FontWeight.w600,
            letterSpacing: -0.14,
            color: Colors.white,
          ),
          dropdownColor: const Color(0xFF091F30),
          underline: const SizedBox(),
          isExpanded: true,
          items: items
              .map((item) => DropdownMenuItem(
                alignment: Alignment.center,
                    value: item,
                    child: Text(item.toString()),
                  ))
              .toList(),
          onChanged: (selectedValue) async {
            if (selectedValue != null) {
              await this.viewModel.applyMonthFilter(selectedValue);
            }
          },
        ),
      );

  Widget _buildMetric(String label, String value) => Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Poppins',
            fontSize: 12,
            fontWeight: FontWeight.bold,
            letterSpacing: -0.12,
            color: Color(0xFF091F30),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontFamily: 'Poppins',
            fontSize: 20,
            fontWeight: FontWeight.w700,
            letterSpacing: -0.2,
            color: Color(0xFF091F30),
          ),
        ),
      ],
    );
}

