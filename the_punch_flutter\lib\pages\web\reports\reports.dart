// lib/pages/web/reports/reports.dart
// ignore_for_file: use_build_context_synchronously, prefer_expression_function_bodies

import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:printing/printing.dart';
import 'package:provider/provider.dart';
import 'package:universal_html/html.dart' as html;
import 'package:universal_html/js.dart' as js;
import 'package:pdf/pdf.dart'; // Correct import for PdfPageFormat

import '../../../api/api_model.dart';
import '../../../api/report_model.dart';
import '../../../dataModel/data_model.dart';
import '../../../dialogs/error_dialog.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/app_localization.dart';
import '../../../misc/extensions.dart';
import '../../../misc/logging.dart';
import '../../view_model_mixin.dart';
import '../my_scaffold.dart';

/// A simple Parameter model
class Parameter {
  final String name;
  final String prompt;
  final String uiControl;

  /// The "raw" value stored for this parameter (e.g. userName, or "ALL")
  String value;

  /// If a date/time param, `dateTime` is used
  DateTime? dateTime;

  /// Map of [key -> display], e.g. { 'username' -> 'First Last' }
  Map<String, String> validValues;

  Parameter({
    required this.name,
    required this.prompt,
    required this.uiControl,
    this.value = '',
    this.validValues = const {},
    this.dateTime,
  });
}

/// A Report model representing each report's details
class Report {
  final String reportId;
  final String reportName;
  final List<Parameter> reportInputs;

  Report({
    required this.reportId,
    required this.reportName,
    required this.reportInputs,
  });
}

/// A Manager class to handle all reports
class ReportManager {
  // Singleton pattern
  ReportManager._privateConstructor();
  static final ReportManager _instance = ReportManager._privateConstructor();
  factory ReportManager() => _instance;

  final List<Report> reports = [
    Report(
      reportId: '03bb3c49-819e-4e15-873b-e82fc0650c09',
      reportName: 'Location Details Hours',
      reportInputs: [
        Parameter(
          name: 'startDate',
          prompt: 'Start Date',
          uiControl: 'Date',
        ),
        Parameter(
          name: 'endDate',
          prompt: 'End Date',
          uiControl: 'Date',
        ),
        Parameter(
          name: 'locationName',
          prompt: 'Location:',
          uiControl: 'Dropdown',
        ),
        // Parameter(
        //   name: 'utcOffset',
        //   prompt: 'UTC Offset',
        //   uiControl: 'Integer',
        // ),
      ],
    ),
    Report(
      reportId: 'b863c9f2-27fa-402c-8ba5-a97b58b4272d',
      reportName: 'Employee Details Hours',
      reportInputs: [
        Parameter(
          name: 'startDate',
          prompt: 'Start Date',
          uiControl: 'Date',
        ),
        Parameter(
          name: 'endDate',
          prompt: 'End Date',
          uiControl: 'Date',
        ),
        Parameter(
          name: 'employeeName',
          prompt: 'Employee:',
          uiControl: 'Dropdown',
        ),
        // Parameter(
        //   name: 'utcOffset',
        //   prompt: 'UTC Offset',
        //   uiControl: 'Integer',
        // ),
        // Parameter(
        //   name: 'SortBy',
        //   prompt: 'Sort By',
        //   uiControl: 'Dropdown',
        // ),
      ],
    ),
    Report(
      reportId: 'fbb7bb12-3c62-4716-b7f8-3d34b6f4a8ea',
      reportName: 'Active Employee Locations',
      reportInputs: [
        Parameter(
          name: 'dateOfDay',
          prompt: 'Date Of Day',
          uiControl: 'DateTime',
        ),
        Parameter(
          name: 'utcOffset',
          prompt: 'UTC Offset',
          uiControl: 'Integer',
        ),
      ],
    ),
    Report(
      reportId: 'cd3b5463-ebec-4e89-bfc7-67a0d522c9df',
      reportName: 'User Sessions',
      reportInputs: [
        Parameter(
          name: 'startDate',
          prompt: 'Start Date',
          uiControl: 'Date',
        ),
        Parameter(
          name: 'endDate',
          prompt: 'End Date',
          uiControl: 'Date',
        ),
      ],
    ),
    Report(
      reportId: '355eb3ef-9103-41a0-a751-eaf246ac05f6',
      reportName: 'Active Managers',
      reportInputs: [
        Parameter(
          name: 'startDate',
          prompt: 'Start Date',
          uiControl: 'Date',
        ),
        Parameter(
          name: 'endDate',
          prompt: 'End Date',
          uiControl: 'Date',
        ),
        Parameter(
          name: 'utcOffset',
          prompt: 'UTC Offset',
          uiControl: 'Integer',
        ),
      ],
    ),
  ];

  /// Retrieves a report by its ID
  Report? getReportById(String reportId) {
    return reports.firstWhere(
      (r) => r.reportId == reportId,
      orElse: () => reports.first, // fallback if not found
    );
  }
}

/// The ViewModel that manages the selected report and its parameters
class _ViewModel extends ChangeNotifier with ViewModelMixin {
  String id;
  final ReportManager reportManager = ReportManager();

  List<Parameter> parameters = [];
  Uint8List reportData = Uint8List(0);
  String fileName = '';
  String csvContent = '';
  Uint8List excelData = Uint8List(0);
  bool isLoading = false;

  // Constructor - intentionally starts async initialization without awaiting
  // This is a common pattern in Flutter for ViewModels
  // ignore: use_build_context_synchronously
  _ViewModel(this.id) {
    debugPrint('ReportsPage ViewModel constructor: id=$id');
    // Initialize asynchronously without awaiting - this is intentional
    _init();
  }

  /// Kicks off initial report loading
  Future<void> _init() async {
    // This is called from the constructor but not awaited
    debugPrint('Initializing ViewModel with report id: $id');
    await selectReport(id);

    // Ensure date parameters are set after report is loaded
    await _ensureDateParametersAreSet();
  }

  /// Makes sure date parameters have values
  Future<void> _ensureDateParametersAreSet() async {
    debugPrint('Ensuring date parameters are set');

    // Get current date values
    final now = DateTime.now();
    final validYear = now.year ;

    // Default start date (first day of current month)
    final defaultStartDate = DateTime(validYear, now.month);

    // Default end date (today)
    final defaultEndDate = DateTime(validYear+1, now.month, now.day);

    // Set locale for formatting
    const locale = Locale('en', 'US');

    // Check if startDate parameter exists and set it if needed
    final startDateParam = parameters.firstWhere(
      (p) => p.name == 'startDate',
      orElse: () => Parameter(name: '', prompt: '', uiControl: ''),
    );

    if (startDateParam.name.isNotEmpty && startDateParam.dateTime == null) {
      debugPrint('Setting default startDate parameter');
      startDateParam.value = defaultStartDate.toFormattedDateWithYear(locale);
      startDateParam.dateTime = defaultStartDate;
    }

    // Check if endDate parameter exists and set it if needed
    final endDateParam = parameters.firstWhere(
      (p) => p.name == 'endDate',
      orElse: () => Parameter(name: '', prompt: '', uiControl: ''),
    );

    if (endDateParam.name.isNotEmpty && endDateParam.dateTime == null) {
      debugPrint('Setting default endDate parameter');
      endDateParam.value = defaultEndDate.toFormattedDateWithYear(locale);
      endDateParam.dateTime = defaultEndDate;
    }

    // Check if dateOfDay parameter exists and set it if needed
    final dateOfDayParam = parameters.firstWhere(
      (p) => p.name == 'dateOfDay',
      orElse: () => Parameter(name: '', prompt: '', uiControl: ''),
    );

    if (dateOfDayParam.name.isNotEmpty && dateOfDayParam.dateTime == null) {
      debugPrint('Setting default dateOfDay parameter');
      final dateTime = DateTime(
        defaultEndDate.year,
        defaultEndDate.month,
        defaultEndDate.day,
        now.hour,
        now.minute,
      );

      if (dateOfDayParam.uiControl == 'DateTime') {
        dateOfDayParam.value = dateTime.toFormattedDateTimeWithYear(locale);
      } else {
        dateOfDayParam.value = dateTime.toFormattedDateWithYear(locale);
      }
      dateOfDayParam.dateTime = dateTime;
    }

    // Notify listeners of the changes
    notifyListeners();
  }

  @override
  Future<void> refresh() => selectReport(id);

  /// Clears old data, loads the requested report, and populates dynamic dropdowns
  Future<void> selectReport(String reportKey) async {
    debugPrint('selectReport called with reportKey=$reportKey');
    isLoading = true;
    notifyListeners();

    try {
      final report = reportManager.getReportById(reportKey);
      if (report == null) {
        debugPrint('reportKey not found; defaulting to first report');
        id = reportManager.reports.first.reportId;
      } else {
        id = report.reportId;
      }

      // Make a fresh copy of the parameters
      final originalParams = reportManager.getReportById(id)?.reportInputs ?? [];
      parameters = originalParams
          .map(
            (orig) => Parameter(
              name: orig.name,
              prompt: orig.prompt,
              uiControl: orig.uiControl,
              validValues: Map.of(orig.validValues),
            ),
          )
          .toList();

      // Clear previous data
      reportData = Uint8List(0);
      fileName = '';
      csvContent = '';
      excelData = Uint8List(0);

      // 1) Populate employeeName param if present
      for (final param in parameters) {
        if (param.name == 'employeeName') {
          // Get all employees
          final employees = await DataModel().userModel.allEmployees;

          // The 'key' => username, the 'value' => "firstName + lastName"
          param.validValues = {'ALL': 'ALL'};
          for (final user in employees) {
            final username = user.username;
            final fullName = '${user.firstName} ${user.lastName}'.trim();
            param.validValues[username!] = fullName;
          }
          // Default to ALL
          param.value = 'ALL';
        }
      }

      // 2) Populate locationName param if present
      for (final param in parameters) {
        if (param.name == 'locationName') {
          final locations = await DataModel().locationModel.all;

          // The 'key' => location.id, the 'value' => location.name
          param.validValues = {'ALL': 'ALL'};
          for (final loc in locations) {
            param.validValues[loc.id] = loc.name;
          }
          // Default to ALL
          param.value = 'ALL';
        }
      }

      // 3) Set default dates for date parameters
      final now = DateTime.now();
      final validYear = now.year > 2024 ? 2024 : now.year;

      // Default start date (first day of current month)
      final defaultStartDate = DateTime(validYear, now.month);

      // Default end date (today)
      final defaultEndDate = DateTime(validYear+1, now.month, now.day);

      // Set default dates for date parameters
      for (final param in parameters) {
        if (param.name == 'startDate' && param.dateTime == null) {
          // Format the date for display
          const locale = Locale('en', 'US'); // Default locale
          param.value = defaultStartDate.toFormattedDateWithYear(locale);
          param.dateTime = defaultStartDate;
        } else if (param.name == 'endDate' && param.dateTime == null) {
          // Format the date for display
          const locale = Locale('en', 'US'); // Default locale
          param.value = defaultEndDate.toFormattedDateWithYear(locale);
          param.dateTime = defaultEndDate;
        } else if (param.name == 'dateOfDay' && param.dateTime == null) {
          // For dateOfDay parameter (Active Employee Locations report)
          final dateTime = DateTime(
            defaultEndDate.year,
            defaultEndDate.month,
            defaultEndDate.day,
            now.hour,
            now.minute,
          );

          // Format the date for display
          const locale = Locale('en', 'US'); // Default locale
          if (param.uiControl == 'DateTime') {
            param.value = dateTime.toFormattedDateTimeWithYear(locale);
          } else {
            param.value = dateTime.toFormattedDateWithYear(locale);
          }
          param.dateTime = dateTime;
        }
      }

    } catch (e) {
      debugPrint('Error selecting report: $e');
      rethrow;
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  /// Reads the current param value
  String getParameter(String name) {
    return parameters
        .firstWhere((p) => p.name == name, orElse: () => parameters.first)
        .value;
  }

  /// Sets a param value
  void setParameter(String name, String value) {
    final param = parameters.firstWhere((p) => p.name == name,
        orElse: () => parameters.first);
    param.value = value;

    // Clear existing PDF
    reportData = Uint8List(0);
    notifyListeners();
  }

  /// For Date/DateTime parameters
  DateTime? getDateTimeParameter(String name) {
    return parameters
        .firstWhere((p) => p.name == name, orElse: () => parameters.first)
        .dateTime;
  }

  void setDateTimeParameter(String name, String displayValue, DateTime dt) {
    final param = parameters.firstWhere((p) => p.name == name,
        orElse: () => parameters.first);
    param.value = displayValue;
    param.dateTime = dt;

    // Clear existing PDF
    reportData = Uint8List(0);
    notifyListeners();
  }

  /// For MultipleChoice parameters
  void setMultiSelectValues(String name, Iterable<String> labels) {
    final param = parameters.firstWhere((p) => p.name == name,
        orElse: () => parameters.first);

    // Convert displayed label back to key
    final selectedKeys = param.validValues.entries
        .where((entry) => labels.contains(entry.value))
        .map((entry) => entry.key);

    param.value = selectedKeys.join(',');
    reportData = Uint8List(0);
    notifyListeners();
  }

  List<String> getMultiSelectValues(String name) {
    final param = parameters.firstWhere((p) => p.name == name,
        orElse: () => parameters.first);
    if (param.value.isEmpty) return [];
    final keys = param.value.split(',');
    return param.validValues.entries
        .where((entry) => keys.contains(entry.key))
        .map((entry) => entry.value)
        .toList();
  }

  /// Called by 'Run Report' -> returns PDF
  Future<void> runReport() async {
    debugPrint('runReport called for id=$id');
    final paramValues = await _buildParamValues();
    isLoading = true;
    notifyListeners();

    try {
      debugPrint('Running with paramValues=$paramValues');
      final response = await ReportModel().runReport(id, paramValues, 'pdf');

      // Base64 -> bytes
      reportData = base64Decode(response.reportData);
      fileName = response.fileName;
      debugPrint('Report loaded successfully');
    } catch (e) {
      debugPrint('Error running report: $e');
      rethrow;
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  /// Called by 'Download Payroll' -> returns CSV
  Future<void> runPayroll() async {
    debugPrint('runPayroll called for id=$id');
    final paramValues = await _buildParamValues();
    isLoading = true;
    notifyListeners();

    try {
      final response = await ReportModel().runPayroll(id, paramValues, 'csv');
      csvContent = response.reportData;
      fileName = response.fileName;

      // Immediately download
      download(csvContent, fileName);
      debugPrint('Payroll downloaded successfully');
    } catch (e) {
      debugPrint('Error running payroll: $e');
      rethrow;
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  /// Called by 'Export Excel'
  Future<void> exportExcel() async {
    debugPrint('exportExcel called for id=$id');
    // We'll use paramValues when implementing Excel export
    isLoading = true;
    notifyListeners();

    try {
      // If your API returns Excel files, do something like:
      // final response = await ReportModel().exportExcel(id, await paramValues, 'xlsx');
      // excelData = base64Decode(response.reportData);
      // final excelFileName = response.fileName;
      // downloadExcel(excelData, excelFileName,
      //     mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      debugPrint('Export Excel functionality not implemented.');
    } catch (e) {
      debugPrint('Error exporting Excel: $e');
      rethrow;
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  /// Builds a Map of param -> value to send to the server
Future<Map<String, String>> _buildParamValues() async {
  final paramValues = <String, String>{};

  // Debug log all parameters
  debugPrint('Building parameter values for report: $id');
  for (final p in parameters) {
    debugPrint('Parameter: ${p.name}, Value: ${p.value}, DateTime: ${p.dateTime}, UIControl: ${p.uiControl}');
  }

  // 1) Gather the "visible" parameters from the user
  for (final p in parameters) {
    if ((p.uiControl == 'Date' || p.uiControl == 'DateTime') && p.dateTime != null) {
      if (p.uiControl == 'Date') {
        // Just the date portion
        paramValues[p.name] = p.dateTime!.toIso8601String().split('T').first;
        debugPrint('Setting date parameter ${p.name} = ${paramValues[p.name]}');
      } else {
        // Full date/time
        paramValues[p.name] = p.dateTime!.toIso8601String();
        debugPrint('Setting datetime parameter ${p.name} = ${paramValues[p.name]}');
      }
    } else {
      paramValues[p.name] = p.value;
      debugPrint('Setting regular parameter ${p.name} = ${paramValues[p.name]}');
    }
  }

  // 2) Force in the hidden parameters
  //    (only for the reports that require them)

  switch (id) {
    case '03bb3c49-819e-4e15-873b-e82fc0650c09':
      // Location Details Hours
      paramValues['utcOffset'] = '-5';
      break;

    case 'b863c9f2-27fa-402c-8ba5-a97b58b4272d':
      // Employee Details Hours
      paramValues['utcOffset'] = '-5';
      paramValues['sortBy'] = 'Clocked In - Ascending';
      break;

    case 'fbb7bb12-3c62-4716-b7f8-3d34b6f4a8ea':
      // Active Employee Locations
      paramValues['utcOffset'] = '-5';
      break;

    case '355eb3ef-9103-41a0-a751-eaf246ac05f6':
      // Active Managers
      paramValues['utcOffset'] = '-5';
      break;

    default:
      break;
  }

  // Possibly include orgName or anything else
  // final prefs = await SharedPreferences.getInstance();
  // paramValues['orgName'] = prefs.getString('organizationName') ?? 'Unknown Org.';

  // Final debug log of all parameter values
  debugPrint('Final parameter values: $paramValues');

  return paramValues;
}


  /// CSV/Excel download for web only
  void download(String content, String fileName, {String mimeType = 'text/csv'}) {
    final blob = html.Blob([content], mimeType);
    final url = html.Url.createObjectUrl(blob);
    final doc = js.context['document'] as html.HtmlDocument;
    final link = html.AnchorElement(href: url)
      ..download = fileName
      ..style.display = 'none';
    doc.body?.append(link);
    link.click();
    link.remove();
    html.Url.revokeObjectUrl(url);
  }

  void downloadExcel(Uint8List bytes, String fileName,
      {String mimeType =
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'}) {
    final blob = html.Blob([bytes], mimeType);
    final url = html.Url.createObjectUrl(blob);
    final doc = js.context['document'] as html.HtmlDocument;
    final link = html.AnchorElement(href: url)
      ..download = fileName
      ..style.display = 'none';
    doc.body?.append(link);
    link.click();
    link.remove();
    html.Url.revokeObjectUrl(url);
  }
}

/// A helper container for consistent styling
class DecoratedContainer extends StatelessWidget {
  final String labelText;
  final bool centered;
  final Widget child;

  const DecoratedContainer({
    super.key,
    required this.labelText,
    this.centered = false,
    required this.child,
  });

  @override
  Widget build(BuildContext context) => Column(
        crossAxisAlignment:
            centered ? CrossAxisAlignment.center : CrossAxisAlignment.start,
        children: [
          Text(
            labelText,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: child,
          ),
        ],
      );
}

/// The main Reports Page with a scaffold
class ReportsPage extends StatelessWidget {
  const ReportsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final initialReportId = ReportManager().reports.isNotEmpty
        ? ReportManager().reports.first.reportId
        : '';

    return ChangeNotifierProvider(
      create: (_) => _ViewModel(initialReportId),
      child: MyScaffold(
        title: AppLocalization.of(context).reports,
        body: const _Body(),
      ),
    );
  }
}

/// The page body with the new design matching the image
class _Body extends StatelessWidget {
  const _Body();

  // No static colors needed anymore

  @override
  Widget build(BuildContext context) {
    final viewModel = context.watch<_ViewModel>();

    return Stack(
      children: [
        Column(
          children: [
            // Header with controls
            _ReportHeader(viewModel: viewModel),

            // Main content area
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.2),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        // PDF Viewer
                        _buildPdfPreview(context, viewModel),

                        // Action buttons at the bottom
                        _buildActionButtons(context, viewModel),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),

        // Show loading overlay
        if (viewModel.isLoading)
          Positioned.fill(
            child: ColoredBox(
              color: Colors.black.withOpacity(0.5),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// PDF preview
  Widget _buildPdfPreview(BuildContext context, _ViewModel viewModel) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isWide = screenWidth > 1290;

    return Container(
      padding: const EdgeInsets.all(16),
      height: 600,
      child: LayoutBuilder(
        builder: (context, constraints) => Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: viewModel.isLoading
              ? const Center(child: CircularProgressIndicator())
              : viewModel.reportData.isEmpty
                  ? Center(
                      child: Text(
                        'No report to show yet.\nRun a report to preview!',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 16,
                        ),
                      ),
                    )
                  : PdfPreview(
                      build: (PdfPageFormat format) async => viewModel.reportData,
                      useActions: false,
                      maxPageWidth: isWide
                          ? constraints.maxWidth * 0.8
                          : constraints.maxWidth,
                    ),
        ),
      ),
    );
  }

  /// Action buttons at the bottom
  Widget _buildActionButtons(BuildContext context, _ViewModel viewModel) {
    return Padding(
      padding: MediaQuery.of(context).size.width < 600
          ? const EdgeInsets.all(6)
          :  const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Download Payroll
          OutlinedButton.icon(
            icon: const Icon(Icons.download),
            label: const Text('Download Payroll'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.blue,
              side: const BorderSide(color: Colors.blue),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () async {
              try {
                await viewModel.runPayroll();
              } on ApiException catch (e, stack) {
                await logApiException(e, stack);
                if (!context.mounted) return;
                await showDialog(
                  context: context,
                  builder: (_) => ErrorDialog(errorCode: e.errorCode),
                );
              } on Exception catch (e, stack) {
                await logException(e, stack);
                if (!context.mounted) return;
                await showDialog(
                  context: context,
                  builder: (_) => const ErrorDialog(),
                );
              }
            },
          ),

          const SizedBox(width: 16),

          // Export Excel
          OutlinedButton.icon(
            icon: const Icon(Icons.grid_on),
            label: const Text('Export Excel'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.blue,
              side: const BorderSide(color: Colors.blue),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () async {
              try {
                await viewModel.exportExcel();
              } on ApiException catch (e, stack) {
                await logApiException(e, stack);
                if (!context.mounted) return;
                await showDialog(
                  context: context,
                  builder: (_) => ErrorDialog(errorCode: e.errorCode),
                );
              } on Exception catch (e, stack) {
                await logException(e, stack);
                if (!context.mounted) return;
                await showDialog(
                  context: context,
                  builder: (_) => const ErrorDialog(),
                );
              }
            },
          ),

          const SizedBox(width: 16),
          // Print PDF
          if( MediaQuery.of(context).size.width > 600 )
          OutlinedButton.icon(
            icon: const Icon(Icons.print),
            label: const Text('Print PDF'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.blue,
              side: const BorderSide(color: Colors.blue),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: viewModel.reportData.isNotEmpty
                ? () async {
                    await Printing.sharePdf(
                      bytes: viewModel.reportData,
                      filename: viewModel.fileName,
                    );
                  }
                : null,
          ),
        ],
      ),
    );
  }
}

/// The Report Header component with controls in a row
class _ReportHeader extends StatelessWidget {
  final _ViewModel viewModel;

  const _ReportHeader({required this.viewModel});

  @override
  Widget build(BuildContext context) {
    // We'll use the current selected report from the viewModel
    bool isMobile = MediaQuery.of(context).size.width < 600;

    // Set default date to today and a valid start date
    final now = DateTime.now();

    // Ensure we use valid dates that won't exceed our date picker limits
    final validYear =  now.year;
    final today = DateTime(validYear+1, now.month, now.day); // Today with valid year
    final defaultStartDate = DateTime(validYear, now.month); // First day of current month

    return Container(
      height: isMobile ? 400 : null  ,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFEBF5FF), // Light blue background
        borderRadius: BorderRadius.circular(8),
      ),
      child: isMobile?  Column(
        children: [
          // Report dropdown
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Report',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                DecoratedBox(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(horizontal: 12),
                    ),
                    value: viewModel.id,
                    onChanged: (val) async {
                      if (val != null) await viewModel.selectReport(val);
                    },
                    items: viewModel.reportManager.reports
                        .map(
                          (report) => DropdownMenuItem<String>(
                            value: report.reportId,
                            child: Text(report.reportName),
                          ),
                        )
                        .toList(),
                  ),
                ),
              ],
            ),
          ),


          // Start Date
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Start Date',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                _buildDatePicker(
                  context,
                  'startDate',
                  defaultStartDate,
                  (date) async {
                    // Find the parameter for start date or dateOfDay
                    final param = viewModel.parameters.firstWhere(
                      (p) => p.name == 'startDate' || p.name == 'dateOfDay',
                      orElse: () => Parameter(name: 'startDate', prompt: 'Start Date', uiControl: 'Date'),
                    );

                    // Set the date
                    final locale = Localizations.localeOf(context);

                    // If this is the Active Employee Locations report with dateOfDay parameter
                    // we need to preserve the time component for DateTime parameters
                    if (param.name == 'dateOfDay' && param.uiControl == 'DateTime') {
                      // For DateTime parameters, preserve the time component
                      final now = DateTime.now();
                      final dateTime = DateTime(
                        date.year,
                        date.month,
                        date.day,
                        now.hour,
                        now.minute,
                      );
                      viewModel.setDateTimeParameter(
                        param.name,
                        dateTime.toFormattedDateTimeWithYear(locale),
                        dateTime,
                      );
                    } else {
                      // For regular Date parameters
                      viewModel.setDateTimeParameter(
                        param.name,
                        date.toFormattedDateWithYear(locale),
                        date,
                      );
                    }
                  },
                ),
              ],
            ),
          ),


          // End Date
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'End Date',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                _buildDatePicker(
                  context,
                  'endDate',
                  today,
                  (date) async {
                    // Find the parameter for end date
                    final param = viewModel.parameters.firstWhere(
                      (p) => p.name == 'endDate',
                      orElse: () => Parameter(name: 'endDate', prompt: 'End Date', uiControl: 'Date'),
                    );

                    // Set the date
                    final locale = Localizations.localeOf(context);

                    // For regular Date parameters
                    viewModel.setDateTimeParameter(
                      param.name,
                      date.toFormattedDateWithYear(locale),
                      date,
                    );
                  },
                ),
              ],
            ),
          ),

         

          // Location dropdown
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Location',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                DecoratedBox(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(horizontal: 12),
                    ),
                    value: 'ALL',
                    onChanged: (val) {
                      if (val != null) {
                        // Find the parameter for location
                        final param = viewModel.parameters.firstWhere(
                          (p) => p.name == 'locationName',
                          orElse: () => Parameter(name: 'locationName', prompt: 'Location', uiControl: 'Dropdown'),
                        );

                        // Set the location
                        viewModel.setParameter(param.name, val);
                      }
                    },
                    items: [
                      const DropdownMenuItem<String>(
                        value: 'ALL',
                        child: Text('All'),
                      ),
                      // Add other locations if available
                      ...viewModel.parameters
                          .firstWhere(
                            (p) => p.name == 'locationName',
                            orElse: () => Parameter(
                              name: 'locationName',
                              prompt: 'Location',
                              uiControl: 'Dropdown',
                              validValues: {'ALL': 'ALL'},
                            ),
                          )
                          .validValues
                          .entries
                          .where((entry) => entry.key != 'ALL')
                          .map(
                            (entry) => DropdownMenuItem<String>(
                              value: entry.key,
                              child: Text(entry.value),
                            ),
                          ),
                    ],
                  ),
                ),
              ],
            ),
          ),


          // Run Report button
          Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              const SizedBox(height: 32), // Align with the inputs
              ElevatedButton.icon(
                icon: const Icon(Icons.play_arrow, color: Colors.white),
                label: const Text(
                  'Run Report',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorHelper.thePunchAdminButtonBlue(), // Blue button
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onPressed: () async {
                  try {
                    await viewModel.runReport();
                  } on ApiException catch (e, stack) {
                    await logApiException(e, stack);
                    if (!context.mounted) return;
                    await showDialog(
                      context: context,
                      builder: (_) => ErrorDialog(errorCode: e.errorCode),
                    );
                  } on Exception catch (e, stack) {
                    await logException(e, stack);
                    if (!context.mounted) return;
                    await showDialog(
                      context: context,
                      builder: (_) => const ErrorDialog(),
                    );
                  }
                },
              ),
            ],
          ),
        ],
      ): Row(
        children: [
          // Report dropdown
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Report',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                DecoratedBox(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(horizontal: 12),
                    ),
                    value: viewModel.id,
                    onChanged: (val) async {
                      if (val != null) await viewModel.selectReport(val);
                    },
                    items: viewModel.reportManager.reports
                        .map(
                          (report) => DropdownMenuItem<String>(
                            value: report.reportId,
                            child: Text(report.reportName),
                          ),
                        )
                        .toList(),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 16),

          // Start Date
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Start Date',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                _buildDatePicker(
                  context,
                  'startDate',
                  defaultStartDate,
                  (date) async {
                    // Find the parameter for start date or dateOfDay
                    final param = viewModel.parameters.firstWhere(
                      (p) => p.name == 'startDate' || p.name == 'dateOfDay',
                      orElse: () => Parameter(name: 'startDate', prompt: 'Start Date', uiControl: 'Date'),
                    );

                    // Set the date
                    final locale = Localizations.localeOf(context);

                    // If this is the Active Employee Locations report with dateOfDay parameter
                    // we need to preserve the time component for DateTime parameters
                    if (param.name == 'dateOfDay' && param.uiControl == 'DateTime') {
                      // For DateTime parameters, preserve the time component
                      final now = DateTime.now();
                      final dateTime = DateTime(
                        date.year,
                        date.month,
                        date.day,
                        now.hour,
                        now.minute,
                      );
                      viewModel.setDateTimeParameter(
                        param.name,
                        dateTime.toFormattedDateTimeWithYear(locale),
                        dateTime,
                      );
                    } else {
                      // For regular Date parameters
                      viewModel.setDateTimeParameter(
                        param.name,
                        date.toFormattedDateWithYear(locale),
                        date,
                      );
                    }
                  },
                ),
              ],
            ),
          ),

          const SizedBox(width: 16),

          // End Date
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'End Date',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                _buildDatePicker(
                  context,
                  'endDate',
                  today,
                  (date) async {
                    // Find the parameter for end date
                    final param = viewModel.parameters.firstWhere(
                      (p) => p.name == 'endDate',
                      orElse: () => Parameter(name: 'endDate', prompt: 'End Date', uiControl: 'Date'),
                    );

                    // Set the date
                    final locale = Localizations.localeOf(context);

                    // For regular Date parameters
                    viewModel.setDateTimeParameter(
                      param.name,
                      date.toFormattedDateWithYear(locale),
                      date,
                    );
                  },
                ),
              ],
            ),
          ),

          const SizedBox(width: 16),

          // Location dropdown
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Location',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                DecoratedBox(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(horizontal: 12),
                    ),
                    value: 'ALL',
                    onChanged: (val) {
                      if (val != null) {
                        // Find the parameter for location
                        final param = viewModel.parameters.firstWhere(
                          (p) => p.name == 'locationName',
                          orElse: () => Parameter(name: 'locationName', prompt: 'Location', uiControl: 'Dropdown'),
                        );

                        // Set the location
                        viewModel.setParameter(param.name, val);
                      }
                    },
                    items: [
                      const DropdownMenuItem<String>(
                        value: 'ALL',
                        child: Text('All'),
                      ),
                      // Add other locations if available
                      ...viewModel.parameters
                          .firstWhere(
                            (p) => p.name == 'locationName',
                            orElse: () => Parameter(
                              name: 'locationName',
                              prompt: 'Location',
                              uiControl: 'Dropdown',
                              validValues: {'ALL': 'ALL'},
                            ),
                          )
                          .validValues
                          .entries
                          .where((entry) => entry.key != 'ALL')
                          .map(
                            (entry) => DropdownMenuItem<String>(
                              value: entry.key,
                              child: Text(entry.value),
                            ),
                          ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 16),

          // Run Report button
          Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              const SizedBox(height: 32), // Align with the inputs
              ElevatedButton.icon(
                icon: const Icon(Icons.play_arrow, color: Colors.white),
                label: const Text(
                  'Run Report',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorHelper.thePunchAdminButtonBlue(), // Blue button
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                onPressed: () async {
                  try {
                    await viewModel.runReport();
                  } on ApiException catch (e, stack) {
                    await logApiException(e, stack);
                    if (!context.mounted) return;
                    await showDialog(
                      context: context,
                      builder: (_) => ErrorDialog(errorCode: e.errorCode),
                    );
                  } on Exception catch (e, stack) {
                    await logException(e, stack);
                    if (!context.mounted) return;
                    await showDialog(
                      context: context,
                      builder: (_) => const ErrorDialog(),
                    );
                  }
                },
              ),
            ],
          ),
        ],
      ),
    );
  
  }

  // Helper method to build a date picker
  Widget _buildDatePicker(
    BuildContext context,
    String fieldName,
    DateTime initialDate,
    Function(DateTime) onDateSelected,
  ) {
    // Get the current date from the viewModel if available
    final paramName = fieldName == 'startDate' ? 'startDate' : 'endDate';
    final now = DateTime.now();

    // Ensure we use valid dates that won't exceed our date picker limits
    final validYear =  now.year;
    final today = DateTime(validYear+1, now.month, now.day); 
    // Make sure we have a valid date within the allowed range
    final lastDate = today;
    final firstDate = DateTime(2020);

    // Get the date from viewModel or use initialDate as fallback
    var dateValue = viewModel.getDateTimeParameter(paramName) ?? initialDate;

    // Ensure the date is within valid range
    if (dateValue.isAfter(lastDate)) {
      dateValue = lastDate;
    } else if (dateValue.isBefore(firstDate)) {
      dateValue = firstDate;
    }

    // Use the validated date
    final currentDate = dateValue;

    return DecoratedBox(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () async {
            // Ensure we're using the mounted context
            if (!context.mounted) return;

            // Show the date picker with proper context
            final picked = await showDatePicker(
              context: context,
              initialDate: currentDate,
              firstDate: firstDate,
              lastDate: lastDate,
              builder: (BuildContext context, Widget? child) {
                return Theme(
                  data: ThemeData.light().copyWith(
                    colorScheme: ColorScheme.light(
                      primary: ColorHelper.thePunchAdminButtonBlue(),
                    ),
                    dialogBackgroundColor: Colors.white,
                  ),
                  child: child!,
                );
              },
            );

            // If a date was picked, call the callback
            if (picked != null) {
              onDateSelected(picked);
            }
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  fieldName == 'endDate' && currentDate.day == DateTime.now().day
                      ? 'Today'
                      : currentDate.toFormattedDateWithYear(Localizations.localeOf(context)),
                  style: const TextStyle(fontSize: 14),
                ),
                const Icon(Icons.calendar_today, size: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }
}


