import 'dart:async';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../../dataModel/data/job_type.dart';
import '../../../dataModel/data_model.dart';
import '../../../misc/app_localization.dart';
import '../../../misc/extensions.dart';
import '../../view_model_mixin.dart';
import '../home/<USER>';
import '../my_scaffold.dart';
import 'job_type_mixin.dart';
import '../../../state/permissions_state.dart';
import '../../../widgets/active_toggle.dart';
import '../../../widgets/decorated_text_field.dart';

class ViewJobTypePage extends StatelessWidget {
  final String jobTypeId;

  ViewJobTypePage(Map<String, String> queryParms, {super.key})
      : jobTypeId = queryParms['id'] ?? '';

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
        create: (context) => _ViewModel(jobTypeId),
        child: MyScaffold(
          title: AppLocalization.of(context).jobType,
          body: _Body(),
        ),
      );
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1300),
          child: Column(
            children: [
              _BodyHeader(),
              _Row1(),
            ],
          ),
        ),
      );
}

class _BodyHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final headline6 = Theme.of(context)
        .textTheme
        .titleLarge
        ?.copyWith(fontWeight: FontWeight.bold);
    return IntrinsicHeight(
      child: Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          final jobType = viewModel.jobType;
          if (jobType == null) return Container();

          return Stack(
            children: [
              Align(
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Text(jobType.name, style: headline6),
                ),
              ),
              Align(
                alignment: Alignment.centerRight,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (PermissionsState().editJobTypes)
                      Padding(
                        padding: const EdgeInsets.all(8),
                        child: ActiveButton(
                          value: jobType.isActive,
                          onChanged: (value) =>
                              unawaited(viewModel.setActive(jobType, value)),
                        ),
                      ),
                    if (PermissionsState().editJobTypes)
                      Padding(
                        padding: const EdgeInsets.all(8),
                        child: ElevatedButton(
                          onPressed: () async => context.pushNamed(
                            '/jobTypes/edit',
                            queryParameters: {'id': jobType.id},
                          ),
                          child: Text(AppLocalization.of(context).editJobType),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class _Row1 extends StatelessWidget {
  @override
  Widget build(BuildContext context) => IntrinsicHeight(
        child: Consumer<_ViewModel>(
          builder: (context, viewModel, child) {
            final jobType = viewModel.jobType;
            if (jobType == null) return Container();

            // A helper method to build a column of label and value text without background.
            Widget buildLabelValue(String label, String value) {
              return Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Colors.grey.shade700,
                          ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      value,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              );
            }

            return Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Expanded(
                  flex: 2,
                  child: buildLabelValue(
                    AppLocalization.of(context).jobType,
                    jobType.name,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: buildLabelValue(
                    AppLocalization.of(context).description,
                    jobType.description ?? '',
                  ),
                ),
                Expanded(
                  child: buildLabelValue(
                    AppLocalization.of(context).activate,
                    jobType.isActive.toActive(context),
                  ),
                ),
              ],
            );
          },
        ),
      );
}

class _ViewModel extends ChangeNotifier with ViewModelMixin, JobTypeMixin {
  final String jobTypeId;
  JobType? jobType;

  _ViewModel(this.jobTypeId) {
    addListenables([
      DataModel().jobTypeModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    jobType = await DataModel().jobTypeModel.getById(jobTypeId);
    notifyListeners();
  }

  Future<void> setActive(JobType jobType, bool value) async {
    jobType.isActive = value;
    await DataModel().jobTypeModel.saveDirty([jobType]);
    notifyListeners();
  }
}
