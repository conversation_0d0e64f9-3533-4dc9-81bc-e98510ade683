import 'package:json_annotation/json_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../misc/json_conversion.dart';

part 'system.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class SystemRequest {
  @JsonKey(name: 'Request_ServerIP', defaultValue: '')
  final String serverIP;
  @JsonKey(name: 'Request_DatabaseName', defaultValue: '')
  final String databaseName;
  @JsonKey(name: 'Request_SessionID', defaultValue: '')
  final String sessionId;

  SystemRequest({required this.serverIP, required this.databaseName, required this.sessionId});

  static Future<SystemRequest> create() async {
    final prefs = await SharedPreferences.getInstance();
    final serverIP = prefs.getString('serverIP');
    final databaseName = prefs.getString('databaseName');
    final sessionId = prefs.getString('sessionId');

    return SystemRequest(
      serverIP: serverIP!,
      databaseName: databaseName!,
      sessionId: sessionId!,
    );
  }

  factory SystemRequest.fromJson(Map<String, dynamic> json) => _$SystemRequestFromJson(json);
  Map<String, dynamic> toJson() => _$SystemRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class SystemResponse {
  String? errorCode;
  @JsonKey(fromJson: nullableDateTimeFromJson, toJson: nullableDateTimeToJson)
  DateTime? serverTime;

  SystemResponse(this.errorCode, this.serverTime);

  bool get isError => errorCode != null;

  bool get isLogoutError => errorCode != null && (errorCode == 'SESSION_LOGGED_OUT' || errorCode == 'SESSION_TIMED_OUT' || errorCode == 'SESSION_FORCED_OUT');

  factory SystemResponse.fromJson(Map<String, dynamic> json) => _$SystemResponseFromJson(json);
  Map<String, dynamic> toJson() => _$SystemResponseToJson(this);
}
