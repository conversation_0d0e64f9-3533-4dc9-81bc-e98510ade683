import 'dart:async';

import 'package:flutter/material.dart';
import '../../dataModel/data/user.dart';
import '../../dataModel/data_model.dart';
import '../constrained_search_dialog.dart';
import '../material_dialog.dart';
import '../../misc/app_localization.dart';
import '../../misc/change_notification_builder.dart';
import '../../pages/view_model_mixin.dart';
import '../../widgets/padded_card.dart';

class EmployeesDialog extends StatelessWidget {
  final Iterable<String> ignoreEmployees;
  final Function(String) onSelection;
  final search = ValueNotifier<String>('');

  EmployeesDialog({super.key, required this.onSelection, this.ignoreEmployees = const []});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline6 = theme.textTheme.titleLarge;

    return ChangeNotifierBuilder<_ViewModel>(
      create: (context) => _ViewModel(ignoreEmployees: ignoreEmployees),
      builder: (context, viewModel, child) {
        if (viewModel.employees.isEmpty) return MaterialDialog(title: AppLocalization.of(context).employees, child: const Text('No other employees.'));
        return ConstrainedSearchDialog(
          title: Text(AppLocalization.of(context).employees, style: headline6),
          builder: (search) {
            final visibleEmployees = viewModel.employees.where((e) => _employeeMatches(e, search));
            return visibleEmployees.map((e) => _Tile(employee: e, employeeSelected: onSelection));
          },
        );
      },
    );
  }

  bool _employeeMatches(User employee, String search) => search.isEmpty || employee.name.toLowerCase().contains(search.toLowerCase());
}

class _Tile extends StatelessWidget {
  const _Tile({required this.employeeSelected, required this.employee});

  final User employee;
  final Function(String) employeeSelected;

  @override
  Widget build(BuildContext context) => GestureDetector(
        onTap: () {
          Navigator.of(context).pop();
          employeeSelected(employee.id);
        },
        child: PaddedCard(
          child: Center(child: Text(employee.name)),
        ),
      );
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  Iterable<User> employees = [];

  Iterable<String> ignoreEmployees;
  _ViewModel({required this.ignoreEmployees}) {
    addListenables([DataModel().userModel]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    employees = (await DataModel().userModel.activeEmployees).where((e) => !ignoreEmployees.contains(e.id));
    notifyListeners();
  }
}
