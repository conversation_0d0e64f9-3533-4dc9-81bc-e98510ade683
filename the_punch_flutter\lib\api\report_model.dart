import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/widgets.dart';
import 'api_model.dart';
import 'requests/reports.dart';
import 'requests/system.dart';

class ReportModel {
  String get baseAddress => ApiModel().baseAddress;
  final _basePath = 'api/report/';

  ValueNotifier<String?> get logoutErrorCode => ApiModel().logoutErrorCode;
  ValueNotifier<DateTime?> get serverTime => ApiModel().serverTime;
  bool get production => ApiModel.production;

  static ReportModel? _singleton;

  factory ReportModel() {
    _singleton ??= ReportModel._();
    return _singleton!;
  }

  ReportModel._();

  Future<Map<String, dynamic>?> post(String endPoint, request) async {
    final body = jsonEncode(request);
    final headers = {'Content-Type': 'application/json'};
    final uri = production ? Uri.https(baseAddress, _basePath + endPoint) : Uri.http(baseAddress, _basePath + endPoint);
    // print('request $uri');
    // print('request body: $body');
    final response = await http.post(uri, body: body, headers: headers);
    if (response.statusCode != 200) throw Exception('unknown response ${response.statusCode}');
    // print('response headers: ${response.headers}');
    // print('response body: ${response.body}');
    return jsonDecode(response.body) as Map<String, dynamic>;
  }

  Future<ReportsResponse> reports(bool? isActive) async {
    final request = await ReportsRequest.create(isActive);
    final map = await post('Reports', request);
    if (map == null) throw ApiException('INTERNAL_ERROR');
    final response = SystemResponse.fromJson(map);
    if (response.isError) {
      if (response.isLogoutError) if (response.isLogoutError) logoutErrorCode.value = response.errorCode;
      throw ApiException(response.errorCode!);
    }
    return ReportsResponse.fromJson(map);
  }

  Future<GetReportResponse> getReport(String id) async {
    final request = await GetReportRequest.create(id);
    final map = await post('GetReport', request);
    if (map == null) throw ApiException('INTERNAL_ERROR');
    final response = SystemResponse.fromJson(map);
    if (response.isError) {
      if (response.isLogoutError) if (response.isLogoutError) logoutErrorCode.value = response.errorCode;
      throw ApiException(response.errorCode!);
    }
    return GetReportResponse.fromJson(map);
  }

  Future<RunReportResponse> runReport(String id, Map<String, String> parameters, String reportType) async {
    print(parameters);
    final request = await RunReportRequest.create(id, parameters, reportType);
    final map = await post('RunReport', request);
    if (map == null) throw ApiException('INTERNAL_ERROR');
    final response = SystemResponse.fromJson(map);
    if (response.isError) {
      if (response.isLogoutError) if (response.isLogoutError) logoutErrorCode.value = response.errorCode;
      throw ApiException(response.errorCode!);
    }
    return RunReportResponse.fromJson(map);
  }

  Future<RunReportResponse> runPayroll(String id, Map<String, String> parameters, String reportType) async {
    final request = await RunReportRequest.create(id, parameters, reportType);
    final map = await post('RunPayroll', request);
    if (map == null) throw ApiException('INTERNAL_ERROR');
    final response = SystemResponse.fromJson(map);
    if (response.isError) {
      if (response.isLogoutError) if (response.isLogoutError) logoutErrorCode.value = response.errorCode;
      throw ApiException(response.errorCode!);
    }
    return RunReportResponse.fromJson(map);
  }
}
