// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule_template.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AddScheduleTemplateRequest _$AddScheduleTemplateRequestFromJson(
        Map<String, dynamic> json) =>
    AddScheduleTemplateRequest(
      scheduleTemplate: ScheduleTemplate.fromJson(
          json['ScheduleTemplate'] as Map<String, dynamic>),
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$AddScheduleTemplateRequestToJson(
        AddScheduleTemplateRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'ScheduleTemplate': instance.scheduleTemplate,
    };

EditScheduleTemplateRequest _$EditScheduleTemplateRequestFromJson(
        Map<String, dynamic> json) =>
    EditScheduleTemplateRequest(
      scheduleTemplate: ScheduleTemplate.fromJson(
          json['ScheduleTemplate'] as Map<String, dynamic>),
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$EditScheduleTemplateRequestToJson(
        EditScheduleTemplateRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'ScheduleTemplate': instance.scheduleTemplate,
    };

DeleteScheduleTemplateRequest _$DeleteScheduleTemplateRequestFromJson(
        Map<String, dynamic> json) =>
    DeleteScheduleTemplateRequest(
      scheduleTemplateId: json['ScheduleTemplateId'] as String,
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$DeleteScheduleTemplateRequestToJson(
        DeleteScheduleTemplateRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'ScheduleTemplateId': instance.scheduleTemplateId,
    };
