import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import '../../dataModel/data/location.dart';
import '../../dataModel/data_model.dart';
import '../../misc/calculate_distance.dart';
import '../../state/location_state.dart';
import '../constrained_search_dialog.dart';
import '../../misc/change_notification_builder.dart';
import '../../pages/view_model_mixin.dart';
import '../../widgets/padded_card.dart';

class LocationsDialog extends StatelessWidget {
  final Function(String) onSelection;
  final String? nullSelectionName;

  const LocationsDialog(
      {super.key, required this.onSelection, this.nullSelectionName});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline6 = theme.textTheme.titleLarge;

    return ChangeNotifierBuilder<_ViewModel>(
      create: (context) => _ViewModel(),
      builder: (context, viewModel, child) {
        if (viewModel.locations.isEmpty) return Container();
        return ConstrainedSearchDialog(
          title:
              Text(AppLocalizations.of(context)!.locations, style: headline6),
          builder: (search) {
            final visibleLocations = <Location>[];
            if (nullSelectionName != null) {
              visibleLocations.add(Location.create()
                ..id = ''
                ..name = nullSelectionName!);
            }
            visibleLocations.addAll(
              viewModel.locations.where(
                (e) => _locationMatches(e, search),
              ),
            );
            return visibleLocations.map(
              (e) => _Tile(location: e, locationSelected: onSelection),
            );
          },
        );
      },
    );
  }

  bool _locationMatches(Location location, String search) =>
      search.isEmpty ||
      location.name.toLowerCase().contains(search.toLowerCase());
}

class _Tile extends StatelessWidget {
  final Function(String) locationSelected;
  final Location location;
  final double? distance;

  _Tile({required this.location, required this.locationSelected, this.distance});

  @override
  Widget build(BuildContext context) {
    bool isCloseEnough = distance != null && distance! <= 1000; // within 1000 feet
    Color textColor = isCloseEnough ? Color.fromARGB(255, 197, 241, 198) : Colors.grey;

    return GestureDetector(
      onTap: isCloseEnough ? () {
        Navigator.of(context).pop();
        locationSelected(location.id);
      } : null,
      child: PaddedCard(
        elevation: 1,
        color: Colors.white,
        child: Center(
          child: Text(location.name, style: TextStyle(color: textColor)),
        ),
      ),
    );
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  List<Location> locations = [];
  Map<String, double> locationDistances = {};

  _ViewModel() {
    addListenables([DataModel().locationModel]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    final allLocations = (await DataModel().locationModel.active).toList();
    final geoLocation = await LocationState().lastLocationData;

    if (geoLocation != null) {
      // Calculate distances
      locationDistances = {};
      for (Location loc in allLocations) {
        final double meters = calculateDistance(
            geoLocation.latitude, geoLocation.longitude, loc.latitude, loc.longitude);
        final double miles = meters * 0.000621371; // Convert meters to miles
        locationDistances[loc.id] = miles;
      }

      // Filter and sort locations
      locations = allLocations
        .where((loc) => locationDistances[loc.id]! <= 5) // Filter within 5 miles
        .toList();

      // Sort locations by distance
      locations.sort((a, b) => locationDistances[a.id]!.compareTo(locationDistances[b.id]!));
    } else {
      locations = [];
    }

    notifyListeners();
  }
}
