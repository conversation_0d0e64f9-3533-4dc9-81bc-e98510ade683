import '../base_data.dart';
import '../data/note.dart';
import '../hive_db.dart';

class NoteModel extends BaseDataModel<Note> {
  @override
  Future<Iterable<Note>> get all async => (await HiveDb.database).notes.values;

  @override
  Future<void> save(Iterable<Note> t) async {
    await (await HiveDb.database).notes.putAll({for (final e in t) e.id: e});
    notifyListeners();
  }

  Future<bool> isAnyByLocationId(String id) async => (await active).any((e) => e.locationId == id);
  Future<Iterable<Note>> getByLocationId(String id) async => (await active).where((e) => e.locationId == id);
  Future<Iterable<Note>> getByUserId(String id) async => (await active).where((e) => e.userId == id);
  Future<Iterable<Note>> getByScheduleId(String id) async => (await active).where((e) => e.scheduleId == id);

  Future<Iterable<Note>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }
}