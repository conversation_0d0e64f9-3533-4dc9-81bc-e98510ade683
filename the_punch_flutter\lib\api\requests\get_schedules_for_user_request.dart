// lib/api/requests/get_schedules_for_user_request.dart

import 'package:json_annotation/json_annotation.dart';
import '../../misc/json_conversion.dart'; 
import 'system.dart';

part 'get_schedules_for_user_request.g.dart';

///
/// Request object for fetching all schedules for a specific user.
///
/// Follows the same structure/pattern as BuildAssetImageRequest.
///
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class GetSchedulesForUserRequest extends SystemRequest {
  final String targetUserId;

  GetSchedulesForUserRequest({
    required this.targetUserId,
    required super.serverIP,
    required super.databaseName,
    required super.sessionId,
  });

  /// Creates a new instance by filling in [SystemRequest] fields from
  /// the current local session info, plus a required [targetUserId].
  static Future<GetSchedulesForUserRequest> create(String targetUserId) async {
    final sysReq = await SystemRequest.create();
    return GetSchedulesForUserRequest(
      targetUserId: targetUserId,
      serverIP: sysReq.serverIP,
      databaseName: sysReq.databaseName,
      sessionId: sysReq.sessionId,
    );
  }

  factory GetSchedulesForUserRequest.fromJson(Map<String, dynamic> json)
    => _$GetSchedulesForUserRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() {
    final data = _$GetSchedulesForUserRequestToJson(this);
    data.removeWhere((key, value) => value == null);
    return data;
  }
}

///
/// (Optional) If you want a typed response too, similar to BuildAssetImageResponse:
///
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class GetSchedulesForUserResponse extends SystemResponse {
  /// A list of schedules returned by the server.
  /// Adjust type or naming if you’re using a different model in Dart.
  final List<Map<String, dynamic>>? schedules;

  GetSchedulesForUserResponse({
    this.schedules,
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory GetSchedulesForUserResponse.fromJson(Map<String, dynamic> json)
    => _$GetSchedulesForUserResponseFromJson(json);

  @override
  Map<String, dynamic> toJson() {
    final data = _$GetSchedulesForUserResponseToJson(this);
    data.removeWhere((key, value) => value == null);
    return data;
  }
}
