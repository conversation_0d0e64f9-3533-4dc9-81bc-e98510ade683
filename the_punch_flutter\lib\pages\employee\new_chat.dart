import 'package:flutter/material.dart';

class UserListComponent_SearchBarComponent_StartChatButtonComponent extends StatefulWidget {
  @override
  _UserListComponent_SearchBarComponent_StartChatButtonComponentState createState() =>
      _UserListComponent_SearchBarComponent_StartChatButtonComponentState();
}

class _UserListComponent_SearchBarComponent_StartChatButtonComponentState
    extends State<UserListComponent_SearchBarComponent_StartChatButtonComponent> {
  List<bool> selectedUsers = List.generate(7, (index) => false);
  TextEditingController searchController = TextEditingController();

  final List<Map<String, String>> users = [
    {'name': '<PERSON>', 'image': 'https://dashboard.codeparrot.ai/api/image/Z_V3ZoDi91IKZZut/frame-3.png'},
    {'name': '<PERSON>', 'image': 'https://dashboard.codeparrot.ai/api/image/Z_V3ZoDi91IKZZut/frame-4.png'},
    {'name': '<PERSON>', 'image': 'https://dashboard.codeparrot.ai/api/image/Z_V3ZoDi91IKZZut/frame-5.png'},
    {'name': 'Forrest Gump', 'image': 'https://dashboard.codeparrot.ai/api/image/Z_V3ZoDi91IKZZut/frame-6.png'},
    {'name': 'Ronald McDonald', 'image': 'https://dashboard.codeparrot.ai/api/image/Z_V3ZoDi91IKZZut/frame-7.png'},
    {'name': 'Walter White', 'image': 'https://dashboard.codeparrot.ai/api/image/Z_V3ZoDi91IKZZut/frame-8.png'},
  ];

  @override
  Widget build(BuildContext context) => Container(
      width: double.infinity,
      constraints: const BoxConstraints(minHeight: 400),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'Select users to create a new chat with:',
              style: TextStyle(
                fontFamily: 'Poppins',
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Color(0xFF969FA5),
              ),
            ),
          ),
          const SizedBox(height: 20),
          // Search Bar
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 16),
            decoration: BoxDecoration(
              color: const Color(0x0F091F30),
              borderRadius: BorderRadius.circular(100),
            ),
            child: Row(
              children: [
                Image.network('https://dashboard.codeparrot.ai/api/image/Z_V3ZoDi91IKZZut/frame.png', width: 24, height: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: searchController,
                    decoration: const InputDecoration(
                      hintText: 'Search by name',
                      hintStyle: TextStyle(
                        fontFamily: 'Poppins',
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        color: Color(0x8F091F30),
                      ),
                      border: InputBorder.none,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          // User List
          Expanded(
            child: ListView.builder(
              itemCount: users.length,
              itemBuilder: (context, index) => Container(
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Color(0x0F091F30)),
                    ),
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 17),
                    title: Text(
                      users[index]['name']!,
                      style: const TextStyle(
                        fontFamily: 'Poppins',
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF091F30),
                      ),
                    ),
                    trailing: Checkbox(
                      value: selectedUsers[index],
                      onChanged: (bool? value) {
                        setState(() {
                          selectedUsers[index] = value!;
                        });
                      },
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ),
            ),
          ),
          const SizedBox(height: 20),
          // Start Chat Button
          Center(
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: ElevatedButton(
                onPressed: () {
                  // Handle start chat action
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4BA2E7),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(100),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                ),
                child: const Text(
                  'Start Group Chat',
                  style: TextStyle(
                    fontFamily: 'Poppins',
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                    letterSpacing: -0.14,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
}

