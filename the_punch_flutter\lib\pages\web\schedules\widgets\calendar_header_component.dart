import 'package:flutter/material.dart';

class CalendarHeaderComponent extends StatelessWidget {
  final String dayLabel;
  final List<TimeSlot> timeSlots;

  const CalendarHeaderComponent({
    Key? key,
    this.dayLabel = 'Sunday, February 2, 2025',
    this.timeSlots = const [
      TimeSlot(label: '12am', time: 0),
      TimeSlot(label: '3am', time: 3),
      TimeSlot(label: '6am', time: 6),
      TimeSlot(label: '9am', time: 9),
      TimeSlot(label: '12pm', time: 12),
      TimeSlot(label: '3pm', time: 15),
      TimeSlot(label: '6pm', time: 18),
      TimeSlot(label: '9pm', time: 21),
    ],
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: const Color(0xFFEBF6FF),
          width: 1.5,
        ),
      ),
      child: Column(
        children: [
          // Day Label
          Container(
            height: 37,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Color(0xFFEBF6FF),
                  width: 1.5,
                ),
              ),
            ),
            child: Center(
              child: Text(
                dayLabel,
                style: const TextStyle(
                  fontFamily: 'Poppins',
                  fontSize: 10,
                  fontWeight: FontWeight.w700,
                  color: Color(0xFF091F30),
                ),
              ),
            ),
          ),
          
          // Time Slots
            Container(
            height: 37,
            child: Stack(
              alignment: Alignment.center, // Center stack children
              children: [
              CustomPaint(
                size: const Size(double.infinity, 37),
                painter: HourLinesPainter(),
              ),
              Center( // Center the Row
                child: Row(
                mainAxisAlignment: MainAxisAlignment.center, // Center horizontally
                children: timeSlots.map((slot) => _buildTimeSlot(slot)).toList(),
                ),
              ),
              ],
            ),
            )
          ,
        ],
      ),
    );

  Widget _buildTimeSlot(TimeSlot slot) => Expanded(
      child: Container(
        padding: const EdgeInsets.only(left: 4),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
           
            Text(
              slot.label,
              style: const TextStyle(
                fontFamily: 'Poppins',
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: Color(0xFF091F30),
              ),
            )
  ],
        ),
      ),
    );
}

class TimeSlot {
  final String label;
  final int time;

  const TimeSlot({
    required this.label,
    required this.time,
  });
}

class HourLinesPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // Draw 24 vertical lines (including start and end)
    for (int i = 0; i <= 24; i++) {
      final paint = Paint()
        ..color = i % 3 == 0 
          ? Color.fromARGB(255, 192, 226, 255) // Darker color for every 3rd line
          : Color.fromARGB(255, 242, 248, 253) // Regular color
        ..strokeWidth = 1;

      final x = (size.width * i) / 24;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}