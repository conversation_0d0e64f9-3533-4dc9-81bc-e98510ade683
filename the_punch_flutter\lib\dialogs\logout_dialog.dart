import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hive/hive.dart';
import '../dataModel/hive_db.dart';
import '../helpers/color_helper.dart';
import '../state/login_state.dart';

class LogoutDialog extends StatelessWidget {
  const LogoutDialog({super.key});

  @override
  Widget build(BuildContext context) => AlertDialog(
    elevation: 1,
    backgroundColor: Colors.white,
    content: Text(AppLocalizations.of(context)!.logout),
    actions: [
      OutlinedButton.icon(
        style: OutlinedButton.styleFrom(
          side: BorderSide(
            color: ColorHelper.thePunchRed(),
          ),
        ),
        icon: const Icon(Icons.cancel),
        label: Text(AppLocalizations.of(context)!.cancel),
        onPressed: () => Navigator.pop(context),
      ),
      ElevatedButton.icon(
        icon: const Icon(
          Icons.logout,
          color: Colors.white,
        ),
        label: Text(
          AppLocalizations.of(context)!.logout,
          style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w700),
        ),
        onPressed: () async {

          // Clear all Hive boxes
          await HiveDb.database.then((db) => db.delete());

          // Perform logout from the app
          await (await LoginState.instance).logout();

          // Redirect to login screen
          GoRouter.of(context).go('/login');

          // Close the dialog
          Navigator.pop(context);
        },
      ),
    ],
  );
}
