// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'send_punch_cards_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SendPunchCardsRequest _$SendPunchCardsRequestFromJson(
        Map<String, dynamic> json) =>
    SendPunchCardsRequest(
      punchCards: (json['PunchCards'] as List<dynamic>)
          .map((e) => PunchCard.fromJson(e as Map<String, dynamic>))
          .toList(),
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$SendPunchCardsRequestToJson(
        SendPunchCardsRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'PunchCards': instance.punchCards,
    };

SendPunchCardsResponse _$SendPunchCardsResponseFromJson(
        Map<String, dynamic> json) =>
    SendPunchCardsResponse(
      success: json['Success'] as bool,
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$SendPunchCardsResponseToJson(
    SendPunchCardsResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['Success'] = instance.success;
  return val;
}
