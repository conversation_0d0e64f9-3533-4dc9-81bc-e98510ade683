// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'build_asset_image_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BuildAssetImageRequest _$BuildAssetImageRequestFromJson(
        Map<String, dynamic> json) =>
    BuildAssetImageRequest(
      guid: json['Guid'] as String,
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$BuildAssetImageRequestToJson(
        BuildAssetImageRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'Guid': instance.guid,
    };

BuildAssetImageResponse _$BuildAssetImageResponseFromJson(
        Map<String, dynamic> json) =>
    BuildAssetImageResponse(
      imageUrl: json['ImageUrl'] as String?,
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$BuildAssetImageResponseToJson(
    BuildAssetImageResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  writeNotNull('ImageUrl', instance.imageUrl);
  return val;
}
