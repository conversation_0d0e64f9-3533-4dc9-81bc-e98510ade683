import 'package:json_annotation/json_annotation.dart';
import '../../dataModel/data/punch_card.dart';
import '../../misc/json_conversion.dart';
import 'system.dart';

part 'send_edited_punch_card_request.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class SendEditedPunchCardRequest extends SystemRequest {
  final PunchCard punchCard;  // The edited punch card to be sent

  SendEditedPunchCardRequest({
    required this.punchCard,
    required String serverIP,
    required String databaseName,
    required String sessionId,
  }) : super(serverIP: serverIP, databaseName: databaseName, sessionId: sessionId);

  static Future<SendEditedPunchCardRequest> create(PunchCard punchCard) async {
    final systemRequest = await SystemRequest.create();
    return SendEditedPunchCardRequest(
      punchCard: punchCard,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory SendEditedPunchCardRequest.fromJson(Map<String, dynamic> json) =>
      _$SendEditedPunchCardRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$SendEditedPunchCardRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class SendEditedPunchCardResponse extends SystemResponse {
  final bool success;

  SendEditedPunchCardResponse({
    required this.success,
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory SendEditedPunchCardResponse.fromJson(Map<String, dynamic> json) =>
      _$SendEditedPunchCardResponseFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$SendEditedPunchCardResponseToJson(this);
}
