import 'dart:async';

import 'package:community_material_icon/community_material_icon.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:the_punch_flutter/dataModel/data/punch_card.dart';
import 'package:the_punch_flutter/dataModel/data_model.dart';
import 'package:the_punch_flutter/dataModel/models/punch_card_model.dart';
import 'package:the_punch_flutter/helpers/color_helper.dart';
import 'package:the_punch_flutter/widgets/ticking_duration.dart';
import 'package:the_punch_flutter/utils/utils.dart';
class ViewPunchCardDialog extends StatefulWidget {
  const ViewPunchCardDialog();

  @override
  _ViewPunchCardDialogState createState() => _ViewPunchCardDialogState();
}

class _ViewPunchCardDialogState extends State<ViewPunchCardDialog> {
  Timer? _timer;
  Duration _totalDuration = Duration.zero;
  Duration _taskDuration = Duration.zero;

  PunchCard? _firstPunchCard;
  List<PunchCard> _relatedPunchCards = [];

  @override
  void initState() {
    super.initState();
    _initializeDurations();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _initializeDurations() async {
    // Fetch the latest punch card
    _firstPunchCard = await _getFirstPunchCard();

    // Fetch all related punch cards
    _relatedPunchCards = await _fetchRelatedPunchCards();

    // Calculate initial Total Time
    _totalDuration = _calculateTotalDuration();

    // Calculate initial Task Time (from the first punch card)
    if (_firstPunchCard != null) {
      _taskDuration = DateTime.now().difference(_firstPunchCard!.clockedIn);
    }

    // Start the timer to update durations every second
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      setState(() {
        if (_firstPunchCard != null) {
          _taskDuration = DateTime.now().difference(_firstPunchCard!.clockedIn);
        }
        _totalDuration = _calculateTotalDuration();
  
      });
    });
  }

  Duration _calculateTotalDuration() {
    Duration total = Duration.zero;
    for (var card in _relatedPunchCards) {
      if (card.clockedIn != null && card.clockedOut != null) {
        total += card.clockedOut!.difference(card.clockedIn!);
      } else if (card.clockedIn != null && card.clockedOut == null) {
        // If the punch card is active, add the duration till now
        total += DateTime.now().difference(card.clockedIn!);
      }
    }
    return total;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AlertDialog(
      title: Center(
        child: Text(
          'TOTAL TIME:',
          style: theme.textTheme.titleMedium?.copyWith(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: ColorHelper.thePunchLightGray(),
          ),
        ),
      ),
      content: SizedBox(
        width: double.infinity,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Total Time Display
            Container(
              padding: const EdgeInsets.all(50),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: ColorHelper.thePunchLightGray(),
                border: Border.all(
                  color: ColorHelper.thePunchGray(),
                  width: 5,
                ),
              ),
              child: _firstPunchCard != null
                  ? _buildTotalTimeDisplay()
                  : Text(
                      '...',
                      style: theme.textTheme.headlineMedium?.copyWith(
                        color: ColorHelper.thePunchGray(),
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
            ),
            const SizedBox(height: 16),
            // Punch Cards List
            _buildPunchCardsList(),
            SizedBox(height: 20),
            // Green Task Timer
            if (_firstPunchCard != null)
              TickingDuration(
                clockedInTime: _firstPunchCard!.clockedIn,
                theme: theme,
              ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: Text('Exit'),
          style: TextButton.styleFrom(
            foregroundColor: Colors.blue,
            side: BorderSide(color: Colors.blue),
          ),
        ),
      ],
    );
  }

  Widget _buildTotalTimeDisplay() {
    final hours = _totalDuration.inHours.toString().padLeft(2, '0');
    final minutes = (_totalDuration.inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (_totalDuration.inSeconds % 60).toString().padLeft(2, '0');

    return Text(
      '$hours:$minutes:$seconds',
      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            color: ColorHelper.thePunchGray(),
            fontWeight: FontWeight.bold,
          ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildPunchCardsList() {
    if (_relatedPunchCards.isEmpty) {
      return const Text(
        'No related punch cards found.',
        style: TextStyle(fontSize: 14, color: Colors.red),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header Row
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                'Task:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: ColorHelper.thePunchGray(),
                    ),
              ),
            ),
            Expanded(
              flex: 1,
              child: Text(
                'Duration:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: ColorHelper.thePunchGray(),
                    ),
              ),
            ),
          ],
        ),
        const Divider(),
        // List of Punch Cards
        for (int i = 0; i < _relatedPunchCards.length; i++)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            child: Row(
              children: [
                // Task Name
                Expanded(
                  flex: 2,
                  child: Text(
                    DataModel().jobTypeModel.getJobTypeName(
                        _relatedPunchCards[i].jobTypeId.toUpperCase()),
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
                // Duration
                Expanded(
                  flex: 1,
                  child: Text(
                    i == 0
                        ? DurationExtensions(_taskDuration)
                            .toFormattedWithSeconds
                        : (_relatedPunchCards[i].clockedOut != null
                            ? DurationExtensions(_relatedPunchCards[i]
                                    .clockedOut!
                                    .difference(
                                        _relatedPunchCards[i].clockedIn!))
                                .toFormattedWithSeconds
                            : '...'),
                    style: i == 0
                        ? TextStyle(
                            fontSize: 12.0,
                            fontWeight: FontWeight.bold,
                            color: ColorHelper.thePunchLightGreen(),
                          )
                        : Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Future<PunchCard?> _getFirstPunchCard() async {
    final prefs = await SharedPreferences.getInstance();
    final linkId = prefs.getString('LinkId') ?? '';

    final punchCardModel = PunchCardModel();
    final allPunchCards =
        (await punchCardModel.getAllByLinkId(linkId)).toList();

    if (allPunchCards.isNotEmpty) {
      // **Sort in descending order to get the latest punch card**
      allPunchCards.sort((a, b) => b.clockedIn.compareTo(a.clockedIn));
      return allPunchCards.first;
    }

    return null;
  }

  Future<List<PunchCard>> _fetchRelatedPunchCards() async {
    final prefs = await SharedPreferences.getInstance();
    final linkId = prefs.getString('LinkId') ?? '';

    // Fetch punch cards from the view model or API
    final punchCardModel = PunchCardModel();
    final allPunchCards =
        (await punchCardModel.getAllByLinkId(linkId)).toList();

    // Sort the punch cards by ClockedIn date (descending order)
    allPunchCards.sort((a, b) => b.clockedIn.compareTo(a.clockedIn));

    return allPunchCards;
  }

}
Future<void> _showViewPunchCardDialog(BuildContext context) async {
  showDialog(
    context: context,
    builder: (BuildContext context) => const ViewPunchCardDialog(),
  );
}


class ViewPunchCardButton extends StatelessWidget {
  const ViewPunchCardButton();

  @override
  Widget build(BuildContext context) => 
        // Align(
        // alignment: Alignment.topRight, // Aligns to the top right
        // child: Padding(
        //   padding: const EdgeInsets.all(0), // Adjust padding as needed
        //   child:
          GestureDetector(
            onTap: () {
             context.push('/employees/punch-card',extra:[{}]);
             // _showViewPunchCardDialog(context);
            },
            child: Container( // Added Container for background
 //             width: ScreenHelper.screenWidth(context)*.30,
            padding: const EdgeInsets.all(0.0),
                                      width: 80,
                          height: 80,
            decoration: BoxDecoration(
              color:ColorHelper.thePunchDesktopLightGray(),
              shape: BoxShape.circle,
              
            ),
              child: const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min, // Prevent Column from taking up extra space
                children: [
                  Icon(
                    CommunityMaterialIcons.calendar_check, // Icon for View Punch Card
                    size: 25, // Adjust icon size
                    color: Colors.black, // Adjust color
                  ),
                  SizedBox(height: 4), // Spacing between icon and text
                  Text(
                    'Card',
                    style: TextStyle(
                      color: Colors.black, // Match icon color
                      fontSize: 12, // Smaller font size for text
                      fontWeight: FontWeight.bold
                    ),
                  ),
                ],
              ),
            ),
          );
       // ),
    //  );

  Future<void> _showViewPunchCardDialog(BuildContext context) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) => const ViewPunchCardDialog(),
    );
  }
}