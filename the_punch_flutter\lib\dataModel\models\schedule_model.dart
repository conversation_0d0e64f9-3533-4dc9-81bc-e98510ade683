import 'package:flutter/material.dart';

import '../../api/api_model.dart';
import '../base_data.dart';
import '../data/schedule.dart';
import '../hive_db.dart';
import '../../misc/extensions.dart';
import 'package:collection/collection.dart';

class ScheduleModel extends BaseDataModel<Schedule> {
  final Set<String> loadedMonths = {};

  ScheduleModel() {
    // Add the current month and year to loadedMonths during initialization
    String currentMonthYear = "${DateTime.now().year}-${DateTime.now().month.toString().padLeft(2, '0')}";
    loadedMonths.add(currentMonthYear);
  }

  @override
  Future<Iterable<Schedule>> getByIds(Iterable<String> ids) async {
    if (ids.isEmpty) return [];
    final db = await HiveDb.database;
    return ids.map((id) => db.schedules.get(id)).whereNotNull();
  }

  @override
  Future<Iterable<Schedule>> get all async =>
      (await HiveDb.database).schedules.values;

  @override
  Future<void> save(Iterable<Schedule> t) async {
    final db = await HiveDb.database;
    await db.schedules.putAll({for (final e in t) e.id: e});
    for (final schedule in t) {
      final startId = schedule.startDateUtc.yearAndMonth.toString();
      final scheduleIds =
          db.scheduleIdsByMonth.get(startId) ?? ScheduleIdsByMonth();
      scheduleIds.ids.add(schedule.id);
      scheduleIds.ids = scheduleIds.ids.toSet().toList();
      await db.scheduleIdsByMonth.put(startId, scheduleIds);
    }
    notifyListeners();
  }

  Future<Schedule?> getScheduleById(String id) async {
    final db = await HiveDb.database;
    return db.schedules.get(id);
  }

  Future<Iterable<Schedule>> getUpdatedSchedulesByEmployeeId(String employeeId) async {
    try {
      final schedules = await ApiModel().getSchedulesForUser(employeeId);

      return schedules;
    } catch (e) {
      debugPrint('Failed to get schedules for user $employeeId: $e');
      rethrow;
    }
  }


  Future<Iterable<DateTime>> getAllStarts() async =>
      (await all).map((e) => e.startDateUtc);

  Future<Iterable<DateTime>> getStartsByEmployeeId(String id) async =>
      (await all).where((e) => e.userId == id).map((e) => e.startDateUtc);

  Future<Iterable<Schedule>> getByEmployeeId(String employeeId) async {
    final db = await HiveDb.database;
    return db.schedules.values.where((schedule) => schedule.userId == employeeId);
  }

  Future<Iterable<Schedule>> getByLocationId(String locationId) async {
    final db = await HiveDb.database;
    return db.schedules.values.where((schedule) => schedule.locationId == locationId);
  }

  Future<Iterable<DateTime>> getStartsByEmployeeIdOrLocations(String id) async {
    final myLocations = (await HiveDb.database)
        .locationContacts
        .values
        .where((e) => e.userId == id && e.isActive)
        .map((e) => e.locationId);

    return (await all)
        .where((e) => e.userId == id || myLocations.contains(e.locationId))
        .map((e) => e.startDateUtc);
  }

  Future<Iterable<DateTime>> getAllStartsWithoutPunchCards() async {
    final scheduleIds = (await HiveDb.database)
        .punchCards
        .values
        .where((e) => e.scheduleId != null)
        .map((e) => e.scheduleId!);
    return (await all)
        .where((e) => !scheduleIds.contains(e.id))
        .map((e) => e.startDateUtc);
  }

  Future<Iterable<Schedule>> getBetween(DateTime start, DateTime end) async {
    final startId = start.yearAndMonth.toString();
    final endId = end.yearAndMonth.toString();
    final scheduleIdLists = (await HiveDb.database)
        .scheduleIdsByMonth
        .valuesBetween(startKey: startId, endKey: endId);
    final scheduleIds = scheduleIdLists.map((e) => e.ids).flattened;
    final schedules = await getByIds(scheduleIds);
    return schedules
        .where((e) => e.startDateUtc >= start && e.startDateUtc < end);
  }

  Future<Iterable<Schedule>> getBetweenByEmployeeId(
          String id, DateTime start, DateTime end) async =>
      (await getBetween(start, end)).where((e) => e.userId == id);

  Future<Iterable<Schedule>> getBetweenByEmployeeIdLocation(
      String id, DateTime start, DateTime end) async {
    final myLocations = (await HiveDb.database)
        .locationContacts
        .values
        .where((e) => e.userId == id && e.isActive)
        .map((e) => e.locationId);
    return (await getBetween(start, end))
        .where((e) => e.userId == id || myLocations.contains(e.locationId));
  }

  Future<Iterable<Schedule>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }

  Future<bool> isMonthLoaded(String monthYear) async {
    return loadedMonths.contains(monthYear);
  }

  Future<void> markMonthAsLoaded(String monthYear) async {
    loadedMonths.add(monthYear);
  }

Future<void> deleteAllFutureSchedulesWithTemplateId(String templateId) async {
  final now = DateTime.now().toUtc();
  
  // Get all schedules
  final schedules = await all;

  // Filter for schedules with this templateId that start in the future
  final futureSchedules = schedules.where((schedule) => schedule.scheduleTemplateId == templateId && schedule.startDateUtc.isAfter(now));

  // If none match, nothing to do
  if (futureSchedules.isEmpty) return;

  // Otherwise, remove them by ID
  await remove(futureSchedules.map((s) => s.id));
}

Future<void> remove(Iterable<String> ids) async {
  if (ids.isEmpty) return;

  final db = await HiveDb.database;

  // 1. Load the Schedules so we know which month-year indexes to touch
  final schedulesToRemove = ids
      .map((id) => db.schedules.get(id))
      .whereNotNull()
      .toList();

  // 2. Delete from schedules
  await db.schedules.deleteAll(ids);

  // 3. Also remove from the scheduleIdsByMonth
  for (final schedule in schedulesToRemove) {
    final monthKey = schedule.startDateUtc.yearAndMonth.toString();
    final schedIdsByMonth = db.scheduleIdsByMonth.get(monthKey);
    if (schedIdsByMonth != null) {
      schedIdsByMonth.ids.remove(schedule.id);
      await db.scheduleIdsByMonth.put(monthKey, schedIdsByMonth);
    }
  }

  // 4. Notify listeners if needed
  notifyListeners();
}




}

