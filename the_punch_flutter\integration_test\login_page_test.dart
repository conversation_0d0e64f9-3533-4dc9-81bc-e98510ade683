import 'package:collection/collection.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_localized_locales/flutter_localized_locales.dart';

import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:the_punch_flutter/pages/login/login.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets('widgets exist', (tester) async {
    await tester.pumpWidget(
      const MaterialApp(
        localizationsDelegates: [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          LocaleNamesLocalizationsDelegate(),
        ],
        supportedLocales: [
          Locale('en', ''),
          Locale('fr', ''),
          Locale('de', ''),
          Locale('it', ''),
        ],
        locale: Locale('en'),
        home: LoginPage(),
      ),
    );
    await tester.pumpAndSettle();

    final organizationIdField = find.byKey(const ValueKey('OrganizationIdField')).evaluate().firstOrNull?.widget as TextFormField?;
    final usernameField = find.byKey(const ValueKey('UsernameField')).evaluate().firstOrNull?.widget as TextFormField?;
    final passwordField = find.byKey(const ValueKey('PasswordField')).evaluate().firstOrNull?.widget as TextFormField?;
    final searchOrganizationButton = find.byKey(const ValueKey('SearchOrganizationButton')).evaluate().firstOrNull?.widget as IconButton?;

    expect(true, organizationIdField != null);
    expect(true, usernameField != null);
    expect(true, passwordField != null);
    expect(true, searchOrganizationButton != null);

    organizationIdField?.controller?.text = 'some text';
    expect(true, organizationIdField?.controller?.text == 'some text');

    await tester.pumpAndSettle();

    // searchOrganizationButton?.onPressed();
    //await Future.delayed(const Duration(seconds: 30));
  });
}
