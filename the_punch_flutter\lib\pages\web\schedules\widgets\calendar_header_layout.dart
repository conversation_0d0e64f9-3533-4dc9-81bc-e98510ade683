import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../misc/extensions.dart';
import 'calendar_header_component.dart';

class CalendarDayHeaderLayout extends StatelessWidget {
  final DateTime date;
  const CalendarDayHeaderLayout({Key? key, required DateTime this.date}) : super(key: key);

  @override
  Widget build(BuildContext context) => Container(
      width: double.infinity, // Take up all available space
      height: 80, // Fixed height as per design
      decoration: BoxDecoration(
        border: Border.all(
          color: const Color(0xFFEBF6FF),
          width: 1.5,
        ),
      ),
      child: Row(
        children: [
          // Label section
          Container(
            width: 154,
            height: double.infinity,
            padding: const EdgeInsets.only(right:10),
          // decoration: const BoxDecoration(
            //   image: DecorationImage(
            //     image: NetworkImage('https://dashboard.codeparrot.ai/api/image/aA94jAROQIWTswws/label.png'),
            //     fit: BoxFit.cover,
            //   ),
            // ),
          ),
          
          // Day header components
          Expanded(
            child: Row(
                children: [
                Expanded(
                  child: CalendarHeaderComponent(
                  dayLabel: DateFormat('EEEE, MMMM d, yyyy').format(date),
                  timeSlots: [
                    const TimeSlot(label: '12am', time: 0),
                    const TimeSlot(label: '3am', time: 3),
                    const TimeSlot(label: '6am', time: 6),
                    const TimeSlot(label: '9am', time: 9),
                    const TimeSlot(label: '12pm', time: 12),
                    const TimeSlot(label: '3pm', time: 15),
                    const TimeSlot(label: '6pm', time: 18),
                    const TimeSlot(label: '9pm', time: 21),
                  ],
                  ),
                ),
                Expanded(
                  child: CalendarHeaderComponent(
                  dayLabel: DateFormat('EEEE, MMMM d, yyyy').format(date.add(const Duration(days: 1))),
                  timeSlots: [
                    const TimeSlot(label: '12am', time: 0),
                    const TimeSlot(label: '3am', time: 3),
                    const TimeSlot(label: '6am', time: 6),
                    const TimeSlot(label: '9am', time: 9),
                    const TimeSlot(label: '12pm', time: 12),
                    const TimeSlot(label: '3pm', time: 15),
                    const TimeSlot(label: '6pm', time: 18),
                    const TimeSlot(label: '9pm', time: 21),
                  ],
                  ),
                ),
                Expanded(
                  child: CalendarHeaderComponent(
                  dayLabel: DateFormat('EEEE, MMMM d, yyyy').format(date.add(const Duration(days: 2))),
                  timeSlots: [
                    const TimeSlot(label: '12am', time: 0),
                    const TimeSlot(label: '3am', time: 3),
                    const TimeSlot(label: '6am', time: 6),
                    const TimeSlot(label: '9am', time: 9),
                    const TimeSlot(label: '12pm', time: 12),
                    const TimeSlot(label: '3pm', time: 15),
                    const TimeSlot(label: '6pm', time: 18),
                    const TimeSlot(label: '9pm', time: 21),
                  ],
                  ),
                ),

              ],
            ),
          ),
        ],
      ),
    );
}


class CalendarWeekHeaderLayout extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Container(
      width: double.infinity,
      child: Row(
      children: [
        Expanded(
          child: Container(
        padding: const EdgeInsets.all(16),
        decoration:  BoxDecoration(
          border: Border.all(color: Color(0xFFEBF6FF), width: 1.5),
          borderRadius: BorderRadius.only(topLeft: Radius.circular(10)),
        ),
        child: const Center(
          child: Text('Date'),
        ),
          ),
        ),
        Expanded(
          child: Container(
        padding: const EdgeInsets.all(16),
        decoration:  BoxDecoration(
          border: Border.all(color: Color(0xFFEBF6FF), width: 1.5),
        ),
        child: const Center(
          child: Text('Time'),
        ),
          ),
        ),
        Expanded(
          child: Container(
        padding: const EdgeInsets.all(16),
        decoration:  BoxDecoration(
          border: Border.all(color: Color(0xFFEBF6FF), width: 1.5),
          borderRadius: BorderRadius.only(topRight: Radius.circular(10)),
        ),
        child: const Center(
          child: Text('Appointment'),
        ),
          ),
        ),
      ],
      ),
    ); 
  
}