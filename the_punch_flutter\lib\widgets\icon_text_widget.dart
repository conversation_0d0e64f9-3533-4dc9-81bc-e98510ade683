import 'package:flutter/widgets.dart';

class IconTextWidget extends StatelessWidget {
  final EdgeInsetsGeometry padding;
  final IconData iconData;
  final String text;
  const IconTextWidget({super.key, this.padding = const EdgeInsets.all(8), required this.iconData, required this.text});

  @override
  Widget build(BuildContext context) => Center(
        child: Padding(
            padding: padding,
            child: Row(children: [
              Padding(
                padding: const EdgeInsets.only(right: 4),
                child: Icon(iconData),
              ),
              Text(text)
            ])),
      );
}
