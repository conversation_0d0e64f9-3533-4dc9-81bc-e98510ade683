import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:the_punch_flutter/widgets/padded_card.dart';

import '../dataModel/data/location.dart';
import '../dataModel/models/punch_view_model.dart';
import '../helpers/color_helper.dart';
import '../helpers/screen_helper.dart';
import '../misc/calculate_distance.dart';



class LocationTile extends StatelessWidget {
  final Function() selected;
  final Location location;
  final double? distance;

  const LocationTile({
    required this.location,
    required this.distance,
    required this.selected,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final secondaryBodyText1 = theme.textTheme.titleMedium?.copyWith(
      color: ColorHelper.thePunchGray(),
    );

    final isWithin250Feet = distance != null && distance! <= 250;
    final cardBackgroundColor = isWithin250Feet
        ? const Color.fromARGB(255, 207, 250, 161)
        : theme.colorScheme.primary;

    final distanceText = distance == null ? '' : distance!.metersToMiles;
    final Widget distanceDisplay = isWithin250Feet
        ? const Padding(
            padding: EdgeInsets.fromLTRB(0, 0, 5, 0),
            child: Column(children: [
              Text('Arrived', style: TextStyle(color: Colors.green))
            ]))
        : Padding(
            padding: const EdgeInsets.fromLTRB(0, 0, 5, 0),
            child: Column(children: [
              Text(distanceText,
                  style: TextStyle(color: ColorHelper.thePunchRed()))
            ]));

    return GestureDetector(
      onTap: () {
        if (isWithin250Feet) {
          selected();
        } else {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text('Too Far Away!',
                  style: TextStyle(color: ColorHelper.thePunchRed())),
              content: const Text("Please move closer to the location."),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text("OK"),
                ),
              ],
            ),
          );
        }
      },
      child: PaddedCard(
        color: cardBackgroundColor,
        child: IntrinsicHeight(
          child: Stack(
            children: [
              const Align(
                alignment: Alignment.centerLeft,
                child: Icon(Icons.pin_drop),
              ),
              Align(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 18),
                  child: Column(
                    children: [
                      Text(location.name, style: secondaryBodyText1),
                      if (location.address1.isNotEmpty)
                        Text(location.address1,
                            style: theme.textTheme.bodyMedium),
                      if (location.address2.isNotEmpty)
                        Text(location.address2,
                            style: theme.textTheme.bodyMedium),
                      if (location.address3.isNotEmpty)
                        Text(location.address3,
                            style: theme.textTheme.bodyMedium),
                    ],
                  ),
                ),
              ),
              Align(
                alignment: Alignment.bottomRight,
                child: distanceDisplay,
              ),
            ],
          ),
        ),
      ),
    );
  }
}