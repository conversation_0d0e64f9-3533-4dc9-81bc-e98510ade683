// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'remove_group_members_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RemoveGroupMembersRequest _$RemoveGroupMembersRequestFromJson(
        Map<String, dynamic> json) =>
    RemoveGroupMembersRequest(
      groupId: json['GroupId'] as String,
      userIds:
          (json['UserIds'] as List<dynamic>).map((e) => e as String).toList(),
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$RemoveGroupMembersRequestToJson(
        RemoveGroupMembersRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'GroupId': instance.groupId,
      'UserIds': instance.userIds,
    };

RemoveGroupMembersResponse _$RemoveGroupMembersResponseFromJson(
        Map<String, dynamic> json) =>
    RemoveGroupMembersResponse(
      successMessage: json['SuccessMessage'] as String?,
      removedCount: (json['RemovedCount'] as num?)?.toInt(),
      errorCode: json['errorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['serverTime']),
    );

Map<String, dynamic> _$RemoveGroupMembersResponseToJson(
    RemoveGroupMembersResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('errorCode', instance.errorCode);
  writeNotNull('serverTime', nullableDateTimeToJson(instance.serverTime));
  writeNotNull('SuccessMessage', instance.successMessage);
  writeNotNull('RemovedCount', instance.removedCount);
  return val;
}
