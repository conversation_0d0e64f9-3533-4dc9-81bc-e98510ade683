import 'dart:async';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../dataModel/data/punch_card.dart';
import '../dataModel/data_model.dart';
import '../dataModel/models/punch_card_model.dart';
import '../helpers/color_helper.dart';
import '../pages/web/my_scaffold.dart';

class PunchCardPage extends StatefulWidget {
  //final Duration time = Duration.zero;
  final String time = '1:00:00PM';
 
  // final Duration totalDuration;
  // final Duration taskDuration;
  const PunchCardPage({super.key});
  @override
  PunchCardPageState createState() => PunchCardPageState();
}

class PunchCardPageState extends State<PunchCardPage> {
  Timer? _timer;
  Duration _totalDuration = Duration.zero;
  Duration _taskDuration = Duration.zero;

  PunchCard? _firstPunchCard;
  List<PunchCard> _relatedPunchCards = [];
  @override
  void initState() {
    super.initState();
    _initializeDurations();
   
  }

  Future<void> _initializeDurations() async {
    // // Fetch the latest punch card
    // _firstPunchCard = await _getFirstPunchCard();

    // // Fetch all related punch cards
     _relatedPunchCards = await _fetchRelatedPunchCards();


    // Calculate initial Total Time
    _totalDuration = _calculateTotalDuration();

    // Calculate initial Task Time (from the first punch card)
    if (_firstPunchCard != null) {
      _taskDuration = DateTime.now().difference(_firstPunchCard!.clockedIn);
    }

    // Start the timer to update durations every second
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_firstPunchCard != null) {
          _taskDuration = DateTime.now().difference(_firstPunchCard!.clockedIn);
        }
        _totalDuration = _calculateTotalDuration();
  
      });
    });
  }


  Duration _calculateTotalDuration() {
  //String _calculateTotalDuration() {
    Duration total = Duration.zero;
    //String total = "2:00:00PM";
    for (var card in _relatedPunchCards) {
      if (card.clockedIn != null && card.clockedOut != null) {
        total += card.clockedOut!.difference(card.clockedIn!);
      } else if (card.clockedIn != null && card.clockedOut == null) {
        // If the punch card is active, add the duration till now
        total += DateTime.now().difference(card.clockedIn!);
      }
    }
    return total;
  }

  @override
  Widget build(BuildContext context) => MyScaffold(
        // Use the custom AppBar widget
        showDrawer: false,
        showBackButton: true,
        showNotificationButton: false,
        showTitle: false,
        //body: _Body(totalDuration: _totalDuration, firstPunchCard: _firstPunchCard,relatedPunchCards: [],),
        body: _Body(totalDuration: _calculateTotalDuration(),punchCards:_relatedPunchCards), 
        titleWidget: const Text(
          'Total Time',
          style: TextStyle(
              fontSize: 30, fontWeight: FontWeight.bold, color: Colors.black),
        ),
        title: "Punch",
      );


  Widget _buildPunchCardsList() {
    if (_relatedPunchCards.isEmpty) {
      return const Text(
        'No related punch cards found.',
        style: TextStyle(fontSize: 14, color: Colors.red),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header Row
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                'Task:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: ColorHelper.thePunchGray(),
                    ),
              ),
            ),
            Expanded(
              flex: 1,
              child: Text(
                'Duration:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: ColorHelper.thePunchGray(),
                    ),
              ),
            ),
          ],
        ),
        const Divider(),
        // List of Punch Cards
        for (int i = 0; i < _relatedPunchCards.length; i++)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            child: Row(
              children: [
                // Task Name
                Expanded(
                  flex: 2,
                  child: Text(
                    DataModel().jobTypeModel.getJobTypeName(
                        _relatedPunchCards[i].jobTypeId.toUpperCase()),
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
                // Duration
                Expanded(
                  flex: 1,
                  child: Text(
                    i == 0
                        ? DurationExtensions(_taskDuration)
                            .toFormattedWithSeconds
                        : (_relatedPunchCards[i].clockedOut != null
                            ? DurationExtensions(_relatedPunchCards[i]
                                    .clockedOut!
                                    .difference(
                                        _relatedPunchCards[i].clockedIn!))
                                .toFormattedWithSeconds
                            : '...'),
                    style: i == 0
                        ? TextStyle(
                            fontSize: 12.0,
                            fontWeight: FontWeight.bold,
                            color: ColorHelper.thePunchLightGreen(),
                          )
                        : Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Future<PunchCard?> _getFirstPunchCard() async {
    final prefs = await SharedPreferences.getInstance();
    final linkId = prefs.getString('LinkId') ?? '';

    final punchCardModel = PunchCardModel();
    final allPunchCards =
        (await punchCardModel.getAllByLinkId(linkId)).toList();

    if (allPunchCards.isNotEmpty) {
      // **Sort in descending order to get the latest punch card**
      allPunchCards.sort((a, b) => b.clockedIn.compareTo(a.clockedIn));
      return allPunchCards.first;
    }

    return null;
  }

  Future<List<PunchCard>> _fetchRelatedPunchCards() async {
    final prefs = await SharedPreferences.getInstance();
    final linkId = prefs.getString('LinkId') ?? '';

    // Fetch punch cards from the view model or API
    final punchCardModel = PunchCardModel();
    final allPunchCards =
        (await punchCardModel.getAllByLinkId(linkId)).toList();

    // Sort the punch cards by ClockedIn date (descending order)
    allPunchCards.sort((a, b) => b.clockedIn.compareTo(a.clockedIn));

    return allPunchCards;
  }


}
class _Body extends StatefulWidget {
  final Duration totalDuration;
  final List<PunchCard> punchCards;

  const _Body({required this.totalDuration, required this.punchCards});

  @override
  _BodyState createState() => _BodyState();
}

class _BodyState extends State<_Body> {
  final Map<String, String> _cachedNames = {}; // Cache for user names
  Map<String, Widget> taskIcons = {
  'Special Event': const Icon(Icons.star, color: Colors.amber),
  'Inspection': const Icon(Icons.search, color: Colors.blue),
  'Administrative': const Icon(Icons.folder, color: Colors.grey),
  'Training': const Icon(Icons.school, color: Colors.green),
  'Project': const Icon(Icons.build, color: Colors.orange),
  'Customer Visit': const Icon(Icons.person, color: Colors.purple),
  'Employee Visit': const Icon(Icons.people, color: Colors.teal),
  'Tracking': const Icon(Icons.location_on, color: Colors.red),
};
  static const String scheduledId = '95E597BF-075A-4048-9ADF-C95E34163C97';
  static const String unscheduledId = '4D024B8E-03F3-4B2D-9CD9-0D6809C786AD';
  static const String administrativeId = '87D05223-4F8E-4B7A-9B1A-1AB9E644590D';
  static const String travelTimeId = '2EBCDAA1-7DE7-4106-BC26-D99CA120C820';

  static const String specialEventId = '5A6B78CF-1234-5678-9ABC-DEF012345678';
  static const String inspectionId = '6B7C89DF-2345-6789-0ABC-EF0123456789';
  static const String trainingId = '7C8D90EF-3456-7890-1ABC-F01234567890';
  static const String projectId = '8D9E01F0-4567-8901-2ABC-012345678901';
  static const String customerVisitId = '9E0123F1-5678-9012-3ABC-123456789012';
  static const String employeeVisitId = '0F1234E2-6789-0123-4ABC-234567890123';

  static const String trackingId = 'F13A4567-1234-5678-ABCD-9876543210FE';
  static const Map<String, Widget> idToIconMap = {
    scheduledId: Icon(Icons.calendar_today, color: Colors.blue),
    unscheduledId: Icon(Icons.notifications_off, color: Colors.grey),
    administrativeId: Icon(Icons.folder_open, color: Colors.orange),
    travelTimeId: Icon(Icons.directions_car, color: Colors.green),
    specialEventId: Icon(Icons.star, color: Colors.amber),
    inspectionId: Icon(Icons.search, color: Colors.lightBlue),
    trainingId: Icon(Icons.school, color: Colors.deepPurple),
    projectId: Icon(Icons.assignment, color: Colors.teal),
    customerVisitId: Icon(Icons.person_pin_circle, color: Colors.indigo),
    employeeVisitId: Icon(Icons.people, color: Colors.lime),
    trackingId: Icon(Icons.pending_outlined, color: Colors.black),
  };
  @override
  void initState() {
    super.initState();
    _fetchNames();
  }

  void _fetchNames() {
    for (var punchCard in widget.punchCards) {
      if (!_cachedNames.containsKey(punchCard.userId)) {
        DataModel().userModel.getFullName(punchCard.userId).then((name) {
          setState(() {
            _cachedNames[punchCard.userId] = name;
          });
        }).catchError((error) {
          setState(() {
            _cachedNames[punchCard.userId] = 'Error loading name';
          });
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) => Container(
  width: double.infinity,
  height: double.infinity,
  color: Colors.white,
  child: Column(
    mainAxisAlignment: MainAxisAlignment.start,
    crossAxisAlignment: CrossAxisAlignment.center,
    children: [
      // Header with Total Time
      Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            const Text(
              'Total Time',
              style: TextStyle(
                fontSize: 30,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.totalDuration.toFormattedWithSeconds ?? '01:02:09',
              style: const TextStyle(
                fontSize: 45,
                fontWeight: FontWeight.bold,
                color: Color(0xFF0A1A2A),
              ),
            ),
          ],
        ),
      ),
      Divider(height: 1, thickness: 1, color: Colors.grey[200]),
      // List of time entries
Expanded(
  child: Container(
    decoration: BoxDecoration(
// gradient: LinearGradient(
//   begin: Alignment.topCenter,
//   end: Alignment.bottomCenter,
//   colors: [
//     Color.fromARGB(255, 236, 236, 236), // Very light gray
//     Colors.white,
//   ],
//   stops: [0.0, .1], // This makes the gradient complete by 30% of the distance
// ),
    ),
    child: ListView(
  padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
  children: widget.punchCards.asMap().entries.map((entry) {
    int index = entry.key;
    var punchCard = entry.value;
    
    return Container(
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Colors.grey[300]!,
            width: 1,
          ),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0,horizontal: 20),
        child: Row(
          children: [
            // Icon based on job type
            Container(
              width: 32,
              height: 32,
              child: Center(
                child: idToIconMap[punchCard.jobTypeId.toUpperCase()] ??
                    Icon(Icons.help, color: Colors.grey[700], size: 18),
              ),
            ),
            const SizedBox(width: 12),
            // Job Type Name
            Expanded(
              child: Text(
                DataModel().jobTypeModel.getJobTypeName(
                  punchCard.jobTypeId.toUpperCase(),
                ),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ),
            // Duration
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: index == 0
                    ? Color.fromARGB(255, 200, 243, 230)
                    : Color.fromARGB(255, 230, 230, 230),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                index == 0
                    ? '${DateTime.now().difference(punchCard.clockedIn).toFormattedWithSeconds}'
                    : '${punchCard.clockedOut?.difference(punchCard.clockedIn).toFormattedWithSeconds}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: index != 0
                      ? Color.fromARGB(255, 85, 85, 85)
                      : Color.fromARGB(255, 0, 166, 119),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }).toList(),
),
  ),
),
    ],
  ),
);
}


extension DurationExtensions on Duration {
  String get toFormatted {
    final hours = inHours.toString().padLeft(2, '0');
    final minutes = (inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (inSeconds % 60).toString().padLeft(2, '0');
    return '$hours:$minutes:$seconds';
  }

  String get toFormattedWithSeconds {
    final hours = inHours.toString().padLeft(2, '0');
    final minutes = (inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (inSeconds % 60).toString().padLeft(2, '0');
    return '$hours:$minutes:$seconds';
  }
}
