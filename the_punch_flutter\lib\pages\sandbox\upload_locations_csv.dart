import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:csv/csv.dart';
import 'package:collection/collection.dart';
import 'package:provider/provider.dart';
import 'package:separated_column/separated_column.dart';
import '../../dataModel/data/location.dart';
import '../../dataModel/data/location_contact.dart';
import '../../dataModel/data/user.dart';
import '../../dataModel/data_model.dart';
import '../../misc/change_notification_builder.dart';
import '../web/my_scaffold.dart';

class UploadLocationsCSVPage extends StatelessWidget {
  const UploadLocationsCSVPage({super.key});
  @override
  Widget build(BuildContext context) => MyScaffold(
        title: 'Upload Locations CSV',
        body: _Body(),
      );
}

class _Body extends StatefulWidget {
  @override
  State<_Body> createState() => _BodyState();
}

class _BodyState extends State<_Body> {
  @override
  Widget build(BuildContext context) => ChangeNotifierBuilder<_ViewModel>(
      create: (context) => _ViewModel(),
      builder: (context, viewModel, _) => Center(
            child: SeparatedColumn(
              separatorBuilder: (BuildContext context, int index) => const Divider(),
              children: [
                ElevatedButton(onPressed: () => uploadTapped(viewModel), child: const Text('Upload')),
                for (final locationContact in viewModel.locationsContacts) LocationContactWidget(locationContact),
                ElevatedButton(onPressed: viewModel.canSave ? viewModel.save : null, child: const Text('Save')),
              ],
            ),
          ));

  void uploadTapped(_ViewModel viewModel) async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['csv'],
      withData: true,
    );

    if (result != null && result.files.isNotEmpty && result.files.first.bytes != null) {
      final fileBytes = result.files.first.bytes!;

      final csvString = String.fromCharCodes(fileBytes);
      final rows = const CsvToListConverter(shouldParseNumbers: false).convert<String>(csvString);
      for (final e in rows) {
        if (e[0].toLowerCase() == 'location name') continue;

        await viewModel.addRow(
          locationName: e[0],
          address1: e[1],
          address2: e[2],
          city: e[3],
          state: e[4],
          zip: e[5],
          phone: e[6],
          emailAddress: e[7],
          contactName: e[8],
        );
      }
    }
  }
}

class LocationContactWidget extends StatelessWidget {
  final LocationContact locationContact;

  const LocationContactWidget(this.locationContact, {super.key});

  @override
  Widget build(BuildContext context) {
    final viewModel = context.read<_ViewModel>();

    final location = viewModel.getLocation(locationContact.locationId);
    final contact = viewModel.getContact(locationContact.userId);

    if (location == null || contact == null) return Container();

    final isNewLocation = viewModel.isNewLocation(locationContact.locationId);
    final isActiveLocation = viewModel.isActiveLocation(locationContact.locationId);
    final isInactiveLocation = viewModel.isInactiveLocation(locationContact.locationId);
    final isNewContact = viewModel.isNewContact(locationContact.userId);
    final isActiveContact = viewModel.isActiveContact(locationContact.userId);
    final isInactiveContact = viewModel.isInactiveContact(locationContact.userId);

    final textTheme = Theme.of(context).textTheme;
    final headline6 = textTheme.titleLarge;
    final infoStyle = textTheme.bodySmall;
    final warningStyle = textTheme.bodySmall?.copyWith(color: Colors.red);
    final locationStyle = isActiveLocation ? textTheme.bodyMedium?.copyWith(color: Colors.grey) : textTheme.bodyMedium;
    final contactStyle = isActiveContact ? textTheme.bodyMedium?.copyWith(color: Colors.grey) : textTheme.bodyMedium;

    return ConstrainedBox(
      constraints: const BoxConstraints(minWidth: 500, maxWidth: 750),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text('Location', style: headline6),
                        const SizedBox(width: 8),
                        if (isNewLocation) Text('New', style: infoStyle),
                        if (isActiveLocation) Text('Already Exists', style: warningStyle),
                        if (isInactiveLocation) Text('Inactive', style: infoStyle),
                      ],
                    ),
                    Text(location.name, style: locationStyle),
                    Text(location.address1, style: locationStyle),
                    if (location.address2.isNotEmpty) Text(location.address2, style: locationStyle),
                    Text('${location.city}, ${location.state} ${location.zip}', style: locationStyle),
                  ],
                ),
              ),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text('Contact', style: headline6),
                        const SizedBox(width: 8),
                        if (isNewContact) Text('New', style: infoStyle),
                        if (isActiveContact) Text('Already Exists', style: warningStyle),
                        if (isInactiveContact) Text('Inactive', style: infoStyle),
                      ],
                    ),
                    Text(contact.name, style: contactStyle),
                    if (contact.phone != null && contact.phone!.isNotEmpty) Text(contact.phone!, style: contactStyle),
                    if (contact.emailAddress != null && contact.emailAddress!.isNotEmpty)
                      Text(contact.emailAddress!, style: contactStyle),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ViewModel extends ChangeNotifier {
  var newLocations = <String, Location>{};
  var newContacts = <String, User>{};
  var locationsContacts = <LocationContact>[];

  var existingLocations = <String, Location>{};
  var existingContacts = <String, User>{};

  Future<void> addRow({
    required String locationName,
    required String address1,
    required String address2,
    required String city,
    required String state,
    required String zip,
    required String phone,
    required String emailAddress,
    required String contactName,
  }) async {
    final organizationLocation = await DataModel().locationModel.getById(Location.primaryLocationId);

    var location = Location.create();
    location.name = locationName;
    location.address1 = address1;
    location.address2 = address2;
    location.city = city;
    location.state = state;
    location.zip = zip;
    location.phone = phone;
    location.emailAddress = emailAddress;
    location.timeZone = organizationLocation!.timeZone;
    final existingLocation = await matchingLocation(location);
    if (existingLocation != null) {
      location = existingLocation;
      existingLocations.putIfAbsent(location.id, () => location);
    } else {
      newLocations.putIfAbsent(location.id, () => location);
    }

    var contact = User.createContact();
    contact.firstName = contactName.split(' ').firstOrNull ?? '';
    contact.lastName = contactName.replaceFirst('${contact.firstName} ', '');
    contact.phone = phone;
    contact.emailAddress = emailAddress;
    final existingContact = await matchingContact(contact);
    if (existingContact != null) {
      contact = existingContact;
      existingContacts.putIfAbsent(contact.id, () => contact);
    } else {
      newContacts.putIfAbsent(contact.id, () => contact);
    }

    final locationContact = LocationContact.create();
    locationContact.locationId = location.id;
    locationContact.userId = contact.id;
    if (!locationsContacts
        .any((e) => e.locationId == locationContact.locationId && e.userId == locationContact.userId)) {
      locationsContacts.add(locationContact);
    }

    notifyListeners();
  }

  Future<Location?> matchingLocation(Location location) async =>
      (await DataModel().locationModel.all).followedBy(newLocations.values).firstWhereOrNull((e) =>
          e.name.toLowerCase() == location.name.toLowerCase() &&
          e.address1.toLowerCase() == location.address1.toLowerCase());

  Future<User?> matchingContact(User contact) async =>
      (await DataModel().userModel.all).followedBy(newContacts.values).firstWhereOrNull((e) =>
          e.name.toLowerCase() == contact.name.toLowerCase() && e.phone?.toLowerCase() == contact.phone?.toLowerCase());

  bool isNewLocation(String locationId) => newLocations.containsKey(locationId);
  bool isNewContact(String contactId) => newContacts.containsKey(contactId);

  bool isActiveLocation(String locationId) => existingLocations[locationId]?.isActive ?? false;
  bool isActiveContact(String contactId) => existingContacts[contactId]?.isActive ?? false;

  bool isInactiveLocation(String locationId) => !(existingLocations[locationId]?.isActive ?? true);
  bool isInactiveContact(String contactId) => !(existingContacts[contactId]?.isActive ?? true);

  Location? getLocation(String locationId) => newLocations[locationId] ?? existingLocations[locationId];
  User? getContact(String contactId) => newContacts[contactId] ?? existingContacts[contactId];

  Future<void> save() async {
    // save new locations and contact
    await DataModel().locationModel.saveDirty(newLocations.values);
    await DataModel().userModel.saveDirty(newContacts.values);

    // change inactive locations and contacts to active
    final activatedLocations = existingLocations.values.where((e) => !e.isActive).toList();
    final activatedContacts = existingContacts.values.where((e) => !e.isActive).toList();
    for (final e in activatedLocations) {
      e.isActive = true;
    }
    for (final e in activatedContacts) {
      e.isActive = true;
    }
    await DataModel().locationModel.saveDirty(activatedLocations);
    await DataModel().userModel.saveDirty(activatedContacts);

    // save new LocationContacts
    final allLocationsContacts = await DataModel().locationContactModel.all;
    final newLocationsContacts = locationsContacts
        .where((locationContact) => !allLocationsContacts
            .any((e) => e.locationId == locationContact.locationId && e.userId == locationContact.userId))
        .toList();
    await DataModel().locationContactModel.saveDirty(newLocationsContacts);

    newLocations.clear();
    newContacts.clear();
    locationsContacts.clear();

    notifyListeners();
  }

  bool get canSave => newLocations.isNotEmpty || newContacts.isNotEmpty || locationsContacts.isNotEmpty;
}
