import 'package:flutter/material.dart';
// A text widget for naming group chats
class Name<PERSON>hat extends StatelessWidget {
  final String placeholder;
  final TextEditingController controller;

  const NameChat({
    Key? key,
    this.placeholder = 'Name your chat',
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => Container(
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 17),
        decoration: BoxDecoration(
          border: Border.all(
            color: const Color(0x0F091F30),
            width: 1,
          ),
        ),
        child: Center(
          child: Container(
            width: 311,
            height: 48,
            decoration: BoxDecoration(
              color: const Color(0x0F091F30),
              borderRadius: BorderRadius.circular(100),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 18),
            child: TextField(
              controller: controller,
              style: const TextStyle(
                fontFamily: 'Poppins',
                fontSize: 13,
                fontWeight: FontWeight.w500,
                letterSpacing: -0.13,
                color: Color(0xFF091F30),
              ),
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: placeholder,
                hintStyle: const TextStyle(
                  fontFamily: 'Poppins',
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                  letterSpacing: -0.13,
                  color: Color(0x8F091F30),
                ),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ),
        ),
      );
}


