import 'package:flutter/material.dart';
import '../widgets/decorated_text_field.dart';

class TextFieldDialog extends StatelessWidget {
  final String? initialValue;
  final String? labelText;
  final void Function(String value)? onChanged;
  final String? Function(String? value)? validator;

  const TextFieldDialog(
      {super.key,
      this.initialValue,
      this.labelText,
      this.onChanged,
      this.validator});

  @override
  Widget build(BuildContext context) => Dialog(
        backgroundColor: Colors.transparent,
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 500),
          child: Card(
            elevation: 1,
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: DecoratedTextField(
                padding: const EdgeInsets.all(8),
                initialValue: initialValue,
                labelText: labelText,
                onChanged: onChanged,
                validator: validator,
                onFieldSubmitted: (value) => Navigator.of(context).pop(),
              ),
            ),
          ),
        ),
      );
}
