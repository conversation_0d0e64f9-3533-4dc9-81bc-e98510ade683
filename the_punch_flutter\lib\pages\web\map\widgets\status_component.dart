import 'package:flutter/material.dart';
import '../../dashboard_widgets/status_widget/StatusComponent.dart';
import 'status_component.dart';

class StatusLayout extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Container(
      width: 468,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        // gradient: const LinearGradient(
        //   begin: Alignment.centerRight,
        //   end: Alignment.centerLeft,
        //   colors: [
        //     Color.fromRGBO(198, 222, 246, 1),
        //     Color.fromRGBO(235, 246, 255, 1),
        //   ],
        // ),
        borderRadius: BorderRadius.circular(32),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top Navigation Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // StatusComponent(label: 'Today', isSelected: true),
              // StatusComponent(label: 'Week'),
              // StatusComponent(label: 'Month'),
              // StatusComponent(label: 'Live'),
              // StatusComponent(label: 'Scheduled'),
            ],
          ),
          const SizedBox(height: 24),
          // Divider Line
          Container(
            width: double.infinity,
            height: 1,
            color: const Color.fromRGBO(9, 31, 48, 0.12),
          ),
          const SizedBox(height: 24),
          // Status Counts and Alerts
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Status Counts
              const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Status Counts',
                    style: TextStyle(
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w700,
                      fontSize: 14,
                      letterSpacing: -0.14,
                    ),
                  ),
                  SizedBox(height: 6),
                  Row(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Live',
                            style: TextStyle(
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w500,
                              fontSize: 12,
                              letterSpacing: -0.12,
                            ),
                          ),
                          Text(
                            '0',
                            style: TextStyle(
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w700,
                              fontSize: 20,
                              letterSpacing: -0.2,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(width: 24),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Scheduled',
                            style: TextStyle(
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w500,
                              fontSize: 12,
                              letterSpacing: -0.12,
                            ),
                          ),
                          Text(
                            '0',
                            style: TextStyle(
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.w700,
                              fontSize: 20,
                              letterSpacing: -0.2,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(height: 6),
                  Text(
                    'Completed',
                    style: TextStyle(
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w700,
                      fontSize: 20,
                      letterSpacing: -0.2,
                    ),
                  ),
                  Text(
                    '229',
                    style: TextStyle(
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w700,
                      fontSize: 20,
                      letterSpacing: -0.2,
                    ),
                  ),
                ],
              ),
              // Alerts
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Alerts',
                    style: TextStyle(
                      fontFamily: 'Poppins',
                      fontWeight: FontWeight.w700,
                      fontSize: 14,
                      letterSpacing: -0.14,
                    ),
                  ),
                  const SizedBox(height: 6),
                  AlertItem(color: Colors.yellow, label: 'Early/Late In', count: 16),
                  AlertItem(color: Colors.orange, label: 'Early/Late Out', count: 18),
                  AlertItem(color: Colors.red, label: 'Geofence Breach', count: 23),
                  AlertItem(color: Colors.brown, label: 'No Show', count: 207),
                ],
              ),
            ],
          ),
        ],
      ),
    );
}

class AlertItem extends StatelessWidget {
  final Color color;
  final String label;
  final int count;

  AlertItem({required this.color, required this.label, required this.count});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '$label • $count',
          style: const TextStyle(
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w500,
            fontSize: 12,
            letterSpacing: -0.12,
          ),
        ),
      ],
    );
  }
}
