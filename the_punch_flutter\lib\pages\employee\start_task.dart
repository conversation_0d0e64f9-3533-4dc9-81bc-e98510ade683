import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:provider/provider.dart';
import 'package:the_punch_flutter/dialogs/view_punch_card_dialog.dart';
import '../../dataModel/data/user.dart';
import '../../dataModel/models/location_model.dart';
import '../../dataModel/models/punch_view_model.dart';
import '../../dataModel/data/job_type.dart';
import '../../dataModel/data/location.dart';
import '../../dataModel/data/schedule.dart';
import '../../dataModel/data_model.dart';
import '../../dataModel/models/punch_card_model.dart';
import '../../dataModel/models/user_model.dart';
import '../../helpers/check_session.dart';
import '../../helpers/color_helper.dart';
import '../../helpers/screen_helper.dart';
import '../../misc/calculate_distance.dart';
import '../../state/location_state.dart';
import '../../state/punch_state.dart';
import '../web/my_scaffold.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class StartTaskPage extends StatefulWidget {
  final location_name;
  const StartTaskPage({super.key, required this.location_name});

  @override
  _StartTaskPageState createState() => _StartTaskPageState();
}

class _StartTaskPageState extends State<StartTaskPage> {
  bool isLoading = true;
  Location? closestLocation;
  List<String> tasks = [
    'Special Event',
    'Inspection',
    'Administrative',
    'Training',
    'Project',
    'Customer Visit',
    'Employee Visit',
  ];
  List<Map<String, dynamic>> cleaningSchedules = [];
  bool showTasks = false;
  bool isStartingTask = false;
  bool selectedisScheduled = false;
  String selectedlocationid = '';
  String? selectedScheduleId;
  bool locationSelected = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) => MultiProvider(
        providers: [
          ChangeNotifierProvider<PunchViewModel>(
            create: (context) => PunchViewModel()..initializeData(),
          ),
          ChangeNotifierProvider<PunchState>.value(
            value: PunchState(),
          ),
        ],
        child: Builder(
          builder: (context) => MyScaffold(
            showDrawer: false,
            showBackButton: true,
            showNotificationButton: false,
            title: AppLocalizations.of(context)!.clock,
            body: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _Header(widget.location_name['closest_location']??'Burger Queen'),
                Expanded(
                  child: _Body(onLocationSelected: _locationSelected),
                )
              ],
            ),
          ),
        ),
      );

  void _locationSelected(BuildContext context, bool isScheduled,
      String locationId, String? scheduleId) async {
    if (mounted) {
      setState(() {
        selectedisScheduled = isScheduled;
        selectedlocationid = locationId;
        selectedScheduleId = scheduleId;
        locationSelected = true;
      });
    }
    Navigator.pop(context);
  }
}

class _Header extends StatefulWidget {
  final String location_name;
  const _Header(this.location_name);

  @override
  State<_Header> createState() => _HeaderState();
}

class _HeaderState extends State<_Header> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      child: Row(
        children: [
          Expanded(
            child: Column(
              children: [
                SizedBox(
                  height: ScreenHelper.screenHeightPercentage(context, 1),
                ),
                const Text(
                  "Start a task",
                  style: TextStyle(
                      color: Colors.black,
                      fontSize: 30,
                      fontWeight: FontWeight.bold),
                ),
                Text(
                  widget.location_name ?? "No location found",
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(
                  height: ScreenHelper.screenHeightPercentage(context, 2),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _Body extends StatefulWidget {
  final Function(BuildContext, bool, String, String?) onLocationSelected;

  const _Body({required this.onLocationSelected});
  @override
  _BodyState createState() => _BodyState();
}

class _BodyState extends State<_Body> {
  bool isLoading = true;
  Location? closestLocation;
  List<String> tasks = [
    'Special Event',
    'Inspection',
    'Administrative',
    'Training',
    'Project',
    'Customer Visit',
    'Employee Visit',
  ];
  List<Map<String, dynamic>> cleaningSchedules = [];
  bool isStartingTask = false;

  @override
  void initState() {
    super.initState();
    _loadClosestLocationAndSchedules();
  }

  Future<void> _loadClosestLocationAndSchedules() async {
    setState(() {
      isLoading = true;
    });

    await LocationState.updateCurrentLocation();
    final currentPosition = LocationState().currentPosition;

    if (currentPosition != null) {
      final locations = (await DataModel().locationModel.active).toList();

      Map<Location, double> distances = {};
      for (var location in locations) {
        final distance = calculateDistance(
          currentPosition.latitude,
          currentPosition.longitude,
          location.latitude,
          location.longitude,
        );
        distances[location] = distance;
      }

      final withinOneMile =
          distances.entries.where((entry) => entry.value <= 1609.34).toList();

      if (withinOneMile.isNotEmpty) {
        withinOneMile.sort((a, b) => a.value.compareTo(b.value));
        closestLocation = withinOneMile.first.key;
        await _loadCleaningSchedules(closestLocation!.id);
      }
    }

    setState(() {
      isLoading = false;
    });
  }

  Future<void> _loadCleaningSchedules(String locationId) async {
    final scheduleModel = DataModel().scheduleModel;
    final allSchedules = await scheduleModel.getByLocationId(locationId);

    final now = DateTime.now();
    final todaySchedules = allSchedules.where((schedule) {
      return schedule.startDateLocal.year == now.year &&
          schedule.startDateLocal.month == now.month &&
          schedule.startDateLocal.day == now.day;
    }).toList();

    todaySchedules.sort((a, b) => a.startDateLocal.compareTo(b.startDateLocal));

    List<Map<String, dynamic>> scheduleInfoList = [];
    for (var schedule in todaySchedules) {
      final user = await UserModel().getById(schedule.userId);
      scheduleInfoList.add({
        'schedule': schedule,
        'user': user,
      });
    }

    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 1);

    final punchCardModel = PunchCardModel();
    final monthlyPunchCards =
        await punchCardModel.getBetween(startOfMonth, endOfMonth);

    final monthlyScheduleIds = monthlyPunchCards
        .where((p) => p.scheduleId != null)
        .map((p) => p.scheduleId!)
        .toSet();

    scheduleInfoList.removeWhere((info) {
      final schedule = info['schedule'] as Schedule;
      return monthlyScheduleIds.contains(schedule.id);
    });

    setState(() {
      cleaningSchedules = scheduleInfoList;
    });
  }

  void _onUnscheduledCleaningSelected() {
    _onTaskSelected('Unscheduled');
  }

  void _onTaskSelected(String task, {String? scheduleId}) async {
    Map<String, String> taskNameToId = {
      'Special Event': JobType.specialEventId,
      'Inspection': JobType.inspectionId,
      'Administrative': JobType.administrativeId,
      'Training': JobType.trainingId,
      'Project': JobType.projectId,
      'Customer Visit': JobType.customerVisitId,
      'Employee Visit': JobType.employeeVisitId,
      'Unscheduled': JobType.unscheduledId,
      'Scheduled': JobType.scheduledId,
    };

    String? jobTypeId = taskNameToId[task];
    if (jobTypeId == null) return;

    final sessionOk = await checkSession(context);
    if (!sessionOk) return;

    setState(() {
      isStartingTask = true;
    });

    await PunchState().managerTaskPunchIn(
      locationId: closestLocation?.id,
      jobTypeId: jobTypeId,
      scheduleId: scheduleId,
    );

    Navigator.of(context).pop();

    await showDialog(
      context: context,
      builder: (BuildContext context) => const ViewPunchCardDialog(),
    );
  }

  Future<Location?> _loadScheduleWidgets(Schedule schedule) async {
    return await LocationModel().getLocationById(schedule.locationId);
  }

  @override
  Widget build(BuildContext context) => SingleChildScrollView(
        child: SizedBox(
          width: double.infinity,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Align(
                alignment: Alignment.center,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: GestureDetector(
                    onTap: _onUnscheduledCleaningSelected,
                    child: Container(
                      width: MediaQuery.of(context).size.width * 0.66,
                      decoration: BoxDecoration(
                        color: Colors.blue[400],
                        borderRadius: BorderRadius.circular(30),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          const Icon(Icons.location_on, color: Colors.white),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Center(
                              child: Text(
                                'Unscheduled',
                                style: Theme.of(context)
                                    .textTheme
                                    .titleMedium
                                    ?.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              Text('Scheduled', style: TextStyle(fontSize:14, color:ColorHelper.thePunchLightGray())),
              if (cleaningSchedules.isNotEmpty)
                ...cleaningSchedules.map((scheduleInfo) {
                  final schedule = scheduleInfo['schedule'] as Schedule;
                  return Align(
                    alignment: Alignment.center,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4.0),
                      child: GestureDetector(
                        onTap: () {
                          _onTaskSelected('Scheduled', scheduleId: schedule.id);
                        },
                        child: Container(
                          width: MediaQuery.of(context).size.width * 0.66,
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: ColorHelper.thePunchAdminButtonBlue(),
                              width: 2,
                            ),
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(30),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Icon(Icons.location_on, color: ColorHelper.thePunchAdminButtonBlue()),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Center(
                                  child: Text(
                                    'Scheduled ${schedule.startDateLocal.hour}${schedule.startDateLocal.hour<12?"AM":"PM"}  ',
                                    style: Theme.of(context)
                                        .textTheme
                                        .labelSmall
                                        ?.copyWith(
                                          color: ColorHelper.thePunchAdminButtonBlue(),
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold
                                        ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                              FutureBuilder<Location?>(
                                future: _loadScheduleWidgets(schedule),
                                builder: (context, snapshot) {
                                  if (!snapshot.hasData) {
                                    return const CircularProgressIndicator();
                                  }
                                  final location = snapshot.data;
                                  return Text(
                                    '${location?.name ?? "Unknown Location"}',
                                    style: TextStyle(color:ColorHelper.thePunchAdminButtonBlue())
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList()
              else
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    'No Schedules Found Today.',
                    style: TextStyle(fontSize: 12),
                  ),
                ),
              Text('Other', style: TextStyle(fontSize:14, color:ColorHelper.thePunchLightGray())),
              ...tasks.map((task) => Align(
                    alignment: Alignment.center,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4.0),
                      child: GestureDetector(
                        onTap: () {
                          _onTaskSelected(task);
                        },
                        child: Container(
                          width: MediaQuery.of(context).size.width * 0.66,
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: ColorHelper.thePunchAdminButtonBlue(),
                              width: 2,
                            ),
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(30),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Icon(getIcon(task), color: ColorHelper.thePunchAdminButtonBlue()),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Center(
                                  child: Text(
                                    task,
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(
                                          color: ColorHelper.thePunchAdminButtonBlue(),
                                        ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  )
                ),
            ],
          ),
        ),
      );

  IconData getIcon(String icon) {
    switch (icon) {
      case 'Special Event':
        return Icons.calendar_today_outlined;
      case 'Inspection':
        return Symbols.data_loss_prevention;
      case 'Administrative':
        return Icons.shield_outlined;
      case 'Training':
        return Icons.school;
      case 'Project':
        return Symbols.tactic;
      case 'Customer Visit':
        return Icons.sentiment_satisfied;
      case 'Employee Visit':
        return Symbols.id_card;
      default:
        return Icons.error;
    }
  }
}
