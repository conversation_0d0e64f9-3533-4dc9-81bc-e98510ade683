import 'package:flutter/material.dart';

class IconCardTile extends Card {
  const IconCardTile({
    super.key,
    super.color = Colors.white,
    super.shadowColor,
    super.elevation = 1,
    super.shape,
    super.borderOnForeground = true,
    super.margin = const EdgeInsets.all(8),
    super.clipBehavior,
    super.child,
    super.semanticContainer = true,
    this.leading,
    this.title,
    this.body,
    this.trailing,
    this.onTap,
  });

  final Widget? leading;
  final Widget? title;
  final Widget? body;
  final Widget? trailing;
  final void Function()? onTap;

  @override
  Widget? get child => InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: IconTile(
              leading: leading, title: title, body: body, trailing: trailing),
        ),
      );
}

class IconTile extends StatelessWidget {
  const IconTile({
    super.key,
    this.leading,
    this.title,
    this.body,
    this.trailing,
    this.padding,
  });

  final Widget? leading;
  final Widget? title;
  final Widget? body;
  final Widget? trailing;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) => Padding(
        padding: padding ?? EdgeInsets.zero,
        child: IntrinsicHeight(
          child: Stack(
            children: [
              if (leading != null)
                Align(
                  alignment: Alignment.topLeft,
                  child: SizedBox(
                    width: 16,
                    height: 16,
                    child: FittedBox(fit: BoxFit.scaleDown, child: leading),
                  ),
                ),
              Container(
                alignment: Alignment.topLeft,
                margin: const EdgeInsets.only(left: 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (title != null)
                      Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: title),
                    if (body != null) body!,
                  ],
                ),
              ),
              if (trailing != null)
                Align(alignment: Alignment.centerRight, child: trailing),
            ],
          ),
        ),
      );
}
