import '../base_data.dart';
import '../data/location_notes.dart';
import '../hive_db.dart';

class LocationNoteModel extends BaseDataModel<LocationNote> {
  @override
  Future<List<LocationNote>> get all async => (await HiveDb.database).locationNotes.values.toList();

  @override
  Future<void> save(Iterable<LocationNote> notes) async {
    var db = await HiveDb.database;
    for (var note in notes) {
      db.locationNotes.put(note.id, note); // Save or update note in HiveDB
      note.isDirty = true; // Mark note as dirty for sync
    }
    notifyListeners();
  }

  Future<List<LocationNote>> getByLocationIds(Iterable<String> ids) async {
    if (ids.isEmpty) return [];
    var db = await HiveDb.database;
    return db.locationNotes.values.where((note) => ids.contains(note.locationId)).toList();
  }

  Future<List<LocationNote>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit).toList();
    }
    return dirtyRecords.toList();
  }

  Future<void> saveDirty(Iterable<LocationNote> notes) async {
    var db = await HiveDb.database;
    for (var note in notes) {
      db.locationNotes.put(note.id, note); // Save or update note in HiveDB
      note.isDirty = true; // Mark note as dirty for sync
    }
    notifyListeners();
  }
}