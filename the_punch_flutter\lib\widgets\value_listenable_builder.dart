import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';

class ValueListenableBuilder2<A, B> extends StatelessWidget {
  const ValueListenableBuilder2({
    super.key,
    required this.valueListenable1,
    required this.valueListenable2,
    required this.builder,
  });

  final ValueListenable<A> valueListenable1;
  final ValueListenable<B> valueListenable2;
  final Widget Function(BuildContext context, A a, B b) builder;

  @override
  Widget build(BuildContext context) => ValueListenableBuilder<A>(
        valueListenable: valueListenable1,
        builder: (_, a, __) => ValueListenableBuilder<B>(
          valueListenable: valueListenable2,
          builder: (context, b, __) => builder(context, a, b),
        ),
      );
}

class ValueListenableBuilder3<A, B, C> extends StatelessWidget {
  const ValueListenableBuilder3({
    super.key,
    required this.valueListenable1,
    required this.valueListenable2,
    required this.valueListenable3,
    required this.builder,
  });

  final ValueListenable<A> valueListenable1;
  final ValueListenable<B> valueListenable2;
  final ValueListenable<C> valueListenable3;
  final Widget Function(BuildContext context, A value1, B value2, C value3) builder;

  @override
  Widget build(BuildContext context) => ValueListenableBuilder<A>(
        valueListenable: valueListenable1,
        builder: (_, a, __) => ValueListenableBuilder<B>(
          valueListenable: valueListenable2,
          builder: (context, b, __) => ValueListenableBuilder<C>(
            valueListenable: valueListenable3,
            builder: (context, c, __) => builder(context, a, b, c),
          ),
        ),
      );
}

class ValueListenableBuilder4<A, B, C, D> extends StatelessWidget {
  const ValueListenableBuilder4({
    super.key,
    required this.valueListenable1,
    required this.valueListenable2,
    required this.valueListenable3,
    required this.valueListenable4,
    required this.builder,
  });

  final ValueListenable<A> valueListenable1;
  final ValueListenable<B> valueListenable2;
  final ValueListenable<C> valueListenable3;
  final ValueListenable<D> valueListenable4;
  final Widget Function(BuildContext context, A value1, B value2, C value3, D value4) builder;

  @override
  Widget build(BuildContext context) => ValueListenableBuilder<A>(
        valueListenable: valueListenable1,
        builder: (_, a, __) => ValueListenableBuilder<B>(
          valueListenable: valueListenable2,
          builder: (context, b, __) => ValueListenableBuilder<C>(
            valueListenable: valueListenable3,
            builder: (context, c, __) => ValueListenableBuilder<D>(
              valueListenable: valueListenable4,
              builder: (context, d, __) => builder(context, a, b, c, d),
            ),
          ),
        ),
      );
}
