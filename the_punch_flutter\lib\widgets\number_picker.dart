import 'package:flutter/material.dart';

class NumberPicker extends StatefulWidget {
  final int value;
  final int? minValue;
  final int? maxValue;
  final Function(int value) onChanged;

  const NumberPicker({super.key, required this.value, this.minValue, this.maxValue, required this.onChanged});

  @override
  State<NumberPicker> createState() => _NumberPickerState();
}

class _NumberPickerState extends State<NumberPicker> {
  late int value;

  @override
  void initState() {
    value = widget.value;
    super.initState();
  }

  @override
  Widget build(BuildContext context) => Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(onPressed: widget.minValue != null && value == widget.minValue ? null : onDecrementTapped, icon: const Icon(Icons.arrow_left)),
        Text(value.toString()),
        IconButton(
          onPressed: widget.maxValue != null && value == widget.maxValue ? null : onIncrementTapped,
          icon: const Icon(Icons.arrow_right),
        ),
      ],
    );

  void onDecrementTapped() {
    setState(() {
      value -= 1;
      if (widget.minValue != null && value < widget.minValue!) value = widget.minValue!;
    });
    widget.onChanged(value);
  }

  void onIncrementTapped() {
    setState(() {
      value += 1;
      if (widget.maxValue != null && value > widget.maxValue!) value = widget.maxValue!;
    });
    widget.onChanged(value);
  }
}
