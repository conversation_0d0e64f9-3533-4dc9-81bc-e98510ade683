import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../dataModel/data_model.dart';
import '../../dataModel/data/location.dart';
import '../../dataModel/data/schedule.dart';
import '../../helpers/color_helper.dart';
import '../../helpers/screen_helper.dart';
import '../../misc/change_notification_builder.dart';
import '../view_model_mixin.dart';
import '../../state/login_state.dart';
import '../../widgets/date_bar.dart';
import '../../widgets/padded_card.dart';
import '../../misc/extensions.dart';
import '../web/my_scaffold.dart';

class CalendarPage extends StatelessWidget {
  const CalendarPage({super.key});
  @override
  Widget build(BuildContext context) => ChangeNotifierProvider(
        create: (context) => _ViewModel(),
        builder: (context, child) => MyScaffold(
          title: AppLocalizations.of(context)!.calendar,
          // body: MyRoundedBody(
          //   header: _Header(),
          //   body: _Body(),
          // ),
          body: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _Header(),
              Expanded(
                child: _Body(),
              )
            ],
          ),
        ),
      );
}

class _Header extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
        builder: (context, viewModel, _) => DateBar(
          initialDate: DateTime.now().startOfWeek,
          pipsByDate: viewModel.scheduleCountByDate,
          selectedDate: viewModel.selectedDate,
        ),
      );
}

class _Body extends StatefulWidget {
  @override
  _BodyState createState() => _BodyState();
}

class _BodyState extends State<_Body> {
  PageController? pageController;

  PageController _createPageController(_ViewModel viewModel) {
    final pageController = PageController(
        initialPage: viewModel.dates!.indexOf(viewModel.selectedDate.value));
    viewModel.selectedDate.addListener(
      () {
        if (pageController.hasClients) {
          final page = viewModel.dates!.indexOf(viewModel.selectedDate.value);
          if (page == -1) return;
          unawaited(
            pageController.animateToPage(
              page,
              duration: const Duration(milliseconds: 500),
              curve: Curves.ease,
            ),
          );
        }
      },
    );
    return pageController;
  }

  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
        builder: (context, data, child) {
          print('VIEW DATES: ${data.dates}');
          if (data.dates == null) return Container();
          final dates = data.dates!;
          pageController ??= _createPageController(data);
          if (pageController!.hasClients &&
              pageController!.page != dates.indexOf(data.selectedDate.value)) {
            pageController!.jumpToPage(
              dates.indexOf(data.selectedDate.value),
            );
          }
          return PageView.builder(
            controller: pageController,
            itemCount: data.dates!.length,
            itemBuilder: (context, index) => _BodyPage(dates[index]),
          );
        },
      );
}

class _BodyPage extends StatelessWidget {
  final DateTime date;

  const _BodyPage(this.date);

  @override
  Widget build(BuildContext context) => ChangeNotifierBuilder<_PageViewModel>(
        create: (context) => _PageViewModel(date: date),
        builder: (context, viewModel, child) {
          if (viewModel.schedules.isEmpty) return Container();
          print('VIEW $viewModel');
          final schedules = viewModel.schedules;
          final locationMap = viewModel.locationMap;
          return Center(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: ScreenHelper.screenWidth(context),
              ),
              child: ListView(
                children: [
                  Container(height: 4),
                  for (final schedule in schedules)
                    Padding(
                      padding: const EdgeInsets.only(left: 4, right: 4),
                      child: Column(
                        children: [
                          Text("DATE"),
                          _ScheduleTile(
                              schedule: schedule,
                              location: locationMap[schedule.locationId]),
                          if (schedule.travelTime != null)
                            _TravelTimeTile(schedule: schedule),
                        ],
                      ),
                    )
                ],
              ),
            ),
          );
        },
      );
}

class _ScheduleTile extends StatelessWidget {
  final Schedule schedule;
  final Location? location;

  const _ScheduleTile({required this.schedule, required this.location});

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);
    final theme = Theme.of(context);
    final caption = theme.textTheme.bodyMedium;
    final body1 = theme.textTheme.titleLarge?.copyWith(
      fontSize: ScreenHelper.screenHeightPercentage(context, 2),
    );
    final address = theme.textTheme.titleMedium;

    final start = schedule.startDateUtc;
    final end = schedule.endDateUtc;
    final duration = end.difference(start);

    return GestureDetector(
      onTap: () async => context
          .pushNamed('/calendar/details', queryParameters: {'id': schedule.id}),
      child: Row(
        children: [
          Container(
            constraints: const BoxConstraints(minWidth: 65),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(start.toFormattedTime(locale), style: caption),
                Text(end.toFormattedTime(locale), style: caption),
              ],
            ),
          ),
          Flexible(
            child: IntrinsicHeight(
              child: PaddedCard(
                elevation: 1,
                child: Stack(
                  children: [
                    if (location != null)
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Padding(
                          padding: const EdgeInsets.only(right: 52),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                location!.name,
                                style: body1,
                              ),
                              if (location!.address1.isNotEmpty)
                                Text(
                                  location!.address1,
                                  style: address,
                                ),
                              if (location!.address2.isNotEmpty)
                                Text(
                                  location!.address2,
                                  style: address,
                                ),
                              if (location!.address3.isNotEmpty)
                                Text(
                                  location!.address3,
                                  style: address,
                                ),
                            ],
                          ),
                        ),
                      ),
                    Align(
                      alignment: Alignment.topRight,
                      child: Text(
                        duration.toFormatted,
                        style: address,
                      ),
                    ),
                    const Align(
                      alignment: Alignment.centerRight,
                      child: Icon(Icons.chevron_right),
                    ),
                  ],
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}

class _TravelTimeTile extends StatelessWidget {
  final Schedule schedule;

  const _TravelTimeTile({required this.schedule});

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);
    final theme = Theme.of(context);
    final caption = theme.textTheme.bodySmall;
    final body1 = theme.textTheme.bodyLarge?.copyWith(
      color: ColorHelper.thePunchGray(), // Use consistent text color
      fontWeight: FontWeight.w800,
    );

    final duration = schedule.travelTime!;
    final start = schedule.endDateUtc;
    final end = start.add(duration);

    return Row(
      children: [
        Container(
          constraints: const BoxConstraints(minWidth: 65),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(start.toFormattedTime(locale), style: caption),
              Text(end.toFormattedTime(locale), style: caption),
            ],
          ),
        ),
        Flexible(
          child: IntrinsicHeight(
            child: PaddedCard(
              elevation: 1,
              child: Stack(
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8.0), // Added padding around the text
                      child: Text(
                        AppLocalizations.of(context)!.travelTime,
                        style: body1,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0), // Added padding around the text
                    child: Align(
                      alignment: Alignment.topRight,
                      child: Text(duration.toFormatted,
                          style: theme.textTheme.titleMedium),
                    ),
                  ),
                ],
              ),
            ),
          ),
        )
      ],
    );
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final selectedDate = ValueNotifier<DateTime>(DateTime.now().dateOnly);

  Map<DateTime, int>? scheduleCountByDate;
  List<DateTime>? get dates => scheduleCountByDate?.keys.toList();

  _ViewModel() {
    addListenables([DataModel().scheduleModel, DataModel().locationModel]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    final employeeId = LoginState.userId;
    final starts = await DataModel()
        .scheduleModel
        .getStartsByEmployeeIdOrLocations(employeeId);
    final dates = starts.map((e) => e.toLocal().dateOnly).toList();
    dates.sort((a, b) => a.compareTo(b));
    scheduleCountByDate = <DateTime, int>{};
    for (final element in dates) {
      if (!scheduleCountByDate!.containsKey(element)) {
        scheduleCountByDate![element] = 1;
      } else {
        scheduleCountByDate![element] = scheduleCountByDate![element]! + 1;
      }
    }
    notifyListeners();
  }
}

class _PageViewModel extends ChangeNotifier with ViewModelMixin {
  final DateTime date;
  Iterable<Schedule> schedules = [];
  Map<String, Location> locationMap = {};

  _PageViewModel({required this.date}) {
    addListenables([DataModel().scheduleModel, DataModel().locationModel]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    final employeeId = LoginState.userId;
    final dayStart = date.dateOnly;
    final dayEnd = date.dateOnly.addDays(1);

    // Fetch schedules and sort them by start time
    schedules = (await DataModel()
            .scheduleModel
            .getBetweenByEmployeeIdLocation(employeeId, dayStart, dayEnd))
        .toList()
      ..sort((a, b) => a.startDateUtc.compareTo(b.startDateUtc));

    final locationIds = schedules.map((e) => e.locationId).toSet();
    final locations = await DataModel().locationModel.getByIds(locationIds);
    locationMap.addAll({for (final e in locations) e.id: e});
    notifyListeners();
  }
}
