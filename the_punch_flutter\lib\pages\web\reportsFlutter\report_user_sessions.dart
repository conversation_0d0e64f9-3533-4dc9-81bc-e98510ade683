// import 'package:flutter/material.dart';
// import 'package:provider/provider.dart';
// import 'package:the_punch_flutter/api/reportFlutterModel.dart';
// import 'package:the_punch_flutter/dialogs/materialDialog.dart';
// import 'package:the_punch_flutter/menus/myAppBar.dart';
// import 'package:the_punch_flutter/misc/appLocalization.dart';
// import 'package:the_punch_flutter/pages/transitionMixin.dart';
// import 'package:the_punch_flutter/pages/viewModelMixin.dart';
// import 'package:the_punch_flutter/pdf/ReportUserSessionsGenerator.dart';
// import 'package:the_punch_flutter/pdf/pdfWidget.dart';
// import 'package:the_punch_flutter/state/appState.dart';
// import 'package:the_punch_flutter/widgets/decoratedTextField.dart';
// import 'package:the_punch_flutter/misc/extensions.dart';

// class ReportUserSessionsPage extends MaterialPage with NoTransitionMixin {
//   ReportUserSessionsPage() : super(key: <PERSON><PERSON><PERSON>('ReportUserSessionsPage'), child: _Scaffold());
// }

// class _Scaffold extends StatelessWidget {
//   const _Scaffold();

//   @override
//   Widget build(BuildContext context) {
//     return ChangeNotifierProvider<_ViewModel>(
//         create: (context) => _ViewModel(),
//         child: MyScaffold(
//           appBar: MyAppBar(
//             AppLocalization.of(context).reportUserSessions,
//             breadcrumbs: {
//               AppLocalization.of(context).reports: () => AppState().replaceStates([PageState.reports()]),
//               AppLocalization.of(context).reportUserSessions: null,
//             },
//           ).appBar,
//           body: _Body(),
//         ));
//   }
// }

// class _Body extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return Center(
//       child: Column(
//         children: [
//           _Row1(),
//           _Row2(),
//         ],
//       ),
//     );
//   }
// }

// class _Row1 extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return Consumer<_ViewModel>(builder: (context, viewModel, _) {
//       return Row(
//         children: [
//           Flexible(
//             child: Padding(
//                 padding: const EdgeInsets.all(8.0),
//                 child: GestureDetector(
//                   onTap: () async {
//                     final pickedDate = await showDatePicker(
//                       context: context,
//                       initialDate: viewModel.startDate,
//                       firstDate: DateTime.utc(DateTime.now().year - 20),
//                       lastDate: DateTime.utc(DateTime.now().year + 20),
//                     );
//                     if (pickedDate != null) viewModel.startDate = pickedDate;
//                   },
//                   child: DecoratedText(
//                     padding: const EdgeInsets.all(8.0),
//                     text: viewModel.startDate.toFormattedDateWithYear(context),
//                     labelText: AppLocalization.of(context).from,
//                   ),
//                 )),
//           ),
//           Flexible(
//             child: Padding(
//                 padding: const EdgeInsets.all(8.0),
//                 child: GestureDetector(
//                   onTap: () async {
//                     final pickedDate = await showDatePicker(
//                       context: context,
//                       initialDate: viewModel.endDate,
//                       firstDate: DateTime.utc(DateTime.now().year - 20),
//                       lastDate: DateTime.utc(DateTime.now().year + 20),
//                     );
//                     if (pickedDate != null) viewModel.endDate = pickedDate;
//                   },
//                   child: DecoratedText(
//                     padding: const EdgeInsets.all(8.0),
//                     text: viewModel.endDate.toFormattedDateWithYear(context),
//                     labelText: AppLocalization.of(context).to,
//                   ),
//                 )),
//           ),
//         ],
//       );
//     });
//   }
// }

// class _Row2 extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return ElevatedButton(onPressed: () => runReport(context), child: Text(AppLocalization.of(context).runReport));
//   }

//   Future<void> runReport(BuildContext context) async {
//     var viewModel = context.read<_ViewModel>();

//     final response = await ReportModel().reportUserSessions(viewModel.startDate, viewModel.endDate);
//     response.entities.sort((a, b) => a.signedInOn.compareTo(b.signedInOn));
//     final tableRows = await ReportUserSessionsEntityConverter().convert(context, response.entities);

//     final title = AppLocalization.of(context).reportUserSessions;
//     final dateRange = viewModel.startDate.toFormattedDate(context) + ' - ' + viewModel.endDate.toFormattedDateWithYear(context);
//     final filename = title + '.pdf';

//     await showDialog(
//         context: context,
//         builder: (context) => MaterialDialog(
//               child: PdfWidget(
//                 pdfGenerator: ReportUserSessionsGenerator(title: title, dateRange: dateRange, tableRows: tableRows),
//                 filename: filename,
//               ),
//             ));
//   }
// }

// class _ViewModel extends ChangeNotifier with ViewModelMixin {
//   var _startDate = DateTime.now().startOfMonth;
//   var _endDate = DateTime.now().startOfMonth.addMonths(1).addDays(-1);

//   _ViewModel() {
//     unawaited(refresh());
//   }

//   @override
//   Future<void> refresh() async {
//     notifyListeners();
//   }

//   DateTime get startDate => _startDate;
//   set startDate(DateTime value) {
//     _startDate = value;
//     notifyListeners();
//   }

//   DateTime get endDate => _endDate;
//   set endDate(DateTime value) {
//     _endDate = value;
//     notifyListeners();
//   }
// }
