import 'package:flutter/material.dart';

mixin NoTransitionMixin implements MaterialPage {
  @override
  Route createRoute(BuildContext context) => PageRouteBuilder(
      settings: this,
      pageBuilder: (BuildContext context, Animation<double> animation, Animation<double> secondaryAnimation) => child,
    );
}

mixin FadeTransitionMixin implements MaterialPage {
  @override
  Route createRoute(BuildContext context) => PageRouteBuilder(
      settings: this,
      transitionDuration: const Duration(milliseconds: 250),
      pageBuilder: (BuildContext context, Animation<double> animation, Animation<double> secondaryAnimation) => FadeTransition(
          opacity: animation,
          child: child,
        ),
    );
}
