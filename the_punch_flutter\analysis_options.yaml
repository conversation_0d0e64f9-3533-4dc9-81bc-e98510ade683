# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

linter:
  # The lint rules applied to this project can be customized in the
  # section below to disable rules from the `package:flutter_lints/flutter.yaml`
  # included above or to enable additional rules. A list of all available lints
  # and their documentation is published at
  # https://dart-lang.github.io/linter/lints/index.html.
  #
  # Instead of disabling a lint rule for the entire project in the
  # section below, it can also be suppressed for a single line of code
  # or a specific dart file by using the `// ignore: name_of_lint` and
  # `// ignore_for_file: name_of_lint` syntax on the line or in the file
  # producing the lint.
  rules:
    # avoid_print: false  # Uncomment to disable the `avoid_print` rule
    prefer_single_quotes: true
    avoid_unused_constructor_parameters: true
    cascade_invocations: false
    parameter_assignments: true
    prefer_final_in_for_each: true
    prefer_relative_imports: true
    always_declare_return_types: true
    # always_put_required_named_parameters_first: true
    avoid_field_initializers_in_const_classes: true
    avoid_redundant_argument_values: true
    avoid_void_async: false
    cast_nullable_to_non_nullable: true
    directives_ordering: false
    join_return_with_assignment: true
    noop_primitive_operations: true
    omit_local_variable_types: true
    prefer_expression_function_bodies: true
    prefer_final_locals: true
    prefer_int_literals: true
    sort_constructors_first: false
    sort_unnamed_constructors_first: true
    unawaited_futures: true
    unnecessary_lambdas: true
    unnecessary_raw_strings: true
    use_colored_box: true
    use_decorated_box: true
    use_named_constants: true
    use_super_parameters: true
    sort_pub_dependencies: true

    discarded_futures: true
    avoid_dynamic_calls: true
    avoid_annotating_with_dynamic: true
    avoid_catching_errors: true
    avoid_classes_with_only_static_members: false
    # lines_longer_than_80_chars: true
    unnecessary_null_checks: true

# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options