// ignore_for_file: use_build_context_synchronously

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../api/api_model.dart';
import '../../dataModel/data/user.dart';
import '../../dataModel/data_model.dart';
import '../../dialogs/busy_dialog.dart';
import '../../dialogs/error_dialog.dart';
import '../../helpers/screen_helper.dart';
import '../../misc/app_localization.dart';
import '../../misc/change_notification_builder.dart';
import '../../misc/logging.dart';
import '../view_model_mixin.dart';
import '../../state/login_state.dart';
import '../../widgets/decorated_text_field.dart';
import '../../widgets/padded_card.dart';
import '../web/my_scaffold.dart';

class UpdatePasswordPage extends StatelessWidget {
  const UpdatePasswordPage({super.key});
  @override
  Widget build(BuildContext context) => MyScaffold(
        enableBottomBar: false,
        title: AppLocalization.of(context).updatePassword,
        showBackButton: true,
        body: _Body(),
      );
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Center(
        child: ChangeNotifierBuilder<_ViewModel>(
          create: (context) => _ViewModel(),
          builder: (context, viewModel, child) {
            if (viewModel.user == null) return Container();
            final user = viewModel.user!;

            final theme = Theme.of(context);
            final title = theme.textTheme.titleLarge;

            return FocusScope(
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: ScreenHelper.screenWidthPercentage(context, 90),
                ),
                child: PaddedCard(
                  elevation: 0,
                  child: ListView(
                    shrinkWrap: true,
                    children: [
                      Center(
                        child: Text(user.name, style: title),
                      ),
                      SizedBox(
                        height: ScreenHelper.screenHeightPercentage(context, 4),
                      ),
                      DecoratedTextField(
                        padding: const EdgeInsets.all(8),
                        initialValue: '',
                        obscureText: true,
                        autofillHints: const [AutofillHints.password],
                        labelText: AppLocalization.of(context).oldPassword,
                        onChanged: (value) => viewModel.setOldPassword(value),
                        validator: (value) => viewModel.oldPassword.isEmpty
                            ? AppLocalization.of(context).oldPasswordRequired
                            : ' ',
                      ),
                      DecoratedTextField(
                        padding: const EdgeInsets.all(8),
                        initialValue: '',
                        obscureText: true,
                        autofillHints: const [AutofillHints.newPassword],
                        labelText: AppLocalization.of(context).newPassword,
                        onChanged: (value) => viewModel.setPassword(value),
                        validator: (value) => viewModel.password.isEmpty
                            ? AppLocalization.of(context).passwordRequired
                            : ' ',
                      ),
                      DecoratedTextField(
                        padding: const EdgeInsets.all(8),
                        initialValue: '',
                        obscureText: true,
                        autofillHints: const [AutofillHints.newPassword],
                        labelText: AppLocalization.of(context).confirmPassword,
                        onChanged: (value) =>
                            viewModel.setConfirmPassword(value),
                        validator: (value) => viewModel.confirmPassword.isEmpty
                            ? AppLocalization.of(context)
                                .confirmPasswordRequired
                            : !viewModel.validateConfirmPassword
                                ? AppLocalization.of(context)
                                    .confirmPasswordNotMatchPassword
                                : ' ',
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8),
                        child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: ElevatedButton(
                            onPressed: !viewModel.canUpdate
                                ? null
                                : () => unawaited(
                                    updatePassword(context, viewModel)),
                            child: Text(
                              AppLocalization.of(context).updatePassword,
                              style: Theme.of(context).textTheme.titleSmall,
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      );

  Future<void> updatePassword(
      BuildContext context, _ViewModel viewModel) async {
    await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          final router = GoRouter.of(context);
          return BusyDialog(
              subTitle: AppLocalization.of(context).updatePassword,
              future: () async {
                try {
                  final response = await ApiModel().updatePassword(
                      viewModel.oldPassword,
                      viewModel.password,
                      viewModel.confirmPassword);
                  if (!response.isError) router.go('/');
                } on ApiException catch (e, stack) {
                  await logApiException(e, stack);
                  if (!context.mounted) return;
                  await showDialog(
                      context: context,
                      builder: (context) =>
                          ErrorDialog(errorCode: e.errorCode));
                } on Exception catch (e, stack) {
                  await logException(e, stack);
                  if (!context.mounted) return;
                  await showDialog(
                      context: context,
                      builder: (context) => const ErrorDialog());
                }
              });
        });
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  User? user;
  var oldPassword = '';
  var password = '';
  var confirmPassword = '';

  _ViewModel() {
    addListenables([
      DataModel().userModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    final userId = LoginState.userId;
    user = await DataModel().userModel.getById(userId);
    notifyListeners();
  }

  bool get validateOldPassword => oldPassword.isNotEmpty;
  bool get validatePassword => password.isNotEmpty;
  bool get validateConfirmPassword =>
      confirmPassword.isNotEmpty && password == confirmPassword;

  bool get canUpdate =>
      validateOldPassword && validatePassword && validateConfirmPassword;

  void setOldPassword(String value) {
    oldPassword = value;
    notifyListeners();
  }

  void setPassword(String value) {
    password = value;
    notifyListeners();
  }

  void setConfirmPassword(String value) {
    confirmPassword = value;
    notifyListeners();
  }
}
