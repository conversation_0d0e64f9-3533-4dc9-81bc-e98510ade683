import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:the_punch_flutter/api/requests/delete_chat_group_request.dart';
import '../dataModel/data/location_notes.dart';
import '../dataModel/data/message_group.dart';
import '../dataModel/data/travel_pings.dart';
import '../services/storage_manager_service.dart';
import '../state/login_state.dart';
import 'requests/build_asset_image_request.dart';
import 'requests/create_app_termination_alert_request.dart';
import 'requests/create_chat_group_request.dart';
import 'requests/create_punch_card_request.dart';
import 'requests/edit_group_members_request.dart';
import 'requests/fetch_messages_request.dart';
import 'requests/fetch_notifications_request.dart';
import 'requests/fetch_punch_card_history_request.dart';
import 'requests/fetch_travel_pings_request.dart';
import 'requests/fetch_travel_time_request.dart';
import 'requests/fetch_travel_data_request.dart';
import 'requests/forgot_login.dart';
import '../dataModel/data/alert.dart';
import '../dataModel/data/location_contact.dart';
import '../dataModel/data/schedule_template.dart';
import '../dataModel/data/user.dart';
import '../dataModel/data/user_type.dart';
import '../dataModel/data/geo_location.dart';
import '../dataModel/data/inspection.dart';
import '../dataModel/data/inspection_template.dart';
import '../dataModel/data/job_type.dart';
import '../dataModel/data/location.dart';
import '../dataModel/data/message.dart';
import '../dataModel/data/note.dart';
import '../dataModel/data/organization.dart';
import '../dataModel/data/schedule.dart';
import '../dataModel/data/punch_card.dart';
import '../dataModel/data/user_type_permission.dart';
import '../misc/json_conversion.dart';
import '../misc/my_platform.dart';
import 'requests/get_changes.dart';
import 'requests/get_organizations.dart';
import 'requests/get_conditional_changes.dart';
import 'requests/get_schedules_for_month.dart';
import 'requests/get_schedules_for_user_request.dart';
import 'requests/login.dart';
import 'requests/post_changes.dart';
import 'requests/post_punch_card_history_request.dart';
import 'requests/registration.dart';
import 'requests/remove_group_members_request.dart';
import 'requests/schedule_template.dart';
import 'requests/send_geo_fence_ping_request.dart';
import 'requests/send_travel_ping_request.dart';
import 'requests/system.dart';
import 'requests/update_password.dart';
import 'package:mime/mime.dart'; // optional if you want to auto-detect mime type
import 'package:http_parser/http_parser.dart'; // for MediaType

// to rebuild json...
// flutter packages pub run build_runner build
class SessionCheckResponse {
  final String? errorCode;
  final String? errorMessage;

  SessionCheckResponse({
    this.errorCode,
    this.errorMessage,
  });
}
class ApiModel {
  //static const production = false;
  static const production = true;

  // debug: need to specify the actual ip address for android emulators and devices; web and window devices are fine with localhost
  //final baseAddress = production ? 'api.thepunch.app' : '************:9877';
  //final baseAddress = production ? 'api.thepunch.app' : '**************:9877';
  final baseAddress = production ? 'api.thepunch.app' : 'localhost:9877';

  //final baseAddress = 'api.thepunch.app';

  final _basePath = 'api/thepunch/';

  //static const String kAppVersion = '1.1.0';

  final logoutErrorCode = ValueNotifier<String?>(null);
  final serverTime = ValueNotifier<DateTime?>(null);

  static ApiModel? _singleton;
  factory ApiModel() {
    _singleton ??= ApiModel._();
    return _singleton!;
  }
  ApiModel._();

  Future<String?> ping() async {
    const endPoint = 'ping';
    final uri = production
        ? Uri.https(baseAddress, _basePath + endPoint)
        : Uri.http(baseAddress, _basePath + endPoint);
    return http.get(uri).then((response) {
      if (response.statusCode == 200) return response.body;
      return Future.error('unknown response ${response.statusCode}');
    });
  }

  Future<Map<String, dynamic>?> get(String endPoint,
      {Map<String, dynamic>? parameters}) async {
    final uri = production
        ? Uri.https(baseAddress, _basePath + endPoint, parameters)
        : Uri.http(baseAddress, _basePath + endPoint, parameters);
    // print('request $uri');
    final response = await http.get(uri);
    if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) {
      await FirebaseAnalytics.instance
          .logEvent(name: 'status_code', parameters: {
        'status_code': response.statusCode.toString(),
        'end_point': endPoint,
      });
    }
    if (response.statusCode != 200) {
      throw Exception('unknown response ${response.statusCode}');
    }
    // print('response ${response.body}');
    return jsonDecode(response.body) as Map<String, dynamic>;
  }

  Future<Map<String, dynamic>?> post(String endPoint, request) async {
    final body = jsonEncode(request);
    final headers = {
      'Content-Type': 'application/json',
      // 'Accept-Encoding': 'gzip',
    };
    final uri = production
        ? Uri.https(baseAddress, _basePath + endPoint)
        : Uri.http(baseAddress, _basePath + endPoint);
    // print('request $uri');
    // print('request body: $body');
    final response = await http.post(uri, body: body, headers: headers);
    if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) {
      await FirebaseAnalytics.instance
          .logEvent(name: 'status_code', parameters: {
        'status_code': response.statusCode.toString(),
        'end_point': endPoint,
      });
    }
    if (response.statusCode != 200) {
      throw Exception('unknown response ${response.statusCode}');
    }
    // print('response headers: ${response.headers}');
    // print('response body: ${response.body}');
    return jsonDecode(response.body) as Map<String, dynamic>;
  }

  Future<Iterable<Organization>> getOrganizations(String search) async {
    if (search.isNotEmpty) {
      final map = await get('GetOrganizations', parameters: {'search': search});
      if (map == null) return [];
      final response = GetOrganizationsResponse.fromJson(map);
      return response.organizations;
    }
    return [];
  }

  Future<SystemResponse> registerOrganization(
      {required Registration registration,
      required String captchaResponse}) async {
    final request = RegistrationRequest(
        registration: registration, captchaResponse: captchaResponse);
    final map = await post('RegisterOrganization', request);
    if (map == null) throw ApiException('INTERNAL_ERROR');
    final response = SystemResponse.fromJson(map);
    if (response.isError) {
      if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) {
        await FirebaseAnalytics.instance
            .logEvent(name: 'api_error', parameters: {
          'error_code': response.errorCode ?? 'no errorCode',
          'end_point': 'RegisterOrganization',
        });
      }
      if (response.isLogoutError) logoutErrorCode.value = response.errorCode;
      throw ApiException(response.errorCode!);
    }
    return response;
  }

  Future<bool> registrationEmailExists(String email) async {
    if (email.isEmpty) return false;
    Uri uri;
    if (production) {
      uri = Uri.https(
          baseAddress, '${_basePath}RegistrationEmailExists', {'email': email});
    } else {
      uri = Uri.http(
          baseAddress, '${_basePath}RegistrationEmailExists', {'email': email});
    }
    final response = await http.get(uri);
    return response.body == 'true';
  }

  Future<CompleteRegistrationResponse> completeRegistration(
      {required String registrationKey,
      required String captchaResponse,
      required String timeZone}) async {
    final request = CompleteRegistrationRequest(
        registrationKey: registrationKey,
        captchaResponse: captchaResponse,
        timeZone: timeZone);
    final map = await post('CompleteRegistration', request);
    if (map == null) throw ApiException('INTERNAL_ERROR');
    final response = CompleteRegistrationResponse.fromJson(map);
    if (response.isError) {
      if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) {
        await FirebaseAnalytics.instance
            .logEvent(name: 'api_error', parameters: {
          'error_code': response.errorCode ?? 'no errorCode',
          'end_point': 'CompleteRegistration',
        });
      }
      if (response.isLogoutError) logoutErrorCode.value = response.errorCode;
      throw ApiException(response.errorCode!);
    }
    return response;
  }

  Future<String?> getMinimumVersion() async {
    // Adjust to match your controller’s route name
    final map = await get('GetMinimumVersion'); 
    if (map == null) throw ApiException('INTERNAL_ERROR');

    // The server returns { "MinimumVersion": "1.1.2+" }
    return map['MinimumVersion']?.toString();
  }

  Future<bool> createAppTerminationAlert({
    required String punchCardId,
    required String userId,
    DateTime? alertOn,
  }) async {
    // 1) Build the request object:
    final request = await CreateAppTerminationAlertRequest.create(
      punchCardId: punchCardId,
      userId: userId,
      alertOn: alertOn,
    );

    // 2) Call the endpoint
    final endpoint = 'CreateAppTerminationAlert'; 
    final responseMap = await post(endpoint, request.toJson());

    if (responseMap == null) {
      throw ApiException('INTERNAL_ERROR');
    }

    // 3) Parse into typed response
    final response = CreateAppTerminationAlertResponse.fromJson(responseMap);

    // Check for error
    if (response.isError) {
      throw ApiException(response.errorCode ?? 'INTERNAL_ERROR');
    }

    // If success == true && errorCode == "0", we’re good
    return response.success;
  }


  Future<List<Schedule>> getSchedulesForUser(String targetUserId) async {
    // 1) Build the request object
    final request = await GetSchedulesForUserRequest.create(targetUserId);

    // 2) Convert request to JSON and post
    final map = await post('GetSchedulesForUser', request.toJson());

    // 3) Check for errors
    if (map == null) {
      throw ApiException('INTERNAL_ERROR');
    }
    final errorCode = map['ErrorCode']?.toString() ?? '';
    if (errorCode != '0') {
      throw ApiException(errorCode);
    }

    // 4) Parse the response
    final response = GetSchedulesForUserResponse.fromJson(map);

    // Because schedules in the .NET code might come back as an array
    // of objects, you can do:
    final rawList = response.schedules ?? [];
    final schedules = rawList.map((json) => Schedule.fromJson(json)).toList();

    // Return the typed list
    return schedules;
  }



  Future<SessionCheckResponse?> checkSession() async {
    const endPoint = 'CheckSession';
    final systemReq = await SystemRequest.create(); // fill serverIP, sessionID, etc.
    final body = jsonEncode(systemReq);

    final uri = production
        ? Uri.https(baseAddress, _basePath + endPoint)
        : Uri.http(baseAddress, _basePath + endPoint);

    final response = await http.post(uri,
        headers: {'Content-Type': 'application/json'}, body: body);

    if (response.statusCode != 200) {
      debugPrint('CheckSession returned status code: ${response.statusCode}');
      return null;
    }

    try {
      final map = jsonDecode(response.body);
      final errorCode = map['ErrorCode']?.toString();
      final errorMessage = map['ErrorMessage']?.toString();
      return SessionCheckResponse(errorCode: errorCode, errorMessage: errorMessage);
    } catch (e) {
      debugPrint('Failed to parse JSON in checkSession response: $e');
      return null;
    }
  }


  Future<LoginResponse> login(String organizationId, String username,
      String password, String deviceId) async {
    final request = LoginRequest(
        organizationId: organizationId,
        username: username,
        password: password,
        deviceId: deviceId);
    final map = await post('Login', request);
    if (map == null) throw ApiException('INTERNAL_ERROR');
    final systemResponse = SystemResponse.fromJson(map);
    if (systemResponse.isError) {
      if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) {
        await FirebaseAnalytics.instance
            .logEvent(name: 'api_error', parameters: {
          'error_code': systemResponse.errorCode ?? 'no errorCode',
          'end_point': 'Login',
        });
      }
      if (systemResponse.isLogoutError)
        logoutErrorCode.value = systemResponse.errorCode;
      throw ApiException(systemResponse.errorCode!);
    }
    return LoginResponse.fromJson(map);
  }

  Future<void> logout(SystemRequest request) async {
    await post('Logout', request);
    // final map = await post('Logout', request);
    // if (map == null) throw ApiException('INTERNAL_ERROR');
    // var response = SystemResponse.fromJson(map);
    // if (response.isError) {
    //   errorCode.value = response.errorCode;
    //   throw ApiException(response.errorCode!);
    // }
  }

  Future<SystemResponse> forgotLogin(
      {required String organizationId,
      required String email,
      required String captchaResponse}) async {
    final request = ForgotLoginRequest(
        organizationId: organizationId,
        email: email,
        captchaResponse: captchaResponse);
    final map = await post('ForgotLogin', request);
    if (map == null) throw ApiException('INTERNAL_ERROR');
    final response = SystemResponse.fromJson(map);
    if (response.isError) {
      if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) {
        await FirebaseAnalytics.instance
            .logEvent(name: 'api_error', parameters: {
          'error_code': response.errorCode ?? 'no errorCode',
          'end_point': 'ForgotLogin',
        });
      }
      if (response.isLogoutError) logoutErrorCode.value = response.errorCode;
      throw ApiException(response.errorCode!);
    }
    return response;
  }

  Future<SystemResponse> forgotLoginConfirmation(
      {required String organizationId,
      required String password,
      required String confirmPassword,
      required String confirmationKey}) async {
    final request = ForgotLoginConfirmationRequest(
        organizationId: organizationId,
        password: password,
        confirmPassword: confirmPassword,
        confirmationKey: confirmationKey);
    final map = await post('ForgotLoginConfirmation', request);
    if (map == null) throw ApiException('INTERNAL_ERROR');
    final response = SystemResponse.fromJson(map);
    if (response.isError) {
      if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) {
        await FirebaseAnalytics.instance
            .logEvent(name: 'api_error', parameters: {
          'error_code': response.errorCode ?? 'no errorCode',
          'end_point': 'ForgotLoginConfirmation',
        });
      }
      if (response.isLogoutError) logoutErrorCode.value = response.errorCode;
      throw ApiException(response.errorCode!);
    }
    return response;
  }

  Future<DateTime> getServerTime() async {
    final map = await get('ServerTime');
    return nullableDateTimeFromJson(map!['ServerTime'])!;
  }

  // Future<GetChangesResponse> getAllData(List<String> tableNames) async {
  //   final request = await GetChangesRequest.create(null, tableNames);
  //   final map = await post('FetchFullData', request);

  //   if (map == null) throw ApiException('INTERNAL_ERROR');

  //   final response = GetChangesResponse.fromJson(map);

  //   if (response.isError) {
  //     if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) {
  //       await FirebaseAnalytics.instance.logEvent(name: 'api_error', parameters: {
  //         'error_code': response.errorCode ?? 'no errorCode',
  //         'end_point': 'FetchFullData',
  //       });
  //     }
  //     if (response.isLogoutError) logoutErrorCode.value = response.errorCode;
  //     throw ApiException(response.errorCode!);
  //   }

  //   serverTime.value = response.serverTime;
  //   return response;
  // }

  // Future<GetChangesResponse> getRecentData(List<String> tableNames, DateTime cutoffDate) async {
  //   final request = await GetChangesRequest.create(cutoffDate, tableNames);
  //   final map = await post('FetchRecentData', request);

  //   if (map == null) throw ApiException('INTERNAL_ERROR');

  //   final response = GetChangesResponse.fromJson(map);

  //   if (response.isError) {
  //     if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) {
  //       await FirebaseAnalytics.instance.logEvent(name: 'api_error', parameters: {
  //         'error_code': response.errorCode ?? 'no errorCode',
  //         'end_point': 'FetchRecentData',
  //       });
  //     }
  //     if (response.isLogoutError) logoutErrorCode.value = response.errorCode;
  //     throw ApiException(response.errorCode!);
  //   }

  //   serverTime.value = response.serverTime;
  //   return response;
  // }

// In your ApiModel class:

Future<bool> deleteChatGroup(String groupId) async {
  try {
    // 1) Build the request
    final request = await DeleteChatGroupRequest.create(groupId);

    // 2) Post to your .NET endpoint "DeleteChatGroup"
    final map = await post("DeleteChatGroup", request.toJson());

    // 3) Check for null
    if (map == null) {
      throw ApiException("INTERNAL_ERROR");
    }

    // 4) Parse into typed response
    final response = DeleteChatGroupResponse.fromJson(map);
    if (response.isError) {
      // The server returned an error code
      throw ApiException(response.errorCode);
    }

    // If no errors, return success
    return true;
  } catch (e) {
    print("Failed to delete chat group: $e");
    rethrow; // or return false
  }
}


  Future<bool> createChatGroup(MessageGroup group, List<String> userIds) async {
    try {
      // 1) Build the request (fills in system fields automatically)
      final request = await CreateChatGroupRequest.create(group, userIds);

      // 2) POST to your .NET endpoint: "CreateChatGroup"
      //    (Match the endpoint name on your server.)
      final map = await post('CreateChatGroup', request.toJson());
      if (map == null) throw ApiException('INTERNAL_ERROR');

      print('CreateChatGroup raw response: $map');

      // 3) Parse the response
      final response = CreateChatGroupResponse.fromJson(map);
      if (response.isError) {
        throw ApiException(response.errorMessage);
      }

      // We can interpret "success" from errorCode == "0"
      return !response.isError;
      } catch (e, stackTrace) {
        print('Failed to create group on server: $e');
        print('Stack trace: $stackTrace');
        rethrow;
        // Or use debugPrint if you prefer
        // debugPrint('Failed to create group on server: $e');
        // debugPrint('Stack trace: $stackTrace');
      }

  }

  Future<bool> createPunchCard(PunchCard punchCard) async {
    try {
      // 1) Build the request
      final request = await CreatePunchCardRequest.create(punchCard);

      // 2) POST to your .NET endpoint: "CreatePunchCard"
      final map = await post('CreatePunchCard', request.toJson());
      if (map == null) throw ApiException('INTERNAL_ERROR');

      // 3) Parse the response
      final response = CreatePunchCardResponse.fromJson(map);
      if (response.isError) {
        throw ApiException(response.errorCode ?? 'UNKNOWN_ERROR');
      }

      debugPrint('Punch card created successfully.');

      // 4) On success, clear any stashed pings:
      await _storageManager.deleteData('travel_ping');
      await _storageManager.deleteData('geo_fence');
      debugPrint('Cleared all stashed pings in storage.');

      // Return success
      return response.success;
    } catch (e) {
      rethrow;
    }
  }

  Future<PostPunchCardHistoryResponse> postPunchCardHistory(PunchCard punchCard) async {
    try {
      // Print out every value of the punchCard object for debugging
      print('PunchCard ID: ${punchCard.id}');
      print('User ID: ${punchCard.userId}');
      print('Location ID: ${punchCard.locationId}');
      print('JobType ID: ${punchCard.jobTypeId}');
      print('Clocked In: ${punchCard.clockedIn}');
      print('Clocked Out: ${punchCard.clockedOut}');
      print('Duration: ${punchCard.duration}');
      
      // Ensure all required fields are filled in
      if (punchCard.id.isEmpty || punchCard.userId.isEmpty || punchCard.jobTypeId.isEmpty) {
        throw Exception('Required PunchCard fields are missing!');
      }

      // Create the request
      final request = await PostPunchCardHistoryRequest.create(punchCard);

      // Log request details
      print('Request JSON: ${request.toJson()}');

      // Send the request
      final map = await post('PostPunchCardHistory', request.toJson());

      // Check if the response is null
      if (map == null) {
        throw ApiException('INTERNAL_ERROR');
      }

      // Parse the response
      return PostPunchCardHistoryResponse.fromJson(map);
    } catch (e) {
      print('Error while sending punch card history: $e');
      rethrow;
    }
  }

Future<FetchMessagesResponse> fetchMessages(String targetUserId) async {
  // Create the request using the targetUserId
  print(targetUserId.toUpperCase());
  final request = await FetchMessagesRequest.create(targetUserId.toUpperCase());

  // Send the request to the API
  final map = await post('FetchMessages', request.toJson());

  // **Print the raw response map here**
  print('FetchMessages raw response: $map');

  // Check for any errors and throw an exception if necessary
  if (map == null) {
    throw ApiException('INTERNAL_ERROR');
  }

  // Parse the response into FetchMessagesResponse
  final response = FetchMessagesResponse.fromJson(map);

  // Optional: Print the parsed response as well
  print('Parsed FetchMessagesResponse: $response');

  // Return the response parsed into FetchMessagesResponse
  return response;
}



  Future<FetchNotificationsResponse> fetchNotifications(String targetUserId) async {
    // Create the request using the targetUserId
    final request = await FetchNotificationsRequest.create(targetUserId);

    // Send the request to the API
    final map = await post('FetchNotifications', request.toJson());

    // Check for any errors and throw an exception if necessary
    if (map == null) {
      throw ApiException('INTERNAL_ERROR');
    }

    // Return the response parsed into FetchNotificationsResponse
    return FetchNotificationsResponse.fromJson(map);
  }

Future<String> buildAssetImage(String guid) async {
  final request = await BuildAssetImageRequest.create(guid);
  final body = jsonEncode(request.toJson());

  // Was: Uri.https(baseAddress, 'api/Assets/BuildAssetImage')
  // Now:  Uri.https(baseAddress, 'api/thepunch/BuildAssetImage')
  final endPoint = 'api/thepunch/BuildAssetImage';
  final uri = production
    ? Uri.https(baseAddress, endPoint)
    : Uri.http(baseAddress, endPoint);

  final resp = await http.post(uri, body: body, headers: {
    'Content-Type': 'application/json',
  });

  if (resp.statusCode == 200) {
    final map = jsonDecode(resp.body);
    if (map['ErrorCode'] == '0') {
      return map['ImageUrl']?.toString() ?? '';
    } else {
      throw Exception('BuildAssetImage error: ${map['ErrorCode']}');
    }
  } else {
    throw Exception('HTTP ${resp.statusCode}: ${resp.body}');
  }
}

// 2) uploadAsset => POST /api/thepunch/PostAssets
Future<String> uploadAsset(File file, String guid) async {
  final systemReq = await SystemRequest.create();
  final systemReqJson = jsonEncode(systemReq);

  // Was: 'api/Assets/PostAssets'
  final endPoint = 'api/thepunch/PostAssets';
  final uri = production
    ? Uri.https(baseAddress, endPoint)
    : Uri.http(baseAddress, endPoint);

  final req = http.MultipartRequest('POST', uri);

  // 1) "SystemRequest" as JSON
  final systemReqBytes = utf8.encode(systemReqJson);
  final systemReqFile = http.MultipartFile.fromBytes(
    'SystemRequest',
    systemReqBytes,
    filename: 'request.json',
    contentType: MediaType('application', 'json'),
  );
  req.files.add(systemReqFile);

  // 2) The actual file, named after the GUID
  final fileName = file.uri.pathSegments.last;
  final mimeType = lookupMimeType(fileName) ?? 'application/octet-stream';
  final filePart = await http.MultipartFile.fromPath(
    guid,
    file.path,
    filename: fileName,
    contentType: MediaType.parse(mimeType),
  );
  req.files.add(filePart);

  // 3) Send
  final streamedResp = await req.send();
  final resp = await http.Response.fromStream(streamedResp);

  if (resp.statusCode == 200) {
    final jsonBody = jsonDecode(resp.body);
    if (jsonBody['ErrorCode'] == '0') {
      return guid;
    } else {
      throw ApiException('Asset upload error: ${jsonBody['ErrorCode']}');
    }
  } else {
    throw ApiException('HTTP ${resp.statusCode} => ${resp.body}');
  }
}


  // Future<bool> removeChatGroupMembers(String groupId, List<String> userIds) async {
  //   final req = await RemoveGroupMembersRequest.create(groupId, userIds);
  //   final map = await post('RemoveGroupMembers', req.toJson());

  //   if (map == null) {
  //     throw ApiException('INTERNAL_ERROR');
  //   }
  //   final errorCode = map['ErrorCode']?.toString() ?? '';
  //   return (errorCode == '0');
  // }

  Future<void> editChatGroupMembers(
    String groupId,
    List<String> addUserIds,
    List<String> removeUserIds,
  ) async {
      final request = await EditGroupMembersRequest.create(
        groupId: groupId,
        addUserIds: addUserIds,
        removeUserIds: removeUserIds,
      );
      final map = await post('EditGroupMembers', request.toJson());

      if (map == null) {
        throw ApiException('INTERNAL_ERROR');
      }
      final errorCode = map['ErrorCode']?.toString() ?? '';
      if (errorCode != '0') {
        // handle error, e.g.
        throw ApiException('SERVER_ERROR_$errorCode');
      }
    }  

  Future<FetchPunchCardHistoryResponse> fetchPunchCardHistory(String punchCardId) async {
    // Create the request using the punchCardId
    final request = await FetchPunchCardHistoryRequest.create(punchCardId);

    // Send the request to the API
    final map = await post('FetchPunchCardHistory', request.toJson());

    // Check for any errors and throw exception if necessary
    if (map == null) {
      throw ApiException('INTERNAL_ERROR');
    }

    // Return the response parsed into FetchPunchCardHistoryResponse
    return FetchPunchCardHistoryResponse.fromJson(map);
  }

  Future<Map<String, dynamic>> fetchGeoCodeData({
    required String address1,
    required String city,
    required String state,
    required String zip,
  }) async {
    final request = {
      'Address1': address1,
      'City': city,
      'State': state,
      'Zip': zip,
    };
    final map = await post('FetchGeoCodeData', request);
    if (map == null) throw ApiException('INTERNAL_ERROR');
    return map;
  }

  Future<FetchTravelPingsResponse> fetchTravelPings(String punchCardId) async {
    final request = await FetchTravelPingsRequest.create(punchCardId);
    final map = await post('FetchTravelPings', request.toJson());
    if (map == null) {
      throw ApiException('INTERNAL_ERROR');
    }

    return FetchTravelPingsResponse.fromJson(map);
  }

  final StorageManager _storageManager = StorageManager();

  Future<SystemResponse?> sendGeoFencePing(GeoLocation location) async {
    var connectivityResult = await (Connectivity().checkConnectivity());

    if (connectivityResult == ConnectivityResult.none) {
      //await _stashGeoFencePing(location);
      print('Queued geo location due to no internet connection.');
      return null; // Returning null to indicate failure due to no connection
    }

    try {
      // // First, attempt to send any stashed geo locations
      //await sendStashedGeoFencePings();

      // Send the current geo location
      final request = await SendGeoFencePingRequest.create(location);
      final map = await post('SendGeoFencePing', request.toJson());
      if (map == null) {
        //await _stashGeoFencePing(location);
        print('Error: Server returned null response.');
        return null;
      }
      return SendGeoFencePingResponse.fromJson(map);
    } catch (e) {
      //await _stashGeoFencePing(location);
      print('An unexpected error occurred: $e');
      return null;
    }
  }

  Future<void> _stashGeoFencePing(GeoLocation location) async {
    var stashedLocations = await _storageManager.readData('geo_fence');
    stashedLocations.add(location.toJson());
    await _storageManager.writeData('geo_fence', stashedLocations);
  }

  Future<void> sendStashedGeoFencePings() async {
    var stashedLocations = await _storageManager.readData('geo_fence');
    var connectivityResult = await (Connectivity().checkConnectivity());
    if (connectivityResult == ConnectivityResult.none) {
      print('Cannot send stashed geo locations: No internet connection.');
      return;
    }

    List<Map<String, dynamic>> successfullySentLocations = [];
    for (var locationData in stashedLocations) {
      try {
        GeoLocation location = GeoLocation.fromJson(locationData);
        final request = await SendGeoFencePingRequest.create(location);
        final map = await post('SendGeoFencePing', request.toJson());
        if (map != null) {
          successfullySentLocations.add(locationData);
          print('MyAppDebug:Geo location sent successfully.');
        }
      } catch (e) {
        print('MyAppDebug:Failed to send geo location due to an error: $e');
        continue;
      }
    }

    // Update stashed geo locations with unsent locations only
    var unsentLocations = stashedLocations
        .where(
            (locationData) => !successfullySentLocations.contains(locationData))
        .toList();
    if (unsentLocations.isEmpty) {
      await _storageManager.deleteData('geo_fence');
    } else {
      await _storageManager.writeData('geo_fence', unsentLocations);
    }
  }

  Future<SystemResponse?> sendTravelPing(TravelPing ping) async {
    var connectivityResult = await (Connectivity().checkConnectivity());

    if (connectivityResult == ConnectivityResult.none) {
      //await _stashTravelPing(ping);
      print('MyAppDebug:Queued ping due to no internet connection.');
      return null; // Returning null to indicate failure due to no connection
    }

    try {
      // First, attempt to send any stashed pings
      //await sendStashedTravelPings();

      // Send the current ping
      final request = await SendTravelPingRequest.create(ping);
      final map = await post('SendTravelPing', request.toJson());

      if (map == null) {
        // await _stashTravelPing(ping);
        print('MyAppDebug:Error: Server returned null response.');
        return null;
      }

      final response = SendTravelPingResponse.fromJson(map!);
      if (response.success) {
        print('MyAppDebug:Ping sent successfully.');
        return response;
      } else {
        // await _stashTravelPing(ping);
        print('MyAppDebug:Error: Server returned error response.');
        return null;
      }
    } catch (e) {
      // await _stashTravelPing(ping);
      print('MyAppDebug:An unexpected error occurred: $e');
      return null;
    }
  }

  Future<void> _stashTravelPing(TravelPing ping) async {
    var stashedPings = await _storageManager.readData('travel_ping');
    stashedPings.add(ping.toJson());
    await _storageManager.writeData('travel_ping', stashedPings);
  }

  Future<void> sendStashedTravelPings() async {
    var stashedPings = await _storageManager.readData('travel_ping');
    var connectivityResult = await (Connectivity().checkConnectivity());
    if (connectivityResult == ConnectivityResult.none) {
      print('MyAppDebug:Cannot send stashed pings: No internet connection.');
      return;
    }

    List<Map<String, dynamic>> successfullySentPings = [];
    for (var pingData in stashedPings) {
      try {
        TravelPing ping = TravelPing.fromJson(pingData);
        final request = await SendTravelPingRequest.create(ping);
        final map = await post('SendTravelPing', request.toJson());
        if (map != null) {
          final response = SendTravelPingResponse.fromJson(map);
          if (response.success) {
            successfullySentPings.add(pingData);
            print('MyAppDebug:Ping sent successfully.');
          }
        }
      } catch (e) {
        print('Failed to send ping due to an error: $e');
        continue;
      }
    }

    // Update stashed pings with unsent pings only
    var unsentPings = stashedPings
        .where((pingData) => !successfullySentPings.contains(pingData))
        .toList();
    if (unsentPings.isEmpty) {
      await _storageManager.deleteData('travel_ping');
    } else {
      await _storageManager.writeData('travel_ping', unsentPings);
    }
  }

  Future<GetTravelDataResponse> fetchTravelData({
    required String originLocationId,
    required String destinationLocationId,
  }) async {
    final request = await GetTravelDataRequest.create(
      originLocationId: originLocationId,
      destinationLocationId: destinationLocationId,
    );
    final map = await post('FetchTravelData', request.toJson());
    if (map == null) {
      throw ApiException('INTERNAL_ERROR');
    }

    return GetTravelDataResponse.fromJson(map);
  }

  Future<FetchTravelTimeResponse> fetchTravelTime({
    required String originLocationId,
    required String destinationLocationId,
  }) async {
    final request = await FetchTravelTimeRequest.create(
      originLocationId: originLocationId,
      destinationLocationId: destinationLocationId,
    );
    final map = await post('FetchTravelTime', request.toJson());
    if (map == null) throw ApiException('INTERNAL_ERROR');
    return FetchTravelTimeResponse.fromJson(map);
  }

  Future<List<Schedule>> getSchedulesForMonth(int month, int year) async {
    final request = await GetSchedulesForMonthRequest.create(month, year);

    final response = await post('GetSchedulesForMonth', request.toJson());
    if (response == null) {
      throw ApiException('INTERNAL_ERROR');
    }
    final schedulesResponse = GetSchedulesForMonthResponse.fromJson(response);
    if (schedulesResponse.isError) {
      throw ApiException(schedulesResponse.errorCode!);
    }
    return schedulesResponse.schedules;
  }

  Future<GetConditionalChangesResponse> getConditionalChanges(
      int month, int year, DateTime? changedAfterForOthers) async {
    print(
        'Starting getConditionalChanges with month: $month, year: $year, changedAfterForOthers: $changedAfterForOthers');
    final request =
        await GetConditionalChangesRequest.create(changedAfterForOthers);
    request.month = month;
    request.year = year;

    print('Created request: $request');

    try {
      final map = await post('GetConditionalChanges', request);

      if (map == null) {
        print('Error: API returned null');
        throw ApiException('INTERNAL_ERROR');
      }

      print('STARTING: Get Conditional Changes Response');
      final response = GetConditionalChangesResponse.fromJson(map);
      print('Parsed GetConditionalChangesResponse: $response');

      if (response.isError) {
        print('API returned error: ${response.errorCode}');
        if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) {
          await FirebaseAnalytics.instance
              .logEvent(name: 'api_error', parameters: {
            'error_code': response.errorCode ?? 'no errorCode',
            'end_point': 'GetConditionalChanges',
          });
          print('Logged error to Firebase Analytics');
        }
        if (response.isLogoutError) {
          logoutErrorCode.value = response.errorCode;
          print('Logout error code set: ${response.errorCode}');
        }
        throw ApiException(response.errorCode!);
      }

      serverTime.value = response.serverTime;
      print('Updated server time: ${response.serverTime}');
      print('Successfully fetched conditional changes');
      return response;
    } catch (e, stackTrace) {
      print('Error fetching conditional changes: $e');
      print('StackTrace: $stackTrace');
      rethrow;
    }
  }

  Future<GetChangesResponse> getChanges(DateTime? changedAfter) async {
    print('Starting getChanges with changedAfter: $changedAfter');
    final request = await GetChangesRequest.create(changedAfter);
    print('Created request: $request');

    try {
      final map = await post('GetChanges', request);
      // print('API Response: $map');

      if (map == null) {
        print('Error: API returned null');
        throw ApiException('INTERNAL_ERROR');
      }

      print('STARTING: Get Changes Response');
      final response = GetChangesResponse.fromJson(map);
      print('Parsed GetChangesResponse: $response');

      if (response.isError) {
        print('API returned error: ${response.errorCode}');
        if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) {
          await FirebaseAnalytics.instance
              .logEvent(name: 'api_error', parameters: {
            'error_code': response.errorCode ?? 'no errorCode',
            'end_point': 'GetChanges',
          });
          print('Logged error to Firebase Analytics');
        }
        if (response.isLogoutError) {
          logoutErrorCode.value = response.errorCode;
          print('Logout error code set: ${response.errorCode}');
        }
        throw ApiException(response.errorCode!);
      }

      serverTime.value = response.serverTime;
      print('Updated server time: ${response.serverTime}');

      print('Successfully fetched changes');
      return response;
    } catch (e, stackTrace) {
      print('Error fetching changes: $e');
      print('StackTrace: $stackTrace');
      rethrow;
    }
  }

  Future<SystemResponse> postChanges({
    Iterable<Alert>? alerts,
    Iterable<LocationContact>? locationContacts,
    Iterable<LocationNote>? locationNotes,
    Iterable<GeoLocation>? geoLocations,
    Iterable<Inspection>? inspections,
    Iterable<InspectionArea>? inspectionAreas,
    Iterable<InspectionItem>? inspectionItems,
    Iterable<InspectionImage>? inspectionImages,
    Iterable<InspectionTemplate>? inspectionTemplates,
    Iterable<InspectionTemplateArea>? inspectionTemplateAreas,
    Iterable<InspectionTemplateItem>? inspectionTemplateItems,
    Iterable<JobType>? jobTypes,
    Iterable<Location>? locations,
    Iterable<Note>? notes,
    Iterable<Message>? messages,
    Iterable<PunchCard>? punchCards,
    Iterable<Schedule>? schedules,
    Iterable<User>? users,
    Iterable<UserType>? userTypes,
    Iterable<UserTypePermission>? userTypePermissions,
  }) async {
    try {
      final request = await PostChangesRequest.create(
        alerts: alerts,
        locationContacts: locationContacts,
        locationNotes: locationNotes,
        geoLocations: geoLocations,
        inspections: inspections,
        inspectionAreas: inspectionAreas,
        inspectionItems: inspectionItems,
        inspectionImages: inspectionImages,
        inspectionTemplates: inspectionTemplates,
        inspectionTemplateAreas: inspectionTemplateAreas,
        inspectionTemplateItems: inspectionTemplateItems,
        jobTypes: jobTypes,
        locations: locations,
        notes: notes,
        messages: messages,
        punchCards: punchCards,
        schedules: schedules,
        users: users,
        userTypes: userTypes,
        userTypePermissions: userTypePermissions,
      );
      final map = await post('PostChanges', request);
      if (map == null) throw ApiException('INTERNAL_ERROR');
      final response = SystemResponse.fromJson(map);
      if (response.isError) {
        if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) {
          await FirebaseAnalytics.instance
              .logEvent(name: 'api_error', parameters: {
            'error_code': response.errorCode ?? 'no errorCode',
            'end_point': 'PostChanges',
          });
        }
        if (response.isLogoutError) logoutErrorCode.value = response.errorCode;
        throw ApiException(response.errorCode!);
      }
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> addScheduleTemplate(ScheduleTemplate scheduleTemplate) async {
    final request = await AddScheduleTemplateRequest.create(scheduleTemplate);
    final map = await post('AddScheduleTemplate', request);
    if (map == null) throw ApiException('INTERNAL_ERROR');
    final response = SystemResponse.fromJson(map);
    if (response.isError) {
      if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) {
        await FirebaseAnalytics.instance
            .logEvent(name: 'api_error', parameters: {
          'error_code': response.errorCode ?? 'no errorCode',
          'end_point': 'AddScheduleTemplate',
        });
      }
      if (response.isLogoutError) logoutErrorCode.value = response.errorCode;
      throw ApiException(response.errorCode!);
    }
    serverTime.value = response.serverTime;
  }

  Future<void> deleteScheduleTemplate(String scheduleTemplateId) async {
    // Construct whatever request your server expects. 
    // Usually, you'll want a "DeleteScheduleTemplateRequest" with the ID plus
    // the standard fields like Request_ServerIP, Request_DatabaseName, Request_UserId, etc.
    final request = await DeleteScheduleTemplateRequest.create(scheduleTemplateId);

    // Post to the backend endpoint
    final map = await post('DeleteScheduleTemplate', request);

    // If the server returned null, treat as an error
    if (map == null) throw ApiException('INTERNAL_ERROR');

    // Parse the response as a SystemResponse
    final response = SystemResponse.fromJson(map);
    if (response.isError) {
      // Log analytics if desired
      if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) {
        await FirebaseAnalytics.instance
            .logEvent(name: 'api_error', parameters: {
          'error_code': response.errorCode ?? 'no errorCode',
          'end_point': 'DeleteScheduleTemplate',
        });
      }
      // If it's a logout-level error, set that
      if (response.isLogoutError) logoutErrorCode.value = response.errorCode;
      throw ApiException(response.errorCode!);
    } 
  }

  Future<void> editScheduleTemplate(ScheduleTemplate scheduleTemplate) async {
    final request = await EditScheduleTemplateRequest.create(scheduleTemplate);
    final map = await post('EditScheduleTemplate', request);
    if (map == null) throw ApiException('INTERNAL_ERROR');
    final response = SystemResponse.fromJson(map);
    if (response.isError) {
      if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) {
        await FirebaseAnalytics.instance
            .logEvent(name: 'api_error', parameters: {
          'error_code': response.errorCode ?? 'no errorCode',
          'end_point': 'EditScheduleTemplate',
        });
      }
      if (response.isLogoutError) logoutErrorCode.value = response.errorCode;
      throw ApiException(response.errorCode!);
    }
    serverTime.value = response.serverTime;
  }

  Future<SystemResponse> updatePassword(
      String oldPassword, String password, String confirmPassword) async {
    final request = await UpdatePasswordRequest.create(
        oldPassword, password, confirmPassword);
    final map = await post('UpdatePassword', request);
    if (map == null) throw ApiException('INTERNAL_ERROR');
    final response = SystemResponse.fromJson(map);
    if (response.isError) {
      if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) {
        await FirebaseAnalytics.instance
            .logEvent(name: 'api_error', parameters: {
          'error_code': response.errorCode ?? 'no errorCode',
          'end_point': 'UpdatePassword',
        });
      }
      if (response.isLogoutError) if (response.isLogoutError)
        logoutErrorCode.value = response.errorCode;
      throw ApiException(response.errorCode!);
    }
    serverTime.value = response.serverTime;
    return response;
  }
}

class ApiException implements Exception {
  String errorCode;
  ApiException(this.errorCode);
  @override
  String toString() => errorCode;
}
