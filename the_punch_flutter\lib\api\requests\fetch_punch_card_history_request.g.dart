// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fetch_punch_card_history_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FetchPunchCardHistoryRequest _$FetchPunchCardHistoryRequestFromJson(
        Map<String, dynamic> json) =>
    FetchPunchCardHistoryRequest(
      punchCardId: json['PunchCardId'] as String,
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$FetchPunchCardHistoryRequestToJson(
        FetchPunchCardHistoryRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'PunchCardId': instance.punchCardId,
    };

FetchPunchCardHistoryResponse _$FetchPunchCardHistoryResponseFromJson(
        Map<String, dynamic> json) =>
    FetchPunchCardHistoryResponse(
      punchCardHistory: (json['PunchCardHistory'] as List<dynamic>)
          .map((e) => PunchCardsHistory.fromJson(e as Map<String, dynamic>))
          .toList(),
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$FetchPunchCardHistoryResponseToJson(
    FetchPunchCardHistoryResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['PunchCardHistory'] = instance.punchCardHistory;
  return val;
}
