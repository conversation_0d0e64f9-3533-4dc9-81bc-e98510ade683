import '../base_data.dart';
import '../data/permission.dart';
import '../hive_db.dart';

class UserPermissionModel extends BaseDataModel<Permission> {
  @override
  Future<Iterable<Permission>> get all async => (await HiveDb.database).userPermissions.values;

  @override
  Future<void> save(Iterable<Permission> t) async {
    await (await HiveDb.database).userPermissions.putAll({for (final e in t) e.id: e});
    notifyListeners();
  }
}
