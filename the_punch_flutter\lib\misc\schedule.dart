import 'package:flutter/material.dart';

import 'booking_card.dart';


class ScheduleScreen extends StatelessWidget {
  final List<ScheduleSlot> slots;

  ScheduleScreen({super.key})
      : slots = [
          ScheduleSlot(time: '9:00 am', bookings: [
            Booking(title: '4LeafCleaners', timeRange: '9:05 - 10:25', duration: '1hr 20m'),
          ]),
          ScheduleSlot(time: '9:30 am', bookings: [
            Booking(title: '4LeafCleaners', timeRange: '9:05 - 10:25', duration: '1hr 20m'),
            Booking(title: '4LeafCleaners', timeRange: '9:05 - 10:25', duration: '1hr 20m'),
          ]),
          ScheduleSlot(time: '11:30 am', bookings: [
            Booking(title: '4LeafCleaners', timeRange: '9:05 - 10:25', duration: '1hr 20m'),
          ]),
        ];

@override
Widget build(BuildContext context) => Container(
      decoration: const BoxDecoration(
        // gradient: LinearGradient(
        //   begin: Alignment.topCenter,
        //   end: Alignment.bottomCenter,
        //   colors: [
        //     Color(0xFFF2F2F2), // Very light gray
        //     Colors.white,       // White
        //   ],
        // ),
      ),
      child: ListView.builder(
        itemCount: slots.length,
        itemBuilder: (context, index) {
          final slot = slots[index];
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      slot.time,
                      style: TextStyle(fontSize: 16, color: Colors.grey[800], fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(width: 8), // Space between text and line
                    Expanded(
                      child: Divider(
                        color: Colors.grey[300], // Line color
                        thickness: 1,       // Line thickness
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Column(
                  children: slot.bookings.map((booking) => 
                    Align(
                      alignment: Alignment.centerRight, // Right-align
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width * 0.75, // 75% width
                        child: BookingCard(booking: booking),
                      ),
                    ),
                  ).toList(),
                ),
              ],
            ),
          );
        },
      ),
    );

}

class ScheduleSlot {
  final String time;
  final List<Booking> bookings;

  ScheduleSlot({required this.time, required this.bookings});
}