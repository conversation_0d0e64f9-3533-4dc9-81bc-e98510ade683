import 'package:flutter/widgets.dart';
import '../../misc/app_localization.dart';

class UserSession {
  static const loggedOutId = '3a2beef1-0a2b-48a7-a53d-616316646a99';
  static const timedOutId = '106363d0-52ba-409d-ba6a-98ffb73d2330';
  static const forcedOutId = 'f7430293-2c3f-40a6-b840-4cfb3b5f23fb';

  static String name(BuildContext context, String id) {
    switch (id) {
      case loggedOutId:
        return AppLocalization.of(context).loggedOut;
      case timedOutId:
        return AppLocalization.of(context).timedOut;
      case forcedOutId:
        return AppLocalization.of(context).forcedOut;
      default:
        return '';
    }
  }
}
