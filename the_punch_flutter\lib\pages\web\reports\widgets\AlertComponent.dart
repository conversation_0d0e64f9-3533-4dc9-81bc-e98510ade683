import 'package:flutter/material.dart';

class AlertComponent extends StatelessWidget {
  final String alertNumber;
  final String alertTitle;
  final String time;
  final String duration;
  final bool isReEntry;
  final String reEntryTime;
    final int alertLength;
  final int  index;
  final bool showRight;
  const AlertComponent({
    super.key,
    this.alertNumber = 'ALERT 1',
    this.alertTitle = 'Breached Geofence',
    this.time = '6:10pm',
    this.duration = '1m 5s',
    this.isReEntry = false, 
    required this.reEntryTime ,
    required this.alertLength,
    this.index = 0,
    this.showRight = true,
  });

  @override
  Widget build(BuildContext context) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
            // Left Alert Box
            Flexible(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 323),
              child: Container(
              height: 78,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                color: const Color(0x29C35B5B),
                width: 1.5,
                ),
              ),
              child: Row(
                children: [
                Expanded(
                  child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                    alertNumber,
                    style: const TextStyle(
                      fontFamily: 'Poppins',
                      fontSize: 11,
                      fontWeight: FontWeight.w700,
                      color: Color(0xFFC35B5B),
                    ),
                    ),
                    const SizedBox(height: 3),
                    Text(
                    alertTitle,
                    style: const TextStyle(
                      fontFamily: 'Poppins',
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      letterSpacing: -0.11,
                      color: Color(0xFFC35B5B),
                    ),
                    ),
                    Text(
                    '${time}',
                    style: const TextStyle(
                      fontFamily: 'Poppins',
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                      letterSpacing: -0.11,
                      color: Color(0xFFC35B5B),
                    ),
                    ),
                  ],
                  ),
                ),
                ],
              ),
              ),
            ),
            ),
          // Line 3
          if(showRight)
          Container(
            width: 18,
            margin: const EdgeInsets.symmetric(vertical: 39),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Color(0x29E44C61),
                  width: 1.5,
                ),
              ),
            ),
          ),
          
          // Duration Container
          if(showRight)
          Container(
            margin: const EdgeInsets.only(top: 0),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical:4),
            decoration: BoxDecoration(
              color: const Color(0x3DC35B5B),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              duration,
              style: const TextStyle(
                fontFamily: 'Poppins',
                fontSize: 11,
                fontWeight: FontWeight.w600,
                letterSpacing: -0.11,
                color: Color(0xFFC35B5B),
              ),
            ),
          ),
          
          // Line 4
          if(showRight)
          Container(
            width: 18,
            margin: const EdgeInsets.symmetric(vertical: 0),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Color(0x29E44C61),
                  width: 1.5,
                ),
              ),
            ),
          ),
          if(showRight)
          if(alertLength > 1)
          // Re-Entry Box
                Expanded(
                  child: Container(
                    height: 78,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: const Color(0x294BA2E7),
                        width: 1.5,
                      ),
                    ),
                    child: Row(
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Text(
                              'ALERT ',
                              style: TextStyle(
                                fontFamily: 'Poppins',
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                                letterSpacing: -0.11,
                                color: Color(0xFF4BA2E7),
                              ),
                            ),
                            const Text(
                              'Re-Entered Geofence',
                              style: TextStyle(
                                fontFamily: 'Poppins',
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                                letterSpacing: -0.11,
                                color: Color(0xFF4BA2E7),
                              ),
                            ),                        
                            Text(
                              reEntryTime,
                              style: const TextStyle(
                                fontFamily: 'Poppins',
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                                letterSpacing: -0.11,
                                color: Color(0xFF4BA2E7),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(width: 16),
            //            Image.network(
          //                'https://dashboard.codeparrot.ai/api/image/aCIcK4_8PH23qy0C/frame-42-2.png',
                        //   width: 14,
                        //   height: 14,
                        // ),
                      ],
                    ),
                  ),
                )
           else               Expanded(
                  child: Container(
                    height: 78,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: const Color(0x294BA2E7),
                        width: 1.5,
                      ),
                    ),
                    child: Row(
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Text(
                              'Alert ',
                              style: TextStyle(
                                fontFamily: 'Poppins',
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                                letterSpacing: -0.11,
                                color: Color(0xFF4BA2E7),
                              ),
                            ),
                            const Text(
                              'Punched Out',
                              style: TextStyle(
                                fontFamily: 'Poppins',
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                                letterSpacing: -0.11,
                                color: Color(0xFF4BA2E7),
                              ),
                            ),                        
                            Text(
                              reEntryTime,
                              style: const TextStyle(
                                fontFamily: 'Poppins',
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                                letterSpacing: -0.11,
                                color: Color(0xFF4BA2E7),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(width: 16),
            //            Image.network(
          //                'https://dashboard.codeparrot.ai/api/image/aCIcK4_8PH23qy0C/frame-42-2.png',
                        //   width: 14,
                        //   height: 14,
                        // ),
                      ],
                    ),
                  ),
                )
           
        
        ],
      ),
    );
}

