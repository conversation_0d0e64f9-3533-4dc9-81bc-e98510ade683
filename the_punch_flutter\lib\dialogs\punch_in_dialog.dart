import 'package:flutter/material.dart';
import 'package:the_punch_flutter/dataModel/data/location.dart';
import 'package:the_punch_flutter/dataModel/data/user_type.dart';
import 'package:the_punch_flutter/dataModel/data_model.dart';
import 'package:the_punch_flutter/dataModel/models/user_model.dart';
import 'package:the_punch_flutter/helpers/color_helper.dart';
import 'package:the_punch_flutter/misc/calculate_distance.dart';
import 'package:the_punch_flutter/state/location_state.dart';
import 'package:the_punch_flutter/state/login_state.dart';
import 'package:the_punch_flutter/widgets/location_tile.dart';

class _PunchInDialogContent extends StatefulWidget {
  final Function(BuildContext, bool, String, String?) onLocationSelected;

  const _PunchInDialogContent({required this.onLocationSelected});

  @override
  _PunchInDialogContentState createState() => _PunchInDialogContentState();
}

class _PunchInDialogContentState extends State<_PunchInDialogContent> {
  bool isLoading = true;
  bool isManagerOrAdmin = false;

  // For employees:
  List<Map<String, dynamic>> scheduledLocations = [];
  List<Location> nearbyLocations = [];
  Map<String, double> locationDistances = {};

  // For managers/admin:
  List<Location> managerNearbyLocations = [];
  Map<String, double> managerLocationDistances = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    await LocationState.updateCurrentLocation();
    final currentPosition = LocationState().currentPosition;
    final userId = LoginState.userId;
    final userTypeId = await UserModel().getCurrentUserTypeId(userId);

    setState(() {
      // Determine if user is manager or admin
      isManagerOrAdmin = (UserType.managerId.toUpperCase() == userTypeId?.toUpperCase()) ||
          (UserType.administratorId.toUpperCase() == userTypeId?.toUpperCase());
    });

    // If manager/admin, gather "detected locations" within ~2 miles
    if (currentPosition != null && isManagerOrAdmin) {
      final locations = (await DataModel().locationModel.active).toList();
      final distances = <String, double>{};
      for (final loc in locations) {
        final d = calculateDistance(
          currentPosition.latitude,
          currentPosition.longitude,
          loc.latitude,
          loc.longitude,
        );
        distances[loc.id] = d;
      }
      managerLocationDistances = distances;

      // Filter to within 2 miles
      final maxDistanceInMeters = 2.0 * 1609.34;
      final managerNearby = locations
          .where((loc) => distances[loc.id]! <= maxDistanceInMeters)
          .toList();
      managerNearby.sort((a, b) => distances[a.id]!.compareTo(distances[b.id]!));

      setState(() {
        managerNearbyLocations = managerNearby;
        isLoading = false;
      });
      return;
    }

    // Otherwise: employee logic
    if (currentPosition != null && !isManagerOrAdmin) {
      final locations = (await DataModel().locationModel.active).toList();
      final schedules = (await DataModel().scheduleModel.active).toList();

      final distances = <String, double>{};
      for (final loc in locations) {
        final meters = calculateDistance(
          currentPosition.latitude,
          currentPosition.longitude,
          loc.latitude,
          loc.longitude,
        );
        distances[loc.id] = meters;
      }
      locationDistances = distances;

      final maxDistanceInMiles = 2.0;
      final maxDistanceInMeters = maxDistanceInMiles * 1609.34;

      final today = DateTime.now();
      final scheduled = schedules
          .where((schedule) {
            final scheduleDate = schedule.startDateLocal;
            return scheduleDate.year == today.year &&
                scheduleDate.month == today.month &&
                scheduleDate.day == today.day;
          })
          .map((schedule) {
            final location = locations.firstWhere(
              (loc) => loc.id == schedule.locationId,
              orElse: () => Location.create(),
            );
            return {'schedule': schedule, 'location': location};
          })
          .toList();

      final scheduledLocationIds =
          scheduled.map((e) => e['location']!.id).toSet();

      final nearby = locations
          .where((loc) =>
              !scheduledLocationIds.contains(loc.id) &&
              distances[loc.id]! <= maxDistanceInMeters)
          .toList();
      nearby.sort((a, b) => distances[a.id]!.compareTo(distances[b.id]!));

      setState(() {
        scheduledLocations = scheduled;
        nearbyLocations = nearby;
        isLoading = false;
      });
    } else {
      // No current position found => stop
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.only(top: 45.0),
          child: SizedBox(
            width: 110,
            height: 105,
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                ColorHelper.thePunchBlue(),
              ),
              strokeWidth: 10.0,
            ),
          ),
        ),
      );
    }

    return AlertDialog(
      title: Center(
        child: Text(
          'Select Location:',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: ColorHelper.thePunchGray(),
              ),
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isManagerOrAdmin)
              _buildManagerLocationSection(context)
            else ...[
              _buildSection(
                'Scheduled Locations',
                scheduledLocations,
                locationDistances,
                context,
                true,
              ),
              _buildSection(
                'Nearby Locations',
                nearbyLocations,
                locationDistances,
                context,
                false,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// For manager/admin: Show "Detected Location(s)" + "No detected locations" if none,
  /// and also the "Other" section with "Start of Day".
  Widget _buildManagerLocationSection(BuildContext context) {
    final theme = Theme.of(context);
    final titleStyle = theme.textTheme.titleMedium;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // DETECTED LOCATIONS
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
          child: Text(
            'Detected Location:',
            style: titleStyle,
          ),
        ),
        if (managerNearbyLocations.isEmpty)
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              'No detected locations',
              style: TextStyle(fontSize: 12, color: Colors.red),
            ),
          )
        else
          ...managerNearbyLocations.map((loc) {
            final distance = managerLocationDistances[loc.id];
            return LocationTile(
              location: loc,
              distance: distance,
              selected: () => widget.onLocationSelected(
                context,
                false,  // isScheduled
                loc.id,
                null,    // scheduleId
              ),
            );
          }).toList(),

        const SizedBox(height: 16),
        // OTHER
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
          child: Text(
            'Other:',
            style: titleStyle,
          ),
        ),
        _buildFakeLocationTile(context),
      ],
    );
  }

  /// The same "Scheduled/Nearby Locations" logic for employees
  Widget _buildSection(
    String title,
    List<dynamic> items,
    Map<String, double> locationDistances,
    BuildContext context,
    bool isScheduled,
  ) {
    final theme = Theme.of(context);
    final titleStyle = theme.textTheme.titleMedium;
    final timeLabelStyle =
        theme.textTheme.titleSmall?.copyWith(color: Colors.grey, fontSize: 8);

    if (isScheduled) {
      items.sort((a, b) => a['schedule']
          .startDateLocal
          .compareTo(b['schedule'].startDateLocal));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
          child: Text(
            title,
            style: titleStyle,
          ),
        ),
        if (items.isEmpty)
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.0),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text('None', style: TextStyle(fontSize: 12)),
            ),
          )
        else
          ...items.map((e) {
            final location = isScheduled ? e['location'] : e;
            final schedule = isScheduled ? e['schedule'] : null;
            final distance = locationDistances[
                isScheduled ? e['location'].id : e.id];

            final startTime = schedule?.startDateLocal != null
                ? TimeOfDay.fromDateTime(schedule.startDateLocal)
                    .format(context)
                : '';
            final endTime = schedule?.endDateLocal != null
                ? TimeOfDay.fromDateTime(schedule.endDateLocal)
                    .format(context)
                : '';

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (isScheduled)
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16.0, vertical: 0.0),
                    child: RichText(
                      text: TextSpan(
                        text: 'Start Time: $startTime  -  End Time: $endTime',
                        style: timeLabelStyle,
                      ),
                    ),
                  ),
                LocationTile(
                  location: location,
                  distance: distance,
                  selected: () => widget.onLocationSelected(
                    context,
                    isScheduled,
                    location.id,
                    isScheduled ? schedule.id : null,
                  ),
                ),
                if (isScheduled) const SizedBox(height: 10),
              ],
            );
          }).toList(),
      ],
    );
  }

  /// For managers, "Start of Day" tile under "Other:"
  Widget _buildFakeLocationTile(BuildContext context) => LocationTile(
      location: Location(
        id: 'fake-location',
        name: 'Start of Day',
        address1: 'Current Location',
        address2: '',
        city: '',
        state: '',
        zip: '',
        country: '',
        phone: '',
        emailAddress: '',
        latitude: 0,
        longitude: 0,
        geoFenceRadius: 1000,
        timeZone: '',
        createdOn: DateTime.now(),
      ),
      distance: 0,
      selected: () => widget.onLocationSelected(
        context,
        false,
        'fake-location',
        null,
      ),
    );
}
