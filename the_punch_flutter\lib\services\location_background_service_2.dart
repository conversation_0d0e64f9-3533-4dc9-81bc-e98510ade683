import 'dart:async';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_background_geolocation/flutter_background_geolocation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:geolocator/geolocator.dart' as geolocator;
import 'package:flutter_background_geolocation/flutter_background_geolocation.dart'
    as bg;
import 'package:shared_preferences/shared_preferences.dart';
import '../dataModel/data/geo_location.dart';
import '../dataModel/data_model.dart';
import '../firebase_options.dart';
import '../state/app_state.dart';
import '../state/location_ping_state.dart';
import '../state/server_time_state.dart';
import '../the_punch_app.dart';
import '../misc/my_platform.dart';

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

//print('Initialized FlutterLocalNotificationsPlugin');

bool? geoFenceBreached; // Nullable variable
//print('Initialized geoFenceBreached with value: $geoFenceBreached');

StreamController<String?> geoFenceStreamController =
    StreamController<String?>.broadcast();
//print('Initialized geoFenceStreamController');

Timer? locationUpdateTimer;
//print('Initialized locationUpdateTimer');

DateTime? breachTime;
//print('Initialized breachTime');

bool isLocationUpdatesRunning =
    false; // New flag to track if location updates are running
//print('Initialized isLocationUpdatesRunning with value: $isLocationUpdatesRunning');

Future<void> setServiceRunning(bool value) async {
  print('setServiceRunning called with value: $value');
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  print('Obtained SharedPreferences instance');
  await prefs.setBool('isServiceRunning', value);
  print('Set isServiceRunning to $value in SharedPreferences');
}

Future<bool> getServiceRunning() async {
  print('getServiceRunning called');
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  print('Obtained SharedPreferences instance');
  bool value = prefs.getBool('isServiceRunning') ?? false;
  print('Retrieved isServiceRunning value: $value');
  return value;
}

Future<void> initializeService() async {
  print('initializeService called');
  await bg.BackgroundGeolocation.ready(
    bg.Config(
      enableHeadless: true, // Enable headless mode
      // debug: true,
      // logLevel: bg.Config.LOG_LEVEL_VERBOSE,
      desiredAccuracy: bg.Config.DESIRED_ACCURACY_HIGH,
      distanceFilter: 15,
      stationaryRadius: 25,
      preventSuspend: true,
      stopTimeout: 1440, // Set to 1440 minutes (24 hours) or any desired value
      heartbeatInterval: 60, // Heartbeat every 60 seconds when stationary
      stopOnTerminate: false, // Ensure service does not stop on terminate
      startOnBoot: false, // Optionally start the service on boot
      disableMotionActivityUpdates: false,
      foregroundService:
          true, // Ensures Android runs it as a foreground service
    ),
  ).then((bg.State state) async {
    print('BackgroundGeolocation.ready completed with state: $state');
    print('MyAppDebug: Service initialized and ready.');
  }).catchError((error) {
    print('Error initializing BackgroundGeolocation: $error');
  });

  bg.BackgroundGeolocation.onMotionChange((bg.Location location) async {
    print('onMotionChange event triggered with location: $location');
    if (location.isMoving) {
      print("MyAppDebug: [onMotionChange] Device started moving");
      if (!isLocationUpdatesRunning) {
        print('Location updates not running. Starting location updates.');
        startLocationUpdates();
      } else {
        print('Location updates already running.');
      }
    } else {
      print("MyAppDebug: [onMotionChange] Device is stationary");
      if (MyPlatform.isAndroid) {
        print('Platform is Android.');
        if (!isLocationUpdatesRunning) {
          print('Location updates not running in stationary mode. Starting location updates.');
          startLocationUpdates(); // Keep location updates running in stationary mode
        } else {
          print('Location updates already running in stationary mode.');
        }
      }
    }
  });

  // Handle heartbeat event for iOS
  // if (MyPlatform.isIOS) {
  bg.BackgroundGeolocation.onHeartbeat((bg.HeartbeatEvent event) async {
    print('onHeartbeat event triggered with event: $event');
    print('MyAppDebug: Heartbeat triggered.');

    if (MyPlatform.isAndroid) {
      print('Platform is Android. Starting location updates on heartbeat.');
      startLocationUpdates();
    } else if (MyPlatform.isIOS) {
      print('Platform is iOS. Processing heartbeat for iOS.');
      DateTime pingTime = ServerTimeState().utcTime;
      print('Retrieved pingTime: $pingTime');

      try {
        print('Attempting to get current position with BackgroundGeolocation.');
        final location = await bg.BackgroundGeolocation.getCurrentPosition(
          samples: 1,
          persist: false,
          desiredAccuracy: bg.Config.DESIRED_ACCURACY_NAVIGATION,
          timeout: 60,
          maximumAge: 5000,
        );
        print('Heartbeat location obtained: ${location.coords}');

        final locationPingState = LocationPingState();
        print('Initialized LocationPingState');
        final isManager = await locationPingState.getIsManager();
        print('isManager: $isManager');

        if (isManager) {
          print('User is a manager. Handling manager tracking.');
          await handleManagerTracking(locationPingState, location, pingTime);
        } else {
          print('User is not a manager. Handling non-manager tracking.');
          await handleNonManagerTracking(locationPingState, location);
        }
      } catch (error) {
        print('Error occurred during heartbeat: $error');
      }
    }
  });
  // }

  // Register the headless task for Android
  if (MyPlatform.isAndroid) {
    print('Platform is Android. Registering headlessTask.');
    await bg.BackgroundGeolocation.registerHeadlessTask(headlessTask);
    print('headlessTask registered successfully.');
  } else {
    print('Platform is not Android. Skipping headlessTask registration.');
  }

  print('initializeService completed');
}

@pragma('vm:entry-point')
void startLocationUpdates() {
  print('startLocationUpdates called');
  if (isLocationUpdatesRunning) {
    print('Location updates are already running. Exiting startLocationUpdates.');
    return;
  } // Prevent starting if already running
  isLocationUpdatesRunning = true;
  print('Set isLocationUpdatesRunning to true');

  print('Starting locationUpdateTimer with 60-second intervals');

  locationUpdateTimer = Timer.periodic(Duration(seconds: 60), (Timer t) async {
    print('locationUpdateTimer ticked');
    bg.Location? location;
    DateTime pingTime = ServerTimeState().utcTime;
    print('Retrieved pingTime: $pingTime');

    try {
      print('Attempting to get current position with BackgroundGeolocation.');
      location = await bg.BackgroundGeolocation.getCurrentPosition(
        samples: 1,
        persist: false,
        desiredAccuracy: bg.Config.DESIRED_ACCURACY_HIGH,
        timeout: 60, // Timeout after 60 seconds
        maximumAge: 5000,
      );
      print('Location obtained: ${location.coords}');
    } catch (error) {
      print('MyAppDebug: Error fetching location: $error');
      location = null; // Handle failure case
    }

    print(
        'MyAppDebug: Location update: ${location?.coords ?? 'failed to fetch location'}');

    final locationPingState = LocationPingState();
    print('Initialized LocationPingState');
    final isManager = await locationPingState.getIsManager();
    print('isManager: $isManager');

    if (isManager) {
      print('User is a manager. Handling manager tracking.');
      await handleManagerTracking(locationPingState, location, pingTime);
    } else {
      print('User is not a manager. Handling non-manager tracking.');
      await handleNonManagerTracking(locationPingState, location);
    }
  });

  print('LocationUpdateTimer started');
}

Future<void> stopLocationUpdates() async {
  print('stopLocationUpdates called');
  if (locationUpdateTimer != null) {
    print('Cancelling locationUpdateTimer');
    locationUpdateTimer?.cancel();
    locationUpdateTimer = null;
    print('locationUpdateTimer cancelled and set to null');
  } else {
    print('locationUpdateTimer is already null');
  }
  isLocationUpdatesRunning = false;
  print('Set isLocationUpdatesRunning to false');
}

Future<void> handleManagerTracking(LocationPingState locationPingState,
    bg.Location? location, DateTime pingTime) async {
  print('handleManagerTracking called with location: $location and pingTime: $pingTime');
  String motionState = await determineMotionState(location);
  print('Determined motionState: $motionState');

  final userId = await locationPingState.getUserId();
  print('Retrieved userId: $userId');

  final punchCardId = await locationPingState.getPunchCardId();
  print('Retrieved punchCardId: $punchCardId');

  final Map<String, String?> lastLocation =
      await locationPingState.getLastPingLocation();
  print('Retrieved lastLocation: $lastLocation');

  final lastPingEndTime = await locationPingState.getLastPingEndTime();
  print('Retrieved lastPingEndTime: $lastPingEndTime');

  geolocator.Position position;

  if (location != null) {
    print('Location is not null. Creating geolocator.Position from bg.Location.');
    position = geolocator.Position(
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
      timestamp: DateTime.now(),
      accuracy: location.coords.accuracy,
      altitude: location.coords.altitude,
      heading: location.coords.heading,
      speed: location.coords.speed,
      speedAccuracy: location.coords.speedAccuracy,
      altitudeAccuracy: 0.0,
      headingAccuracy: 0.0,
    );
    print('Created geolocator.Position: $position');
  } else {
    print('Location is null. Attempting to get current position via Geolocator.');
    try {
      position = await geolocator.Geolocator.getCurrentPosition(
        desiredAccuracy: geolocator.LocationAccuracy.best,
      );
      print('Geolocator.Position obtained: $position');
    } catch (e) {
      print('MyAppDebug: Failed to fetch location via Geolocator: $e');
      position = defaultPosition(lastLocation);
      print('Used defaultPosition: $position');
      motionState = 'failed';
      print('Set motionState to "failed" due to location fetch failure.');
    }
  }

  if (userId == null) {
    print('userId is null. Exiting handleManagerTracking.');
    return;
  }

  print('Creating and saving ping with DataModel.');
  await DataModel().travelPingModel.createAndSavePing(
      position,
      userId,
      punchCardId,
      lastLocation['latitude'],
      lastLocation['longitude'],
      lastPingEndTime,
      motionState,
      pingTime);
  print('Ping created and saved successfully.');
}

Future<String> determineMotionState(bg.Location? location) async {
  print('determineMotionState called with location: $location');
  if (location != null) {
    String state = location.isMoving ? "moving" : "stationary";
    print('Location is not null. Motion state determined as: $state');
    return state;
  } else {
    print('Location is null. Attempting to get current position via Geolocator to determine motion state.');
    try {
      var currentLocation = await geolocator.Geolocator.getCurrentPosition(
        desiredAccuracy: geolocator.LocationAccuracy.best,
      );
      print('Geolocator.Position obtained: $currentLocation');
      print('Motion state determined as: stationary');
      return "stationary";
    } catch (e) {
      print('Error determining motion state: $e');
      print('Motion state set to: failed');
      return 'failed';
    }
  }
}

geolocator.Position defaultPosition(Map<String, String?> lastLocation) {
  print('defaultPosition called with lastLocation: $lastLocation');
  geolocator.Position position = geolocator.Position(
    latitude: double.parse(lastLocation['latitude'] ?? '0.0'),
    longitude: double.parse(lastLocation['longitude'] ?? '0.0'),
    timestamp: DateTime.now(),
    accuracy: 0.0,
    altitude: 0.0,
    heading: 0.0,
    speed: 0.0,
    speedAccuracy: 0.0,
    altitudeAccuracy: 0.0,
    headingAccuracy: 0.0,
  );
  print('Created default geolocator.Position: $position');
  return position;
}

Future<void> handleNonManagerTracking(
    LocationPingState locationPingState, bg.Location? location) async {
  print('handleNonManagerTracking called with location: $location');
  try {
    final punchCardId = await locationPingState.getPunchCardId();
    print('Retrieved punchCardId: $punchCardId');
    if (punchCardId == null) {
      print('punchCardId is null. Exiting handleNonManagerTracking.');
      return;
    }

    geolocator.Position? position;

    if (location != null) {
      print('Location is not null. Creating geolocator.Position from bg.Location.');
      position = geolocator.Position(
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        timestamp: DateTime.now(),
        accuracy: location.coords.accuracy,
        altitude: location.coords.altitude,
        heading: location.coords.heading,
        speed: location.coords.speed,
        speedAccuracy: location.coords.speedAccuracy,
        altitudeAccuracy: 0.0,
        headingAccuracy: 0.0,
      );
      print('Created geolocator.Position: $position');
    } else {
      print('Location is null. Attempting to get current position via Geolocator.');
      try {
        position = await geolocator.Geolocator.getCurrentPosition(
          desiredAccuracy: geolocator.LocationAccuracy.best,
        );
        print('Geolocator.Position obtained: $position');
      } catch (e) {
        print('MyAppDebug: Failed to fetch location via Geolocator: $e');
        position = geolocator.Position(
          latitude: 0.0,
          longitude: 0.0,
          timestamp: DateTime.now(),
          accuracy: 0.0,
          altitude: 0.0,
          heading: 0.0,
          speed: 0.0,
          speedAccuracy: 0.0,
          altitudeAccuracy: 0.0,
          headingAccuracy: 0.0,
        );
        print('Created fallback geolocator.Position with default values: $position');
      }
    }

    final locationData = await locationPingState.getLocation();
    print('Retrieved locationData: $locationData');

    final geoFenceRadius = await locationPingState.getLocationGeofence();
    print('Retrieved geoFenceRadius: $geoFenceRadius');

    double distance = geolocator.Geolocator.distanceBetween(
      position.latitude,
      position.longitude,
      double.parse(locationData['latitude'] ?? '0'),
      double.parse(locationData['longitude'] ?? '0'),
    );
    print('Calculated distance from geofence center: $distance meters');

    bool isInsideGeofence = false;
    if (geoFenceRadius != null && geoFenceRadius['radius'] != null) {
      isInsideGeofence = distance <= (double.tryParse(geoFenceRadius['radius']!.toString()) ?? 0.0);
      print('isInsideGeofence calculated as: $isInsideGeofence');
    } else {
      print('geoFenceRadius or its radius value is null. Assuming isInsideGeofence as false.');
    }

    if (isInsideGeofence) {
      print('User is inside the geofence.');
      if (geoFenceBreached == true) {
        print('Previously breached. Saving re-entry ping.');
        await DataModel().geoLocationModel.saveGeoFencePing(
              punchCardId,
              position.latitude,
              position.longitude,
              distance,
              GeoLocation.geoFenceEnteredId,
            );
        print('Re-entry ping saved.');

        geoFenceBreached = false;
        print('Set geoFenceBreached to false');

        geoFenceStreamController.add('false');
        print('Added "false" to geoFenceStreamController');

        await showNotification(
            'Re-entry Detected', 'You have re-entered the location.', 0);
        print('Re-entry notification shown.');
      } else {
        print('No previous breach detected. No action needed.');
      }
    } else {
      print('User is outside the geofence.');
      if (geoFenceBreached != true) {
        print('Setting geoFenceBreached to true and recording breachTime.');
        geoFenceBreached = true;
        print('Set geoFenceBreached to true');

        geoFenceStreamController.add('true');
        print('Added "true" to geoFenceStreamController');

        breachTime = DateTime.now();
        print('Set breachTime to current time: $breachTime');
      } else {
        print('geoFenceBreached is already true.');
      }

      await DataModel().geoLocationModel.saveGeoFencePing(
            punchCardId,
            position.latitude,
            position.longitude,
            distance,
            GeoLocation.geoFenceExitedId,
          );
      print('Geo-fence exit ping saved.');

      if (breachTime != null) {
        int minutesElapsed = DateTime.now().difference(breachTime!).inMinutes;
        print('Minutes elapsed since breach: $minutesElapsed');
        await showNotification(
            'WARNING: Breach Detected',
            'You left the location without Punching out! - Please re-enter the site or Punch Out.',
            minutesElapsed);
        print('Breach notification shown with elapsed time.');
      } else {
        print('breachTime is null. Showing breach notification without elapsed time.');
        await showNotification(
            'WARNING: Breach Detected',
            'You left the location without Punching out! - Please re-enter the site or Punch Out.',
            0);
        print('Breach notification shown without elapsed time.');
      }
    }
  } catch (e) {
    print('Error occurred while handling non-manager tracking: $e');
  }
}

Future<void> resetGeoFenceBreached() async {
  print('resetGeoFenceBreached called');
  geoFenceBreached = null;
  print('Set geoFenceBreached to null');

  geoFenceStreamController.add(null);
  print('Added null to geoFenceStreamController');
}

Future<void> showNotification(
    String title, String body, int minutesElapsed) async {
  print('showNotification called with title: "$title", body: "$body", minutesElapsed: $minutesElapsed');
  String updatedTitle = '$title';
  print('Updated title: "$updatedTitle"');

  String message = minutesElapsed > 0
      ? '$minutesElapsed minute${minutesElapsed > 1 ? 's' : ''} ago - $body'
      : body;
  print('Constructed notification message: "$message"');

  const AndroidNotificationDetails androidPlatformChannelSpecifics =
      AndroidNotificationDetails(
    'geofence_channel_id',
    'Geofence Alerts',
    channelDescription: 'Notifications for geofence entry and exit alerts',
    importance: Importance.max,
    priority: Priority.high,
    ticker: 'ticker',
  );
  print('Configured AndroidNotificationDetails');

  const DarwinNotificationDetails iosPlatformChannelSpecifics =
      DarwinNotificationDetails();
  print('Configured DarwinNotificationDetails');

  const NotificationDetails platformChannelSpecifics = NotificationDetails(
    android: androidPlatformChannelSpecifics,
    iOS: iosPlatformChannelSpecifics,
  );
  print('Combined platform-specific notification details');

  await flutterLocalNotificationsPlugin.show(
    0,
    updatedTitle,
    message,
    platformChannelSpecifics,
    payload: 'item x',
  );
  print('Notification displayed with ID 0');
}

// Headless task for background events when the app is terminated
@pragma('vm:entry-point')
void headlessTask(bg.HeadlessEvent headlessEvent) async {
  print('[HeadlessTask]: Event received: ${headlessEvent}');
  
  // Initialize Firebase
  print('Initializing Firebase in headlessTask.');
  try {
    await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
    print('Firebase initialized successfully in headlessTask.');
  } catch (e) {
    print('Error initializing Firebase in headlessTask: $e');
  }

  // Ensure AppState is initialized before doing anything
  if (!AppState.isInitialized) {
    print("MyAppDebug: AppState is not initialized. Initializing now...");
    await AppState.initialize();
    print("MyAppDebug: AppState initialized.");
  } else {
    print("MyAppDebug: AppState is already initialized.");
  }

  switch (headlessEvent.name) {
    case bg.Event.TERMINATE:
      print('HeadlessTask: App terminated event received.');
      // Handle termination event, e.g., save necessary state or data
      break;
    case bg.Event.HEARTBEAT:
      print('HeadlessTask: Heartbeat event received.');
      // Access the location within the HeartbeatEvent
      if (headlessEvent.event is bg.HeartbeatEvent) {
        bg.HeartbeatEvent heartbeatEvent =
            headlessEvent.event as bg.HeartbeatEvent;
        bg.Location? location =
            heartbeatEvent.location; // Location can be nullable
        print('HeartbeatEvent location: $location');
        if (location != null) {
          print('Saving location ping from HeartbeatEvent.');
          await saveLocationPing(location);
          print('Location ping saved from HeartbeatEvent.');
        } else {
          print('No location data available in HeartbeatEvent.');
        }
      } else {
        print('HeadlessTask: Event is not of type HeartbeatEvent.');
      }
      break;
    case bg.Event.LOCATION:
      print('HeadlessTask: Location event received.');
      if (headlessEvent.event is bg.Location) {
        print('Saving location ping from Location event.');
        await saveLocationPing(headlessEvent.event as bg.Location);
        print('Location ping saved from Location event.');
      } else {
        print('HeadlessTask: Event is not of type Location.');
      }
      break;
    case bg.Event.MOTIONCHANGE:
      print('HeadlessTask: MotionChange event received.');
      if (headlessEvent.event is bg.Location) {
        print('Saving location ping from MotionChange event.');
        await saveLocationPing(headlessEvent.event as bg.Location);
        print('Location ping saved from MotionChange event.');
      } else {
        print('HeadlessTask: Event is not of type Location for MotionChange.');
      }
      break;
    default:
      print('HeadlessTask: Unhandled event type: ${headlessEvent.name}');
      break;
  }

  print('HeadlessTask processing completed for event: ${headlessEvent.name}');
}

Future<void> continueLocationUpdates() async {
  print('continueLocationUpdates called');
  if (!isLocationUpdatesRunning) {
    print('Location updates are not running. Starting location updates.');
    startLocationUpdates();
  } else {
    print('Location updates are already running. No action taken.');
  }
}

Future<void> saveLocationPing(bg.Location location) async {
  print('saveLocationPing called with location: $location');
  DateTime pingTime = DateTime.now();
  print('Captured pingTime: $pingTime');

  final locationPingState = LocationPingState();
  print('Initialized LocationPingState');

  final isManager = await locationPingState.getIsManager();
  print('isManager: $isManager');

  if (isManager) {
    print('User is a manager. Handling manager tracking.');
    await handleManagerTracking(locationPingState, location, pingTime);
    print('Manager tracking handled successfully.');
  } else {
    print('User is not a manager. Handling non-manager tracking.');
    await handleNonManagerTracking(locationPingState, location);
    print('Non-manager tracking handled successfully.');
  }
}
