import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'screen_helper.dart';

import 'color_helper.dart';

class TextStyleHelper {
  static TextStyle grayTitle(BuildContext context) => GoogleFonts.inter(
        color: ColorHelper.thePunchGray(),
        fontWeight: FontWeight.w600,
        fontSize: ScreenHelper.screenHeightPercentage(context, 2.5),
      );

  static TextStyle lightGrayTitle(BuildContext context) => GoogleFonts.inter(
        color: ColorHelper.thePunchLightGray(),
        fontWeight: FontWeight.w700,
        fontSize: ScreenHelper.screenHeightPercentage(context, 1.5),
      );

  static TextStyle whiteTitle(BuildContext context) => GoogleFonts.inter(
        color: ColorHelper.lightThemeWhite(),
        fontWeight: FontWeight.w800,
        fontSize: ScreenHelper.screenHeightPercentage(context, 2),
      );

  static TextStyle whiteTitleSmall(BuildContext context) => GoogleFonts.inter(
        color: ColorHelper.lightThemeWhite(),
        fontWeight: FontWeight.w800,
        fontSize: ScreenHelper.screenHeightPercentage(context, 1.3),
      );

  static TextStyle redBodyText(BuildContext context) => GoogleFonts.inter(
        color: ColorHelper.thePunchRed(),
        fontWeight: FontWeight.w500,
        fontSize: ScreenHelper.screenHeightPercentage(context, 1.4),
      );

  static TextStyle grayBodyText(BuildContext context) => GoogleFonts.inter(
        color: ColorHelper.thePunchGray(),
        fontWeight: FontWeight.w500,
        fontSize: ScreenHelper.screenHeightPercentage(context, 1.3),
      );

  static TextStyle bigBoldBlueTitle(BuildContext context) => GoogleFonts.inter(
        color: ColorHelper.thePunchBlue(),
        fontWeight: FontWeight.w800,
        fontSize: ScreenHelper.screenHeightPercentage(context, 5),
      );

  static TextStyle desktopTableHeaderText(BuildContext context) =>
      GoogleFonts.inter(
        color: Colors.white,
        fontWeight: FontWeight.w800,
        fontSize: 15,
      );

  static TextStyle bigBlackTitle(BuildContext context) => GoogleFonts.inter(
        color: Colors.black,
        fontWeight: FontWeight.w800,
        fontSize: ScreenHelper.screenHeightPercentage(context, 5),
      );
}
