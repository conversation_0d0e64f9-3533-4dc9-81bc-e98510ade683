// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'send_edited_punch_card_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SendEditedPunchCardRequest _$SendEditedPunchCardRequestFromJson(
        Map<String, dynamic> json) =>
    SendEditedPunchCardRequest(
      punchCard: PunchCard.fromJson(json['PunchCard'] as Map<String, dynamic>),
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$SendEditedPunchCardRequestToJson(
        SendEditedPunchCardRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'PunchCard': instance.punchCard,
    };

SendEditedPunchCardResponse _$SendEditedPunchCardResponseFromJson(
        Map<String, dynamic> json) =>
    SendEditedPunchCardResponse(
      success: json['Success'] as bool,
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$SendEditedPunchCardResponseToJson(
    SendEditedPunchCardResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['Success'] = instance.success;
  return val;
}
