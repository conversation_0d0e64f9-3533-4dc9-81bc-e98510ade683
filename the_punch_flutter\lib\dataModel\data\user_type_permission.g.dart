// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_type_permission.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserTypePermissionAdapter extends TypeAdapter<UserTypePermission> {
  @override
  final int typeId = 21;

  @override
  UserTypePermission read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserTypePermission(
      isDirty: fields[0] as bool,
      isActive: fields[1] as bool,
      createdOn: fields[2] as DateTime,
      createdByUserId: fields[3] as String?,
      lastChangedOn: fields[4] as DateTime?,
      lastChangedByUserId: fields[5] as String?,
      id: fields[100] as String,
      userTypeId: fields[101] as String,
      permissionId: fields[102] as String,
    );
  }

  @override
  void write(BinaryWriter writer, UserTypePermission obj) {
    writer
      ..writeByte(9)
      ..writeByte(100)
      ..write(obj.id)
      ..writeByte(101)
      ..write(obj.userTypeId)
      ..writeByte(102)
      ..write(obj.permissionId)
      ..writeByte(0)
      ..write(obj.isDirty)
      ..writeByte(1)
      ..write(obj.isActive)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdByUserId)
      ..writeByte(4)
      ..write(obj.lastChangedOn)
      ..writeByte(5)
      ..write(obj.lastChangedByUserId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserTypePermissionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserTypePermission _$UserTypePermissionFromJson(Map<String, dynamic> json) =>
    UserTypePermission(
      isActive: json['IsActive'] as bool? ?? true,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
      id: idFromJson(json['Id']),
      userTypeId: idFromJson(json['UserTypeId']),
      permissionId: idFromJson(json['PermissionId']),
    );

Map<String, dynamic> _$UserTypePermissionToJson(UserTypePermission instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['UserTypeId'] = instance.userTypeId;
  val['PermissionId'] = instance.permissionId;
  return val;
}
