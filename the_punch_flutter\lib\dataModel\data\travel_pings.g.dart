// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'travel_pings.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TravelPingAdapter extends TypeAdapter<TravelPing> {
  @override
  final int typeId = 27;

  @override
  TravelPing read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TravelPing(
      isDirty: fields[0] as bool,
      isActive: fields[1] as bool,
      punchCardId: fields[7] as String,
      userId: fields[8] as String,
      polyline: fields[9] as String,
      startTime: fields[10] as DateTime,
      endTime: fields[11] as DateTime,
      distance: fields[12] as double,
      startLat: fields[13] as String,
      endLat: fields[14] as String,
      startLong: fields[15] as String,
      endLong: fields[16] as String,
      pingSource: fields[17] as String?,
      createdOn: fields[2] as DateTime,
      createdByUserId: fields[3] as String?,
      lastChangedOn: fields[4] as DateTime?,
      lastChangedByUserId: fields[5] as String?,
    ).._id = fields[6] as String;
  }

  @override
  void write(BinaryWriter writer, TravelPing obj) {
    writer
      ..writeByte(18)
      ..writeByte(6)
      ..write(obj._id)
      ..writeByte(7)
      ..write(obj.punchCardId)
      ..writeByte(8)
      ..write(obj.userId)
      ..writeByte(9)
      ..write(obj.polyline)
      ..writeByte(10)
      ..write(obj.startTime)
      ..writeByte(11)
      ..write(obj.endTime)
      ..writeByte(12)
      ..write(obj.distance)
      ..writeByte(13)
      ..write(obj.startLat)
      ..writeByte(14)
      ..write(obj.endLat)
      ..writeByte(15)
      ..write(obj.startLong)
      ..writeByte(16)
      ..write(obj.endLong)
      ..writeByte(17)
      ..write(obj.pingSource)
      ..writeByte(0)
      ..write(obj.isDirty)
      ..writeByte(1)
      ..write(obj.isActive)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdByUserId)
      ..writeByte(4)
      ..write(obj.lastChangedOn)
      ..writeByte(5)
      ..write(obj.lastChangedByUserId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TravelPingAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TravelPing _$TravelPingFromJson(Map<String, dynamic> json) => TravelPing(
      id: json['Id'] as String?,
      isActive: json['IsActive'] as bool? ?? true,
      punchCardId: idFromJson(json['PunchCardId'] as String),
      userId: idFromJson(json['UserId'] as String),
      polyline: json['Polyline'] as String? ?? '',
      startTime: DateTime.parse(json['StartTime'] as String),
      endTime: DateTime.parse(json['EndTime'] as String),
      distance: (json['Distance'] as num?)?.toDouble() ?? 0.0,
      startLat: json['StartLat'] as String? ?? '',
      endLat: json['EndLat'] as String? ?? '',
      startLong: json['StartLong'] as String? ?? '',
      endLong: json['EndLong'] as String? ?? '',
      pingSource: json['PingSource'] as String?,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
    );

Map<String, dynamic> _$TravelPingToJson(TravelPing instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['PunchCardId'] = idToJson(instance.punchCardId);
  val['UserId'] = idToJson(instance.userId);
  val['Polyline'] = instance.polyline;
  val['StartTime'] = instance.startTime.toIso8601String();
  val['EndTime'] = instance.endTime.toIso8601String();
  val['Distance'] = instance.distance;
  val['StartLat'] = instance.startLat;
  val['EndLat'] = instance.endLat;
  val['StartLong'] = instance.startLong;
  val['EndLong'] = instance.endLong;
  writeNotNull('PingSource', instance.pingSource);
  return val;
}
