

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../../dataModel/data/user.dart';
import '../../../helpers/screen_helper.dart';
import '../../../misc/app_localization.dart';
import '../../../state/login_state.dart';
import '../../../state/page_title_state.dart';

class _ProfileIcon extends StatelessWidget {
  @override
  Widget build(BuildContext context) => IconButton(
        tooltip: AppLocalization.of(context).profile,
        icon: Icon(
          Icons.account_circle,
          color: Colors.grey,
          size: ScreenHelper.screenHeightPercentage(context, 5.5),
        ),
        onPressed: () {
          if (ModalRoute.of(context)!.settings.name! == '/profile') {
            Scaffold.of(context).openEndDrawer();
          } else {
            context.go('/profile');
          }
        },
      );
}
class DesktopHeader extends StatefulWidget {
  final bool showSalutation;
  const DesktopHeader({super.key, required this.showSalutation});

  @override
  State<DesktopHeader> createState() =>
      _DesktopHeaderState();
}
class _DesktopHeaderState extends State<DesktopHeader> {
   @override
  void initState() {
    super.initState();

  } 
 
 

  @override
  Widget build(BuildContext context) => ListenableBuilder(
            listenable: Provider.of<PageTitleProvider>(context, listen: false),
            builder: (BuildContext context, Widget? child) {
             
              var currentTitle = Provider.of<PageTitleProvider>(context, listen: false).currentTitle;
              //return Text('${Provider.of<PageTitleProvider>(context, listen: false).currentTitle}');
            return Container(
            width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if(currentTitle != 'Overview')
                      Text(
                        currentTitle,
                        style: const TextStyle(
                            fontSize: 32, fontWeight: FontWeight.w900,
                            color: Colors.black
                            ),
                      ) else
                      _AlertsIcon(),
                      if (widget.showSalutation)
                        ValueListenableBuilder<User>(
                          valueListenable: LoginState.userNotifier,
                          builder: (context, employee, _) => Text(
                            '',
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge
                                ?.copyWith(
                                  fontSize: ScreenHelper.screenHeightPercentage(
                                      context, 1.7),
                                ),
                            textAlign: TextAlign.start,
                          ),
                        ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    // ValueListenableBuilder<User>(
                    //   valueListenable: LoginState.userNotifier,
                    //   builder: (context, employee, _) => Text(
                    //     employee.name,
                    //     style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    //           fontSize: ScreenHelper.screenHeightPercentage(
                    //               context, 1.9),
                    //         ),
                    //   ),
                    // ),
                   
                    _ProfileIcon(),
                  ],
                )
              ],
            ),
          );
        },
      );
            
          

}
class CounterModel with ChangeNotifier {
  int _count = 0;
  int get count => _count;

  void increment() {
    _count += 1;
    notifyListeners();
  }
}

/// The new Alerts icon we want on the far left of the header.
class _AlertsIcon extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // Fixed container size for the circle
    final double circleSize = 45;

    return Container(
      width: circleSize,
      height: circleSize,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.grey, // Light gray background

      ),
      child: IconButton(
        icon: Icon(
          Icons.notifications_active_outlined,
          color: Colors.white,      // White bell icon
          size: circleSize * 0.6,   // Scale the icon within the circle
        ),
        tooltip: 'Alerts',
        onPressed: () {
          if (ModalRoute.of(context)?.settings.name == '/alerts') {
            Scaffold.of(context).openEndDrawer();
          } else {
            context.go('/alerts');
          }
        },
      ),
    );
  }
}


