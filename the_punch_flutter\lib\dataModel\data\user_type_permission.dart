import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../base_data.dart';
import '../../misc/json_conversion.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import 'package:uuid/uuid.dart';

part 'user_type_permission.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
@HiveType(typeId: 21)
class UserTypePermission extends BaseData {
  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  @JsonKey(fromJson: idFromJson)
  String userTypeId;

  @HiveField(102)
  @JsonKey(fromJson: idFromJson)
  String permissionId;

  UserTypePermission({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.userTypeId,
    required this.permissionId,
  });

  factory UserTypePermission.fromJson(Map<String, dynamic> json) {
    try {
      return _$UserTypePermissionFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }
  Map<String, dynamic> toJson() => _$UserTypePermissionToJson(this);

  factory UserTypePermission.from(UserTypePermission o) => UserTypePermission(
        isDirty: o.isDirty,
        isActive: o.isActive,
        createdOn: o.createdOn,
        createdByUserId: o.createdByUserId,
        lastChangedOn: o.lastChangedOn,
        lastChangedByUserId: o.lastChangedByUserId,
        id: o.id,
        permissionId: o.permissionId,
        userTypeId: o.userTypeId,
      );

  factory UserTypePermission.create() => UserTypePermission(
        id: const Uuid().v4(),
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
        userTypeId: '',
        permissionId: '',
      );
}
