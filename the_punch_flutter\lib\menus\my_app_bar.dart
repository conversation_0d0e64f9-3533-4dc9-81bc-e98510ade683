// ignore_for_file: use_named_constants, avoid_redundant_argument_values

import 'dart:async';
import 'dart:ui';

import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import '../api/api_model.dart';
import '../api/sync_model.dart';
import '../dataModel/data/user.dart';
import '../dialogs/create_inspection_dialog.dart';
import '../dialogs/logout_dialog.dart';
import '../helpers/color_helper.dart';
import '../helpers/screen_helper.dart';
import '../misc/app_localization.dart';
import '../misc/logging.dart';
import '../misc/my_platform.dart';
import '../state/login_state.dart';
import '../state/permissions_state.dart';
import '../state/punch_state.dart';
import '../the_punch_icons_icons.dart';
import '../widgets/menu/aligned_popup_menu_button.dart';
import '../widgets/menu/popup_menu_label.dart';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

bool isMobilePlatform(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;
  return screenWidth < 1024; // Adjust threshold if needed
}

class MyAppBar {
  MyAppBar(
      {required this.context,
      required this.title,
      this.titleWidget,
      this.showTitle = false,
      this.showSubTitle = false,
      this.showBackButton = false,
      this.hideBar = false,
      this.totalMembers = 0,
      this.showNotificationButton = false});

  final BuildContext context;
  final String title;
  final Widget? titleWidget;
  final int totalMembers;
  final bool showTitle;
  final bool showSubTitle;
  final bool showBackButton;
  final bool hideBar;
  final bool showNotificationButton;

  /// If mobile => build `handheldAppBar()`.
  /// If desktop => build `desktopAppBar()`.
PreferredSizeWidget? get appBar =>
    isMobilePlatform(context) ? handheldAppBar() : null;

  AppBar handheldAppBar() => AppBar(
        scrolledUnderElevation: 0,
        toolbarHeight: 80,
        automaticallyImplyLeading: false,
        centerTitle: true,
        backgroundColor: Colors.white,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo
            if (!hideBar)
              GestureDetector(
                onTap: () {
                  context.go('/clock');
                },
                child: SizedBox(
                  height: showTitle ? 50 : 100,
                  width: showTitle ? 50 : 100,
                  child: Image.asset('images/the_punch_logo_small.png'),
                ),
              ),
            const SizedBox(width: 8),
            // Title
            if (showTitle)
            titleWidget ?? Text(''),

            if (hideBar) Container(),
          ],
        ),
        flexibleSpace: SafeArea(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Left side of the AppBar
              Row(
                children: [
                  if (showNotificationButton)
                    Padding(
                      padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
                      child: IconButton(
                        icon: const Icon(
                          Icons.notifications_outlined,
                          color: Colors.grey,
                        ),
                        iconSize: 40.0,
                        onPressed: () => context.go('/notifications'),
                      ),
                    ),

                  if (showBackButton)
                    IconButton(
                      icon: Icon(
                        Icons.arrow_back_rounded,
                        color: ColorHelper.thePunchRed(),
                      ),
                      onPressed: () {
                        if (context.canPop()) {
                          Navigator.of(context).pop();
                        } else {
                          // Fallback to a different route if there's nothing to pop
                          context.go('/clock');
                        }
                      },
                    ),
                  if (!showBackButton && !showNotificationButton && kIsWeb)
                    Builder(
                      builder: (ctx) => IconButton(
                        icon: const Icon(
                          Icons.menu,
                          color: Colors.grey,
                        ),
                        iconSize: 40.0,
                        onPressed: () => Scaffold.of(ctx).openDrawer(),
                      ),
                    ),
                ],
              ),
              // Right side of the AppBar
              if (!hideBar)
                Align(
                  alignment: Alignment.centerRight,
                  child: _ProfileIcon(),
                ),
              if (hideBar) Container(),
            ],
          ),
        ),
      );

  AppBar desktopAppBar() => AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: Colors.white,
        title: Text(title),
        actions: [
          Center(
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: _EmployeeName(),
            ),
          ),
        ],
      );
}



// CHANGED HERE: Force MyDrawer().drawer to never be null.
class MyDrawer {
  Widget get drawer =>
      MyPlatform.isHandheld ? const SizedBox() : _DesktopDrawer();
}

class LoggedOutDrawer {
  Widget? get drawer => MyPlatform.isHandheld
      ? _MobileLoggedOutDrawer()
      : _DesktopLoggedOutDrawer();
}

// Mobile Logged Out Drawer
class _MobileLoggedOutDrawer extends StatelessWidget {
  @override
  Widget build(BuildContext context) => SizedBox(
        width: ScreenHelper.screenWidth(context),
        child: BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 8,
            sigmaY: 8,
          ),
          child: Drawer(
            elevation: 0,
            backgroundColor: Colors.white.withOpacity(0.7),
            child: SafeArea(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Header with close icon, logo, and profile icon
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal:
                          ScreenHelper.screenWidthPercentage(context, 2),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Builder(
                          builder: (context) => Padding(
                            padding: const EdgeInsets.all(8),
                            child: DecoratedBox(
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: ColorHelper.thePunchGray(),
                                ),
                                borderRadius: BorderRadius.circular(15),
                                color: Colors.transparent,
                              ),
                              child: IconButton(
                                icon: Icon(
                                  Icons.close,
                                  color: ColorHelper.thePunchGray(),
                                ),
                                onPressed: () =>
                                    Scaffold.of(context).closeDrawer(),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 100,
                          width: 100,
                          child: AspectRatio(
                            aspectRatio: 4.5,
                            child:
                                Image.asset('images/the_punch_logo_small.png'),
                          ),
                        ),
                        _ProfileIcon()
                      ],
                    ),
                  ),
                  const Spacer(),
                  // Login Button
                  Padding(
                    padding: EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal:
                          ScreenHelper.screenWidthPercentage(context, 20),
                    ),
                    child: _Login(),
                  ),
                  // Forgot Password Button
                  Padding(
                    padding: EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal:
                          ScreenHelper.screenWidthPercentage(context, 20),
                    ),
                    child: _ForgotPassword(),
                  ),
                  Expanded(
                    child: Container(),
                  ),
                  // About Button
                  Padding(
                    padding: EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal:
                          ScreenHelper.screenWidthPercentage(context, 20),
                    ),
                    child: _About(),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
}
class _DesktopLoggedOutDrawer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    return Drawer(
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 20,
          vertical: ScreenHelper.screenHeightPercentage(context, 5),
        ),
        child: DecoratedBox(
          decoration: BoxDecoration(
            border: Border.all(
              color: ColorHelper.thePunchDesktopLightGray(),
            ),
            borderRadius: BorderRadius.circular(26),
            color: ColorHelper.thePunchDesktopGray(),
          ),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: screenHeight - 100,
            ),
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Image.asset('images/the_punch_logo_small.png',
                        width: 100, height: 100),
                  ),
                  SizedBox(
                    height: ScreenHelper.screenHeightPercentage(context, 5),
                  ),
                  _Login(),
                  _ForgotPassword(),
                  const Spacer(),
                  _About(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
  DrawerHeader buildHeader(BuildContext context) {
    final onPrimary = Theme.of(context).colorScheme.onPrimary;

    return DrawerHeader(
      decoration: BoxDecoration(color: Theme.of(context).primaryColorDark),
      child: LayoutBuilder(
        builder: (context, constraints) => Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'The Punch',
              style: TextStyle(
                color: onPrimary,
                fontSize: 24,
              ),
            ),
            AspectRatio(
                aspectRatio: 2.6,
                child: Image.asset('images/the_punch_logo_small.png',
                    width: constraints.maxWidth))
          ],
        ),
      ),
    );
  }

// handheld

class _HandheldDrawer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    const onSurface = Color.fromRGBO(70, 166, 244, 1);
    return SizedBox(
      width: ScreenHelper.screenWidth(context),
      child: BackdropFilter(
        filter: ImageFilter.blur(
          sigmaX: 8,
          sigmaY: 8,
        ),
        child: Drawer(
          elevation: 0,
          backgroundColor: Colors.white.withOpacity(0.7),
          child: SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Header with close icon, logo, and profile icon
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: ScreenHelper.screenWidthPercentage(context, 2),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Builder(
                        builder: (context) => Padding(
                          padding: const EdgeInsets.all(8),
                          child: DecoratedBox(
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: ColorHelper.thePunchGray(),
                              ),
                              borderRadius: BorderRadius.circular(15),
                              color: Colors.transparent,
                            ),
                            child: IconButton(
                              icon: Icon(
                                Icons.close,
                                color: ColorHelper.thePunchGray(),
                              ),
                              onPressed: () =>
                                  Scaffold.of(context).closeDrawer(),
                            ),
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          context.go('/clock');
                          Scaffold.of(context).closeDrawer();
                        },
                        child: SizedBox(
                          height: 100,
                          width: 100,
                          child: AspectRatio(
                            aspectRatio: 4.5,
                            child:
                                Image.asset('images/the_punch_logo_small.png'),
                          ),
                        ),
                      ),
                      _ProfileIcon()
                    ],
                  ),
                ),
                const Spacer(),
                if (PermissionsState().viewSchedules ||
                    PermissionsState().viewPunchCards)
                  Padding(
                    padding: EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal:
                          ScreenHelper.screenWidthPercentage(context, 20),
                    ),
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(
                          color: onSurface,
                        ),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: _Agenda(),
                    ),
                  ),
                Expanded(
                  child: Container(),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: ScreenHelper.screenWidthPercentage(context, 20),
                  ),
                  child: _About(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Mobile Profile Icon

// ignore: unused_element
class _PunchedIn extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    if (!PunchState.canPunchIn) return Container();

    final isPunchedIn = context.select<PunchState, bool>((v) => v.isPunchedIn);
    final durationStream =
        context.select<PunchState, Stream<String>>((v) => v.durationStream);
    if (!isPunchedIn) return Container();

    return SizedBox(
      width: ScreenHelper.screenWidthPercentage(context, 7.5),
      child: GestureDetector(
        onTap: () => context.go('/'),
        child: StreamBuilder<String>(
          stream: durationStream,
          builder: (context, snapshot) => Text(snapshot.data ?? ''),
        ),
      ),
    );
  }
}

class _ProfileIcon extends StatelessWidget {
  @override
  Widget build(BuildContext context) => IconButton(
        icon: Icon(
          Icons.account_circle,
          color: Colors.grey,
          size: ScreenHelper.screenHeightPercentage(context, 5.5),
        ),
        tooltip: AppLocalizations.of(context)!.profile,
        onPressed: () => {
          if (ModalRoute.of(context)!.settings.name! == '/profile')
            {Scaffold.of(context).openEndDrawer()}
          else
            {context.go('/profile')}
        },
      );
}

// Desktop

class DrawerStateProvider with ChangeNotifier {
  final List<bool> _expandedPanels;
  String _activeRoute;

  DrawerStateProvider(int panelCount, {String initialRoute = '/'})
      : _expandedPanels = List.filled(panelCount, false),
        _activeRoute = initialRoute;

  List<bool> get expandedPanels => _expandedPanels;
  String get activeRoute => _activeRoute;

  void setActiveRoute(String route) {
    _activeRoute = route;
    notifyListeners();
  }

  void togglePanel(int index) {
    _expandedPanels[index] = !_expandedPanels[index];
    notifyListeners();
  }

  void setPanelState(int index, bool isExpanded) {
    _expandedPanels[index] = isExpanded;
    notifyListeners();
  }
}

class _DesktopDrawer extends StatefulWidget {
  final String urlParams = '?anything=1';

  @override
  _DesktopDrawerState createState() => _DesktopDrawerState();
}

class _DesktopDrawerState extends State<_DesktopDrawer> {
  String? activeParent;
  String? activeChild;

  @override
  Widget build(BuildContext context) {
    _updateActiveState(context);
    final drawerStateProvider = Provider.of<DrawerStateProvider>(context);

    // Create a single ScrollController
    final scrollController = ScrollController();

    return Padding( // Wrap the SizedBox with Padding
  padding: const EdgeInsets.symmetric(
    horizontal: 20,
    vertical: 20,
  ), // Adjust the padding as needed
  child: SizedBox(
    width: 250, // Fixed width for the Drawer
    child: Drawer(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(25)),
      ),
      elevation: 1,
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: ColorHelper.thePunchDarkBlue(),
          // border: const Border(
          //   right: BorderSide(
          //     color: Color.fromRGBO(223, 220, 226, 1),
          //     width: 2,
          //   ),
          // ),
        ),
        child: Stack(
          children: [
            ScrollConfiguration(
              behavior: SubtleScrollBehavior(),
              child: SingleChildScrollView(
                controller: scrollController, // Use the same controller
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 40),
                      child: _buildLogo(),
                    ),
                   // _buildLogo(),
                   Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child:  _buildDrawerContents(context, drawerStateProvider)
                   ),

                  ],
                ),
              ),
            ),
            Positioned(
              right: -50, // Move the scrollbar 50px to the right
              top: 0,
              bottom: 0,
              child: Scrollbar(
                thumbVisibility: true,
                thickness: 8, // Optional: Adjust scrollbar thickness
                controller: scrollController,
                child: Container(), // Same controller
              ),
            ),
          ],
        ),
      ),
    ),
  ),
);
  }

  void _updateActiveState(BuildContext context) {
    final String? currentRoute =
        ModalRoute.of(context)?.settings.name?.split('?').first;

    setState(() {
      switch (currentRoute) {
        case '/':
        case '/home':
        case null:
          activeParent = "Home";
          activeChild = "Home";
          break;
        case '/map':
          activeParent = "Map";
          activeChild = "Map";
          break;

        case '/employees':
        case '/employees/edit':
        case '/employeeTypes':
        case '/employeeTypes/edit':
          activeParent = "Employees";
          activeChild = _getEmployeeChild(currentRoute);
          break;
        case '/locations':
        case '/locations/edit':
          activeParent = "Locations";
          activeChild =
              currentRoute == '/locations' ? 'View Locations' : 'Add Location';
          break;
        case '/contacts':
        case '/contacts/edit':
          activeParent = "Contacts";
          activeChild =
              currentRoute == '/contacts' ? 'View Contacts' : 'Add Contact';
          break;
        case '/schedules':
        case '/schedules/addRepeatingSchedule':
          activeParent = "Schedules";
          activeChild =
              currentRoute == '/schedules' ? 'View Schedules' : 'Add Schedule';
          break;
        case '/punchCards':
        case '/punchCards/edit':
        case '/jobTypes':
        case '/jobTypes/edit':
          activeParent = "Punch Cards";
          activeChild = _getPunchCardChild(currentRoute);
          break;
        case '/reports':
          activeParent = "Reports";
          activeChild = "Reports";
          break;
        default:
          activeParent = null;
          activeChild = null;
          break;
      }
    });
  }

  String _getEmployeeChild(String? route) {
    if (route == '/employees') return 'View Employees';
    if (route == '/employees/edit') return 'Add Employee';
    if (route == '/employeeTypes') return 'View Employee Types';
    return 'Add Employee Type';
  }

  String _getPunchCardChild(String? route) {
    if (route == '/punchCards') return 'View Punch Cards';
    if (route == '/punchCards/edit') return 'Add Punch Card';
    if (route == '/jobTypes') return 'View Job Types';
    return 'Add Job Type';
  }

  Widget _buildDrawerContents(
          BuildContext context, DrawerStateProvider drawerStateProvider) =>
      Column(
        children: [
          _buildNonExpandableMenuItem(context, "Home", ThePunchIcons.home, '/'),
          const SizedBox(height: 10),
          _buildNonExpandableMenuItem(context, "Map", Icons.travel_explore_rounded, '/map'),
          const SizedBox(height: 10),
          _buildMenuExpansionPanel(
            context,
            "Employees",
            ThePunchIcons.face,
            drawerStateProvider.expandedPanels[0],
            () => drawerStateProvider.togglePanel(0),
            [
              _buildMenuItem(
                  context, '/employees', 'View Employees', "Employees"),
              _buildMenuItem(context, '/employees/edit${widget.urlParams}',
                  'Add Employee', "Employees"),
              // const Divider(
              //     indent: 20, endIndent: 20, thickness: 1, color: Colors.grey),
              // _buildMenuItem(context, '/employeeTypes', 'View Employee Types',
              //     "Employees"),
              //_buildMenuItem(context, '/employeeTypes/edit${widget.urlParams}',
                  //'Add Employee Type', "Employees"),
            ],
          ),
          const SizedBox(height: 10),
          _buildMenuExpansionPanel(
            context,
            "Locations",
            ThePunchIcons.personPinCircle,
            drawerStateProvider.expandedPanels[1],
            () => drawerStateProvider.togglePanel(1),
            [
              _buildMenuItem(
                  context, '/locations', 'View Locations', "Locations"),
              _buildMenuItem(context, '/locations/edit${widget.urlParams}',
                  'Add Location', "Locations"),
            ],
          ),
          const SizedBox(height: 10),
          _buildMenuExpansionPanel(
            context,
            "Contacts",
            Icons.contact_page,
            drawerStateProvider.expandedPanels[2],
            () => drawerStateProvider.togglePanel(2),
            [
              _buildMenuItem(context, '/contacts', 'View Contacts', "Contacts"),
              _buildMenuItem(context, '/contacts/edit${widget.urlParams}',
                  'Add Contact', "Contacts"),
            ],
          ),
          const SizedBox(height: 10),
          _buildMenuExpansionPanel(
            context,
            "Schedules",
            Icons.schedule,
            drawerStateProvider.expandedPanels[3],
            () => drawerStateProvider.togglePanel(3),
            [
              _buildMenuItem(
                  context, '/schedules', 'View Schedules', "Schedules"),
              _buildMenuItem(
                  context,
                  '/schedules/addRepeatingSchedule${widget.urlParams}',
                  'Add Schedule',
                  "Schedules"),
            ],
          ),
          const SizedBox(height: 10),
          _buildMenuExpansionPanel(
            context,
            "Punch Cards",
            ThePunchIcons.punchClock,
            drawerStateProvider.expandedPanels[4],
            () => drawerStateProvider.togglePanel(4),
            [
              _buildMenuItem(
                  context, '/punchCards', 'View Punch Cards', "Punch Cards"),
              _buildMenuItem(context, '/punchCards/edit${widget.urlParams}',
                  'Add Punch Card', "Punch Cards"),
              // const Divider(
              //     indent: 20, endIndent: 20, thickness: 1, color: Colors.grey),
              // _buildMenuItem(
              //     context, '/jobTypes', 'View Job Types', "Punch Cards"),
              // _buildMenuItem(context, '/jobTypes/edit${widget.urlParams}',
              //     'Add Job Type', "Punch Cards"),
            ],
          ),
          const SizedBox(height: 10),
          _buildNonExpandableMenuItem(
              context, "Reports", Icons.pie_chart, '/reports'),
          // _NewSandboxMenu(),
        ],
      );

  Widget _buildLogo() => Center(
      child: Padding(
        padding: EdgeInsets.only(right: 20),
        child: Image.asset('images/the_punch_logo_small.png',
            width: 120, height: 120
      ),
    )
  );

  Widget _buildNonExpandableMenuItem(
      BuildContext context, String title, IconData icon, String route) {
    bool isActive = activeParent == title && activeChild == title;
    return _buildMenuItemContainer(
      context,
      title,
      icon,
      route,
      isActive: isActive,
      onTap: () {
        setState(() {
          activeParent = title;
          activeChild = title;
        });
        context.go(route);
      },
    );
  }

  Widget _buildMenuExpansionPanel(
    BuildContext context,
    String title,
    IconData icon,
    bool isExpanded,
    Function onExpansionCallback,
    List<Widget> items,
  ) {
    // Check if the current parent is active
    bool isActiveParent = activeParent == title;

    return ExpansionPanelList(
      elevation: 0,
      expandedHeaderPadding: EdgeInsets.zero,
      animationDuration: const Duration(milliseconds: 600),
      expansionCallback: (int index, bool expanded) {
        onExpansionCallback();
        setState(() {
          activeParent = title; // Set the active parent to the clicked one
        });
      },
      children: [
        ExpansionPanel(
          canTapOnHeader: true,
          headerBuilder: (_, isExpanded) => Padding(
            padding: const EdgeInsets.only(right: 0), // Add padding if needed
            child: ListTile(
              leading: Icon(
                icon,
                color: !isActiveParent
                    ? ColorHelper.thePunchGray() // Turns red when active
                    : Colors.white, // Default gray
              ),
              title: Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  color: !isActiveParent
                      ? ColorHelper.thePunchGray() // Active route color (red)
                      : Colors.white, // Default route color (gray)
                ),
                softWrap: false, // Prevents wrapping
                overflow: TextOverflow.visible, // Forces full visibility
              ),
              tileColor: Colors.transparent,
            ),
          ),
          body: Column(children: items),
          isExpanded: isExpanded,
          backgroundColor: Colors.transparent,
        ),
      ],
    );
  }

  Widget _buildMenuItemContainer(
          BuildContext context, String title, IconData icon, String? route,
          {required bool isActive, required Function() onTap}) =>
      Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        child: MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: onTap,
            child: Row(
              children: [
                Icon(icon,
                    color: !isActive
                        ? ColorHelper.thePunchGray()
                        : Colors.white),
                const SizedBox(width: 16),
                Expanded(
                  // Ensures the text doesn't overflow the container
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                          color: !isActive
                              ? ColorHelper.thePunchGray()
                              : Colors.white,
                        ),
                    softWrap: false, // Prevents wrapping
                    overflow:
                        TextOverflow.ellipsis, // Adds ellipsis on overflow
                  ),
                ),
              ],
            ),
          ),
        ),
      );

  Widget _buildMenuItem(
      BuildContext context, String route, String title, String parent) {
    bool isActive = activeChild == title && activeParent == parent;
    return Padding(
      padding: const EdgeInsets.only(left: 20.0),
      child: ListTile(
        onTap: () {
          setState(() {
            activeParent = parent;
            activeChild = title;
          });
          context.go(route);
        },
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 12,
            color: !isActive
                ? ColorHelper.thePunchGray()
                : Colors.white,
          ),
          softWrap: false, // Prevents wrapping
          overflow: TextOverflow.ellipsis, // Adds ellipsis on overflow
        ),
        tileColor: Colors.transparent,
      ),
    );
  }
}

// class _DesktopDrawTile extends StatelessWidget {
//   final Widget? leading;
//   final Widget title;

//   const _DesktopDrawerTile({this.leading, required this.title});

//   @override
//   Widget build(BuildContext context) => Material(
//     child: ListTile(
//       tileColor: ColorHelper.thePunchDesktopGray(),
//       leading: leading ?? const SizedBox(width: 8, height: 25),
//       title: title,
//     ),
//   );
// }

class SubtleScrollBehavior extends ScrollBehavior {
  @override
  Widget buildViewportChrome(
      BuildContext context, Widget child, AxisDirection axisDirection) {
    return child; // Removes default overscroll indicator
  }

  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    return const BouncingScrollPhysics(); // Adds subtle bounce effect (optional)
  }
}

class _Agenda extends StatelessWidget {
  @override
  Widget build(BuildContext context) => ListTile(
        title: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.event_available_outlined,
              color: ColorHelper.thePunchBlue(),
            ),
            SizedBox(
              width: ScreenHelper.screenWidthPercentage(context, 2),
            ),
            Text(
              AppLocalizations.of(context)!.agenda,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: ColorHelper.thePunchBlue(),
                    fontWeight: FontWeight.w800,
                  ),
            ),
          ],
        ),
        onTap: () => context.go('/agenda'),
      );
}

class _About extends StatelessWidget {
  @override
  Material build(BuildContext context) => Material(
        color: Colors.transparent,
        child: FutureBuilder<PackageInfo>(
          future: Future(PackageInfo.fromPlatform),
          builder: (context, snapshot) => DecoratedBox(
            decoration: BoxDecoration(
              border: Border.all(
                width: 1,
                color: ColorHelper.thePunchBlue(),
              ),
              borderRadius: BorderRadius.circular(15),
              color: Colors.white,
            ),
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Center(
                child: MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () {
                      showAboutDialog(
                        context: context,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Image.asset('images/the_punch_logo_small.png',
                                  scale: 5),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    'The Punch',
                                    style:
                                        Theme.of(context).textTheme.titleLarge,
                                  ),
                                  SizedBox(
                                    height: ScreenHelper.screenHeightPercentage(
                                        context, 1),
                                  ),
                                  Text(
                                    snapshot.data!.version,
                                    style:
                                        Theme.of(context).textTheme.titleMedium,
                                  ),
                                  SizedBox(
                                    height: ScreenHelper.screenHeightPercentage(
                                        context, 1),
                                  ),
                                  SizedBox(
                                    width: ScreenHelper.screenWidthPercentage(
                                        context, 40),
                                    child: Text(
                                      '\u{a9} 2023 Morris Consulting Service',
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleMedium,
                                      overflow: TextOverflow.visible,
                                      textAlign: TextAlign.end,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          )
                        ],
                      );
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.info,
                          color: ColorHelper.thePunchBlue(),
                        ),
                        const SizedBox(
                          width: 20,
                        ),
                        Text(
                          'About The Punch',
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium
                              ?.copyWith(
                                  color: ColorHelper.thePunchBlue(),
                                  fontWeight: FontWeight.w800),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      );
}

class _DesktopDrawerTile extends StatelessWidget {
  final Widget? leading;
  final Widget title;

  const _DesktopDrawerTile({this.leading, required this.title});

  @override
  Widget build(BuildContext context) => Material(
        child: ListTile(
          tileColor: ColorHelper.thePunchDesktopGray(),
          leading: leading ?? const SizedBox(width: 8, height: 25),
          title: title,
        ),
      );
}

class _Home extends StatelessWidget {
  @override
  Padding build(BuildContext context) => Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        child: MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              if (Scaffold.of(context).hasDrawer) Navigator.of(context).pop();
              context.go('/');
            },
            child: Row(
              children: [
                Icon(
                  ThePunchIcons.pieChart,
                  color: ModalRoute.of(context)!.settings.name!.contains('home')
                      ? ColorHelper.thePunchRed()
                      : ColorHelper.thePunchGray(),
                ),
                const SizedBox(
                  width: 16,
                ),
                Text(
                  AppLocalizations.of(context)!.reports,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: ModalRoute.of(context)!
                                .settings
                                .name!
                                .contains('home')
                            ? ColorHelper.thePunchRed()
                            : ColorHelper.thePunchGray(),
                      ),
                ),
              ],
            ),
          ),
        ),
      );
}

class _Login extends StatelessWidget {
  @override
  Padding build(BuildContext context) {
    const onSurface = Color.fromRGBO(70, 166, 244, 1);

    return Padding(
      padding: const EdgeInsets.only(bottom: 5),
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(
            color: onSurface,
          ),
          borderRadius: BorderRadius.circular(15),
        ),
        child: ListTile(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.login,
                color: ColorHelper.thePunchBlue(),
              ),
              const SizedBox(
                width: 20,
              ),
              Text(
                AppLocalizations.of(context)!.login,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: ColorHelper.thePunchBlue(),
                    fontWeight: FontWeight.w800),
              ),
            ],
          ),
          onTap: () {
            if (Scaffold.of(context).hasDrawer) Navigator.of(context).pop();
            context.go('/login');
          },
        ),
      ),
    );
  }
}

class _ForgotPassword extends StatelessWidget {
  @override
  Padding build(BuildContext context) {
    const onSurface = Color.fromRGBO(70, 166, 244, 1);

    return Padding(
      padding: const EdgeInsets.only(bottom: 5),
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(
            color: onSurface,
          ),
          borderRadius: BorderRadius.circular(15),
        ),
        child: ListTile(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.password_rounded,
                color: ColorHelper.thePunchBlue(),
              ),
              const SizedBox(
                width: 20,
              ),
              Flexible(
                // Wrap the Text widget with Flexible
                child: Text(
                  AppLocalizations.of(context)!.forgotLogin,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: ColorHelper.thePunchBlue(),
                      fontWeight: FontWeight.w800),
                  overflow:
                      TextOverflow.ellipsis, // Add ellipsis to handle overflow
                ),
              ),
            ],
          ),
          onTap: () {
            if (Scaffold.of(context).hasDrawer) Navigator.of(context).pop();
            context.go('/forgotLogin');
          },
        ),
      ),
    );
  }
}

class _Reports extends StatelessWidget {
  @override
  Padding build(BuildContext context) => Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        child: MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              if (Scaffold.of(context).hasDrawer) Navigator.of(context).pop();
              context.go('/reports');
            },
            child: Row(
              children: [
                Icon(
                  ThePunchIcons.pieChart,
                  color:
                      ModalRoute.of(context)!.settings.name!.contains('reports')
                          ? ColorHelper.thePunchRed()
                          : ColorHelper.thePunchGray(),
                ),
                const SizedBox(
                  width: 16,
                ),
                Text(
                  AppLocalizations.of(context)!.reports,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: ModalRoute.of(context)!
                                .settings
                                .name!
                                .contains('reports')
                            ? ColorHelper.thePunchRed()
                            : ColorHelper.thePunchGray(),
                      ),
                ),
              ],
            ),
          ),
        ),
      );
}

// ignore: unused_element
class _Profile extends StatelessWidget {
  final onSurface = const Color.fromRGBO(70, 166, 244, 1);
  @override
  Widget build(BuildContext context) {
    Theme.of(context);
    return ListTile(
      title: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_circle,
            color: ColorHelper.thePunchBlue(),
          ),
          SizedBox(
            width: ScreenHelper.screenWidthPercentage(context, 2),
          ),
          ValueListenableBuilder<User>(
              valueListenable: LoginState.userNotifier,
              builder: (context, employee, _) => Text(
                    employee.name,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: ColorHelper.thePunchBlue(),
                        fontWeight: FontWeight.w800),
                  )),
        ],
      ),
      onTap: () => context.go('/profile'),
    );
  }
}

// ignore: unused_element
class _Profilexpto extends StatelessWidget {
  @override
  Padding build(BuildContext context) {
    const onSurface = Color.fromRGBO(70, 166, 244, 1);

    return Padding(
      padding: const EdgeInsets.only(bottom: 5),
      child: Material(
        color: Theme.of(context).primaryColorDark,
        surfaceTintColor: const Color.fromARGB(255, 94, 94, 94),
        child: ListTile(
          leading: const Icon(Icons.account_circle, color: onSurface),
          title: ValueListenableBuilder<User>(
              valueListenable: LoginState.userNotifier,
              builder: (context, employee, _) => Text(employee.name,
                  style: const TextStyle(
                      color: onSurface, fontWeight: FontWeight.bold))),
          onTap: () => context.go('/profile'),
        ),
      ),
    );
  }
}

class _NewEmployeesMenu extends StatefulWidget {
  const _NewEmployeesMenu();
  @override
  _NewEmployeesDrop createState() => _NewEmployeesDrop();
}

class _NewEmployeesDrop extends State<_NewEmployeesMenu> {
  final List<Map<String, dynamic>> _items = List.generate(
      1,
      (index) => {
            'id': index,
            'isExpanded': false,
          });

  @override
  Widget build(BuildContext context) => Theme(
        data: Theme.of(context).copyWith(
          cardColor: ColorHelper.thePunchDesktopGray(),
        ),
        child: SingleChildScrollView(
          child: ExpansionPanelList(
            elevation: 0,
            expandedHeaderPadding: EdgeInsets.zero,
            expansionCallback: (index, isExpanded) {
              setState(() {
                _items[index]['isExpanded'] = isExpanded;
              });
            },
            animationDuration: const Duration(milliseconds: 600),
            children: _items
                .map(
                  (item) => ExpansionPanel(
                    canTapOnHeader: true,
                    headerBuilder: (_, isExpanded) => Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 7),
                          child: Text(
                            AppLocalization.of(context).employees,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: ModalRoute.of(context)!
                                          .settings
                                          .name!
                                          .contains('employees') ||
                                      ModalRoute.of(context)!
                                          .settings
                                          .name!
                                          .contains('employeeTypes')
                                  ? ColorHelper.thePunchRed()
                                  : Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                    body: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ListTile(
                          onTap: () => context.go('/employees'),
                          title: Text(
                            AppLocalization.of(context).viewEmployees,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                        ListTile(
                          onTap: () async =>
                              context.push('/employees/edit?anything=1'),
                          title: Text(
                            AppLocalization.of(context).addEmployee,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                        const PopupMenuDivider(),
                        ListTile(
                          onTap: () => context.go('/employeeTypes'),
                          title: Text(
                            AppLocalization.of(context).viewEmployeeTypes,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                        ListTile(
                          onTap: () =>
                              context.go('/employeeTypes/edit?anything=1'),
                          title: Text(
                            AppLocalization.of(context).addEmployeeType,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                      ],
                    ),
                    isExpanded: item['isExpanded'],
                  ),
                )
                .toList(),
          ),
        ),
      );
}

class _NewLocationsMenu extends StatefulWidget {
  const _NewLocationsMenu();
  @override
  _NewLocationsDrop createState() => _NewLocationsDrop();
}

class _NewLocationsDrop extends State<_NewLocationsMenu> {
  final List<Map<String, dynamic>> _items = List.generate(
      1,
      (index) => {
            'id': index,
            'isExpanded': false,
          });

  @override
  Widget build(BuildContext context) => Theme(
        data: Theme.of(context).copyWith(
          cardColor: ColorHelper.thePunchDesktopGray(),
        ),
        child: SingleChildScrollView(
          child: ExpansionPanelList(
            elevation: 0,
            expandedHeaderPadding: EdgeInsets.zero,
            expansionCallback: (index, isExpanded) {
              setState(() {
                _items[index]['isExpanded'] = isExpanded;
              });
            },
            animationDuration: const Duration(milliseconds: 600),
            children: _items
                .map(
                  (item) => ExpansionPanel(
                    canTapOnHeader: true,
                    headerBuilder: (_, isExpanded) => Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 7),
                          child: Text(
                            AppLocalization.of(context).locations,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: ModalRoute.of(context)!
                                      .settings
                                      .name!
                                      .contains('locations')
                                  ? ColorHelper.thePunchRed()
                                  : Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                    body: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ListTile(
                          onTap: () => context.go('/locations'),
                          title: Text(
                            AppLocalization.of(context).viewLocations,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                        ListTile(
                          onTap: () => context.go('/locations/edit?anything=1'),
                          title: Text(
                            AppLocalization.of(context).addLocation,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                      ],
                    ),
                    isExpanded: item['isExpanded'],
                  ),
                )
                .toList(),
          ),
        ),
      );
}

class _NewContactsMenu extends StatefulWidget {
  const _NewContactsMenu();
  @override
  _NewContactssDrop createState() => _NewContactssDrop();
}

class _NewContactssDrop extends State<_NewContactsMenu> {
  final List<Map<String, dynamic>> _items = List.generate(
      1,
      (index) => {
            'id': index,
            'isExpanded': false,
          });

  @override
  Widget build(BuildContext context) => Theme(
        data: Theme.of(context).copyWith(
          cardColor: ColorHelper.thePunchDesktopGray(),
        ),
        child: SingleChildScrollView(
          child: ExpansionPanelList(
            elevation: 0,
            expandedHeaderPadding: EdgeInsets.zero,
            expansionCallback: (index, isExpanded) {
              setState(() {
                _items[index]['isExpanded'] = isExpanded;
              });
            },
            animationDuration: const Duration(milliseconds: 600),
            children: _items
                .map(
                  (item) => ExpansionPanel(
                    canTapOnHeader: true,
                    headerBuilder: (_, isExpanded) => Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 7),
                          child: Text(
                            AppLocalization.of(context).contacts,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: ModalRoute.of(context)!
                                      .settings
                                      .name!
                                      .contains('contacts')
                                  ? ColorHelper.thePunchRed()
                                  : Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                    body: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ListTile(
                          onTap: () => context.go('/contacts'),
                          title: Text(
                            AppLocalization.of(context).viewContacts,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                        ListTile(
                          onTap: () {
                            if (Scaffold.of(context).hasDrawer) {
                              Navigator.of(context).pop();
                            }
                            context.go('/contacts/edit?anything=1');
                          },
                          title: Text(
                            AppLocalization.of(context).addContact,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                      ],
                    ),
                    isExpanded: item['isExpanded'],
                  ),
                )
                .toList(),
          ),
        ),
      );
}

class _NewSchedulesMenu extends StatefulWidget {
  const _NewSchedulesMenu();
  @override
  _NewSchedulesDrop createState() => _NewSchedulesDrop();
}

class _NewSchedulesDrop extends State<_NewSchedulesMenu> {
  final List<Map<String, dynamic>> _items = List.generate(
      1,
      (index) => {
            'id': index,
            'isExpanded': false,
          });

  @override
  Widget build(BuildContext context) => Theme(
        data: Theme.of(context).copyWith(
          cardColor: ColorHelper.thePunchDesktopGray(),
        ),
        child: SingleChildScrollView(
          child: ExpansionPanelList(
            elevation: 0,
            expandedHeaderPadding: EdgeInsets.zero,
            expansionCallback: (index, isExpanded) {
              setState(() {
                _items[index]['isExpanded'] = isExpanded;
              });
            },
            animationDuration: const Duration(milliseconds: 600),
            children: _items
                .map(
                  (item) => ExpansionPanel(
                    canTapOnHeader: true,
                    headerBuilder: (_, isExpanded) => Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 7),
                          child: Text(
                            AppLocalization.of(context).schedule,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: (ModalRoute.of(context)!.settings.name! ==
                                          '/schedules') ||
                                      (ModalRoute.of(context)!.settings.name! ==
                                          '/schedules/addRepeatingSchedule')
                                  ? ColorHelper.thePunchRed()
                                  : ColorHelper.thePunchGray(),
                            ),
                          ),
                        ),
                      ],
                    ),
                    body: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ListTile(
                          onTap: () => context.go('/schedules'),
                          title: Text(
                            AppLocalization.of(context).viewSchedule,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                        ListTile(
                          onTap: () =>
                              context.go('/schedules/addRepeatingSchedule'),
                          title: Text(
                            AppLocalization.of(context).addSchedule,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                      ],
                    ),
                    isExpanded: item['isExpanded'],
                  ),
                )
                .toList(),
          ),
        ),
      );
}

class _NewPunchCardsMenu extends StatefulWidget {
  const _NewPunchCardsMenu();
  @override
  _NewPunchCardsDrop createState() => _NewPunchCardsDrop();
}

class _NewPunchCardsDrop extends State<_NewPunchCardsMenu> {
  final List<Map<String, dynamic>> _items = List.generate(
    1,
    (index) => {'id': index, 'isExpanded': false},
  );

  @override
  Widget build(BuildContext context) => Theme(
        data: Theme.of(context).copyWith(
          cardColor: Colors.transparent, // Set card background to transparent
        ),
        child: SingleChildScrollView(
          child: ExpansionPanelList(
            elevation: 0,
            expandedHeaderPadding: EdgeInsets.zero,
            expansionCallback: (index, isExpanded) {
              setState(() {
                _items[index]['isExpanded'] = isExpanded;
              });
            },
            animationDuration: const Duration(milliseconds: 600),
            children: _items
                .map(
                  (item) => ExpansionPanel(
                    canTapOnHeader: true,
                    headerBuilder: (_, isExpanded) => Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 7),
                          child: Text(
                            AppLocalization.of(context).punchCard,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: ModalRoute.of(context)!
                                      .settings
                                      .name!
                                      .contains('punchCards')
                                  ? ColorHelper.thePunchRed()
                                  : ColorHelper.thePunchGray(),
                            ),
                          ),
                        ),
                      ],
                    ),
                    body: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ListTile(
                          onTap: () => context.go('/punchCards'),
                          title: Text(
                            AppLocalization.of(context).viewPunchCards,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                        ListTile(
                          onTap: () async => context.pushNamed(
                              '/punchCards/edit',
                              queryParameters: {'anything': '1'}),
                          title: Text(
                            AppLocalization.of(context).addPunchCard,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                        const Divider(
                          thickness: 1,
                          color: Colors
                              .grey, // Divider between punch card and job types
                        ),
                        ListTile(
                          onTap: () => context.go('/jobTypes'),
                          title: Text(
                            AppLocalization.of(context).viewJobTypes,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                        ListTile(
                          onTap: () async => context.pushNamed('/jobTypes/edit',
                              queryParameters: {'anything': '1'}),
                          title: Text(
                            AppLocalization.of(context).addJobType,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                      ],
                    ),
                    isExpanded: item['isExpanded'],
                  ),
                )
                .toList(),
          ),
        ),
      );
}

class _NewInspectionsMobileMenu extends StatefulWidget {
  const _NewInspectionsMobileMenu();
  @override
  _NewInspectionsMobileDrop createState() => _NewInspectionsMobileDrop();
}

class _NewInspectionsMobileDrop extends State<_NewInspectionsMobileMenu> {
  final List<Map<String, dynamic>> _items = List.generate(
      1,
      (index) => {
            'id': index,
            'isExpanded': false,
          });
  @override
  Widget build(BuildContext context) => Theme(
        data: Theme.of(context).copyWith(
          cardColor: ColorHelper.thePunchDesktopGray(),
        ),
        child: SingleChildScrollView(
          child: ExpansionPanelList(
            elevation: 0,
            expandedHeaderPadding: EdgeInsets.zero,
            expansionCallback: (index, isExpanded) {
              setState(() {
                _items[index]['isExpanded'] = isExpanded;
              });
            },
            animationDuration: const Duration(milliseconds: 600),
            children: _items
                .map(
                  (item) => ExpansionPanel(
                    canTapOnHeader: true,
                    headerBuilder: (context, isExpanded) => Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 7),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              const Icon(
                                Icons.content_paste_search,
                              ),
                              SizedBox(
                                width: ScreenHelper.screenWidthPercentage(
                                    context, 2),
                              ),
                              Text(
                                AppLocalization.of(context).inspections,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    body: Column(
                      // mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ListTile(
                          onTap: () => context.go('/inspections'),
                          title: Text(
                            AppLocalizations.of(context)!.viewInspections,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                        ListTile(
                          onTap: () async => {
                            await showDialog(
                              context: context,
                              builder: (context) => CreateInspectionDialog(
                                inspectionCreated: (id, newInspection) {
                                  Navigator.pop(context);
                                  context.pushNamed('/inspections/edit',
                                      queryParameters: {
                                        'id': id,
                                        'anything': newInspection
                                      });
                                },
                              ),
                            )
                          },
                          title: Text(
                            AppLocalizations.of(context)!.addInspections,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                      ],
                    ),
                    isExpanded: item['isExpanded'],
                  ),
                )
                .toList(),
          ),
        ),
      );
}

class _NewInspectionsMenu extends StatefulWidget {
  const _NewInspectionsMenu();
  @override
  _NewInspectionsDrop createState() => _NewInspectionsDrop();
}

class _NewInspectionsDrop extends State<_NewInspectionsMenu> {
  final List<Map<String, dynamic>> _items = List.generate(
      1,
      (index) => {
            'id': index,
            'isExpanded': false,
          });

  @override
  Widget build(BuildContext context) => Theme(
        data: Theme.of(context).copyWith(
          cardColor: ColorHelper.thePunchDesktopGray(),
        ),
        child: SingleChildScrollView(
          child: ExpansionPanelList(
            elevation: 0,
            expandedHeaderPadding: EdgeInsets.zero,
            expansionCallback: (index, isExpanded) {
              setState(() {
                _items[index]['isExpanded'] = isExpanded;
              });
            },
            animationDuration: const Duration(milliseconds: 600),
            children: _items
                .map(
                  (item) => ExpansionPanel(
                    canTapOnHeader: true,
                    headerBuilder: (_, isExpanded) => Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 7),
                          child: Text(
                            AppLocalization.of(context).inspections,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: ModalRoute.of(context)!
                                          .settings
                                          .name!
                                          .contains('inspections') ||
                                      ModalRoute.of(context)!
                                          .settings
                                          .name!
                                          .contains('inspectionTemplates')
                                  ? ColorHelper.thePunchRed()
                                  : ColorHelper.thePunchGray(),
                            ),
                          ),
                        ),
                      ],
                    ),
                    body: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ListTile(
                          onTap: () => context.go('/inspections'),
                          title: Text(
                            AppLocalizations.of(context)!.viewInspections,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                        ListTile(
                          onTap: () => unawaited(showDialog(
                              context: context,
                              builder: (context) => CreateInspectionDialog(
                                      inspectionCreated: (id, newInspection) {
                                    Navigator.pop(context);
                                    context.pushNamed('/inspections/edit',
                                        queryParameters: {
                                          'id': id,
                                          'anything': newInspection
                                        });
                                  }))),
                          title: Text(
                            AppLocalizations.of(context)!.addInspections,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                        const PopupMenuDivider(),
                        ListTile(
                          onTap: () => context.go('/inspectionTemplates'),
                          title: Text(
                            AppLocalizations.of(context)!
                                .viewInspectionTemplates,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                        ListTile(
                          onTap: () async {
                            if (Scaffold.of(context).hasDrawer) {
                              Navigator.of(context).pop();
                            }
                            await context.pushNamed('/inspectionTemplates/edit',
                                queryParameters: {'anything': '1'});
                          },
                          title: Text(
                            AppLocalizations.of(context)!
                                .addInspectionTemplates,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        )
                      ],
                    ),
                    isExpanded: item['isExpanded'],
                  ),
                )
                .toList(),
          ),
        ),
      );
}

class _NewSandboxMenu extends StatefulWidget {
  const _NewSandboxMenu();
  @override
  _NewSandboxDrop createState() => _NewSandboxDrop();
}

class _NewSandboxDrop extends State<_NewSandboxMenu> {
  final List<Map<String, dynamic>> _items = List.generate(
    1,
    (index) => {'id': index, 'isExpanded': false},
  );

  @override
  Widget build(BuildContext context) => Theme(
        data: Theme.of(context).copyWith(
          cardColor: Colors.transparent, // Set card background to transparent
        ),
        child: SingleChildScrollView(
          child: ExpansionPanelList(
            elevation: 0,
            expandedHeaderPadding: EdgeInsets.zero,
            expansionCallback: (index, isExpanded) {
              setState(() {
                _items[index]['isExpanded'] = isExpanded;
              });
            },
            animationDuration: const Duration(milliseconds: 600),
            children: _items
                .map(
                  (item) => ExpansionPanel(
                    canTapOnHeader: true,
                    headerBuilder: (_, isExpanded) => Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 7),
                          child: Text(
                            'Sandbox',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green, // Set the color to green
                            ),
                          ),
                        ),
                      ],
                    ),
                    body: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ListTile(
                          onTap: () => unawaited(
                              SyncModel().sync(lastSynced: DateTime.utc(1970))),
                          title: const Text('Sync'),
                        ),
                        ListTile(
                          onTap: () => unawaited(SyncModel().pushAll()),
                          title: const Text('Push All'),
                        ),
                        ListTile(
                          onTap: () => context.go('/clock'),
                          title: const Text('Clock Page'),
                        ),
                        ListTile(
                          onTap: () => context.go('/sandbox/timeTests'),
                          title: const Text('Time Tests'),
                        ),
                        ListTile(
                          onTap: () =>
                              context.go('/sandbox/uploadLocationsCSV'),
                          title: const Text('Upload Locations CSV'),
                        ),
                        ListTile(
                          onTap: () {
                            try {
                              throw ApiException('TestErrorCode');
                            } on ApiException catch (e, stack) {
                              unawaited(logApiException(e, stack));
                            } on Exception catch (e, stack) {
                              unawaited(logException(e, stack));
                            }
                          },
                          title: const Text('Soft Crash'),
                        ),
                        ListTile(
                          onTap: () => FirebaseCrashlytics.instance.crash(),
                          title: const Text('Hard Crash'),
                        ),
                        const ListTile(
                          onTap: Exception.new,
                          title: Text('Unhandled Exception'),
                        ),
                        ListTile(
                          onTap: () => context.go('/new-contacts'),
                          title: const Text('New Contacts Page'),
                        ),
                      ],
                    ),
                    isExpanded: item['isExpanded'],
                  ),
                )
                .toList(),
          ),
        ),
      );
}

class _EmployeeName extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onSurface = theme.colorScheme.onSurface;
    return AlignedPopupMenuButton<int>(
      onSelected: (value) {
        switch (value) {
          case 1:
            context.go('/profile');
            break;
          case 2:
            context.go('/updatePassword');
            break;
          case 3:
            unawaited(showDialog(
                context: context, builder: (context) => const LogoutDialog()));
            break;
        }
      },
      itemBuilder: (context) => [
        PopupMenuLabel(
            child: ValueListenableBuilder<User>(
                valueListenable: LoginState.userNotifier,
                builder: (context, employee, _) {
                  final employeeName = employee.name;
                  final employeeEmail = employee.emailAddress ?? '';
                  return Row(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(10),
                        child: Text('$employeeName\n[$employeeEmail]'),
                      ),
                      Icon(Icons.account_circle, color: onSurface),
                    ],
                  );
                })),
        PopupMenuItem<int>(
            value: 1, child: Text(AppLocalization.of(context).profile)),
        PopupMenuItem<int>(
            value: 2, child: Text(AppLocalization.of(context).updatePassword)),
        const PopupMenuDivider(),
        PopupMenuItem<int>(
            value: 3,
            child: Text(AppLocalization.of(context).logout,
                style: const TextStyle(
                    color: Colors.white, fontWeight: FontWeight.w700))),
      ],
      child: Row(
        children: [
          const Icon(Icons.account_circle, color: Colors.white),
          ValueListenableBuilder<User>(
              valueListenable: LoginState.userNotifier,
              builder: (context, employee, _) => Padding(
                    padding: const EdgeInsets.all(8),
                    child: Text(employee.name,
                        style: const TextStyle(color: Colors.white)),
                  )),
        ],
      ),
    );
  }
}
