import 'dart:async';

import 'package:async/async.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../api/api_model.dart';
import '../../api/requests/registration.dart';
import '../../dataModel/data/language.dart';
import '../../dialogs/busy_dialog.dart';
import '../../dialogs/error_dialog.dart';
import '../../misc/app_localization.dart';
import '../../misc/logging.dart';
import '../../misc/my_platform.dart';
import '../../widgets/decorated_text_field.dart';
import '../../widgets/padded_card.dart';
import '../../misc/extensions.dart';
import '../../widgets/captcha/grecaptcha2_stub.dart'
    if (dart.library.html) 'package:the_punch_flutter/widgets/captcha/grecaptcha2.dart'
    as grecaptcha2;
import '../web/my_scaffold.dart';

class RegisterPage extends StatelessWidget {
  const RegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    print('RegisterPage build called');
    return MyScaffold(
      enableBottomBar: false,
      showLoggedOutDrawer: true,
      showDesktopHeader: false,
      title: AppLocalization.of(context).register,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: _Body(),
        ),
      ),
    );
  }
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    print('_Body build called');
    final theme = Theme.of(context);
    return ChangeNotifierProvider<_ViewModel>(
      create: (context) {
        print('_ViewModel created.');
        return _ViewModel();
      },
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 1300),
        child: ListView(
          shrinkWrap: true,
          children: [
            Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'Register New Organization',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            FocusScope(
              child: _Section(
                title: AppLocalization.of(context).contactInformation,
                child: _Row1(),
              ),
            ),
            const SizedBox(height: 16),
            FocusScope(
              child: _Section(
                title: AppLocalization.of(context).accountInformation,
                child: _Row2(),
              ),
            ),
            const SizedBox(height: 16),
            FocusScope(
              child: _Section(
                title: AppLocalization.of(context).personalInformation,
                child: _Row3(),
              ),
            ),
            const SizedBox(height: 16),
            FocusScope(
              child: _Section(
                title: AppLocalization.of(context).language,
                child: _Row4(),
              ),
            ),
            const SizedBox(height: 16),
            FocusScope(
              child: _Section(
                title: AppLocalization.of(context).addressInformation,
                child: _Row5(),
              ),
            ),
            const SizedBox(height: 16),
            FocusScope(
              child: _Section(
                title: AppLocalization.of(context).additionalAddressInformation,
                child: _Row6(),
              ),
            ),
            const SizedBox(height: 16),
            _Captcha(),
            const SizedBox(height: 16),
            _RegisterButton(),
          ],
        ),
      ),
    );
  }
}

class _Section extends StatelessWidget {
  final String title;
  final Widget child;

  const _Section({required this.title, required this.child, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    print('_Section($title) build called');
    final isMobile = MediaQuery.of(context).size.width < 600;
    return Container(
      decoration: BoxDecoration(
        color: const Color.fromARGB(255, 247, 247, 247),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 2,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w700,
                  fontSize: 18,
                  color: Colors.red,
                ),
          ),
          const SizedBox(height: 16),
          isMobile
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [child],
                )
              : child,
        ],
      ),
    );
  }
}

class _Row1 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    print('_Row1 build called');
    return Consumer<_ViewModel>(
      builder: (context, viewModel, _) {
        final isMobile = MediaQuery.of(context).size.width < 600;
        return isMobile
            ? Column(
                children: [
                  _buildEmailField(context, viewModel),
                  const SizedBox(height: 16),
                  _buildOrganizationNameField(context, viewModel),
                ],
              )
            : Row(
                children: [
                  Expanded(child: _buildEmailField(context, viewModel)),
                  const SizedBox(width: 16),
                  Expanded(child: _buildOrganizationNameField(context, viewModel)),
                ],
              );
      },
    );
  }

  Widget _buildEmailField(BuildContext context, _ViewModel viewModel) {
    print('_Row1._buildEmailField');
    return DecoratedTextField(
      padding: const EdgeInsets.all(8),
      initialValue: viewModel.registration.emailAddress,
      labelText: AppLocalization.of(context).mainEmailAddress,
      onChanged: (value) => unawaited(viewModel.setEmailAddress(value)),
      validator: (value) {
        if (viewModel.registration.emailAddress.isEmpty) {
          return AppLocalization.of(context).mainEmailAddressRequired;
        } else if (viewModel.emailExists) {
          return AppLocalization.of(context).emailAddressAlreadyRegistered;
        } else if (!viewModel.validateEmailAddress) {
          return 'Invalid email address.';
        } else {
          return ' ';
        }
      },
    );
  }

  Widget _buildOrganizationNameField(
      BuildContext context, _ViewModel viewModel) {
    print('_Row1._buildOrganizationNameField');
    return DecoratedTextField(
      padding: const EdgeInsets.all(8),
      initialValue: viewModel.registration.organizationName,
      labelText: AppLocalization.of(context).organizationName,
      onChanged: (value) => viewModel.setOrganizationName(value),
      validator: (value) => !viewModel.validateOrganizationName
          ? AppLocalization.of(context).organizationNameRequired
          : ' ',
    );
  }
}

class _Row2 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    print('_Row2 build called');
    return Consumer<_ViewModel>(
      builder: (context, viewModel, _) {
        final isMobile = MediaQuery.of(context).size.width < 600;
        return isMobile
            ? Column(
                children: [
                  _buildUsernameField(context, viewModel),
                  const SizedBox(height: 16),
                  _buildPasswordField(context, viewModel),
                  const SizedBox(height: 16),
                  _buildConfirmPasswordField(context, viewModel),
                ],
              )
            : Row(
                children: [
                  Expanded(child: _buildUsernameField(context, viewModel)),
                  const SizedBox(width: 16),
                  Expanded(child: _buildPasswordField(context, viewModel)),
                  const SizedBox(width: 16),
                  Expanded(child: _buildConfirmPasswordField(context, viewModel)),
                ],
              );
      },
    );
  }

  Widget _buildUsernameField(BuildContext context, _ViewModel viewModel) {
    print('_Row2._buildUsernameField');
    return DecoratedTextField(
      padding: const EdgeInsets.all(8),
      initialValue: viewModel.registration.username,
      labelText: AppLocalization.of(context).username,
      onChanged: (value) => viewModel.setUserName(value),
      validator: (value) => !viewModel.validateUsername
          ? AppLocalization.of(context).usernameRequired
          : ' ',
    );
  }

  /// Updated password field validator to show *why* it's invalid.
  Widget _buildPasswordField(BuildContext context, _ViewModel viewModel) {
    print('_Row2._buildPasswordField');
    return DecoratedTextField(
      padding: const EdgeInsets.all(8),
      initialValue: viewModel.registration.password,
      autofillHints: const [AutofillHints.newPassword],
      obscureText: true,
      labelText: AppLocalization.of(context).password,
      onChanged: (value) => viewModel.setPassword(value),
      validator: (value) {
        // 1) If it's empty, show "Required"
        if (!viewModel.validatePassword) {
          return AppLocalization.of(context).passwordRequired;
        }

        // 2) If there are missing strength rules, show them
        final missing = viewModel.getPasswordStrengthErrors(value ?? '');
        if (missing.isNotEmpty) {
          // Combine all missing rules in a single message
          // Or join with newlines if you prefer: missing.join('\n')
          return missing.join(' • ');
        }

        // 3) If everything is good, just return space
        return ' ';
      },
    );
  }

  Widget _buildConfirmPasswordField(
      BuildContext context, _ViewModel viewModel) {
    print('_Row2._buildConfirmPasswordField');
    return DecoratedTextField(
      padding: const EdgeInsets.all(8),
      initialValue: viewModel.registration.confirmPassword,
      autofillHints: const [AutofillHints.newPassword],
      obscureText: true,
      labelText: AppLocalization.of(context).confirmPassword,
      onChanged: (value) => viewModel.setConfirmPassword(value),
      validator: (value) {
        if (viewModel.registration.confirmPassword.isEmpty) {
          return AppLocalization.of(context).confirmPasswordRequired;
        } else if (!viewModel.validateConfirmPassword) {
          return AppLocalization.of(context).confirmPasswordNotMatchPassword;
        } else {
          return ' ';
        }
      },
    );
  }
}

class _Row3 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    print('_Row3 build called');
    return Consumer<_ViewModel>(
      builder: (context, viewModel, _) {
        final isMobile = MediaQuery.of(context).size.width < 600;
        return isMobile
            ? Column(
                children: [
                  _buildFirstNameField(context, viewModel),
                  const SizedBox(height: 16),
                  _buildLastNameField(context, viewModel),
                  const SizedBox(height: 16),
                  _buildPhoneField(context, viewModel),
                ],
              )
            : Row(
                children: [
                  Expanded(child: _buildFirstNameField(context, viewModel)),
                  const SizedBox(width: 16),
                  Expanded(child: _buildLastNameField(context, viewModel)),
                  const SizedBox(width: 16),
                  Expanded(child: _buildPhoneField(context, viewModel)),
                ],
              );
      },
    );
  }

  Widget _buildFirstNameField(BuildContext context, _ViewModel viewModel) {
    print('_Row3._buildFirstNameField');
    return DecoratedTextField(
      padding: const EdgeInsets.all(8),
      initialValue: viewModel.registration.firstName,
      labelText: AppLocalization.of(context).firstName,
      onChanged: (value) => viewModel.setFirstName(value),
      validator: (value) =>
          !viewModel.validatefirstName ? AppLocalization.of(context).firstNameRequired : ' ',
    );
  }

  Widget _buildLastNameField(BuildContext context, _ViewModel viewModel) {
    print('_Row3._buildLastNameField');
    return DecoratedTextField(
      padding: const EdgeInsets.all(8),
      initialValue: viewModel.registration.lastName,
      labelText: AppLocalization.of(context).lastName,
      onChanged: (value) => viewModel.setLastName(value),
      validator: (value) =>
          !viewModel.validateLastName ? AppLocalization.of(context).lastNameRequired : ' ',
    );
  }

  Widget _buildPhoneField(BuildContext context, _ViewModel viewModel) {
    print('_Row3._buildPhoneField');
    return DecoratedTextField(
      padding: const EdgeInsets.all(8),
      initialValue: viewModel.registration.phone,
      labelText: AppLocalization.of(context).phone,
      onChanged: (value) => viewModel.setPhone(value),
      validator: (value) {
        if (viewModel.registration.phone.isEmpty) {
          return 'Phone required.';
        } else if (!viewModel.validatePhone) {
          return 'Invalid phone.';
        } else {
          return ' ';
        }
      },
    );
  }
}

class _Row4 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    print('_Row4 build called');
    return Consumer<_ViewModel>(
      builder: (context, viewModel, _) {
        final isMobile = MediaQuery.of(context).size.width < 600;
        return Align(
          alignment: Alignment.centerLeft,
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: DecoratedContainer(
              padding: const EdgeInsets.all(8),
              labelText: AppLocalization.of(context).language,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(24),
                borderSide: const BorderSide(
                  color: Colors.blue,
                ),
              ),
              filled: false,
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxHeight: 50),
                child: Center(
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      isExpanded: true,
                      value: viewModel.selectedLanguage,
                      onChanged: (value) {
                        print('_Row4 onChanged -> $value');
                        if (value != null) {
                          viewModel.setLanguage(value);
                        }
                      },
                      items: [
                        for (final e in viewModel.languages)
                          DropdownMenuItem<String>(
                            value: e.key,
                            child: Text(
                              AppLocalization.localeName(context, e.key),
                            ),
                          )
                      ],
                      hint: Text(AppLocalization.of(context).selectLanguage),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class _Row5 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    print('_Row5 build called');
    return Consumer<_ViewModel>(
      builder: (context, viewModel, _) {
        final isMobile = MediaQuery.of(context).size.width < 600;
        return isMobile
            ? Column(
                children: [
                  _buildAddress1Field(context, viewModel),
                  const SizedBox(height: 16),
                  _buildAddress2Field(context, viewModel),
                ],
              )
            : Row(
                children: [
                  Expanded(child: _buildAddress1Field(context, viewModel)),
                  const SizedBox(width: 16),
                  Expanded(child: _buildAddress2Field(context, viewModel)),
                ],
              );
      },
    );
  }

  Widget _buildAddress1Field(BuildContext context, _ViewModel viewModel) {
    print('_Row5._buildAddress1Field');
    return DecoratedTextField(
      padding: const EdgeInsets.all(8),
      initialValue: viewModel.registration.addressInfo.address1,
      labelText: AppLocalization.of(context).address1,
      onChanged: (value) => viewModel.setAddress1(value),
      validator: (value) => !viewModel.validateAddress1 ? 'Address required' : ' ',
    );
  }

  Widget _buildAddress2Field(BuildContext context, _ViewModel viewModel) {
    print('_Row5._buildAddress2Field');
    return DecoratedTextField(
      padding: const EdgeInsets.all(8),
      initialValue: viewModel.registration.addressInfo.address2,
      labelText: AppLocalization.of(context).address2,
      onChanged: (value) => viewModel.setAddress2(value),
      validator: (value) => !viewModel.validateAddress2 ? 'Invalid address' : ' ',
    );
  }
}

class _Row6 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    print('_Row6 build called');
    return Consumer<_ViewModel>(
      builder: (context, viewModel, _) {
        final isMobile = MediaQuery.of(context).size.width < 600;
        return isMobile
            ? Column(
                children: [
                  _buildCityField(context, viewModel),
                  const SizedBox(height: 16),
                  _buildStateField(context, viewModel),
                  const SizedBox(height: 16),
                  _buildZipField(context, viewModel),
                ],
              )
            : Row(
                children: [
                  Expanded(child: _buildCityField(context, viewModel)),
                  const SizedBox(width: 16),
                  Expanded(child: _buildStateField(context, viewModel)),
                  const SizedBox(width: 16),
                  Expanded(child: _buildZipField(context, viewModel)),
                ],
              );
      },
    );
  }

  Widget _buildCityField(BuildContext context, _ViewModel viewModel) {
    print('_Row6._buildCityField');
    return DecoratedTextField(
      padding: const EdgeInsets.all(8),
      initialValue: viewModel.registration.addressInfo.city,
      labelText: AppLocalization.of(context).city,
      onChanged: (value) => viewModel.setCity(value),
      validator: (value) => !viewModel.validateCity ? 'City required' : ' ',
    );
  }

  Widget _buildStateField(BuildContext context, _ViewModel viewModel) {
    print('_Row6._buildStateField');
    return DecoratedTextField(
      padding: const EdgeInsets.all(8),
      initialValue: viewModel.registration.addressInfo.state,
      labelText: AppLocalization.of(context).state,
      onChanged: (value) => viewModel.setState(value),
      validator: (value) => !viewModel.validateState ? 'State required' : ' ',
    );
  }

  Widget _buildZipField(BuildContext context, _ViewModel viewModel) {
    print('_Row6._buildZipField');
    return DecoratedTextField(
      padding: const EdgeInsets.all(8),
      initialValue: viewModel.registration.addressInfo.zip,
      labelText: AppLocalization.of(context).zip,
      onChanged: (value) => viewModel.setZip(value),
      validator: (value) => !viewModel.validateZip ? 'Zip required' : ' ',
    );
  }
}

class _Captcha extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    print('_Captcha build called');
    final viewModel = context.watch<_ViewModel>();
    return grecaptcha2.GRecaptcha2(viewModel.setCaptchaResponse);
  }
}

class _RegisterButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    print('_RegisterButton build called');
    return Consumer<_ViewModel>(
      builder: (context, viewModel, _) {
        final isMobile = MediaQuery.of(context).size.width < 600;
        print('_RegisterButton Consumer: canRegister = ${viewModel.canRegister}');
        return Center(
          child: FittedBox(
            fit: BoxFit.scaleDown,
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: SizedBox(
                width: isMobile ? MediaQuery.of(context).size.width * 0.77 : null,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      vertical: 24,
                      horizontal: 48,
                    ),
                  ),
                  onPressed: !viewModel.canRegister
                      ? null
                      : () => unawaited(completeRegistration(context, viewModel)),
                  child: Text(
                    AppLocalization.of(context).register,
                    style: const TextStyle(fontSize: 24),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> completeRegistration(
      BuildContext context, _ViewModel viewModel) async {
    print('completeRegistration called');
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        final router = GoRouter.of(context);
        return BusyDialog(
          subTitle: AppLocalization.of(context).registrationConfirmation,
          future: () async {
            try {
              final response = await ApiModel().registerOrganization(
                registration: viewModel.registration,
                captchaResponse: viewModel.captchaResponse,
              );
              print('Registration response: ${response.toString()}');
              if (!response.isError) {
                router.go('/completeRegistration');
              }
            } on ApiException catch (e, stack) {
              await logApiException(e, stack);
              if (!context.mounted) return;
              await showDialog(
                context: context,
                builder: (context) => ErrorDialog(errorCode: e.errorCode),
              );
            } on Exception catch (e, stack) {
              print('Generic Exception caught: $e');
              await logException(e, stack);
              if (!context.mounted) return;
              await showDialog(
                context: context,
                builder: (context) => const ErrorDialog(),
              );
            }
          },
        );
      },
    );
  }
}

class _ViewModel extends ChangeNotifier {
  Registration registration = Registration.create();
  String captchaResponse = '';
  bool emailExists = false;
  RestartableTimer? _timer;
  Iterable<Language> languages = [];
  String selectedLanguage = 'en-US'; // Default language

  final TextEditingController phoneController = TextEditingController();

  _ViewModel() {
    print('_ViewModel constructor: setting default timeZone and languages');
    registration.timeZone = MyPlatform.deviceIana;

    languages = [
      Language(
        createdOn: DateTime.now(),
        id: '1',
        key: 'en-US',
        isDefault: true,
        name: 'English',
      ),
      Language(
        createdOn: DateTime.now(),
        id: '2',
        key: 'es-ES',
        isDefault: false,
        name: 'Spanish',
      ),
      // Add more languages if needed
    ];
  }

  @override
  void dispose() {
    phoneController.dispose();
    super.dispose();
  }

  // =============== Password Strength Errors ===============
  /// Returns a list of all the missing password requirements.
  List<String> getPasswordStrengthErrors(String password) {
    final errors = <String>[];

    if (password.length < 8) {
      errors.add('Must be at least 8 characters');
    }
    if (!RegExp(r'[A-Z]').hasMatch(password)) {
      errors.add('Must contain an uppercase letter');
    }
    if (!RegExp(r'[a-z]').hasMatch(password)) {
      errors.add('Must contain a lowercase letter');
    }
    if (!RegExp(r'\d').hasMatch(password)) {
      errors.add('Must contain a digit');
    }
    if (!RegExp(r'[@$!%*?&]').hasMatch(password)) {
      errors.add('Must contain a special char (@\$!%*?&)');
    }

    return errors;
  }

  // =============== Validation Getters =====================
  bool get validateEmailAddress {
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
    final valid = emailRegex.hasMatch(registration.emailAddress) && !emailExists;
    print(
        'validateEmailAddress => email: "${registration.emailAddress}", emailExists: $emailExists, isValid: $valid');
    return valid;
  }

  bool get validatePasswordStrength {
    // If there are *no* missing rules, it's valid.
    return getPasswordStrengthErrors(registration.password).isEmpty;
  }

  bool get validateOrganizationName {
    final valid = registration.organizationName.isNotEmpty;
    print(
        'validateOrganizationName => organizationName: "${registration.organizationName}", isValid: $valid');
    return valid;
  }

  bool get validateUsername {
    final valid = registration.username.isNotEmpty;
    print('validateUsername => username: "${registration.username}", isValid: $valid');
    return valid;
  }

  bool get validatePassword {
    final valid = registration.password.isNotEmpty;
    print('validatePassword => passwordIsNotEmpty: $valid');
    return valid;
  }

  bool get validateConfirmPassword {
    final valid = registration.confirmPassword.isNotEmpty &&
        registration.password == registration.confirmPassword;
    print(
        'validateConfirmPassword => confirmPassword: "${registration.confirmPassword}", matches: ${registration.password == registration.confirmPassword}, isValid: $valid');
    return valid;
  }

  bool get validatefirstName {
    final valid = registration.firstName.isNotEmpty;
    print('validatefirstName => firstName: "${registration.firstName}", isValid: $valid');
    return valid;
  }

  bool get validateLastName {
    final valid = registration.lastName.isNotEmpty;
    print('validateLastName => lastName: "${registration.lastName}", isValid: $valid');
    return valid;
  }

  bool get validatePhone {
    final valid = registration.phone.validatePhone;
    print('validatePhone => phone: "${registration.phone}", isValid: $valid');
    return valid;
  }

  bool get validateAddress1 {
    final valid = registration.addressInfo.address1.isNotEmpty;
    print(
        'validateAddress1 => address1: "${registration.addressInfo.address1}", isValid: $valid');
    return valid;
  }

  bool get validateAddress2 {
    // If you truly need advanced validation for address2, do it here.
    return true;
  }

  bool get validateCity {
    final valid = registration.addressInfo.city.isNotEmpty;
    print(
        'validateCity => city: "${registration.addressInfo.city}", isValid: $valid');
    return valid;
  }

  bool get validateState {
    final valid = registration.addressInfo.state.isNotEmpty;
    print(
        'validateState => state: "${registration.addressInfo.state}", isValid: $valid');
    return valid;
  }

  bool get validateZip {
    final valid = registration.addressInfo.zip.isNotEmpty;
    print('validateZip => zip: "${registration.addressInfo.zip}", isValid: $valid');
    return valid;
  }

  bool get validateCaptchaResponse {
    final valid = captchaResponse.isNotEmpty;
    print('validateCaptchaResponse => captchaResponse: "$captchaResponse", isValid: $valid');
    return valid;
  }

  bool get canRegister {
    final result = validateEmailAddress &&
        validateOrganizationName &&
        validateUsername &&
        validatePassword &&
        validateConfirmPassword &&
        validatefirstName &&
        validateLastName &&
        validatePhone &&
        validateAddress1 &&
        validateCity &&
        validateState &&
        validateZip &&
        validateCaptchaResponse &&
        validatePasswordStrength;

    print('canRegister => '
        'validateEmailAddress=$validateEmailAddress, '
        'validateOrganizationName=$validateOrganizationName, '
        'validateUsername=$validateUsername, '
        'validatePassword=$validatePassword, '
        'validateConfirmPassword=$validateConfirmPassword, '
        'validatefirstName=$validatefirstName, '
        'validateLastName=$validateLastName, '
        'validatePhone=$validatePhone, '
        'validateAddress1=$validateAddress1, '
        'validateCity=$validateCity, '
        'validateState=$validateState, '
        'validateZip=$validateZip, '
        'validateCaptchaResponse=$validateCaptchaResponse, '
        'validatePasswordStrength=$validatePasswordStrength, '
        'OVERALL=$result');

    return result;
  }

  // =============== State Setters ==========================
  Future<void> setEmailAddress(String value) async {
    print('setEmailAddress: "$value"');
    registration.emailAddress = value;
    notifyListeners();

    // Debounce: Check email existence after 1 second of no typing
    if (_timer != null) {
      _timer?.reset();
    } else {
      _timer = RestartableTimer(const Duration(seconds: 1), () async {
        _timer = null;
        print('Checking email existence...');
        if (validateEmailAddress) {
          emailExists = await ApiModel()
              .registrationEmailExists(registration.emailAddress);
          print('Email exists? $emailExists');
        } else {
          emailExists = false;
        }
        notifyListeners();
      });
    }
  }

  void setOrganizationName(String value) {
    print('setOrganizationName: "$value"');
    registration.organizationName = value;
    notifyListeners();
  }

  void setUserName(String value) {
    print('setUserName: "$value"');
    registration.username = value;
    notifyListeners();
  }

  void setPassword(String value) {
    print('setPassword: "$value"');
    registration.password = value;
    notifyListeners();
  }

  void setConfirmPassword(String value) {
    print('setConfirmPassword: "$value"');
    registration.confirmPassword = value;
    notifyListeners();
  }

  void setFirstName(String value) {
    print('setFirstName: "$value"');
    registration.firstName = value;
    notifyListeners();
  }

  void setLastName(String value) {
    print('setLastName: "$value"');
    registration.lastName = value;
    notifyListeners();
  }

  void setPhone(String value) {
    print('setPhone: "$value"');
    registration.phone = value;
    notifyListeners();
  }

  void setAddress1(String value) {
    print('setAddress1: "$value"');
    registration.addressInfo.address1 = value;
    notifyListeners();
  }

  void setAddress2(String value) {
    print('setAddress2: "$value"');
    registration.addressInfo.address2 = value;
    notifyListeners();
  }

  void setCity(String value) {
    print('setCity: "$value"');
    registration.addressInfo.city = value;
    notifyListeners();
  }

  void setState(String value) {
    print('setState: "$value"');
    registration.addressInfo.state = value;
    notifyListeners();
  }

  void setZip(String value) {
    print('setZip: "$value"');
    registration.addressInfo.zip = value;
    notifyListeners();
  }

  void setCaptchaResponse(String value) {
    print('setCaptchaResponse: "$value"');
    captchaResponse = value;
    notifyListeners();
  }

  void setLanguage(String value) {
    print('setLanguage: "$value"');
    selectedLanguage = value;
    notifyListeners();
  }
}
