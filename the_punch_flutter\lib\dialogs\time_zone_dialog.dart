import 'package:after_layout/after_layout.dart';
import 'package:flutter/material.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'constrained_dialog.dart';
import '../misc/app_localization.dart';
import '../widgets/padded_card.dart';
import '../misc/window_time_zone.dart';
import '../widgets/search_text_field.dart';

class TimeZoneDialog extends StatefulWidget {
  final Function(String) selected;
  final String? currentSelection;

  const TimeZoneDialog({super.key, required this.selected, this.currentSelection});

  @override
  State<TimeZoneDialog> createState() => _TimeZoneDialogState();
}

class _TimeZoneDialogState extends State<TimeZoneDialog> with AfterLayoutMixin<TimeZoneDialog> {
  final _search = ValueNotifier<String>('');
  final timeZones = WindowsTimeZone.windowsTimeZones.toList();
  final _controller = ItemScrollController();

  @override
  Widget build(BuildContext context) => ConstrainedDialog(
      constraints: const BoxConstraints(maxWidth: 500, maxHeight: 810),
      child: Expanded(
        child: Flex(
          direction: Axis.vertical,
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
                flex: 0,
                child: SearchTextField(
                  hintText: AppLocalization.of(context).timeZone,
                  notifier: _search,
                )),
            Flexible(
              child: ValueListenableBuilder<String>(
                  valueListenable: _search,
                  builder: (context, search, child) {
                    final visibleTimeZones = timeZones.where((e) {
                      final lowerCaseSearch = search.toLowerCase();
                      return e.name.toLowerCase().contains(lowerCaseSearch) || e.iana.toLowerCase().contains(lowerCaseSearch);
                    }).toList();
                    return ScrollablePositionedList.builder(
                      itemScrollController: _controller,
                      itemCount: visibleTimeZones.length,
                      itemBuilder: (context, index) => _Tile(timeZone: visibleTimeZones[index], selected: widget.selected),
                    );
                  }),
            ),
            Align(
              alignment: Alignment.centerRight,
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
              ),
            ),
          ],
        ),
      ),
    );

  @override
  void afterFirstLayout(BuildContext context) {
    var index = timeZones.indexWhere((e) => e.iana == widget.currentSelection);
    if (index == -1) {
      final offset = DateTime(2020).timeZoneOffset.inMilliseconds;
      index = timeZones.indexWhere((e) => e.offset == offset);
    }
    if (_controller.isAttached) {
      _controller.jumpTo(index: index);
    }
    _search.addListener(() {
      if (_search.value.isEmpty) {
        Future.delayed(const Duration(milliseconds: 500), () => _controller.jumpTo(index: index));
      }
    });
  }
}

class _Tile extends StatelessWidget {
  final WindowsTimeZone timeZone;
  final Function(String) selected;

  const _Tile({required this.timeZone, required this.selected});

  @override
  Widget build(BuildContext context) => InkWell(
        onTap: () {
          Navigator.of(context).pop();
          selected(timeZone.iana);
        },
        child: PaddedCard(
            child: Column(
          children: [
            Text(timeZone.name),
            Text(timeZone.iana),
          ],
        )));
}
