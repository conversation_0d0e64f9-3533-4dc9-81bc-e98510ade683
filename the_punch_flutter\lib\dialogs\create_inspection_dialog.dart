import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import '../dataModel/data_model.dart';
import '../dataModel/data/inspection.dart';
import '../dataModel/data/inspection_template.dart';
import '../dataModel/data/location.dart';
import '../helpers/color_helper.dart';
import 'constrained_dialog.dart';
import 'inspection_templates_dialog.dart';
import 'handheld/locations_dialog.dart';
import '../misc/collection_extensions.dart';

class CreateInspectionDialog extends StatefulWidget {
  final Function(String, String) inspectionCreated;

  const CreateInspectionDialog({super.key, required this.inspectionCreated});

  @override
  State<CreateInspectionDialog> createState() => _CreateInspectionDialogState();
}

class _CreateInspectionDialogState extends State<CreateInspectionDialog> {
  Location? location;
  InspectionTemplate? template;

  @override
  Widget build(BuildContext context) => ConstrainedDialog(
        title: AppLocalizations.of(context)!.createNewInspection,
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(8),
                child: OutlinedButton(
                  onPressed: () => unawaited(
                    _locationTapped(context),
                  ),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(
                      color: ColorHelper.thePunchLightGray(),
                    ),
                  ),
                  child: Text(
                    location?.name ?? 'Choose Location',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(8),
                child: OutlinedButton(
                  onPressed: (location != null)
                      ? () => unawaited(_inspectionTapped(context))
                      : null,
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(
                      color: ColorHelper.thePunchLightGray(),
                    ),
                  ),
                  child: Text(
                    template?.name ?? 'Choose Inspection',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(8),
                child: ElevatedButton(
                  onPressed: (location != null && template != null)
                      ? _createTapped
                      : null,
                  child:
                      Text(AppLocalizations.of(context)!.createNewInspection),
                ),
              ),
            ],
          ),
        ),
      );

  Future<void> _locationTapped(BuildContext context) async => await showDialog(
        context: context,
        builder: (context) => LocationsDialog(
          onSelection: (locationId) async {
            final location =
                await DataModel().locationModel.getById(locationId);
            setState(() => this.location = location);
          },
        ),
      );

  Future<void> _inspectionTapped(BuildContext context) async {
    if (location == null) return;
    await showDialog(
      context: context,
      builder: (context) => InspectionTemplatesDialog(
        locationId: location!.id,
        templateSelected: (templateId) async {
          final template =
              await DataModel().inspectionTemplateModel.getById(templateId);
          setState(() => this.template = template);
          if (!mounted) return;
          Navigator.pop(context);
        },
      ),
    );
  }

  Future<void> _createTapped() async {
    if (location == null || template == null) return;

    final templateAreas = await DataModel()
        .inspectionTemplateAreaModel
        .getActiveByInspectionTemplateId(template!.id);

    final templateItems = await DataModel()
        .inspectionTemplateItemModel
        .getActiveByInspectionTemplateId(template!.id);
    final templateItemMap =
        templateItems.groupBy((e) => e.inspectionTemplateAreaId);

    final inspection = Inspection.create()
      ..name = template!.name
      ..locationId = location!.id;

    final areas = <InspectionArea>[];
    final items = <InspectionItem>[];

    for (final templateArea in templateAreas) {
      final area = InspectionArea.create()
        ..name = templateArea.name
        ..inspectionId = inspection.id
        ..order = templateArea.order;
      areas.add(area);
      final templateItems = templateItemMap[templateArea.id];
      if (templateItems != null) {
        items.addAll(templateItems.map((templateItem) => InspectionItem.create()
          ..name = templateItem.name
          ..inspectionId = inspection.id
          ..inspectionAreaId = area.id
          ..order = templateItem.order));
      }
    }

    await DataModel().inspectionModel.saveDirty([inspection]);
    await DataModel().inspectionAreaModel.saveDirty(areas);
    await DataModel().inspectionItemModel.saveDirty(items);

    widget.inspectionCreated(inspection.id, '1');
    if (!mounted) return;
    Navigator.pop(context);
  }
}
