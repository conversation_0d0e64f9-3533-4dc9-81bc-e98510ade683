import 'package:collection/collection.dart';
import '../../api/sync_model.dart';
import '../../misc/extensions.dart';
import '../../state/server_time_state.dart';
import '../base_data.dart';
import '../data/message.dart';
import '../data_model.dart';
import '../hive_db.dart';

class MessageModel extends BaseDataModel<Message> {
  @override
  Future<Iterable<Message>> get all async =>
      (await HiveDb.database).messages.values;

  @override
  Future<void> save(Iterable<Message> t) async {
    final box = (await HiveDb.database).messages;
    await box.putAll({for (final e in t) e.id: e});
    notifyListeners();
  }

  @override
  Future<void> saveDirty(Iterable<Message> t) async {
    // Example: automatically set `sentOn` to server's UTC time if "dirty"
    for (final e in t) {
      e.sentOn = ServerTimeState().utcTime;
      e.isDirty = true;
    }
    return super.saveDirty(t);
  }

  /// Example method: find last from-messages
  Future<Iterable<Message>> findLastFromMessages() async {
    return (await all)
        .groupListsBy((e) => e.fromUserId)
        .entries
        .map((e) => e.value.reduce((v, f) => (f.sentOn.isAfter(v.sentOn)) ? f : v));
  }

  /// Example method: find last to-messages
  Future<Iterable<Message>> findLastToMessages() async {
    return (await all)
        .groupListsBy((e) => e.toGroupId)
        .entries
        .map((e) => e.value.reduce((v, f) => (f.sentOn.isAfter(v.sentOn)) ? f : v));
  }

  /// Finds messages where [employeeId] matches either `fromUserId` or `toGroupId`.
  Future<Iterable<Message>> findByEmployeeId(String employeeId) async {
    return (await all).where(
      (e) => e.fromUserId == employeeId || e.toGroupId == employeeId,
    );
  }

  /// Finds messages by a specific [groupId].
  Future<Iterable<Message>> findByGroupId(String groupId) async {
    return (await all).where((m) => m.toGroupId == groupId);
  }

  /// Basic unread count for direct messages (non-group).
  /// If your app uses only group chats, or a mix, adjust accordingly.
  Future<int> getUnreadCount(String employeeId) async {
    return (await all)
        .where((e) => e.openedOn == null && e.toGroupId == employeeId)
        .length;
  }

  /// Mark all messages from [employeeId] as read.
  /// Adjust to handle groups if needed (or pass in groupId).
  Future<void> markAsRead(String employeeId) async {
    final allMsgs = await all;
    final messages = allMsgs.where((e) => e.openedOn == null && e.fromUserId == employeeId);

    final utcTime = ServerTimeState().utcTime;
    for (final e in messages) {
      e.openedOn = utcTime;
      e.lastChangedOn = utcTime;
      e.isDirty = true;
    }

    if (messages.isNotEmpty) {
      final box = (await HiveDb.database).messages;
      await box.putAll({for (final e in messages) e.id: e});
      notifyListeners();

      // Optionally sync to server if you have such logic
      await SyncModel().sync();
    }
  }

  /// Return a limited number of dirty records for sync
  Future<Iterable<Message>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }

  /// Helper to pick whichever datetime is more recent
  DateTime _mostRecentDateTime(DateTime? dt1, DateTime? dt2) {
    if (dt1 == null && dt2 == null) {
      return DateTime.fromMillisecondsSinceEpoch(0);
    } else if (dt1 == null) {
      return dt2!;
    } else if (dt2 == null) {
      return dt1;
    } else {
      return dt1.isAfter(dt2) ? dt1 : dt2;
    }
  }

  /// Merge newly fetched messages with local.
  /// - If local message is dirty, keep local fields for [body].
  /// - Otherwise, use fetched fields.
  /// - Also keeps the [toGroupId] from fetched or local as needed.
  Future<void> updateMessages(Iterable<Message> fetchedMessages) async {
    final db = await HiveDb.database;
    final localBox = db.messages;

    // Create a map for faster local lookups
    final localMap = {
      for (final msg in localBox.values) msg.id: msg,
    };

    final toSave = <String, Message>{};

    for (final fetched in fetchedMessages) {
      final localMsg = localMap[fetched.id];

      if (localMsg == null) {
        // It's new to local
        toSave[fetched.id] = fetched;
      } else {
        // Merge
        final updatedMsg = Message(
          id: fetched.id,
          fromUserId: fetched.fromUserId,
          toGroupId: fetched.toGroupId,
          body: localMsg.isDirty ? localMsg.body : fetched.body,
          sentOn: _mostRecentDateTime(fetched.sentOn, localMsg.sentOn),
          openedOn: localMsg.openedOn ?? fetched.openedOn,
          lastChangedOn: _mostRecentDateTime(
            fetched.lastChangedOn,
            localMsg.lastChangedOn,
          ),
          isDirty: localMsg.isDirty,
          createdOn: fetched.createdOn ?? localMsg.createdOn,
        );
        toSave[fetched.id] = updatedMsg;
      }
    }

    if (toSave.isNotEmpty) {
      await localBox.putAll(toSave);
      notifyListeners();
    }
  }
}
