import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:uuid/uuid.dart';
import '../../misc/json_conversion.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import '../base_data.dart';

part 'group_member.g.dart';

@HiveType(typeId: 29)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class GroupMember extends BaseData {
  @override
  @HiveField(6) // use a different field index to avoid conflicts with BaseData
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(7)
  @JsonKey(fromJson: idFromJson)
  String groupId;

  @HiveField(8)
  @JsonKey(fromJson: idFromJson)
  String userId;

  GroupMember({
    required this.id,
    required this.groupId,
    required this.userId,

    // These params are required by BaseData:
    bool isDirty = false,
    bool isActive = true,
    required String createdByUserId,
    required DateTime createdOn,
    DateTime? lastChangedOn,
    String? lastChangedByUserId,
  }) : super(
          isDirty: isDirty,
          isActive: isActive,
          createdByUserId: createdByUserId,
          createdOn: createdOn,
          lastChangedOn: lastChangedOn,
          lastChangedByUserId: lastChangedByUserId,
        );

  factory GroupMember.fromJson(Map<String, dynamic> json) =>
      _$GroupMemberFromJson(json);

  Map<String, dynamic> toJson() => _$GroupMemberToJson(this);

  /// Convenient factory that auto-fills `id`, `createdByUserId`, and `createdOn`.
  factory GroupMember.create({
    required String groupId,
    required String userId,
  }) {
    return GroupMember(
      id: const Uuid().v4(),
      groupId: groupId,
      userId: userId,
      createdByUserId: LoginState.userId,
      createdOn: ServerTimeState().utcTime,
    );
  }
}
