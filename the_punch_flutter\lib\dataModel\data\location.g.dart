// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'location.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LocationAdapter extends TypeAdapter<Location> {
  @override
  final int typeId = 13;

  @override
  Location read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Location(
      isDirty: fields[0] as bool,
      isActive: fields[1] as bool,
      createdOn: fields[2] as DateTime,
      createdByUserId: fields[3] as String?,
      lastChangedOn: fields[4] as DateTime?,
      lastChangedByUserId: fields[5] as String?,
      id: fields[100] as String,
      name: fields[101] as String,
      address1: fields[103] as String,
      address2: fields[104] as String,
      city: fields[105] as String,
      state: fields[106] as String,
      zip: fields[107] as String,
      country: fields[108] as String,
      phone: fields[109] as String,
      emailAddress: fields[110] as String,
      latitude: fields[111] as double,
      longitude: fields[112] as double,
      geoFenceRadius: fields[113] as double,
      timeZone: fields[114] as String,
    );
  }

  @override
  void write(BinaryWriter writer, Location obj) {
    writer
      ..writeByte(20)
      ..writeByte(100)
      ..write(obj.id)
      ..writeByte(101)
      ..write(obj.name)
      ..writeByte(103)
      ..write(obj.address1)
      ..writeByte(104)
      ..write(obj.address2)
      ..writeByte(105)
      ..write(obj.city)
      ..writeByte(106)
      ..write(obj.state)
      ..writeByte(107)
      ..write(obj.zip)
      ..writeByte(108)
      ..write(obj.country)
      ..writeByte(109)
      ..write(obj.phone)
      ..writeByte(110)
      ..write(obj.emailAddress)
      ..writeByte(111)
      ..write(obj.latitude)
      ..writeByte(112)
      ..write(obj.longitude)
      ..writeByte(113)
      ..write(obj.geoFenceRadius)
      ..writeByte(114)
      ..write(obj.timeZone)
      ..writeByte(0)
      ..write(obj.isDirty)
      ..writeByte(1)
      ..write(obj.isActive)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdByUserId)
      ..writeByte(4)
      ..write(obj.lastChangedOn)
      ..writeByte(5)
      ..write(obj.lastChangedByUserId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Location _$LocationFromJson(Map<String, dynamic> json) => Location(
      isActive: json['IsActive'] as bool? ?? true,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
      id: idFromJson(json['Id']),
      name: json['Name'] as String,
      address1: json['Address1'] as String? ?? '',
      address2: json['Address2'] as String? ?? '',
      city: json['City'] as String? ?? '',
      state: json['State'] as String? ?? '',
      zip: json['Zip'] as String? ?? '',
      country: json['Country'] as String? ?? '',
      phone: json['Phone'] as String? ?? '',
      emailAddress: json['EmailAddress'] as String? ?? '',
      latitude: (json['Latitude'] as num?)?.toDouble() ?? 0,
      longitude: (json['Longitude'] as num?)?.toDouble() ?? 0,
      geoFenceRadius: (json['GeoFenceRadius'] as num?)?.toDouble() ?? 100,
      timeZone: json['TimeZone_IANA'] as String,
    );

Map<String, dynamic> _$LocationToJson(Location instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['Name'] = instance.name;
  val['Address1'] = instance.address1;
  val['Address2'] = instance.address2;
  val['City'] = instance.city;
  val['State'] = instance.state;
  val['Zip'] = instance.zip;
  val['Country'] = instance.country;
  val['Phone'] = instance.phone;
  val['EmailAddress'] = instance.emailAddress;
  val['Latitude'] = instance.latitude;
  val['Longitude'] = instance.longitude;
  val['GeoFenceRadius'] = instance.geoFenceRadius;
  val['TimeZone_IANA'] = instance.timeZone;
  return val;
}
