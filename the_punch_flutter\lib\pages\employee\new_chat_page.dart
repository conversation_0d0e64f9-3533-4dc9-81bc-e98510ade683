import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import '../../api/api_model.dart';
import '../../dataModel/data/group_member.dart';
import '../../dataModel/data/message_group.dart';
import '../../dataModel/data/user_type.dart';
import '../../helpers/color_helper.dart';
import '../web/my_scaffold.dart';
import 'employee_widgets/name_chat.dart';
import '../../dataModel/data/user.dart';
import '../../state/login_state.dart';
import '../../dataModel/data_model.dart';

class StartNewChatPage extends StatefulWidget {
  final dynamic vM;
  final void Function(List<String> selectedEmployeeIds) onMultiSelection;
  
  const StartNewChatPage({
    super.key, 
    required this.onMultiSelection, 
    required this.vM,
  });

  @override
  _StartNewChatPageState createState() => _StartNewChatPageState();
}

class _StartNewChatPageState extends State<StartNewChatPage> {
  late List<User> _allUsers = [];
  final _selectedEmployeeIds = <String>{};
  String _searchTerm = '';
  bool _currentUserIsManagerOrAdmin = false;
  late final viewModel;
  final TextEditingController _nameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    viewModel = widget.vM;
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    final currentUser = await DataModel().userModel.getCurrentUser(viewModel.employeeId);
    _currentUserIsManagerOrAdmin = currentUser != null
        ? _isManagerOrAdminUserType(currentUser.userTypeId)
        : false;
    
    final allUsers = await DataModel().userModel.getAllUsers();
    _allUsers = allUsers.where((u) => u.id != viewModel.employeeId).toList();
    _applyFilters();
  }

  Future<void> _createGroup(String? groupName) async {
    if (groupName == null || groupName.trim().isEmpty) {
      final randomNumber = Random().nextInt(100000);
      groupName = 'group-$randomNumber';
    }

    final group = MessageGroup.create(groupName);
    final groupBox = Hive.box<MessageGroup>('messageGroup');
    await groupBox.put(group.id, group);

    final groupMemberBox = Hive.box<GroupMember>('groupMember');
    for (final userId in _selectedEmployeeIds) {
      final gm = GroupMember.create(groupId: group.id, userId: userId);
      await groupMemberBox.put(gm.id, gm);
    }

    try {
      await ApiModel().createChatGroup(group, _selectedEmployeeIds.toList());
    } catch (e) {
      print('Failed to create group on server: $e');
    }
  }

  bool _isManagerOrAdminUserType(String userTypeId) =>
      userTypeId.toUpperCase() == UserType.managerId.toUpperCase() ||
      userTypeId.toUpperCase() == UserType.administratorId.toUpperCase();

  void _applyFilters() {
    final searchLower = _searchTerm.trim().toLowerCase();

    if (searchLower.isEmpty) {
      if (_currentUserIsManagerOrAdmin) {
        setState(() {});
      } else {
        setState(() {});
      }
    } else {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) => MyScaffold(
      title: 'New Chat',
      showBackButton: true,
      body: Column(
        children: [
          const SizedBox(height: 10),
          const Center(
            child: Text(
              'New Chat', 
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold, 
                color: Colors.black
              ),
            ),
          ),
          const SizedBox(height: 20), 
          NameChat(controller: _nameController),
          Expanded(
            child: ListView.builder(
              itemCount: _allUsers.length,
              itemBuilder: (context, index) {
                final user = _allUsers[index];
                final bool isSelected = _selectedEmployeeIds.contains(user.id);
                return Column(
                  children: [
                    Center(
                      child: Container(
                        width: MediaQuery.of(context).size.width * 0.85,
                        child: ListTile(
                          title: Text(user.name),
                          textColor: Colors.black,
                          titleTextStyle: const TextStyle(fontWeight: FontWeight.bold),
                          trailing: InkWell(
                            onTap: () {
                              setState(() {
                                if (isSelected) {
                                  _selectedEmployeeIds.remove(user.id);
                                } else {
                                  _selectedEmployeeIds.add(user.id);
                                }
                              });
                            },
                            customBorder: const CircleBorder(),
                            splashColor: ColorHelper.thePunchAdminButtonBlue().withOpacity(0.2),
                            highlightColor: Colors.grey.withOpacity(0.1),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Icon(
                                isSelected ? Icons.cancel : Icons.add_circle_outline,
                                size: 20.0,
                                color: isSelected
                                  ? ColorHelper.thePunchAdminButtonBlue()
                                  : Colors.grey[700],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Divider(color: Colors.grey[300]),
                  ],
                );
              },
            ),
          ),
          const SizedBox(height: 20),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorHelper.thePunchAdminButtonBlue(),
              minimumSize: Size(MediaQuery.of(context).size.width * 0.7, 50),
              alignment: Alignment.center,
            ),
            onPressed: () async {
              if (_selectedEmployeeIds.isEmpty) {
                Navigator.of(context).pop();
                return;
              }

              _selectedEmployeeIds.add(LoginState.userId);

              if (_selectedEmployeeIds.length == 2) {
                await _createGroup(null);
              } else {
                final typedName = _nameController.text;
                if (typedName.isEmpty) {
                  return;
                }
                await _createGroup(typedName);
              }

              widget.onMultiSelection(_selectedEmployeeIds.toList());
              await viewModel.refresh();
              Navigator.of(context).pop();
            },
            child: const Text('Start Group Chat'),
          ),
        ],
      ),
    );
}


class _TileData {
  final String groupId;
  final String title; // The group or the other user's name
  final String lastMessageBody;
  final DateTime lastMessageSentOn;
  final bool isNew; // Whether there's an unread message
  final int memberCount; // <-- NEW field to show total group members

  _TileData({
    required this.groupId,
    required this.title,
    required this.lastMessageBody,
    required this.lastMessageSentOn,
    required this.isNew,
    required this.memberCount,
  });
}

