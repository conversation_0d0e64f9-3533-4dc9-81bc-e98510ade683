import 'dart:async';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/material_symbols_icons.dart';
import 'package:the_punch_flutter/dataModel/models/location_notes_model.dart';
import 'package:the_punch_flutter/helpers/color_helper.dart';

class LocationNotesButton extends StatelessWidget {
  final String locationId;

  const LocationNotesButton({required this.locationId});

  @override
  Widget build(BuildContext context) => GestureDetector(
        onTap: () {


          

          _showLocationNotesDialog(context, locationId);
        },
        child:CircleAvatar(
  backgroundColor: ColorHelper.thePunchLightYellow(), // Background color of the circle
  radius: 30, // Adjust the radius to control the size of the circle (half of diameter)
  child: 
  // Stack(
  // children: [
    Icon(
      Symbols.add_notes,
      size: 30,
      color: ColorHelper.thePunchMustard(),
      
    ),

)
      );

  Future<void> _showLocationNotesDialog(
      BuildContext context, String locationId) async {
    // Fetch notes for the given locationId
    final location_notes = await LocationNoteModel().getByLocationIds([locationId]);



    List<Object> extra = ["Location Notes",location_notes];
unawaited(context.push('/employees/location-notes',extra:extra));
  }
}