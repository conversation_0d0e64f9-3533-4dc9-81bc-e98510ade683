import 'dart:async';

import 'package:flutter/material.dart';
import '../helpers/color_helper.dart';
import '../misc/app_localization.dart';
import 'labeled_switch.dart';
import '../misc/extensions.dart';

enum ActiveToggleState { active, inactive, all, both }

class ActiveToggle extends StatelessWidget {
  final ValueNotifier<ActiveToggleState> activeNotifier;
  const ActiveToggle(this.activeNotifier, {super.key});

  @override
  Widget build(BuildContext context) => ValueListenableBuilder(
      valueListenable: activeNotifier,
      builder: (context, activeState, child) =>
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: ColorHelper.thePunchDarkBlue()),
                color: ColorHelper.thePunchDarkBlue(), // Background color of the button
                borderRadius: BorderRadius.circular(10), // Optional: Add rounded corners
              ),
              padding: EdgeInsets.symmetric(horizontal: 20,vertical: 10), // Add padding for better appearance
              child: DropdownButton<ActiveToggleState>(
                dropdownColor: ColorHelper.thePunchDarkBlue(),
                
                iconEnabledColor: ColorHelper.lightThemeWhite(),
                underline: SizedBox(), // Removes the default underline
                value: activeNotifier.value
                ,
                isDense: true,
                onChanged: (ActiveToggleState? newValue) {
                  if (newValue == null) return;
                  activeNotifier.value = newValue;
                },
                items: [
                    DropdownMenuItem<ActiveToggleState>(
                    value: ActiveToggleState.active,
                    child: Text(
                      AppLocalization.of(context).active,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(color: ColorHelper.lightThemeWhite()),
                    ),
                  ),
                  DropdownMenuItem<ActiveToggleState>(
                    value: ActiveToggleState.inactive,
                    child: Text(
                      AppLocalization.of(context).inactive,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(color: ColorHelper.lightThemeWhite()),
                    ),
                  ),
                  DropdownMenuItem<ActiveToggleState>(
                    value: ActiveToggleState.all,
                    child: Text(
                      AppLocalization.of(context).all,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(color: ColorHelper.lightThemeWhite()),
                    ),
                  ),
                ],
              ),
            )
            );
  //   List<bool> isSelected;
  //   switch (activeState) {
  //     case ActiveToggleState.active:
  //       isSelected = [true, false, false];
  //       break;
  //     case ActiveToggleState.inactive:
  //       isSelected = [false, true, false];
  //       break;
  //     default:
  //       isSelected = [false, false, true];
  //       break;
  //   }

  //   return ToggleButtons(
  //     onPressed: (index) => activeNotifier.value = ActiveToggleState.values[index],
  //     isSelected: isSelected,
  //     children: [
  //       Text('Active'),
  //       Text('Inactive'),
  //       Text('All'),
  //     ],
  //   );
}

class ActiveButton extends StatelessWidget {
  final bool value;
  final Function(bool isActive) onChanged;
  final bool enableDialog;
  final Widget? dialogTitle;
  final Widget? dialogMessage;
  final Widget? dialogCaption;

  const ActiveButton(
      {super.key,
      required this.value,
      required this.onChanged,
      this.enableDialog = true,
      this.dialogTitle,
      this.dialogMessage,
      this.dialogCaption});

  @override
  Widget build(BuildContext context) {
    if (value) {
      return OutlinedButton(
        style: OutlinedButton.styleFrom(
          side: BorderSide(
            color: ColorHelper.thePunchRed(),
          ),
        ),
        onPressed: () {
          if (enableDialog) {
            unawaited(showDialog(
                context: context,
                builder: (context) => DeactivateDialog(
                      selected: () => onChanged(false),
                      title: dialogTitle,
                      message: dialogMessage,
                      caption: dialogCaption,
                    )));
          } else {
            onChanged(false);
          }
        },
        child: Text(AppLocalization.of(context).deactivate),
      );
    } else {
      return OutlinedButton(
        onPressed: () => onChanged(true),
        child: Text(AppLocalization.of(context).activate),
      );
    }
  }
}

class ActiveSwitch extends StatelessWidget {
  final bool value;
  final Function(bool isActive) onChanged;
  final bool enableDialog;
  final Widget? dialogTitle;
  final Widget? dialogMessage;
  final Widget? dialogCaption;

  const ActiveSwitch(
      {super.key,
      required this.value,
      required this.onChanged,
      this.enableDialog = true,
      this.dialogTitle,
      this.dialogMessage,
      this.dialogCaption});

  @override
  Widget build(BuildContext context) => LabeledSwitch(
      label: Text(value.toActive(context)),
      value: value,
      onChanged: (value) {
        if (!value && enableDialog) {
          if (enableDialog) {
            unawaited(showDialog(
                context: context,
                builder: (context) => DeactivateDialog(
                      selected: () => onChanged(false),
                      title: dialogTitle,
                      message: dialogMessage,
                      caption: dialogCaption,
                    )));
          } else {
            onChanged(false);
          }
        } else {
          onChanged(value);
        }
      });
}

class DeactivateDialog extends StatelessWidget {
  final Function() selected;
  final Widget? title;
  final Widget? message;
  final Widget? caption;

  const DeactivateDialog(
      {super.key,
      required this.selected,
      this.title,
      this.message,
      this.caption});

  @override
  Widget build(BuildContext context) => AlertDialog(
          title: title ?? Text(AppLocalization.of(context).deactivateQuestion),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              message ?? Text(AppLocalization.of(context).deactivateAreYouSure),
              if (caption != null) caption!,
            ],
          ),
          actions: [
            OutlinedButton.icon(
                icon: const Icon(Icons.cancel),
                label: Text(AppLocalization.of(context).no),
                onPressed: () => Navigator.pop(context)),
            ElevatedButton.icon(
                icon: const Icon(Icons.remove),
                label: Text(AppLocalization.of(context).deactivate),
                onPressed: () async {
                  Navigator.pop(context);
                  selected();
                })
          ]);

          
}

