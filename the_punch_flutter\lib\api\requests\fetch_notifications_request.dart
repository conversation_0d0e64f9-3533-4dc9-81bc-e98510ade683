import 'package:json_annotation/json_annotation.dart';
import '../../dataModel/data/the_punch_notifications.dart';
import '../../misc/json_conversion.dart';
import 'system.dart';

part 'fetch_notifications_request.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class FetchNotificationsRequest extends SystemRequest {
  final String targetUserId;

  FetchNotificationsRequest({
    required this.targetUserId,
    required String serverIP,
    required String databaseName,
    required String sessionId,
  }) : super(serverIP: serverIP, databaseName: databaseName, sessionId: sessionId);

  static Future<FetchNotificationsRequest> create(String targetUserId) async {
    final systemRequest = await SystemRequest.create(); // Fetch system details
    return FetchNotificationsRequest(
      targetUserId: targetUserId,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory FetchNotificationsRequest.fromJson(Map<String, dynamic> json) =>
      _$FetchNotificationsRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$FetchNotificationsRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class FetchNotificationsResponse extends SystemResponse {
  final List<ThePunchNotification> notifications;

  FetchNotificationsResponse({
    required this.notifications,
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory FetchNotificationsResponse.fromJson(Map<String, dynamic> json) =>
      _$FetchNotificationsResponseFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$FetchNotificationsResponseToJson(this);
}
