import 'package:json_annotation/json_annotation.dart';
import 'system.dart'; // your SystemRequest is typically declared here

part 'delete_chat_group_request.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class DeleteChatGroupRequest extends SystemRequest {
  final String groupId;

  DeleteChatGroupRequest({
    required this.groupId,
    required super.serverIP,
    required super.databaseName,
    required super.sessionId,
  });

  /// Create a new instance by pulling the current session info from [SystemRequest.create()].
  static Future<DeleteChatGroupRequest> create(String groupId) async {
    final systemReq = await SystemRequest.create();
    return DeleteChatGroupRequest(
      groupId: groupId,
      serverIP: systemReq.serverIP,
      databaseName: systemReq.databaseName,
      sessionId: systemReq.sessionId,
    );
  }

  factory DeleteChatGroupRequest.fromJson(Map<String, dynamic> json) =>
      _$DeleteChatGroupRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() {
    final data = _$DeleteChatGroupRequestToJson(this);
    data.removeWhere((key, value) => value == null);
    return data;
  }
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class DeleteChatGroupResponse {
  final String errorCode;
  final String errorMessage;

  bool get isError => errorCode != "0";

  DeleteChatGroupResponse({
    required this.errorCode,
    required this.errorMessage,
  });

  factory DeleteChatGroupResponse.fromJson(Map<String, dynamic> json) {
    return DeleteChatGroupResponse(
      errorCode: json['ErrorCode'] as String? ?? '',
      errorMessage: json['ErrorMessage'] as String? ?? '',
    );
  }
}
