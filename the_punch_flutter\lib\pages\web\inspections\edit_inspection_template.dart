import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../../dataModel/data/inspection_template.dart';
import '../../../dataModel/data_model.dart';
import '../../../dataModel/data/location.dart';
import '../../../dialogs/delete_dialog.dart';
import '../../../dialogs/save_dialog.dart';
import '../../../helpers/color_helper.dart';
import '../../view_model_mixin.dart';
import '../../../state/server_time_state.dart';
import '../../../widgets/decorated_text_field.dart';
import 'package:collection/collection.dart';
import '../../../widgets/padded_card.dart';
import '../home/<USER>';
import '../my_scaffold.dart';

class EditInspectionTemplatePage extends StatelessWidget {
  final String id;
  final String? locationId;
  final String anything;
  late final viewModel = _ViewModel(id, locationId);

  EditInspectionTemplatePage(Map<String, String> queryParms, {super.key})
      : locationId = queryParms['locationId'] ?? '',
        id = queryParms['id'] ?? '',
        anything = queryParms['anything'] ?? '';

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider.value(
        value: viewModel,
        builder: (context, child) => MyScaffold(
          title: anything == '1'
              ? AppLocalizations.of(context)!.addInspectionTemplates
              : AppLocalizations.of(context)!.editInspectionTemplates,
          body: _Body(),
        ),
      );
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Stack(children: [
        SingleChildScrollView(
          child: FocusScope(
            child: Column(
              children: [
                
                _LocationName(),
                _TemplateName(),
                const Areas(),
              ],
            ),
          ),
        ),
        Align(
          alignment: Alignment.bottomRight,
          child: _SaveButton(),
        ),
      ]);
}

class _TemplateName extends StatefulWidget {
  @override
  __TemplateNameState createState() => __TemplateNameState();
}

class __TemplateNameState extends State<_TemplateName> {
  @override
  Widget build(BuildContext context) => Padding(
        padding: const EdgeInsets.all(8),
        child: Consumer<_ViewModel>(builder: (context, viewModel, _) {
          final template = viewModel.template;
          if (template == null) return Container();

          return ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 500),
            child: DecoratedTextField(
              initialValue: template.name,
              minLines: 1,
              hintText: 'Template Name Here',
              maxLines: 5,
              onChanged: (value) =>
                  setState(() => viewModel.setTemplateName(value)),
              validator: (value) =>
                  value == null ? 'Template name required.' : null,
            ),
          );
        }),
      );
}

class _LocationName extends StatefulWidget {
  @override
  //__TemplateNameState createState() => __TemplateNameState();
  _LocationNameState createState() => _LocationNameState();
}

class _LocationNameState extends State<_LocationName> {
  String dropdownValue = '';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline5 = theme.textTheme.headlineSmall?.copyWith(
      fontSize: 18,
      color: ColorHelper.thePunchGray(),
    );
    final headline6 = theme.textTheme.bodySmall;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 6, left: 2),
          child: Text('Current Location: ', style: headline5),
        ),
        Consumer<_ViewModel>(builder: (context, viewModel, child) {
          final locations = viewModel.locations;
          if (dropdownValue.isEmpty) {
            dropdownValue = 'AllLocations';
          }
          return DropdownButton<String>(
            value: dropdownValue,
            onChanged: (String? newValue) {
              setState(() {
                dropdownValue = newValue!;
                viewModel.location?.id = newValue;
              });
            },
            items: [
              DropdownMenuItem<String>(
                  value: 'AllLocations',
                  child: Text('All Locations', style: headline6)),
              ...locations.map((e) => DropdownMenuItem<String>(
                  value: e.id, child: Text(e.name, style: headline6))),
            ],
          );
        }),
      ],
    );
  }
}

// Keeping until API/Database error with templates not saving to any location except "All Locations" is fixed. I want to make sure that the correct location displays and this is for reference as this worked to display the correct location.
// class _LocationName extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     final theme = Theme.of(context);
//     final headline5 = theme.textTheme.headline5;

//     return Consumer<_ViewModel>(builder: (context, viewModel, _) {
//       return Text(
//         viewModel.location?.name ?? AppLocalizations.of(context)!.allLocations,
//         style: headline5,
//         textAlign: TextAlign.center,
//       );
//     });
//   }
// }

// class _ChangeLocationButton extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     final viewModel = Provider.of<_ViewModel>(context, listen: false);
//     return Padding(
//       padding: const EdgeInsets.all(8.0),
//       child: OutlinedButton.icon(
//           onPressed: () => showDialog(
//               context: context,
//               builder: (BuildContext context) {
//                 return LocationsDialog(nullSelectionName: AppLocalizations.of(context)!.allLocations, onSelection: (locationId) async => await viewModel.setLocation(locationId));
//               }),
//           icon: const Icon(Icons.pin_drop),
//           label: Text(AppLocalizations.of(context)!.changeLocation)),
//     );
//   }
// }

class Areas extends StatelessWidget {
  const Areas({super.key});
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
        builder: (context, viewModel, _) {
          final areas = viewModel.areas;
          return Column(
            children: [
              for (final area in areas)
                _AreasTile(
                  key: Key(area.id),
                  area: area,
                  viewModel: viewModel,
                ),
              Padding(
                padding: const EdgeInsets.all(30),
                child: OutlinedButton.icon(
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(
                      color: ColorHelper.thePunchRed(),
                    ),
                  ),
                  icon: const Icon(Icons.add),
                  label: const Text('Add Area'),
                  onPressed: () => viewModel.addArea(),
                ),
              ),
            ],
          );
        },
      );
}

class _AreasTile extends StatelessWidget {
  const _AreasTile({super.key, required this.area, required this.viewModel});

  final InspectionTemplateArea area;
  final _ViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    final removeAreaButton = Padding(
      padding: const EdgeInsets.all(8),
      child: Tooltip(
        message: 'Remove Area and Items',
        child: OutlinedButton.icon(
          style: OutlinedButton.styleFrom(
              side: BorderSide(
            color: ColorHelper.thePunchRed(),
          )),
          icon: const Icon(Icons.delete),
          label: const Text('Remove Area'),
          onPressed: () => viewModel.removeArea(area.id),
        ),
      ),
    );

    return ConstrainedBox(
      constraints: const BoxConstraints(maxWidth: 1200),
      child: PaddedCard(
        elevation: 0,
        child: Column(
          children: [
            _AreaTile(area: area, viewModel: viewModel),
            ...viewModel
                .itemsByArea(area.id)
                .map((item) => _ItemTile(
                    key: Key(item.id), viewModel: viewModel, item: item))
                .toList(),
            Padding(
              key: const Key('AddItemButton'),
              padding: const EdgeInsets.all(8),
              child: Center(
                child: OutlinedButton.icon(
                  style: OutlinedButton.styleFrom(
                      side: BorderSide(
                    color: ColorHelper.thePunchRed(),
                  )),
                  icon: const Icon(Icons.add),
                  label: const Text('Add Item'),
                  onPressed: () => viewModel.addItem(area.id),
                ),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                if (viewModel.areas.length > 1) removeAreaButton,
                //if (viewModel.areas.isEmpty) Container(),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _AreaTile extends StatefulWidget {
  const _AreaTile({required this.area, required this.viewModel});

  final InspectionTemplateArea area;
  final _ViewModel viewModel;

  @override
  __AreaTileState createState() => __AreaTileState();
}

class __AreaTileState extends State<_AreaTile> {
  @override
  Widget build(BuildContext context) => ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 800),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: DecoratedTextField(
            initialValue: widget.area.name,
            minLines: 1,
            hintText: 'Area Name Here',
            maxLines: 5,
            onChanged: (value) => setState(
                () => widget.viewModel.setAreaName(widget.area.id, value)),
            validator: (value) =>
                value == null ? 'Area name is required.' : null,
          ),
        ),
      );
}

class _ItemTile extends StatefulWidget {
  const _ItemTile({super.key, required this.viewModel, required this.item});

  final _ViewModel viewModel;
  final InspectionTemplateItem item;

  @override
  __ItemTileState createState() => __ItemTileState();
}

class __ItemTileState extends State<_ItemTile> {
  @override
  Widget build(BuildContext context) => Padding(
        padding: const EdgeInsets.all(8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 300),
              child: DecoratedTextField(
                initialValue: widget.item.name,
                minLines: 1,
                hintText: 'Item Name Here',
                maxLines: 5,
                onChanged: (value) => setState(
                    () => widget.viewModel.setItemName(widget.item.id, value)),
                validator: (value) =>
                    value == null ? 'Template Name is required.' : null,
              ),
            ),
            if (widget.viewModel.items.length > 1)
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () => widget.viewModel.removeItem(widget.item.id),
                tooltip: 'Remove Item',
              ),
          ],
        ),
      );
}

class _SaveButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<_ViewModel>(context, listen: false);
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.all(8),
          child: OutlinedButton(
            style: OutlinedButton.styleFrom(
              side: BorderSide(
                color: ColorHelper.thePunchRed(),
              ),
            ),
            onPressed: () async => await showDialog(
              context: context,
              builder: (context) => DeleteDialog(
                  message: 'Are you sure you want to cancel this?',
                  buttonText: AppLocalizations.of(context)!.cancel,
                  delete: () async {
                    context.go('/inspectionTemplates');
                  }),
            ),
            child: Text(AppLocalizations.of(context)!.cancel),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(8),
          child: ValueListenableBuilder<bool>(
            valueListenable: viewModel.isValid,
            builder: (context, isValid, child) => ElevatedButton.icon(
                onPressed: !isValid
                    ? null
                    : () async => await showDialog(
                        context: context,
                        builder: (context) => SaveDialog(save: () async {
                              context.pop();
                              await viewModel.save();
                            })),
                icon: const Icon(Icons.save),
                label: Text(AppLocalizations.of(context)!.save)),
          ),
        ),
      ],
    );
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String id;
  final String? locationId;
  Iterable<Location> locations = [];

  InspectionTemplate? template;
  Location? location;
  List<InspectionTemplateArea> areas = [];
  List<InspectionTemplateItem> items = [];
  final List<String> _removedAreas = [];
  final List<String> _removedItems = [];
  final isValid = ValueNotifier(false);

  _ViewModel(this.id, this.locationId) {
    addListenables([
      DataModel().inspectionModel,
      DataModel().locationModel,
      DataModel().inspectionAreaModel,
      DataModel().inspectionItemModel,
      DataModel().inspectionImageModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    locations = await DataModel().locationModel.active;
    if (id.isEmpty) {
      template = InspectionTemplate.create()
        ..isDirty = true
        ..locationId = locationId;
      final area = InspectionTemplateArea.create()
        ..isDirty = true
        ..inspectionTemplateId = template!.id
        ..order = areas.length;
      areas.add(area);
      final item = InspectionTemplateItem.create()
        ..isDirty = true
        ..inspectionTemplateId = template!.id
        ..inspectionTemplateAreaId = area.id
        ..order = items.length;
      items.add(item);
    } else {
      template = await DataModel().inspectionTemplateModel.getById(id);
      areas = (await DataModel()
              .inspectionTemplateAreaModel
              .getActiveByInspectionTemplateId(id))
          .toList();
      items = (await DataModel()
              .inspectionTemplateItemModel
              .getActiveByInspectionTemplateId(id))
          .toList();
      areas.sort((a, b) => a.order.compareTo(b.order));
      items.sort((a, b) => a.order.compareTo(b.order));
      isValid.value = _isValid;
    }
    if (template?.locationId != null) {
      location = await DataModel().locationModel.getById(template!.locationId!);
    }

    notifyListeners();
  }

  Future<void> setLocation(String locationId) async {
    template!.locationId = locationId;
    if (template?.locationId == null) {
      location = null;
    } else {
      location = await DataModel().locationModel.getById(template!.locationId!);
    }
    template!.isDirty = true;
    template!.lastChangedOn = ServerTimeState().utcTime;
    notifyListeners();
  }

  void setTemplateName(String name) {
    template!.name = name;
    template!.isDirty = true;
    template!.lastChangedOn = ServerTimeState().utcTime;
    isValid.value = _isValid;
  }

  void addArea() {
    final area = InspectionTemplateArea.create()
      ..isDirty = true
      ..inspectionTemplateId = template!.id
      ..order = areas.length;
    areas.add(area);
    final item = InspectionTemplateItem.create()
      ..isDirty = true
      ..inspectionTemplateId = template!.id
      ..inspectionTemplateAreaId = area.id
      ..order = items.length;
    items.add(item);
    isValid.value = _isValid;
    notifyListeners();
  }

  void setAreaName(String id, String name) {
    final area = areas.firstWhere((e) => e.id == id);
    area.name = name;
    area.isDirty = true;
    area.lastChangedOn = ServerTimeState().utcTime;
    isValid.value = _isValid;
  }

  void removeArea(String id) {
    _removedAreas.add(id);
    _removedItems.addAll(
        items.where((e) => e.inspectionTemplateAreaId == id).map((e) => e.id));
    areas.removeWhere((e) => e.id == id);
    items.removeWhere((e) => e.inspectionTemplateAreaId == id);
    isValid.value = _isValid;
    notifyListeners();
  }

  void addItem(String areaId) {
    final item = InspectionTemplateItem.create()
      ..isDirty = true
      ..inspectionTemplateId = template!.id
      ..inspectionTemplateAreaId = areaId
      ..order = items.length;
    items.add(item);
    isValid.value = _isValid;
    notifyListeners();
  }

  void setItemName(String id, String name) {
    final item = items.firstWhere((e) => e.id == id);
    item.name = name;
    item.isDirty = true;
    item.lastChangedOn = ServerTimeState().utcTime;
    isValid.value = _isValid;
  }

  void removeItem(String id) {
    _removedItems.add(id);
    items.removeWhere((e) => e.id == id);
    isValid.value = _isValid;
    notifyListeners();
  }

  List<InspectionTemplateItem> itemsByArea(String id) =>
      items.where((e) => e.inspectionTemplateAreaId == id).toList();

  bool get _isValid {
    if (template!.name.isEmpty) return false;
    if (areas.isEmpty) return false;
    if (areas.any((e) => e.name.isEmpty)) return false;
    if (items.any((e) => e.name.isEmpty)) return false;
    return true;
  }

  Future<void> save() async {
    final removedAreas =
        (await DataModel().inspectionTemplateAreaModel.getByIds(_removedAreas))
            .toList();
    final removedItems =
        (await DataModel().inspectionTemplateItemModel.getByIds(_removedItems))
            .toList();
    for (final e in removedAreas) {
      e.isActive = false;
    }
    for (final e in removedItems) {
      e.isActive = false;
    }

    areas.sort((a, b) => a.order.compareTo(b.order));
    for (var i = 0; i < areas.length; i++) {
      if (areas[i].order != i) {
        areas[i].order = i;
        areas[i].isDirty = true;
        areas[i].lastChangedOn = ServerTimeState().utcTime;
      }
    }

    final itemsInAreas =
        items.groupListsBy((e) => e.inspectionTemplateAreaId).values;
    for (final itemsInArea in itemsInAreas) {
      itemsInArea.sort((a, b) => a.order.compareTo(b.order));
      for (var i = 0; i < itemsInArea.length; i++) {
        if (itemsInArea[i].order != i) {
          itemsInArea[i].order = i;
          itemsInArea[i].isDirty = true;
          itemsInArea[i].lastChangedOn = ServerTimeState().utcTime;
        }
      }
    }

    final areas0 =
        areas.where((e) => e.isDirty).followedBy(removedAreas).toList();
    final items0 =
        items.where((e) => e.isDirty).followedBy(removedItems).toList();

    if (areas0.isNotEmpty || items0.isNotEmpty) {
      template!.isDirty = true;
      template!.lastChangedOn = ServerTimeState().utcTime;
    }

    if (template!.isDirty) {
      await DataModel().inspectionTemplateModel.saveDirty([template!]);
    }
    if (areas0.isNotEmpty) {
      await DataModel().inspectionTemplateAreaModel.saveDirty(areas0);
    }
    if (items0.isNotEmpty) {
      await DataModel().inspectionTemplateItemModel.saveDirty(items0);
    }
  }

  void setAreaOrder(int oldIndex, int newIndex) {
    final area = areas[oldIndex];

    if (newIndex < oldIndex) {
      for (var i = newIndex; i < areas.length; i++) {
        areas[i].order = i + 2;
      }
      area.order = newIndex + 1;
    } else {
      for (var i = oldIndex; i < newIndex; i++) {
        areas[i].order = i;
      }
      area.order = newIndex;
    }

    areas.sort((a, b) => a.order.compareTo(b.order));
    notifyListeners();
  }

  void setItemOrder(int oldIndex, int newIndex, String areaId) {
    final itemsInArea =
        items.where((e) => e.inspectionTemplateAreaId == areaId).toList();
    itemsInArea.sort((a, b) => a.order.compareTo(b.order));
    final item = itemsInArea[oldIndex];

    if (newIndex < oldIndex) {
      for (var i = newIndex; i < areas.length; i++) {
        itemsInArea[i].order = i + 2;
      }
      item.order = newIndex + 1;
    } else {
      for (var i = oldIndex; i < newIndex; i++) {
        itemsInArea[i].order = i;
      }
      item.order = newIndex;
    }

    items.sort((a, b) => a.order.compareTo(b.order));
    notifyListeners();
  }
}
