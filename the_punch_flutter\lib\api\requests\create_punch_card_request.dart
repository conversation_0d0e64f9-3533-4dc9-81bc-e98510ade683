import 'package:json_annotation/json_annotation.dart';
import '../../dataModel/data/punch_card.dart';
import '../../misc/json_conversion.dart';
import 'system.dart';

part 'create_punch_card_request.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class CreatePunchCardRequest extends SystemRequest {
  /// Instead of separate fields, we just send one entire PunchCard object.
  final PunchCard punchCard;

  CreatePunchCardRequest({
    required this.punchCard,
    required super.serverIP,
    required super.databaseName,
    required super.sessionId,
  });

  /// Like your `PostPunchCardHistoryRequest.create(...)`,
  /// we call `SystemRequest.create()` to automatically fill serverIP, etc.
  static Future<CreatePunchCardRequest> create(PunchCard punchCard) async {
    final systemReq = await SystemRequest.create();
    return CreatePunchCardRequest(
      punchCard: punchCard,
      serverIP: systemReq.serverIP,
      databaseName: systemReq.databaseName,
      sessionId: systemReq.sessionId,
    );
  }

  factory CreatePunchCardRequest.fromJson(Map<String, dynamic> json) =>
      _$CreatePunchCardRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() {
    final data = _$CreatePunchCardRequestToJson(this);
    // Remove null fields if you want to keep the payload clean
    data.removeWhere((key, value) => value == null);
    return data;
  }
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class CreatePunchCardResponse extends SystemResponse {
  final bool success;

  CreatePunchCardResponse({
    required this.success,
    // Matches your SystemResponse constructor style:
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory CreatePunchCardResponse.fromJson(Map<String, dynamic> json) =>
      _$CreatePunchCardResponseFromJson(json);

  @override
  Map<String, dynamic> toJson() =>
      _$CreatePunchCardResponseToJson(this);
}
