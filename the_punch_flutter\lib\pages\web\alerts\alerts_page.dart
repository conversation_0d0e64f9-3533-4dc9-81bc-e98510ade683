import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../dataModel/data/alert.dart';
import '../../../dataModel/data/punch_card.dart';
import '../../../dataModel/data_model.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/extensions.dart';
import '../../view_model_mixin.dart';
import '../my_scaffold.dart';
import '../reports/widgets/layout.dart';

/// Simple data holder for a PunchCard + its associated alerts.

// Styles moved into respective widget build methods for better scoping

/// ViewModel that pulls all PunchCards and their alerts, then
/// groups them by user name.
class _AlertsViewModel extends ChangeNotifier with ViewModelMixin {
  bool isLoading = false;

  /// Map of `username` → list of `_PunchCardAlertsData`
  Map<String, List<_PunchCardAlertsData>> groupedPunchCards = {};

  /// Selected user's alerts data for the Layout widget
  List<Map<String, dynamic>>? selectedUserAlerts;

  _AlertsViewModel() {
    // Add listenable data models
    DataModel().punchCardModel.addListener(notifyListeners);
    DataModel().alertModel.addListener(notifyListeners);
  }

  @override
  Future<void> refresh() async {
    try {
      isLoading = true;
      notifyListeners();

      final allPunchCards = await DataModel().punchCardModel.all;
      final tempMap = <String, List<_PunchCardAlertsData>>{};

      for (final pc in allPunchCards) {
        final fetchedAlerts =
            await DataModel().alertModel.getActiveByPunchCardId(pc.id);

        if (fetchedAlerts.isNotEmpty) {
          final user = await DataModel().userModel.getById(pc.userId);
          final userName = (user != null && user.name.trim().isNotEmpty)
              ? user.name
              : 'Unknown User';

          tempMap.putIfAbsent(userName, () => []);
          tempMap[userName]!.add(
            _PunchCardAlertsData(
              punchCard: pc,
              alerts: fetchedAlerts.toList(),
            ),
          );
        }
      }

      // Sort punch cards for each user by clockIn
      tempMap.forEach((userName, listOfData) {
        listOfData.sort((a, b) => a.punchCard.clockedIn.compareTo(b.punchCard.clockedIn));
      });

      groupedPunchCards = tempMap;
    } catch (e) {
      debugPrint('Failed to load PunchCards or Alerts: $e');
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }
}

// Scroll Behavior Customization
class CustomScrollBehavior extends ScrollBehavior {
  Widget buildViewportChrome(
      BuildContext context, Widget child, AxisDirection axisDirection) => child;
}

/// A page to display alerts in a modern dashboard layout
class AlertsPage extends StatelessWidget {
  const AlertsPage({super.key});

  @override
  Widget build(BuildContext context) => MyScaffold(
        title: 'Reports',
        titleWidget: const Text(
          'Alerts',
          style: TextStyle(
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w700,
            fontSize: 40,
            letterSpacing: -0.4,
            color: Color(0xFF091F30),
          ),
        ),
        body: ChangeNotifierProvider<_AlertsViewModel>(
          create: (_) => _AlertsViewModel()..refresh(),
          child:  _AlertsBody(),
        ),
      );
}

/// Modern alerts dashboard layout
class _AlertsBody extends StatefulWidget {
  const _AlertsBody();

  @override
  State<_AlertsBody> createState() => _AlertsBodyState();
}

class _AlertsBodyState extends State<_AlertsBody> {
  String selectedUser = '';
  bool _isScheduleAlert(Alert alert) {
    final id = alert.alertTypeId.toUpperCase();
    return id == 'ED3D03ED-EDEE-409A-8DB8-030A183934E0' ||
        id == 'C6F68F26-D161-41B5-A6B8-9DDA683BF904' ||
        id == '22C5F8A2-C660-411F-AA91-12A7DF664C1F' ||
        id == '0A9CB952-FDB5-4CA1-AD2F-FCCFB13037AF' ||
        id == '3E668A44-6995-4DBF-BB20-593D7E2FEA57';
  }

  bool _isGeofenceAlert(Alert alert) {
    final id = alert.alertTypeId.toUpperCase();
    return id == '2D8EA258-9199-4375-81FC-61414128ABD1' ||
        id == '883EBCC9-81B2-4769-ACFA-7F3A0E9BEEB1';
  }

  bool _isBreach(Alert alert) =>
      alert.alertTypeId.toUpperCase() == '2D8EA258-9199-4375-81FC-61414128ABD1';
  bool _isReEnter(Alert alert) =>
      alert.alertTypeId.toUpperCase() == '883EBCC9-81B2-4769-ACFA-7F3A0E9BEEB1';

  /// Helper to compute total "breach" time for a single PunchCard
  Duration _computeBreachTimeForPunchCard(List<Alert> alerts, PunchCard pc) {
    final sorted = alerts.toList()..sort((a, b) => a.alertOn.compareTo(b.alertOn));
    Duration total = Duration.zero;
    Alert? currentBreach;

    for (int i = 0; i < sorted.length; i++) {
      final a = sorted[i];
      final isLast = (i == sorted.length - 1);

      // If we hit a BREACH
      if (_isBreach(a) && currentBreach == null) {
        currentBreach = a;
      }
      // If we find a RE-ENTER or we've reached the last alert => close out a breach
      if (currentBreach != null && (_isReEnter(a) || isLast)) {
        final isPunchOut = !_isReEnter(a) && isLast;
        final endTime = isPunchOut ? (pc.clockedOut ?? a.alertOn) : a.alertOn;
        total += endTime.difference(currentBreach.alertOn);
        currentBreach = null;
      }
    }
    return total;
  }

  /// Formats a Duration like "1h 23m 45s", "10m 2s", etc.
  String _formatDuration(Duration d) {
    final hours = d.inHours;
    final minutes = d.inMinutes % 60;
    final seconds = d.inSeconds % 60;
    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }
  /// Build the stats cards at the top of the page
  Widget _buildStatsSection(BuildContext context, _AlertsViewModel vm) {
    // We're using hardcoded values to match the design in the image
    // In a real implementation, we would calculate these values from the data
    int globalAlerts = 0;
    int globalSchedule = 0;
    int globalGeofence = 0;
    Duration globalBreachTime = Duration.zero;

    vm.groupedPunchCards.forEach((userName, punchCardDataList) {
      for (final pcData in punchCardDataList) {
        final alerts = pcData.alerts;
        globalAlerts += alerts.length;

        for (final a in alerts) {
          if (_isScheduleAlert(a)) globalSchedule++;
          if (_isGeofenceAlert(a)) globalGeofence++;
        }
        globalBreachTime += _computeBreachTimeForPunchCard(alerts, pcData.punchCard);
      }
    });

    final breachStr = _formatDuration(globalBreachTime);
    // Create the stats data for the cards
    final statsData = [
      {
        'title': 'Total Alerts',
        'value': globalAlerts.toString(),
        'iconUrl': Icons.warning_rounded,
        'iconColor': Colors.amber,
      },
      {
        'title': 'Total Schedule Alerts',
        'value': globalSchedule.toString(),
        'iconUrl': Icons.calendar_today_rounded,
        'iconColor': Colors.blue,
      },
      {
        'title': 'Total Geofence Alerts',
        'value': globalGeofence.toString(),
        'iconUrl': Icons.location_off_rounded,
        'iconColor': Colors.red,
      },
      {
        'title': 'Total Combined Breached Time',
        'value': breachStr,
        'iconUrl': Icons.public_rounded,
        'iconColor': Colors.green,
      },
    ];

    return SizedBox(
      width: double.infinity,
      child: Wrap(
        spacing: 16,
        runSpacing: 16,
        children: statsData.map(_buildStatCard).toList(),
      ),
    );
  }

  Widget _buildStatCard(Map<String, dynamic> data) => Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(32),
        ),
        color: const Color(0xFFEBF6FF),
        child: Container(
          width: 269.25,
          height: 162,
          padding: const EdgeInsets.fromLTRB(32, 32, 32, 28),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Text(
                      data['title'],
                      style: const TextStyle(
                        fontFamily: 'Poppins',
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        letterSpacing: -0.14,
                        color: Color(0xFF091F30),
                      ),
                    ),
                  ),
                  Icon(
                    data['iconUrl'],
                    size: 32,
                    color: data['iconColor'],
                  ),
                ],
              ),
              const Padding(
                padding: EdgeInsets.only(bottom: 16),
              ),
              Text(
                data['value'],
                style: const TextStyle(
                  fontFamily: 'Poppins',
                  fontSize: 32,
                  fontWeight: FontWeight.w600,
                  height: 1.375,
                  color: Color(0xFF091F30),
                ),
              ),
            ],
          ),
        ),
      );

  /// Build the data table with employee alerts
  Widget _buildTableSection(BuildContext context, _AlertsViewModel viewModel) {
    // Generate table data from viewModel.groupedPunchCards
    final tableData = <Map<String, dynamic>>[];    viewModel.groupedPunchCards.forEach((userName, punchCardDataList) {
      final userScheduleCount = punchCardDataList.fold<int>(
        0,
        (sum, pcData) => sum + pcData.alerts.where(_isScheduleAlert).length,
      );
      final userGeofenceCount = punchCardDataList.fold<int>(
        0,
        (sum, pcData) => sum + pcData.alerts.where(_isGeofenceAlert).length,
      );
      final userBreachTime = punchCardDataList.fold<Duration>(
        Duration.zero,
        (sum, pcData) => sum + _computeBreachTimeForPunchCard(pcData.alerts, pcData.punchCard),
      );

      // Add this user's data to the tableData
      tableData.add({
        'name': userName,
        'scheduleAlerts': userScheduleCount.toString(),
        'geofenceAlerts': userGeofenceCount.toString(),
        'totalBreachedTime': _formatDuration(userBreachTime),
        'isHighlighted': selectedUser == userName,
      });
    });

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32),
        border: Border.all(
          color: const Color(0xFFEBF6FF),
          width: 1.5,
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns: _buildTableColumns(),
          rows: _buildTableRows(tableData),
          headingRowHeight: 56,
          dataRowMinHeight: 52,
          dataRowMaxHeight: 52,
          columnSpacing: 12,
          horizontalMargin: 24,
        ),
      ),
    );
  }

  List<DataColumn> _buildTableColumns() => [
        DataColumn(label: _buildHeaderCell('Name')),
        DataColumn(label: _buildHeaderCell('Schedule Alerts')),
        DataColumn(label: _buildHeaderCell('Geofence Alerts')),
        DataColumn(label: _buildHeaderCell('Total Breached Time')),
        DataColumn(label: _buildHeaderCell('Action')),
      ];

  Widget _buildHeaderCell(String text) => Text(
        text,
        style: const TextStyle(
          fontFamily: 'Poppins',
          fontSize: 11,
          fontWeight: FontWeight.w700,
          color: Color(0xFF091F30),
        ),
      );

  List<DataRow> _buildTableRows(List<Map<String, dynamic>> tableData) =>
      tableData.map(_buildTableRow).toList();

  DataRow _buildTableRow(Map<String, dynamic> data) => DataRow(
        color: MaterialStateProperty.resolveWith<Color?>(
            (Set<MaterialState> states) {
          if (data['isHighlighted'] == true) {
            return const Color(0xFFDAF9E1);
          }
          return null;
        }),
        cells: [          _buildCell(data['name']),
          _buildCell(data['scheduleAlerts']),
          _buildCell(data['geofenceAlerts']),
          _buildBreachedTimeCell(data['totalBreachedTime']),
          _buildActionCell(data),
        ],
      );

  DataCell _buildCell(String text) => DataCell(
        Text(
          text,
          style: const TextStyle(
            fontFamily: 'Poppins',
            fontSize: 11,
            fontWeight: FontWeight.w500,
            color: Color(0xFF091F30),
          ),
        ),
      );

  DataCell _buildBreachedTimeCell(String text) => DataCell(
        Text(
          text,
          style: const TextStyle(
            fontFamily: 'Poppins',
            fontSize: 11,
            fontWeight: FontWeight.w500,
            color: Colors.red,
          ),
        ),
      );

  DataCell _buildActionCell(Map<String, dynamic> data) => DataCell(
        Consumer<_AlertsViewModel>(
          builder: (context, viewModel, _) => ElevatedButton(
            onPressed: () {
              // Get the user's name from the current row
              final userName = data['name'] as String;
              this.selectedUser = userName;
              // Get the punch card data for this user
              final punchCardDataList = viewModel.groupedPunchCards[userName] ?? [];

              if (punchCardDataList.isNotEmpty) {
                // Prepare the data for the Layout widget
                final alertsData = punchCardDataList.map((pcData) {
                  // Calculate breached time for this punch card
                  final breachedTime = _formatDuration(
                    _computeBreachTimeForPunchCard(pcData.alerts, pcData.punchCard)
                  );

                  return {
                    'punchCard': pcData.punchCard,
                    'alerts': pcData.alerts,
                    'breachedTime': breachedTime,
                  };
                }).toList();

                // Update the state to show the selected user's alerts
                viewModel.selectedUserAlerts = alertsData;
                viewModel.notifyListeners();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: Colors.blue,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
                side: const BorderSide(color: Colors.blue),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            child: const Text(
              'View Alerts',
              style: TextStyle(
                fontFamily: 'Poppins',
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      );

@override
Widget build(BuildContext context) => Consumer<_AlertsViewModel>(
  builder: (context, viewModel, child) {
    if (viewModel.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    // Wrap the entire scrollable content in a CustomScrollBehavior
    return ScrollConfiguration(
      behavior: CustomScrollBehavior(),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStatsSection(context, viewModel),
              const SizedBox(height: 32),
              // Use a Column with SizedBox instead of IntrinsicHeight
              SizedBox(
                height: 600, // Set a fixed height or use MediaQuery to make it responsive
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Flexible(
                      flex: 2,
                      child: _buildTableSection(context, viewModel),
                    ),
                    const SizedBox(width: 16), // Add spacing between columns
                    Flexible(
                      flex: 2,
                      child: Layout(alertsData: viewModel.selectedUserAlerts, userName: selectedUser),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  },
);
}



/// Widget that displays the list of alerts for a single PunchCard:
/// merges BREACH→REENTER or BREACH→PUNCHOUT into one row, etc.
class _AlertsListWidget extends StatelessWidget {
  final PunchCard punchCard;
  final List<Alert> alerts;

  const _AlertsListWidget({
    Key? key,
    required this.punchCard,
    required this.alerts,
  }) : super(key: key);

  bool _isBreach(Alert alert) =>
      alert.alertTypeId.toUpperCase() == '2D8EA258-9199-4375-81FC-61414128ABD1';
  bool _isReEnter(Alert alert) =>
      alert.alertTypeId.toUpperCase() == '883EBCC9-81B2-4769-ACFA-7F3A0E9BEEB1';

  @override
  Widget build(BuildContext context) {
    final alertWidgets = <Widget>[];
    Alert? currentBreach;

    for (int i = 0; i < alerts.length; i++) {
      final currentAlert = alerts[i];
      final isLast = (i == alerts.length - 1);

      // If it's a BREACH
      if (_isBreach(currentAlert) && currentBreach == null) {
        currentBreach = currentAlert;
      }

      // If we found a RE-ENTER or we've reached the last alert
      if (currentBreach != null && (_isReEnter(currentAlert) || isLast)) {
        final isPunchOutScenario = !_isReEnter(currentAlert) && isLast;
        final endTime = isPunchOutScenario
            ? (punchCard.clockedOut ?? currentAlert.alertOn)
            : currentAlert.alertOn;
        final breachDuration = endTime.difference(currentBreach.alertOn);

        alertWidgets.add(
          _buildBreachRow(
            context,
            breachAlert: currentBreach,
            reenterAlert: isPunchOutScenario ? null : currentAlert,
            duration: breachDuration,
            punchCardClockOut: punchCard.clockedOut,
          ),
        );
        currentBreach = null;
      }

      // If it's not a BREACH or RE-ENTER, it’s a "normal" alert
      if (!_isBreach(currentAlert) && !_isReEnter(currentAlert)) {
        alertWidgets.add(
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Align(
              alignment: Alignment.centerLeft,
              child: _buildAlertCard(
                context,
                currentAlert,
                dotColor: Colors.grey,
                timeColor: Colors.black,
              ),
            ),
          ),
        );
      }
    }

    return Column(children: alertWidgets);
  }

  Widget _buildBreachRow(
    BuildContext context, {
    required Alert breachAlert,
    required Alert? reenterAlert,
    required Duration duration,
    required DateTime? punchCardClockOut,
  }) => Container(
      margin: const EdgeInsets.only(bottom: 8),
      width: double.infinity,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          _buildAlertCard(
            context,
            breachAlert,
            dotColor: ColorHelper.thePunchRed(),
            timeColor: ColorHelper.thePunchRed(),
          ),
          _buildArrowWithDuration(duration),
          if (reenterAlert != null)
            _buildAlertCard(
              context,
              reenterAlert,
              dotColor: ColorHelper.thePunchBlue(),
              timeColor: ColorHelper.thePunchBlue(),
            )
          else
            _buildNeverReenteredCard(context, punchCardClockOut),
        ],
      ),
    );

  Widget _buildArrowWithDuration(Duration duration) {
    final totalSec = duration.inSeconds;
    final mins = totalSec ~/ 60;
    final secs = totalSec % 60;
    final text = '${mins}m ${secs}s';

    return Container(
      width: 105,
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            text,
            style: TextStyle(color: ColorHelper.thePunchRed(), fontSize: 10),
          ),
          Icon(Icons.arrow_forward, color: ColorHelper.thePunchGray()),
        ],
      ),
    );
  }

  Widget _buildNeverReenteredCard(BuildContext context, DateTime? punchOutTime) {
    final locale = Localizations.localeOf(context);
    final outStr = punchOutTime == null
        ? 'LIVE'
        : punchOutTime.toFormattedTime(locale);

    return Container(
      width: 220,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 8,
                height: 8,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: ColorHelper.thePunchRed(),
                  shape: BoxShape.circle,
                ),
              ),
              Text(
                punchOutTime == null ? 'LIVE' : 'Punched Out',
                style: TextStyle(
                  color: ColorHelper.thePunchRed(),
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          Text(
            punchOutTime == null ? 'Time: LIVE' : 'Time: $outStr',
            style: TextStyle(fontSize: 12, color: ColorHelper.thePunchGray()),
          ),
        ],
      ),
    );
  }

  Widget _buildAlertCard(
    BuildContext context,
    Alert alert, {
    required Color dotColor,
    required Color timeColor,
  }) {
    final locale = Localizations.localeOf(context);

    return Container(
      width: 220,
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      padding: const EdgeInsets.all(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Dot + Title
          Row(
            children: [
              Container(
                width: 8,
                height: 8,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: dotColor,
                  shape: BoxShape.circle,
                ),
              ),
              Expanded(
                child: Text(
                  alert.name(context),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            'Time: ${alert.alertOn.toFormattedTime(locale)}',
            style: TextStyle(fontSize: 12, color: timeColor),
          ),
        ],
      ),
    );
  }
}

/// Simple data holder for a PunchCard + its associated alerts.
class _PunchCardAlertsData {
  final PunchCard punchCard;
  final List<Alert> alerts;

  _PunchCardAlertsData({
    required this.punchCard,
    required this.alerts,
  });
}



