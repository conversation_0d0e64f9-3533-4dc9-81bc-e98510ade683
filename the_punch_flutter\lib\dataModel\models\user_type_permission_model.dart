import '../base_data.dart';
import '../data/user_type_permission.dart';
import '../hive_db.dart';
import 'package:collection/collection.dart';

class UserTypePermissionModel extends BaseDataModel<UserTypePermission> {
  @override
  Future<Iterable<UserTypePermission>> get all async => (await HiveDb.database).userTypePermissions.values;

  @override
  Future<void> save(Iterable<UserTypePermission> t) async {
    await (await HiveDb.database).userTypePermissions.putAll({for (final e in t) e.id: e});
    notifyListeners();
  }

  Future<Iterable<UserTypePermission>> getByUserTypeId(String id) async => (await all).where((e) => e.userTypeId == id);

  Future<Iterable<UserTypePermission>> getActiveByUserTypeId(String id) async => (await active).where((e) => e.userTypeId == id);

  Future<UserTypePermission?> getByPermissionIdUserTypeId(String permissionId, String userTypeId) async =>
      (await all).firstWhereOrNull((e) => e.permissionId == permissionId && e.userTypeId == userTypeId);

  Future<Iterable<UserTypePermission>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }
}
