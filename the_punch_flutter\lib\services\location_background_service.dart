import 'dart:async';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_background_geolocation/flutter_background_geolocation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:geolocator/geolocator.dart' as geolocator;
import 'package:flutter_background_geolocation/flutter_background_geolocation.dart'
    as bg;
import 'package:shared_preferences/shared_preferences.dart';
import '../dataModel/data/geo_location.dart';
import '../dataModel/data_model.dart';
import '../firebase_options.dart';
import '../state/app_state.dart';
import '../state/location_ping_state.dart';
import '../state/server_time_state.dart';
import '../the_punch_app.dart';
import '../misc/my_platform.dart';

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

bool? geoFenceBreached;
StreamController<String?> geoFenceStreamController =
    StreamController<String?>.broadcast();

//Timer? locationUpdateTimer;
DateTime? breachTime;
//bool isLocationUpdatesRunning = false;

/// --------------------------
/// NEW: SharedPreferences keys & helpers for "isLocationUpdatesRunning"
/// --------------------------
const _kIsLocationUpdatesRunningKey = 'isLocationUpdatesRunningKey';

Future<bool> getIsLocationUpdatesRunning() async {
  final prefs = await SharedPreferences.getInstance();
  return prefs.getBool(_kIsLocationUpdatesRunningKey) ?? false;
}

Future<void> setIsLocationUpdatesRunning(bool value) async {
  final prefs = await SharedPreferences.getInstance();
  await prefs.setBool(_kIsLocationUpdatesRunningKey, value);
  print('setIsLocationUpdatesRunning: $value');
}
/// --------------------------

Future<void> setServiceRunning(bool value) async {
  print('setServiceRunning called with value: $value');
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  await prefs.setBool('isServiceRunning', value);
}

Future<bool> getServiceRunning() async {
  print('getServiceRunning called');
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  bool value = prefs.getBool('isServiceRunning') ?? false;
  return value;
}

Future<void> initializeService() async {
  print('initializeService called');
  await bg.BackgroundGeolocation.ready(
    bg.Config(
      enableHeadless: true, // Enable headless mode
      //debug: true,
      //logLevel: bg.Config.LOG_LEVEL_VERBOSE,
      //disableStopDetection: true,
      //pausesLocationUpdatesAutomatically: false,
      desiredAccuracy: bg.Config.DESIRED_ACCURACY_HIGH,
      distanceFilter: 30,
      stationaryRadius: 25,
      preventSuspend: true,
      //stopTimeout: 1440, // in minutes (24 hours)
      heartbeatInterval: 60, // Heartbeat every 60 seconds when stationary
      stopOnTerminate: false, // Ensure service does not stop on terminate
      startOnBoot: false,     // Optionally start the service on boot
      disableMotionActivityUpdates: false,
      foregroundService: true, // Force Android foreground service
    ),
  ).then((bg.State state) async {
    print('BackgroundGeolocation.ready completed with state: $state');
    print('MyAppDebug: Service initialized and ready.');

    // ----------------------------------------------------------------------
    // Auto-resume if we already had location updates running.
    // ----------------------------------------------------------------------
    bool wasRunning = await getIsLocationUpdatesRunning();
    print('MyAppDebug: $wasRunning');
    print('MyAppDebug: $state');
    if (wasRunning && !state.enabled) {
      print('MyAppDebug: Auto-resuming plugin because wasRunning == true');
      await bg.BackgroundGeolocation.start();
      print('MyAppDebug: BackgroundGeolocation is now started.');
    }

  }).catchError((error) {
    print('Error initializing BackgroundGeolocation: $error');
  });

  // ----------------------------------------------------------------------
  //  Listen for continuous location updates while moving.
  //     This fires every time the plugin posts a new location,
  //     as you cross distanceFilter or the OS triggers an update.
  // ----------------------------------------------------------------------
  bg.BackgroundGeolocation.onLocation((bg.Location location) async {
    print('[onLocation] lat=${location.coords.latitude}, '
          'lng=${location.coords.longitude}, '
          'accuracy=${location.coords.accuracy}, '
          'isMoving=${location.isMoving}');

    // Reuse your existing "saveLocationPing" or manager logic:
    await saveLocationPing(location, pingSource: 'onLocation');
  }, (bg.LocationError error) {
    print('[onLocation] ERROR - $error');
  });
  // ----------------------------------------------------------------------

  // Listen for motion changes
  bg.BackgroundGeolocation.onMotionChange((bg.Location location) async {

      final location = await bg.BackgroundGeolocation.getCurrentPosition(
        samples: 1,
        persist: false,
        desiredAccuracy: bg.Config.DESIRED_ACCURACY_NAVIGATION,
        timeout: 60,
        maximumAge: 5000,
      );

      print('onMotionChange event triggered with location: $location');

      if (MyPlatform.isIOS) {
        await saveLocationPing(location, pingSource: 'onMotionChange');
      } else if(MyPlatform.isAndroid){
        await saveLocationPing(location, pingSource: 'onMotionChange');
        print("MyAppDebug: [onMotionChange]");
      }
    // } else {
    //   print("MyAppDebug: [onMotionChange] Device is stationary");
    //   if (MyPlatform.isAndroid) {
    //     print('Location updates not running in stationary mode. Starting...');
    //     //locationUpdate();
    //   }
    // }
  });

  // Listen for heartbeat (on iOS, or forcibly on Android if configured)
  bg.BackgroundGeolocation.onHeartbeat((bg.HeartbeatEvent event) async {
    print('onHeartbeat event triggered with event: $event');
    print('MyAppDebug: Heartbeat triggered.');

        print('Attempting to get current position with BackgroundGeolocation.');
        final location = await bg.BackgroundGeolocation.getCurrentPosition(
          samples: 1,
          persist: false,
          desiredAccuracy: bg.Config.DESIRED_ACCURACY_NAVIGATION,
          timeout: 60,
          maximumAge: 5000,
        );
        // print('Heartbeat location obtained: ${location.coords}');    

    if (MyPlatform.isAndroid) {
      print('Platform is Android.');
      // Just rely on the location from event.location if it's non-null:
      //bg.Location? location = event.location;

      //if (location != null) {
      await saveLocationPing(location, pingSource: 'HeartbeatPing');
      //} else {
        // Optionally forcibly fetch current position (but that means an extra ping).
        // location = await bg.BackgroundGeolocation.getCurrentPosition(...);
      //}
    } else if (MyPlatform.isIOS) {
      print('Platform is iOS.');
      //bg.Location? location = event.location;

      //if (location != null) {
      await saveLocationPing(location, pingSource: 'HeartbeatPing');
      //} else {
        // Optionally forcibly fetch current position (but that means an extra ping).
        // location = await bg.BackgroundGeolocation.getCurrentPosition(...);
      //}
    }
  });

  // Register the headless task for Android
  if (MyPlatform.isAndroid) {
    await bg.BackgroundGeolocation.registerHeadlessTask(headlessTask);
    print('headlessTask registered successfully for Android.');
  }

  print('initializeService completed');
}

Future<void> stopLocationUpdates() async {
  print('stopLocationUpdates called');
  await setIsLocationUpdatesRunning(false);

  // Also mark them as NOT running in SharedPreferences
  //await setIsLocationUpdatesRunning(false);
}

Future<void> handleManagerTracking(
  LocationPingState locationPingState,
  bg.Location? location,
  DateTime pingTime, {
  String pingSource = 'UnknownManagerPing',
}) async {
  print('[handleManagerTracking] called with pingSource: $pingSource');
  String motionState = await determineMotionState(location);

  final userId = await locationPingState.getUserId();
  final punchCardId = await locationPingState.getPunchCardId();
  final Map<String, String?> lastLocation =
      await locationPingState.getLastPingLocation();
  final lastPingEndTime = await locationPingState.getLastPingEndTime();

  if (userId == null) {
    print('handleManagerTracking: userId is null; exiting.');
    return;
  }

  // Convert background_geolocation.Location to geolocator.Position
  geolocator.Position position;
  if (location != null) {
    position = geolocator.Position(
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
      timestamp: DateTime.now(),
      accuracy: location.coords.accuracy,
      altitude: location.coords.altitude,
      heading: location.coords.heading,
      speed: location.coords.speed,
      speedAccuracy: location.coords.speedAccuracy,
      altitudeAccuracy: 0.0,
      headingAccuracy: 0.0,
    );
  } else {
    // Fallback to geolocator or last known position
    try {
      position = await geolocator.Geolocator.getCurrentPosition(
        desiredAccuracy: geolocator.LocationAccuracy.best,
      );
    } catch (e) {
      print('MyAppDebug: Failed to fetch location via Geolocator: $e');
      position = defaultPosition(lastLocation);
      motionState = 'failed';
    }
  }

  print('[handleManagerTracking] Creating/saving manager ping...');
  await DataModel().travelPingModel.createAndSavePing(
    position,
    userId,
    punchCardId,
    lastLocation['latitude'],
    lastLocation['longitude'],
    lastPingEndTime,
    motionState,
    pingTime,
    pingSource: pingSource, // Pass the source to createAndSavePing
  );
}

Future<String> determineMotionState(bg.Location? location) async {
  if (location != null) {
    return location.isMoving ? "moving" : "stationary";
  } else {
    return 'failed';
  }
}

geolocator.Position defaultPosition(Map<String, String?> lastLocation) => geolocator.Position(
    latitude: double.parse(lastLocation['latitude'] ?? '0.0'),
    longitude: double.parse(lastLocation['longitude'] ?? '0.0'),
    timestamp: DateTime.now(),
    accuracy: 0.0,
    altitude: 0.0,
    heading: 0.0,
    speed: 0.0,
    speedAccuracy: 0.0,
    altitudeAccuracy: 0.0,
    headingAccuracy: 0.0,
  );

Future<void> handleNonManagerTracking(
  LocationPingState locationPingState,
  bg.Location? location, {
  String pingSource = 'UnknownNonManagerPing',
}) async {
  print('[handleNonManagerTracking] called with pingSource: $pingSource');
  try {
    final punchCardId = await locationPingState.getPunchCardId();
    if (punchCardId == null) {
      print('handleNonManagerTracking: punchCardId is null; exiting.');
      return;
    }

    geolocator.Position? position;
    if (location != null) {
      position = geolocator.Position(
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        timestamp: DateTime.now(),
        accuracy: location.coords.accuracy,
        altitude: location.coords.altitude,
        heading: location.coords.heading,
        speed: location.coords.speed,
        speedAccuracy: location.coords.speedAccuracy,
        altitudeAccuracy: 0.0,
        headingAccuracy: 0.0,
      );
    } else {
      try {
        position = await geolocator.Geolocator.getCurrentPosition(
          desiredAccuracy: geolocator.LocationAccuracy.best,
        );
      } catch (e) {
        print('MyAppDebug: Failed to fetch location via Geolocator: $e');
        position = geolocator.Position(
          latitude: 0.0,
          longitude: 0.0,
          timestamp: DateTime.now(),
          accuracy: 0.0,
          altitude: 0.0,
          heading: 0.0,
          speed: 0.0,
          speedAccuracy: 0.0,
          altitudeAccuracy: 0.0,
          headingAccuracy: 0.0,
        );
      }
    }

    final locationData = await locationPingState.getLocation();
    final geoFenceRadius = await locationPingState.getLocationGeofence();
    double distance = geolocator.Geolocator.distanceBetween(
      position.latitude,
      position.longitude,
      double.parse(locationData['latitude'] ?? '0'),
      double.parse(locationData['longitude'] ?? '0'),
    );

    bool isInsideGeofence = false;
    if (geoFenceRadius != null && geoFenceRadius['radius'] != null) {
      isInsideGeofence =
          distance <= (double.tryParse(geoFenceRadius['radius']!.toString()) ?? 0.0);
    }

    if (isInsideGeofence) {
      print('User is inside the geofence.');
      if (geoFenceBreached == true) {
        // re-entered
        await DataModel().geoLocationModel.saveGeoFencePing(
          punchCardId,
          position.latitude,
          position.longitude,
          distance,
          GeoLocation.geoFenceEnteredId,
        );
        geoFenceBreached = false;
        geoFenceStreamController.add('false');
        await showNotification(
          'Re-entry Detected',
          'You have re-entered the location.',
          0,
        );
      }
    } else {
      print('User is outside the geofence.');
      if (geoFenceBreached != true) {
        geoFenceBreached = true;
        geoFenceStreamController.add('true');
        breachTime = DateTime.now();
      }
      await DataModel().geoLocationModel.saveGeoFencePing(
        punchCardId,
        position.latitude,
        position.longitude,
        distance,
        GeoLocation.geoFenceExitedId,
      );

      if (breachTime != null) {
        int minutesElapsed = DateTime.now().difference(breachTime!).inMinutes;
        await showNotification(
          'WARNING: Breach Detected',
          'You left the location without Punching out! - Please re-enter the site or Punch Out.',
          minutesElapsed,
        );
      } else {
        await showNotification(
          'WARNING: Breach Detected',
          'You left the location without Punching out! - Please re-enter the site or Punch Out.',
          0,
        );
      }
    }
  } catch (e) {
    print('Error occurred while handling non-manager tracking: $e');
  }
}

Future<void> resetGeoFenceBreached() async {
  geoFenceBreached = null;
  geoFenceStreamController.add(null);
}

Future<void> showNotification(
  String title,
  String body,
  int minutesElapsed,
) async {
  String updatedTitle = '$title';
  String message = minutesElapsed > 0
      ? '$minutesElapsed minute${minutesElapsed > 1 ? 's' : ''} ago - $body'
      : body;

  const AndroidNotificationDetails androidPlatformChannelSpecifics =
      AndroidNotificationDetails(
    'geofence_channel_id',
    'Geofence Alerts',
    channelDescription: 'Notifications for geofence entry and exit alerts',
    importance: Importance.max,
    priority: Priority.high,
    ticker: 'ticker',
  );

  const DarwinNotificationDetails iosPlatformChannelSpecifics =
      DarwinNotificationDetails();

  const NotificationDetails platformChannelSpecifics = NotificationDetails(
    android: androidPlatformChannelSpecifics,
    iOS: iosPlatformChannelSpecifics,
  );

  await flutterLocalNotificationsPlugin.show(
    0,
    updatedTitle,
    message,
    platformChannelSpecifics,
    payload: 'item x',
  );
}

@pragma('vm:entry-point')
void headlessTask(bg.HeadlessEvent headlessEvent) async {
  print('[HeadlessTask]: Event received: ${headlessEvent}');

  // Initialize Firebase
  try {
    await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
    print('Firebase initialized successfully in headlessTask.');
  } catch (e) {
    print('Error initializing Firebase in headlessTask: $e');
  }

  // Ensure AppState is initialized
  if (!AppState.isInitialized) {
    print("MyAppDebug: AppState is not initialized. Initializing now...");
    await AppState.initialize();
    print("MyAppDebug: AppState initialized.");
  } else {
    print("MyAppDebug: AppState is already initialized.");
  }

  switch (headlessEvent.name) {
    case bg.Event.TERMINATE:
      print('HeadlessTask: App terminated event received.');
      // Handle termination if needed
      break;
    case bg.Event.HEARTBEAT:
      print('HeadlessTask: Heartbeat event received.');
      if (headlessEvent.event is bg.HeartbeatEvent) {
        bg.HeartbeatEvent heartbeatEvent =
            headlessEvent.event as bg.HeartbeatEvent;
        bg.Location? location = heartbeatEvent.location;
        if (location != null) {
          await saveLocationPing(location, pingSource: 'HeadlessHeartbeatPing');
        } else {
          print('No location data available in HeartbeatEvent.');
        }
      }
      break;
    case bg.Event.LOCATION:
      print('HeadlessTask: Location event received.');
      if (headlessEvent.event is bg.Location) {
        await saveLocationPing(
          headlessEvent.event as bg.Location,
          pingSource: 'HeadlessLocationEventPing',
        );
      }
      break;
    case bg.Event.MOTIONCHANGE:
      print('HeadlessTask: MotionChange event received.');
      if (headlessEvent.event is bg.Location) {
        await saveLocationPing(
          headlessEvent.event as bg.Location,
          pingSource: 'HeadlessMotionChangePing',
        );
      }
      break;
    default:
      print('HeadlessTask: Unhandled event type: ${headlessEvent.name}');
      break;
  }

  print('HeadlessTask processing completed for event: ${headlessEvent.name}');
}

const _kLastLocationPingTimeKey = 'lastLocationPingTimeKey';

  /// Return `null` if we have never stored a last ping time.
  @pragma('vm:entry-point')
  Future<DateTime?> getLastPingTime() async {
    final prefs = await SharedPreferences.getInstance();
    final millis = prefs.getInt(_kLastLocationPingTimeKey);
    if (millis == null) return null;
    return DateTime.fromMillisecondsSinceEpoch(millis);
  }

  Future<void> setLastPingTime(DateTime time) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_kLastLocationPingTimeKey, time.millisecondsSinceEpoch);
  }



@pragma('vm:entry-point')
Future<void> saveLocationPing(
  bg.Location location, {
  String pingSource = 'saveLocationPing',
}) async {
  // If the plugin flagged this location as `sample:true`, skip it entirely
  if (location.sample == true) {
    return;
  }

  // 1) Get the last ping time from SharedPreferences
  final lastPingTime = await getLastPingTime();
  final now = DateTime.now();

  // 2) Compare the difference in seconds
  if (lastPingTime != null) {
    final difference = now.difference(lastPingTime).inSeconds;

    // --- MODIFICATION: Skip *only* if difference < 20 AND pingSource != onMotionChange ---
    if (difference < 30 && pingSource != 'onMotionChange') {
      print(
        '[saveLocationPing]: Skipping ping, only $difference seconds '
        'since last ping and pingSource is $pingSource',
      );
      return;
    }
  }

  // 3) If we reach here, it means either:
  //    a) >= 20s have passed since last ping, OR 
  //    b) pingSource == 'onMotionChange' (which we always save).

  print('saveLocationPing triggered with location: $location from $pingSource');

  // 4) Update last ping time to "now"
  await setLastPingTime(now);

  // 5) Continue your existing logic...
  DateTime pingTime = ServerTimeState().utcTime;
  final locationPingState = LocationPingState();
  final isManager = await locationPingState.getIsManager();

  if (isManager) {
    await handleManagerTracking(
      locationPingState,
      location,
      pingTime,
      pingSource: pingSource,
    );
  } else {
    await handleNonManagerTracking(
      locationPingState,
      location,
      pingSource: pingSource,
    );
  }
}

