import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../dataModel/data_model.dart';
import '../helpers/color_helper.dart';
import '../helpers/screen_helper.dart';
import '../misc/my_platform.dart';
import '../pages/view_model_mixin.dart';
import '../state/login_state.dart';
import '../state/permissions_state.dart';
import '../state/punch_state.dart';
import '../widgets/badged_icon.dart';

class MyBottomBar {
  Widget? get bottomBar {
    if (MyPlatform.isHandheld) return const _BottomBar();
    return null;
  }
}

class _BottomBar extends StatelessWidget {
  const _BottomBar();

  @override
  Widget build(BuildContext context) {
    Theme.of(context);

    final selectedTextStyle = TextStyle(
        fontWeight: FontWeight.w600, color: ColorHelper.thePunchGray());

    final unselectedTextStyle =
        TextStyle(color: ColorHelper.thePunchLightGray());

    return ChangeNotifierProvider(
      create: (context) => _ViewModel(),
      builder: (context, child) {
        final viewModel = context.watch<_ViewModel>();
        return Padding(
          padding: EdgeInsets.all(
            ScreenHelper.screenHeightPercentage(context, 0),
          ),
          child:  Padding(
              padding: EdgeInsets.symmetric(
                vertical: ScreenHelper.screenHeightPercentage(context, 1),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  if (viewModel.canPunchIn)
                    _BottomBarButton(
                      icon: BadgedIcon(
                        Icons.schedule,
                        showBadge: viewModel.isPunchedIn,
                      color: Colors.green,
                       
                        isSelected: (ModalRoute.of(context)!.settings.name! ==
                                '/clock' ||
                            ModalRoute.of(context)!.settings.name! == '/home'),
                      ),
                      onPressed: () => context.go('/clock'),
                      isSelected: (ModalRoute.of(context)!.settings.name! ==
                                '/clock' ||
                            ModalRoute.of(context)!.settings.name! == '/home') ,
                      label: Text(AppLocalizations.of(context)!.clock,
                          style: ModalRoute.of(context)!.settings.name! ==
                                      '/clock' ||
                                  ModalRoute.of(context)!.settings.name! ==
                                      'home'
                              ? selectedTextStyle
                              : unselectedTextStyle),
                    ),
                  _BottomBarButton(
                    icon:
                        (ModalRoute.of(context)!.settings.name! == '/calendar')
                            ? Icon(
                                Icons.calendar_today,
                                color: ColorHelper.thePunchDesktopLightGray(),
                              )
                            : Icon(
                                Icons.calendar_today_outlined,
                                color: ColorHelper.thePunchLightGray(),
                              ),
                    onPressed: () => context.go('/calendar'),
                    isSelected: ModalRoute.of(context)!.settings.name! == '/calendar' ,
                    label: Text(AppLocalizations.of(context)!.calendar,
                        style: ModalRoute.of(context)!.settings.name! ==
                                '/calendar'
                            ? selectedTextStyle
                            : unselectedTextStyle),
                  ),
                  if (viewModel.canSendChatMessage)
                    _BottomBarButton(
                      icon: viewModel.unreadCount <= 0
                          ? BadgedIcon(
                              Icons.message,
                              isSelected:
                                  ModalRoute.of(context)!.settings.name! ==
                                      '/chat',
                            )
                          : BadgedIcon(
                              Icons.message,
                              count: viewModel.unreadCount,
                              isSelected:
                                  ModalRoute.of(context)!.settings.name! ==
                                      '/chat',
                            ),
                      onPressed: () => context.go('/chat'),
                      isSelected: ModalRoute.of(context)!.settings.name! == '/chat' ,                      
                      label: Text(
                        AppLocalizations.of(context)!.chat,
                        style: ModalRoute.of(context)!.settings.name! == '/chat'
                            ? selectedTextStyle
                            : unselectedTextStyle,
                      ),
                    ),
                  _BottomBarButton(
                    icon: (ModalRoute.of(context)!.settings.name! == '/punches')
                        ? Icon(
                            Icons.timelapse,
                            color: ColorHelper.thePunchDesktopLightGray(),
                          )
                        : Icon(
                            Icons.timelapse,
                            color: ColorHelper.thePunchLightGray(),
                          ),
                    onPressed: () => context.go('/punches'),
                    isSelected: ModalRoute.of(context)!.settings.name! == '/punches' ,
                    label: Text(AppLocalizations.of(context)!.punches,
                        style:
                            ModalRoute.of(context)!.settings.name! == '/punches'
                                ? selectedTextStyle
                                : unselectedTextStyle),
                  ),
                ],
              ),
            ),
          );
      },
    );
  }
}

class _BottomBarButton extends StatelessWidget {
  final Widget icon;
  final void Function()? onPressed;
  final Widget label;
  final bool isSelected;

  const _BottomBarButton({
    required this.icon,
    required this.onPressed,
    required this.label,
    required this.isSelected,
  });
  
  @override
  Widget build(BuildContext context) => InkWell(
        onTap: onPressed,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        hoverColor: Colors.transparent,
        child: Container(
          width: ScreenHelper.screenWidthPercentage(context, 15),
          height: ScreenHelper.screenWidthPercentage(context, 15),
          decoration:  BoxDecoration(
            borderRadius: const BorderRadius.all(
              Radius.circular(30),
            ),
          color: isSelected ? ColorHelper.thePunchBlueGray() : Colors.white,

          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                icon,
                //const SizedBox(height: 5),
                //label,
              ],
            ),
          ),
        ),
      );
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  int unreadCount = 0;
  bool isPunchedIn = false;
  bool canPunchIn = false;
  bool canSendChatMessage = false;

  _ViewModel() {
    addListenables([
      DataModel().messageModel,
      PermissionsState(),
      PunchState(),
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    canPunchIn = PermissionsState().punchIn && PunchState.canPunchIn;
    canSendChatMessage = PermissionsState().sendChatMessage;
    isPunchedIn = PunchState().isPunchedIn;

    final employeeId = LoginState.userId;
    unreadCount = await DataModel().messageModel.getUnreadCount(employeeId);

    notifyListeners();
  }
}
