import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../dataModel/data/location.dart';
import '../../../dataModel/data/schedule.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data_model.dart';
import '../../../dialogs/handheld/employees_dialog.dart';
import '../../../dialogs/handheld/locations_dialog.dart';
import '../../../helpers/color_helper.dart';
import '../../view_model_mixin.dart';
import '../../../widgets/active_toggle.dart';
import '../../../widgets/decorated_text_field.dart';
import '../../../misc/extensions.dart';
import '../home/<USER>';
import '../my_scaffold.dart';

class EditSchedulePage extends StatelessWidget {
  final String scheduleId;

  EditSchedulePage(Map<String, String> queryParms, {super.key})
      : scheduleId = queryParms['id'] ?? '';

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
      create: (context) => _ViewModel(scheduleId),
      child: MyScaffold(
        title: AppLocalizations.of(context)!.editSchedule,
        body: _Body(),
      ));
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1300),
          child: Column(
            children: [
              _BodyHeader(),
              _Row1(),
              _Row2(),
              _Row3(),
            ],
          ),
        ),
      );
}

class _BodyHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);

    final headline6 = Theme.of(context)
        .textTheme
        .titleLarge
        ?.copyWith(fontWeight: FontWeight.bold);
    return IntrinsicHeight(child: Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final schedule = viewModel.schedule;
        if (schedule == null) return Container();
        final s = '${AppLocalizations.of(context)!.scheduledTime}: '
            '(${schedule.startDateLocal.toFormattedDayNoLocal(locale)}) '
            '${schedule.startDateLocal.toFormattedDateTimeNoLocal(locale)} - '
            '${schedule.endDateLocal.toFormattedTimeNoLocal(locale)} '
            '(${schedule.endDateUtc.difference(schedule.startDateUtc).toFormatted})';
        return Stack(
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Text(s, style: headline6),
              ),
            ),
            Align(
              alignment: Alignment.centerRight,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: ActiveButton(
                      value: schedule.isActive,
                      onChanged: (value) => viewModel.setActive(value),
                      enableDialog: false,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: ElevatedButton(
                      onPressed: !viewModel.schedule!.isDirty
                          ? null
                          : () async {
                              Navigator.of(context).pop();
                              await viewModel.save();
                            },
                      child: Text(AppLocalizations.of(context)!.save),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    ));
  }
}

class _Row1 extends StatelessWidget {
  @override
  Widget build(BuildContext context) => IntrinsicHeight(
        child: Consumer<_ViewModel>(
          builder: (context, viewModel, child) {
            final location = viewModel.location;
            final employee = viewModel.employee;
            if (location == null || employee == null) return Container();
            return Flex(
              direction: Axis.horizontal,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Flexible(
                  flex: 2,
                  child: InkWell(
                    onTap: () => unawaited(showDialog(
                      context: context,
                      builder: (context) => LocationsDialog(
                          onSelection: (locationId) async =>
                              await viewModel.setLocation(locationId)),
                    )),
                    child: DecoratedText(
                      padding: const EdgeInsets.all(8),
                      filled: true,
                      text: location.name,
                      labelText: AppLocalizations.of(context)!.location,
                    ),
                  ),
                ),
                Flexible(
                    flex: 2,
                    child: InkWell(
                      onTap: () => unawaited(showDialog(
                        context: context,
                        builder: (context) => EmployeesDialog(
                            onSelection: (employeeId) =>
                                viewModel.setEmployee(employeeId)),
                      )),
                      child: DecoratedText(
                        padding: const EdgeInsets.all(8),
                        filled: true,
                        text: employee.name,
                        labelText: AppLocalizations.of(context)!.employee,
                      ),
                    )),
              ],
            );
          },
        ),
      );
}

class _Row2 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);

    return IntrinsicHeight(
      child: Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          final schedule = viewModel.schedule;
          if (schedule == null) return Container();
          final timeZone = viewModel.location?.timeZone ?? '';

          return Flex(
            direction: Axis.horizontal,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Flexible(
                  flex: 8,
                  child: InkWell(
                    onTap: () async {
                      final pickedDate = await showDatePicker(
                        context: context,
                        builder: (context, child) => Theme(
                          data: ThemeData.light().copyWith(
                            colorScheme: ColorScheme.light(
                              primary: ColorHelper.thePunchRed(),
                            ),
                          ),
                          child: child!,
                        ),
                        initialDate: schedule.startDateLocal,
                        firstDate: DateTime.utc(DateTime.now().year - 20),
                        lastDate: DateTime.utc(DateTime.now().year + 20),
                      );
                      if (pickedDate != null) viewModel.setDate(pickedDate);
                    },
                    child: DecoratedText(
                      padding: const EdgeInsets.all(8),
                      filled: true,
                      text: schedule.startDateLocal
                          .toFormattedDateNoLocal(locale),
                      labelText: AppLocalizations.of(context)!.date,
                    ),
                  )),
              Flexible(
                  flex: 10,
                  child: InkWell(
                    onTap: () async {
                      final pickedTime = await showTimePicker(
                        context: context,
                        initialTime: schedule.startDateLocal.timeOfDay,
                      );
                      if (pickedTime != null) viewModel.setStart(pickedTime);
                    },
                    child: DecoratedText(
                      padding: const EdgeInsets.all(8),
                      filled: true,
                      text: schedule.startDateLocal
                          .toFormattedTimeNoLocal(locale),
                      labelText:
                          AppLocalizations.of(context)!.startLocationTimeZone,
                    ),
                  )),
              Flexible(
                  flex: 10,
                  child: InkWell(
                    onTap: () async {
                      final pickedTime = await showTimePicker(
                        context: context,
                        initialTime: schedule.endDateLocal.timeOfDay,
                      );
                      if (pickedTime != null) viewModel.setEnd(pickedTime);
                    },
                    child: DecoratedText(
                      padding: const EdgeInsets.all(8),
                      filled: true,
                      text:
                          schedule.endDateLocal.toFormattedTimeNoLocal(locale),
                      labelText:
                          AppLocalizations.of(context)!.endTimeLocationTimeZone,
                    ),
                  )),
              Flexible(
                  flex: 8,
                  child: InkWell(
                    onTap: () async {
                      final duration = schedule.endDateUtc
                          .difference(schedule.startDateUtc)
                          .ceilMinutes;
                      final pickedTime = await showTimePicker(
                        helpText: AppLocalizations.of(context)!.duration,
                        context: context,
                        initialTime: duration.timeOfDay,
                        builder: (context, child) => MediaQuery(
                          data: MediaQuery.of(context)
                              .copyWith(alwaysUse24HourFormat: true),
                          child: child!,
                        ),
                      );
                      if (pickedTime != null) {
                        viewModel.setDuration(pickedTime.duration);
                      }
                    },
                    child: DecoratedText(
                      padding: const EdgeInsets.all(8),
                      filled: true,
                      text: schedule.endDateUtc
                          .difference(schedule.startDateUtc)
                          .toFormatted,
                      labelText: AppLocalizations.of(context)!.duration,
                    ),
                  )),
              Flexible(
                  flex: 10,
                  child: DecoratedText(
                    padding: const EdgeInsets.all(8),
                    text: timeZone,
                    labelText: AppLocalizations.of(context)!.timeZone,
                  )),
            ],
          );
        },
      ),
    );
  }
}

class _Row3 extends StatelessWidget {
  @override
  Widget build(BuildContext context) => IntrinsicHeight(
        child: Consumer<_ViewModel>(
          builder: (context, viewModel, child) {
            final schedule = viewModel.schedule;
            if (schedule == null) return Container();
            return Flex(
              direction: Axis.horizontal,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Flexible(
                    child: DecoratedText(
                  padding: const EdgeInsets.all(8),
                  isEmpty: false,
                  text: viewModel.nextLocation?.name ?? '',
                  labelText: AppLocalizations.of(context)!.nextLocation,
                )),
                Flexible(
                    child: DecoratedText(
                  padding: const EdgeInsets.all(8),
                  isEmpty: false,
                  text: schedule.travelTime?.toFormatted ?? '',
                  labelText: AppLocalizations.of(context)!.travelTime,
                )),
              ],
            );
          },
        ),
      );
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String scheduleId;
  Schedule? schedule;
  Location? location;
  User? employee;
  Location? nextLocation;
  final noteIds = ValueNotifier<Iterable<String>>([]);

  _ViewModel(this.scheduleId) {
    addListenables([
      DataModel().scheduleModel,
      DataModel().locationModel,
      DataModel().userModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    schedule = await DataModel().scheduleModel.getById(scheduleId);
    if (schedule == null) return;
    schedule = Schedule.from(schedule!);
    location = await DataModel().locationModel.getById(schedule!.locationId);
    employee = await DataModel().userModel.getById(schedule!.userId);
    if (schedule?.nextLocationId != null) {
      nextLocation =
          await DataModel().locationModel.getById(schedule!.nextLocationId!);
    }
    noteIds.value = (await DataModel().noteModel.getByScheduleId(scheduleId))
        .map((e) => e.id);
    notifyListeners();
  }

  Future<void> save() async {
    if (schedule == null) return;
    if (schedule!.isDirty) {
      await DataModel().scheduleModel.saveDirty([schedule!]);
    }
  }

  void setActive(bool value) {
    if (schedule == null) return;
    schedule!.isActive = value;
    schedule!.isDirty = true;
    notifyListeners();
  }

  void setDate(DateTime date) {
    if (schedule == null) return;
    final daysOffset =
        date.dateOnly.difference(schedule!.startDateLocal.dateOnly);
    if (daysOffset.inDays == 0) return;
    schedule!.startDateLocal = schedule!.startDateLocal.add(daysOffset);
    schedule!.endDateLocal = schedule!.endDateLocal.add(daysOffset);
    schedule!.startDateUtc = schedule!.startDateLocal.toUTC(location!.timeZone);
    schedule!.endDateUtc = schedule!.endDateLocal.toUTC(location!.timeZone);
    schedule!.isDirty = true;
    notifyListeners();
  }

  void setStart(TimeOfDay timeOfDay) {
    if (schedule == null) return;
    schedule!.startDateLocal = schedule!.startDateLocal.setTimeOfDay(timeOfDay);
    if (schedule!.startDateLocal >= schedule!.endDateLocal) {
      schedule!.startDateLocal =
          schedule!.startDateLocal.add(const Duration(days: -1));
    }
    schedule!.startDateUtc = schedule!.startDateLocal.toUTC(location!.timeZone);
    schedule!.isDirty = true;
    notifyListeners();
  }

  void setEnd(TimeOfDay timeOfDay) {
    if (schedule == null) return;
    schedule!.endDateLocal = schedule!.endDateLocal.setTimeOfDay(timeOfDay);
    if (schedule!.startDateLocal >= schedule!.endDateLocal) {
      schedule!.endDateLocal =
          schedule!.endDateLocal.add(const Duration(days: 1));
    }
    schedule!.endDateUtc = schedule!.endDateLocal.toUTC(location!.timeZone);
    schedule!.isDirty = true;
    notifyListeners();
  }

  void setDuration(Duration duration) {
    if (schedule == null) return;
    schedule!.endDateLocal = schedule!.startDateLocal.add(duration);
    schedule!.endDateUtc = schedule!.endDateLocal.toUTC(location!.timeZone);
    schedule!.isDirty = true;
    notifyListeners();
  }

  Future<void> setEmployee(String employeeId) async {
    schedule!.userId = employeeId;
    employee = await DataModel().userModel.getById(employeeId);
    schedule!.isDirty = true;
    notifyListeners();
  }

  Future<void> setLocation(String locationId) async {
    schedule!.locationId = locationId;
    location = await DataModel().locationModel.getById(locationId);
    schedule!.isDirty = true;
    notifyListeners();
  }
}
