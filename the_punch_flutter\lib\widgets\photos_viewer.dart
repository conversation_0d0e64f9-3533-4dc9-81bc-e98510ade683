import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import '../api/assets_model.dart';
import '../dialogs/delete_dialog.dart';

class PhotosViewer extends StatefulWidget {
  final List<String> ids;
  final int initialPhoto;
  final Function(String id)? deletePhoto;

  const PhotosViewer({super.key, required this.ids, required this.initialPhoto, this.deletePhoto});

  @override
  State<PhotosViewer> createState() => _PhotosViewerState();
}

class _PhotosViewerState extends State<PhotosViewer> {
  late final PageController _pageController;
  int _page = 0;
  var _scale = 1.0;
  var _offset = Offset.zero;
  var _size = Size.zero;
  var _layoutSize = Size.zero;

  @override
  void initState() {
    super.initState();
    _page = widget.initialPhoto;
    _pageController = PageController(initialPage: widget.initialPhoto);
    _pageController.addListener(() {
      final page = _pageController.page;
      if (page == null) return;
      if (page == page.toInt()) {
        setState(() {
          _page = page.toInt();
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final buttonColor = Theme.of(context).colorScheme.secondary;
    return Card(
      child: Stack(
        children: [
          PageView.builder(
            physics: _scale <= 1 ? null : const NeverScrollableScrollPhysics(),
            controller: _pageController,
            itemCount: widget.ids.length,
            itemBuilder: (context, index) {
              final id = widget.ids[index];
              return FutureBuilder<Image?>(future: Future(() async {
                final image = await AssetsModel().getImage(id);
                if (image != null) {
                  image.image.resolve(ImageConfiguration.empty).addListener(ImageStreamListener((info, _) {
                    _size = Size(info.image.width.toDouble(), info.image.height.toDouble());
                  }));
                }
                return image;
              }), builder: (context, snapshot) {
                final image = snapshot.data;
                if (!snapshot.hasData || image == null) return const Icon(Icons.photo);
                return LayoutBuilder(builder: (context, layout) {
                  final layoutSize = Size(layout.maxWidth, layout.maxHeight);
                  if (layoutSize != _layoutSize) {
                    _layoutSize = layoutSize;
                    Future.delayed(const Duration(milliseconds: 100), () => reposition(_offset, _scale));
                  }
                  return GestureDetector(
                      onPanUpdate: _scale <= 1 ? null : (details) => reposition(_offset + details.delta, _scale),
                      onDoubleTap: () {
                        double scale;
                        if (_scale > 1) {
                          scale = 1.0;
                        } else {
                          scale = 2.0;
                        }
                        reposition(_offset, scale);
                      },
                      child: Transform.scale(
                        scale: _scale,
                        child: Transform.translate(
                          offset: _offset,
                          child: image,
                        ),
                      ));
                });
              });
            },
          ),
          Align(
              alignment: Alignment.topLeft,
              child: Card(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(40),
                ),
                child: IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.arrow_back),
                  color: buttonColor,
                ),
              )),
          if (widget.deletePhoto != null)
            Align(
                alignment: Alignment.topRight,
                child: Card(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(40),
                  ),
                  child: IconButton(
                    onPressed: () async => await showDialog(
                        context: context,
                        builder: (context) => DeleteDialog(
                                // message: 'Are you sure you want to delete this photo?',
                                delete: () async {
                              await widget.deletePhoto!.call(widget.ids[_page]);
                              if (widget.ids.length == 1) {
                                if (!mounted) return;
                                Navigator.of(context).pop();
                              } else {
                                setState(() => widget.ids.removeAt(_page));
                              }
                            })),
                    icon: const Icon(Icons.delete),
                    color: buttonColor,
                  ),
                )),
          Align(
              alignment: Alignment.bottomCenter,
              child: Card(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(40),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: IconButton(
                        onPressed: _page == 0
                            ? null
                            : () {
                                _scale = 1.0;
                                _offset = Offset.zero;
                                unawaited(_pageController.previousPage(
                                    duration: const Duration(milliseconds: 500), curve: Curves.ease));
                              },
                        icon: const Icon(Icons.arrow_back),
                        color: buttonColor,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8),
                      child: IconButton(
                        onPressed: _page == widget.ids.length - 1
                            ? null
                            : () {
                                _scale = 1.0;
                                _offset = Offset.zero;
                                unawaited(_pageController.nextPage(
                                    duration: const Duration(milliseconds: 500), curve: Curves.ease));
                              },
                        icon: const Icon(Icons.arrow_forward),
                        color: buttonColor,
                      ),
                    ),
                  ],
                ),
              )),
          Align(
            alignment: Alignment.bottomRight,
            child: Card(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(40),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      onPressed: _scale >= 10.0
                          ? null
                          : () {
                              final scale = _scale * 2.0;
                              reposition(_offset, scale);
                            },
                      icon: const Icon(Icons.zoom_in),
                      color: buttonColor,
                    ),
                    IconButton(
                      onPressed: _scale <= 1.0
                          ? null
                          : () {
                              final scale = _scale * 0.5;
                              reposition(_offset, scale);
                            },
                      icon: const Icon(Icons.zoom_out),
                      color: buttonColor,
                    ),
                  ],
                )),
          ),
        ],
      ),
    );
  }

  void reposition(Offset offset, double scale) {
    var offset0 = offset;
    final layoutScale = [_size.height / _layoutSize.height, _size.width / _layoutSize.width].reduce(max);
    final size = _size / layoutScale;
    final top = offset0.dy + (size.height * scale - _layoutSize.height) / 2;
    final bottom = -offset0.dy + (size.height * scale - _layoutSize.height) / 2;
    final start = offset0.dx + (size.width * scale - _layoutSize.width) / 2;
    final end = -offset0.dx + (size.width * scale - _layoutSize.width) / 2;
    if (top < 0 && bottom < 0) {
      offset0 = Offset(offset0.dx, 0);
    } else {
      if (top < 0) offset0 = Offset(offset0.dx, offset0.dy - top);
      if (bottom < 0) offset0 = Offset(offset0.dx, offset0.dy + bottom);
    }
    if (start < 0 && end < 0) {
      offset0 = Offset(0, offset0.dy);
    } else {
      if (start < 0) offset0 = Offset(offset0.dx - start, offset0.dy);
      if (end < 0) offset0 = Offset(offset0.dx + end, offset0.dy);
    }
    setState(() {
      _scale = scale;
      _offset = offset0;
    });
  }
}
