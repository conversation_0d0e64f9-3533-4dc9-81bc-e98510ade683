import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import '../dataModel/data/location.dart';
import '../dataModel/data/note.dart';
import '../dataModel/data_model.dart';
import 'constrained_dialog.dart';
import '../misc/change_notification_builder.dart';
import '../pages/view_model_mixin.dart';
import '../widgets/padded_card.dart';

class NotesDialog extends StatelessWidget {
  final String locationId;

  const NotesDialog({super.key, required this.locationId});

  @override
  Widget build(BuildContext context) => ChangeNotifierBuilder<_ViewModel>(
        create: (context) => _ViewModel(locationId),
        builder: (context, viewModel, child) {
          if (viewModel.location == null) return Container();
          final notes = viewModel.notes;
          final location = viewModel.location!;
          return ConstrainedDialog(
              title: AppLocalizations.of(context)!.notes,
              subTitle: location.name,
              child: Flexible(
                child: ListView(
                  shrinkWrap: true,
                  children: [for (final note in notes) PaddedCard(child: Text(note.text))],
                ),
              ));
        },
      );
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String locationId;
  Iterable<Note> notes = [];
  Location? location;

  _ViewModel(this.locationId) {
    addListenables([
      DataModel().locationModel,
      DataModel().noteModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    notes = await DataModel().noteModel.getByLocationId(locationId);
    location = await DataModel().locationModel.getById(locationId);
    notifyListeners();
  }
}
