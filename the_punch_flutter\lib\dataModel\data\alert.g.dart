// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'alert.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AlertAdapter extends TypeAdapter<Alert> {
  @override
  final int typeId = 1;

  @override
  Alert read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Alert(
      isDirty: fields[0] as bool,
      isActive: fields[1] as bool,
      createdOn: fields[2] as DateTime,
      createdByUserId: fields[3] as String?,
      lastChangedOn: fields[4] as DateTime?,
      lastChangedByUserId: fields[5] as String?,
      id: fields[100] as String,
      alertTypeId: fields[101] as String,
      punchCardId: fields[102] as String?,
      scheduleId: fields[103] as String?,
      locationId: fields[104] as String?,
      alertOn: fields[105] as DateTime,
      geoLocationId: fields[106] as String?,
      userId: fields[107] as String,
    );
  }

  @override
  void write(BinaryWriter writer, Alert obj) {
    writer
      ..writeByte(14)
      ..writeByte(100)
      ..write(obj.id)
      ..writeByte(101)
      ..write(obj.alertTypeId)
      ..writeByte(102)
      ..write(obj.punchCardId)
      ..writeByte(103)
      ..write(obj.scheduleId)
      ..writeByte(104)
      ..write(obj.locationId)
      ..writeByte(105)
      ..write(obj.alertOn)
      ..writeByte(106)
      ..write(obj.geoLocationId)
      ..writeByte(107)
      ..write(obj.userId)
      ..writeByte(0)
      ..write(obj.isDirty)
      ..writeByte(1)
      ..write(obj.isActive)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdByUserId)
      ..writeByte(4)
      ..write(obj.lastChangedOn)
      ..writeByte(5)
      ..write(obj.lastChangedByUserId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AlertAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Alert _$AlertFromJson(Map<String, dynamic> json) => Alert(
      isActive: json['IsActive'] as bool? ?? true,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
      id: idFromJson(json['Id']),
      alertTypeId: idFromJson(json['AlertTypeId']),
      punchCardId: nullableIdFromJson(json['PunchCardId']),
      scheduleId: nullableIdFromJson(json['ScheduleId']),
      locationId: nullableIdFromJson(json['LocationId']),
      alertOn: dateTimeFromJson(json['AlertOn']),
      geoLocationId: nullableIdFromJson(json['GeoLocationId']),
      userId: idFromJson(json['UserId']),
    );

Map<String, dynamic> _$AlertToJson(Alert instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['AlertTypeId'] = instance.alertTypeId;
  writeNotNull('PunchCardId', instance.punchCardId);
  writeNotNull('ScheduleId', instance.scheduleId);
  writeNotNull('LocationId', instance.locationId);
  val['AlertOn'] = dateTimeToJson(instance.alertOn);
  writeNotNull('GeoLocationId', instance.geoLocationId);
  val['UserId'] = instance.userId;
  return val;
}
