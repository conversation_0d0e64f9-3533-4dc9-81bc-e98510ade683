

import 'package:flutter/material.dart';


import '../dataModel/data_model.dart';
import 'dart:async';

class PageTitleProvider with ChangeNotifier {
  String _currentTitle = 'Home';
  
  // Add DataModel listener
  PageTitleProvider() {
    DataModel().userModel.addListener(refresh);
    unawaited(refresh());
  }

  String get currentTitle => _currentTitle;

  Future<void> refresh() async {
    final employees = await DataModel().userModel.allEmployees;
    final activeEmployees = employees.where((e) => e.isActive).length;
    
    if (_currentTitle.contains('Active Employees')) {
      updateTitle('$activeEmployees Active Employees');
    }
    notifyListeners();
  }

  void updateTitle(String newTitle) {
    if (_currentTitle != newTitle) {
      _currentTitle = newTitle;
      notifyListeners();
    }
  }

  void updateTitleFromUrl(String url) async {
    final uri = Uri.parse(url);
    final path = uri.path;
    //print(' ABCDEF CHANGE WHAT LOCAL UPDATE FROM URL $path');
    ///print(path);
    if (path.contains('/map')) {
      final employees = await DataModel().userModel.allEmployees;
      final activeCount = employees.where((e) => e.isActive).length;
      updateTitle('$activeCount Active Employees');
    } else
    if (path.endsWith('/employees')) {
    // print(' ABCDEFCHANGE 1 WHAT LOCAL UPDATE FROM URL $path');
      updateTitle('View Employees');
    } else if (path.contains('/employees/view')) {
      //   print(' ABCDEFCHANGE 2 WHAT LOCAL UPDATE FROM URL $path');
      updateTitle('View Employee');
    } else if (path.contains('/employees/edit')) {
    //  print(' ABCDEF CHANGE 3 WHAT LOCAL UPDATE FROM URL $path');
      updateTitle('Edit Employee');
    } else if (path.endsWith('/employeeTypes')) {
      updateTitle('Employee Types');
    } else if (path.contains('/employeeTypes/view')) {
      updateTitle('View Employee Type');
    } else if (path.contains('/employeeTypes/edit')) {
      updateTitle('Edit Employee Type');
    } else if (path.contains('/new-contacts')) {
      updateTitle('New Contacts');
    } else if (path.endsWith('/contacts')) {
      updateTitle('View Contacts');
    } else if (path.contains('/contacts/view')) {
      updateTitle('View Contact');
    } else if (path.contains('/contacts/edit')) {
      updateTitle('Edit Contact');
    } else if (path.endsWith('/locations')) {
      updateTitle('View Locations');
    } else if (path.contains('/locations/view')) {
      updateTitle('View Location');
    } else if (path.contains('/locations/edit')) {
      updateTitle('Edit Location');
    } else if (path.contains('/jobTypes')) {
      updateTitle('Job Types');
    } else if (path.contains('/jobTypes/view')) {
      updateTitle('View Job Type');
    } else if (path.contains('/jobTypes/edit')) {
      updateTitle('Edit Job Type');
    } else if (path.contains('/inspectionTemplates')) {
      updateTitle('Inspection Templates');
    } else if (path.contains('/inspectionTemplates/edit')) {
      updateTitle('Edit Inspection Template');
    } else if (path.contains('/inspections')) {
      updateTitle('Inspections');
    } else if (path.contains('/inspections/edit')) {
      updateTitle('Edit Inspection');
    } else if (path.contains('/agenda')) {
      updateTitle('Agenda');
    } else if (path.contains('/agenda/editPunchCard')) {
      updateTitle('Edit Punch Card');
    } else if (path.contains('/schedules')) {
      updateTitle('Schedules');
    } else if (path.contains('/schedules/addRepeatingSchedule')) {
      updateTitle('Add Repeating Schedule');
    } else if (path.contains('/schedules/editRepeatingSchedule')) {
      updateTitle('Edit Repeating Schedule');
    } else if (path.contains('/schedules/view')) {
      updateTitle('View Schedule');
    } else if (path.contains('/schedules/edit')) {
      updateTitle('Edit Schedule');
    } else if (path.contains('/punchCards')) {
      updateTitle('Punch Cards');
    } else if (path.contains('/punchCards/punchedIn')) {
      updateTitle('Punched In');
    } else if (path.contains('/punchCards/view')) {
      updateTitle('View Punch Card');
    } else if (path.contains('/punchCards/edit')) {
      updateTitle('Edit Punch Card');
    } else if (path.contains('/')) {
      updateTitle('Overview');
    } else {
      updateTitle('Overview');
    }
  }
}
