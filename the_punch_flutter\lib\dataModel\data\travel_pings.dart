import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:uuid/uuid.dart';
import '../../misc/json_conversion.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import '../base_data.dart';

part 'travel_pings.g.dart';

@HiveType(typeId: 27) // Ensure the typeId is unique within your project
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class TravelPing extends BaseData {
  @HiveField(6)
  @Json<PERSON>ey(name: 'Id', fromJson: idFromJson, toJson: idToJson)
  String _id;
  @override
  String get id => _id;
  @override
  set id(String value) => _id = value;

  @HiveField(7)
  @JsonKey(name: 'PunchCardId', fromJson: idFromJson, toJson: idToJson)
  final String punchCardId;

  @HiveField(8)
  @<PERSON>son<PERSON>ey(name: 'UserId', fromJson: idFromJson, toJson: idToJson)
  final String userId;

  @HiveField(9)
  @JsonKey(name: 'Polyline')
  String polyline; 
  // This can store codes such as "Permission Denied", "No Movement", etc.

  @HiveField(10)
  @JsonKey(name: 'StartTime')
  DateTime startTime;

  @HiveField(11)
  @JsonKey(name: 'EndTime')
  DateTime endTime;

  @HiveField(12)
  @JsonKey(name: 'Distance')
  double distance;

  @HiveField(13)
  @JsonKey(name: 'StartLat')
  String startLat;

  @HiveField(14)
  @JsonKey(name: 'EndLat')
  String endLat;

  @HiveField(15)
  @JsonKey(name: 'StartLong')
  String startLong;

  @HiveField(16)
  @JsonKey(name: 'EndLong')
  String endLong;

  /// New field to help identify which part of the app (or background) created the ping.
  @HiveField(17)
  @JsonKey(name: 'PingSource')
  final String? pingSource;

  TravelPing({
    String? id,
    bool isDirty = false,
    bool isActive = true,
    required this.punchCardId,
    required this.userId,
    this.polyline = '',
    required this.startTime,
    required this.endTime,
    this.distance = 0.0,
    this.startLat = '',
    this.endLat = '',
    this.startLong = '',
    this.endLong = '',
    this.pingSource, // Optional, can be null
    required DateTime createdOn,
    String? createdByUserId,
    DateTime? lastChangedOn,
    String? lastChangedByUserId,
  })  : _id = id ?? Uuid().v4(),
        super(
          isDirty: isDirty,
          isActive: isActive,
          createdOn: createdOn,
          createdByUserId: createdByUserId,
          lastChangedOn: lastChangedOn,
          lastChangedByUserId: lastChangedByUserId,
        );

  // From JSON
  factory TravelPing.fromJson(Map<String, dynamic> json) =>
      _$TravelPingFromJson(json);

  // To JSON
  Map<String, dynamic> toJson() => _$TravelPingToJson(this);

  // Factory to create a new instance with defaults
  factory TravelPing.create({
    required String punchCardId,
    required String userId,
    required DateTime startTime,
    required DateTime endTime,
    required String startLat,
    required String startLong,
    required String endLat,
    required String endLong,
    required String polyline,
    String? pingSource, // <- Accept the new param
  }) {
    return TravelPing(
      punchCardId: punchCardId,
      userId: userId,
      polyline: polyline,
      startTime: startTime,
      endTime: endTime,
      distance: 0.0,
      startLat: startLat,
      endLat: endLat,
      startLong: startLong,
      endLong: endLong,
      pingSource: pingSource, // pass it through
      createdOn: ServerTimeState().utcTime,
      createdByUserId: LoginState.userId,
    );
  }
}

String idToJson(String id) => id;
String idFromJson(String id) => id;
