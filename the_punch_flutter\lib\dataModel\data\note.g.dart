// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'note.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class NoteAdapter extends TypeAdapter<Note> {
  @override
  final int typeId = 15;

  @override
  Note read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Note(
      isDirty: fields[0] as bool,
      isActive: fields[1] as bool,
      createdOn: fields[2] as DateTime,
      createdByUserId: fields[3] as String?,
      lastChangedOn: fields[4] as DateTime?,
      lastChangedByUserId: fields[5] as String?,
      id: fields[100] as String,
      text: fields[101] as String,
      userId: fields[102] as String?,
      locationId: fields[103] as String?,
      scheduleTemplateId: fields[104] as String?,
      scheduleId: fields[105] as String?,
      punchCardId: fields[106] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Note obj) {
    writer
      ..writeByte(13)
      ..writeByte(100)
      ..write(obj.id)
      ..writeByte(101)
      ..write(obj.text)
      ..writeByte(102)
      ..write(obj.userId)
      ..writeByte(103)
      ..write(obj.locationId)
      ..writeByte(104)
      ..write(obj.scheduleTemplateId)
      ..writeByte(105)
      ..write(obj.scheduleId)
      ..writeByte(106)
      ..write(obj.punchCardId)
      ..writeByte(0)
      ..write(obj.isDirty)
      ..writeByte(1)
      ..write(obj.isActive)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdByUserId)
      ..writeByte(4)
      ..write(obj.lastChangedOn)
      ..writeByte(5)
      ..write(obj.lastChangedByUserId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NoteAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Note _$NoteFromJson(Map<String, dynamic> json) => Note(
      isActive: json['IsActive'] as bool? ?? true,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
      id: idFromJson(json['Id']),
      text: json['Text'] as String? ?? '',
      userId: nullableIdFromJson(json['UserId']),
      locationId: nullableIdFromJson(json['LocationId']),
      scheduleTemplateId: nullableIdFromJson(json['ScheduleTemplateId']),
      scheduleId: nullableIdFromJson(json['ScheduleId']),
      punchCardId: nullableIdFromJson(json['PunchCardId']),
    );

Map<String, dynamic> _$NoteToJson(Note instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['Text'] = instance.text;
  writeNotNull('UserId', instance.userId);
  writeNotNull('LocationId', instance.locationId);
  writeNotNull('ScheduleTemplateId', instance.scheduleTemplateId);
  writeNotNull('ScheduleId', instance.scheduleId);
  writeNotNull('PunchCardId', instance.punchCardId);
  return val;
}
