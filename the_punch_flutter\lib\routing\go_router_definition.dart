import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../dataModel/data/location_notes.dart';
import '../navigator_keys.dart';
import '../pages/admin/agenda.dart';
import '../pages/admin/edit_punch_card.dart';
import '../pages/admin/inspections/edit_inspection.dart';
import '../pages/admin/inspections/inspections.dart';
import '../pages/employee/calendar.dart';
import '../pages/employee/calendar_details.dart';
import '../pages/employee/chat.dart';
import '../pages/employee/chat_details.dart';
import '../pages/employee/clock.dart';
import '../pages/employee/clock2.dart';
import '../pages/employee/edit_chat_page.dart';
import '../pages/employee/location_notes_pages.dart';
import '../pages/employee/new_chat_page.dart';
import '../pages/employee/notifications.dart';
import '../pages/employee/profile.dart';
import '../pages/employee/start_task.dart';
import '../pages/employee/punches.dart';
import '../pages/employee/punches_details.dart';
import '../pages/login/forgot_login.dart';
import '../pages/login/forgot_login_confirmation.dart';
import '../pages/login/register.dart';
import '../pages/login/register_confirmation.dart';
import '../pages/login/update_password.dart';
import '../pages/privacy_policy.dart';
import '../pages/sandbox/time_tests.dart';
import '../pages/sandbox/upload_locations_csv.dart';
import '../pages/web/alerts/alerts_page.dart';
import '../pages/web/contacts/contacts.dart';
import '../pages/web/contacts/edit_contact.dart';
import '../pages/web/contacts/new_contacts.dart';
import '../pages/web/contacts/view_contact.dart';
import '../pages/web/employees/edit_employee.dart';
import '../pages/web/employees/edit_employee_type.dart';
import '../pages/web/employees/employee_types.dart';
import '../pages/web/employees/employees.dart';
import '../pages/web/employees/view_employee.dart';
import '../pages/web/employees/view_employee_type.dart';
import '../pages/web/home/<USER>';
import '../pages/web/inspections/edit_inspection_template.dart';
import '../pages/web/inspections/inspection_templates.dart';
import '../pages/web/jobTypes/edit_job_type.dart';
import '../pages/web/jobTypes/job_types.dart';
import '../pages/web/jobTypes/view_job_type.dart';
import '../pages/web/locations/edit_location.dart';
import '../pages/web/locations/locations.dart';
import '../pages/web/locations/view_location.dart';
import '../pages/web/map/map.dart';
import '../pages/web/punchCards/punch_cards.dart';
import '../pages/web/punchCards/punched_in.dart';
import '../pages/web/punchCards/view_punch_card.dart';
import '../pages/web/punchCards/web_edit_punch_card.dart';
import '../pages/web/reports/reports.dart';
import '../pages/web/reports/run_report.dart';
import '../pages/web/schedules/add_repeating_schedule.dart';
import '../pages/web/schedules/edit_repeating_schedule.dart';
import '../pages/web/schedules/edit_schedule.dart';
import '../pages/web/schedules/schedules.dart';
import '../pages/web/schedules/view_schedule.dart';
import '../pages/splash.dart';
import '../pages/unknown.dart';
import '../misc/my_platform.dart';
import '../pages/login/login.dart';
import '../state/login_state.dart';
import '../state/page_title_state.dart';
import '../widgets/card_page.dart';

/// 1) Define NoTransitionPage helper with a `name` param:
class NoTransitionPage<T> extends CustomTransitionPage<T> {
  NoTransitionPage({
    LocalKey? key,
    required Widget child,
    bool fullscreenDialog = false,
    String? name,
  }) : super(
          key: key,
          name:
              name, // pass it up to CustomTransitionPage so ModalRoute.settings.name is set
          child: child,
          fullscreenDialog: fullscreenDialog,
          transitionDuration: Duration.zero,
          reverseTransitionDuration: Duration.zero,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return child;
          },
        );
}

final pageTitleProvider = PageTitleProvider();

final goRouterDeclaration = GoRouter(
  navigatorKey: rootNavigatorKey,
  initialLocation: '/',

  /// 2) Use `errorPageBuilder` with NoTransitionPage:
  errorPageBuilder: (context, state) => NoTransitionPage(
    key: state.pageKey,
    name: state.name, // pass state.name
    child: const UnknownPage(),
  ),

  observers: MyPlatform.isWeb || MyPlatform.isHandheld
      ? null
      : [FirebaseAnalyticsObserver(analytics: FirebaseAnalytics.instance)],

  redirect: (_, state) async {
    final pageTitleProvider = Provider.of<PageTitleProvider>(_, listen: false);
    try {
      await LoginState.instance;
      if (!LoginState.isLoggedIn) {
        switch (state.matchedLocation.split('?').first) {
          case '/login':
          case '/register':
          case '/completeRegistration':
          case '/RegistrationConfirmation':
          case '/forgotLogin':
          case '/forgotLoginConfirmation':
          case '/privacyPolicy':
            return null;
          default:
            return '/login';
        }
      } else {
        pageTitleProvider.updateTitleFromUrl(state.matchedLocation);
        return null;
      }

      return null;
    } catch (e, stacktrace) {
      print('Redirect error: $e\n$stacktrace');
      return '/login';
    }
  },

  routes: [
    GoRoute(
      path: '/notifications',
      name: '/notifications',
      pageBuilder: (context, state) => NoTransitionPage(
        key: state.pageKey,
        name: state.name,
        child: const NotificationPage(),
      ),
    ),
    GoRoute(
      path: '/splash',
      name: '/splash',
      pageBuilder: (context, state) => NoTransitionPage(
        key: state.pageKey,
        name: state.name,
        child: const SplashPage(),
      ),
    ),
    GoRoute(
      path: '/',
      name: '/home',
      pageBuilder: (context, state) => NoTransitionPage(
        key: state.pageKey,
        name: state.name,
        child: MyPlatform.isHandheld ? const ClockMain() : const HomePage(),
      ),
      routes: [
        GoRoute(
          path: 'clock',
          name: '/clock',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const ClockMain(),
          ),
        ),
        GoRoute(
          path: 'clockHome',
          name: '/clockHome',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const ClockMain(),
          ),
        ),
        GoRoute(
          path: 'map',
          name: '/map',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: MapPage(),
          ),
        ),
        GoRoute(
          path: 'profile',
          name: '/profile',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const ProfilePage(),
          ),
        ),
        GoRoute(
          path: 'sandbox/timeTests',
          name: '/sandbox/timeTests',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const TimeTestsPage(),
          ),
        ),
        GoRoute(
          path: 'sandbox/uploadLocationsCSV',
          name: '/sandbox/uploadLocationsCSV',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const UploadLocationsCSVPage(),
          ),
        ),
        GoRoute(
          path: 'updatePassword',
          name: '/updatePassword',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const UpdatePasswordPage(),
          ),
        ),
        GoRoute(
          path: 'reports',
          name: '/reports',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const ReportsPage(),
          ),
          routes: [
            GoRoute(
              path: 'runReport',
              name: '/reports/runReports',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: RunReportPage(state.uri.queryParameters),
              ),
            ),
          ],
        ),
        GoRoute(
          path: 'calendar',
          name: '/calendar',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const CalendarPage(),
          ),
          routes: [
            GoRoute(
              path: 'details',
              name: '/calendar/details',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: CalendarDetailsPage(state.uri.queryParameters),
              ),
            ),
          ],
        ),
        GoRoute(
          path: 'punches',
          name: '/punches',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child:   PunchesPage(),
          ),
          routes: [
            GoRoute(
              path: 'details',
              name: '/punches/details',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: PunchesDetailsPage(state.uri.queryParameters),
              ),
            ),
          ],
        ),
        GoRoute(
          path: 'chat',
          name: '/chat',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: ChatPage(),
          ),
          routes: [

                        GoRoute(
              path: 'create',
              name: '/chat/create',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: StartNewChatPage(vM: state.extra! ,onMultiSelection: (selectedEmployeeIds) async {
                        if (selectedEmployeeIds.isNotEmpty) {
                          //await viewModel.refresh();
                          // Potentially navigate to a group chat page
                          print('Users selected: $selectedEmployeeIds');
                        }
                        }
                        ),
              ),
            ),
                        GoRoute(
              path: 'details',
              name: '/chat/details',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: ChatDetailsPage(state.uri.queryParameters),
              ),
            ),
                        GoRoute(
              path: 'edit',
              name: '/chat/edit',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: EditChatPage(
                  groupId: state.extra! as String
                  ),
              ),
            ),

          ],
        ),
        GoRoute(
          path: 'alerts',
          name: '/alerts',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const AlertsPage(),
          ),
        ),
        GoRoute(
          path: 'login',
          name: '/login',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const LoginPage(),
          ),
        ),
        GoRoute(
          path: 'forgotLogin',
          name: '/forgotLogin',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const ForgotLoginPage(),
          ),
        ),
        GoRoute(
          path: 'forgotLoginConfirmation',
          name: '/forgotLoginConfirmation',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: ForgotLoginConfirmationPage(state.uri.queryParameters),
          ),
        ),
        GoRoute(
          path: 'register',
          name: '/register',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const RegisterPage(),
          ),
        ),
        GoRoute(
          path: 'completeRegistration',
          name: '/completeRegistration',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: CompleteRegistrationPage(state.uri.queryParameters),
          ),
        ),
        GoRoute(
          path: 'RegistrationConfirmation',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: CompleteRegistrationPage(state.uri.queryParameters),
          ),
        ),
        GoRoute(
          path: 'employees',
          name: '/employees',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const EmployeesPage(),
          ),
          routes: [
            GoRoute(
              path: 'view',
              name: '/employees/view',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: ViewEmployeePage(state.uri.queryParameters),
              ),
            ),
            GoRoute(
              path: 'edit',
              name: '/employees/edit',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: EditEmployeePage(state.uri.queryParameters),
              ),
            ),
            GoRoute(
                path: 'start-task',
                name: '/employees/start-task',
                pageBuilder: (context, state) {
                  final extra = state.extra as List;
                  return NoTransitionPage(
                    key: state.pageKey,
                    name: state.name,
                    child: StartTaskPage(
                      location_name: extra[0],
                    ),
                  );
                }),
            GoRoute(
                path: 'punch-card',
                name: '/employees/punch-card',
                pageBuilder: (context, state) {
                  final extra = state.extra as List;
                  
                  return NoTransitionPage(
                    key: state.pageKey,
                    name: state.name,
                    //child: PunchCardPage(timer: extra![0].timer, totalDuration: extra![0].totalDuration, taskDuration: extra![0].taskDuration,),
                    child: PunchCardPage(),
                  );
                }),
            GoRoute(
              path: 'location-notes',
              name: '/employees/location-notes',
              pageBuilder: (context, state) {
                print('STAE EXTRA ${state.extra}');
                final String locationId = state.extra as String;
                final String notes = "NOTE HERE";

                return NoTransitionPage(
                  key: state.pageKey,
                  name: state.name,
                  child: LocationNotes(locationId: locationId),
                );
              },
            ),
          ],
        ),
        GoRoute(
          path: 'employeeTypes',
          name: '/employeeTypes',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const EmployeeTypesPage(),
          ),
          routes: [
            GoRoute(
              path: 'view',
              name: '/employeeTypes/view',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: ViewEmployeeTypePage(state.uri.queryParameters),
              ),
            ),
            GoRoute(
              path: 'edit',
              name: '/employeeTypes/edit',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: EditEmployeeTypePage(state.uri.queryParameters),
              ),
            ),
          ],
        ),
        GoRoute(
          path: 'new-contacts',
          name: '/new-contacts',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const NewContactsPage(),
          ),
        ),
        GoRoute(
          path: 'contacts',
          name: '/contacts',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const ContactsPage(),
          ),
          routes: [
            GoRoute(
              path: 'view',
              name: '/contacts/view',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: ViewContactPage(state.uri.queryParameters),
              ),
            ),
            GoRoute(
              path: 'edit',
              name: '/contacts/edit',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: EditContactPage(state.uri.queryParameters),
              ),
            ),
          ],
        ),
        GoRoute(
          path: 'locations',
          name: '/locations',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const LocationsPage(),
          ),
          routes: [
            GoRoute(
              path: 'view',
              name: '/locations/view',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: ViewLocationPage(state.uri.queryParameters),
              ),
            ),
            GoRoute(
              path: 'edit',
              name: '/locations/edit',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: EditLocationPage(state.uri.queryParameters),
              ),
            ),
          ],
        ),
        GoRoute(
          path: 'jobTypes',
          name: '/jobTypes',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const JobTypesPage(),
          ),
          routes: [
            GoRoute(
              path: 'view',
              name: '/jobTypes/view',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: ViewJobTypePage(state.uri.queryParameters),
              ),
            ),
            GoRoute(
              path: 'edit',
              name: '/jobTypes/edit',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: EditJobTypePage(state.uri.queryParameters),
              ),
            ),
          ],
        ),
        GoRoute(
          path: 'inspectionTemplates',
          name: '/inspectionTemplates',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: InspectionTemplatesPage(),
          ),
          routes: [
            GoRoute(
              path: 'edit',
              name: '/inspectionTemplates/edit',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: EditInspectionTemplatePage(state.uri.queryParameters),
              ),
            ),
          ],
        ),
        GoRoute(
          path: 'inspections',
          name: '/inspections',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: InspectionsPage(),
          ),
          routes: [
            GoRoute(
              path: 'edit',
              name: '/inspections/edit',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: EditInspectionPage(state.uri.queryParameters),
              ),
            ),
          ],
        ),
        GoRoute(
          path: 'agenda',
          name: '/agenda',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const AgendaPage(),
          ),
          routes: [
            GoRoute(
              path: 'editPunchCard',
              name: '/agenda/editPunchCard',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: EditPunchCardPage(state.uri.queryParameters),
              ),
            ),
          ],
        ),
        GoRoute(
          path: 'schedules',
          name: '/schedules',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            // pass `state.extra` if needed
            child: SchedulesPage(selectedDate: state.extra),
          ),
          routes: [
            GoRoute(
              path: 'addRepeatingSchedule',
              name: '/schedules/addRepeatingSchedule',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: const AddRepeatingSchedulePage(),
              ),
            ),
            GoRoute(
              path: 'editRepeatingSchedule',
              name: '/schedules/editRepeatingSchedule',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: EditRepeatingSchedulePage(state.uri.queryParameters),
              ),
            ),
            GoRoute(
              path: 'view',
              name: '/schedules/view',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: ViewSchedulePage(state.uri.queryParameters),
              ),
            ),
            GoRoute(
              path: 'edit',
              name: '/schedules/edit',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: EditSchedulePage(state.uri.queryParameters),
              ),
            ),
          ],
        ),
        GoRoute(
          path: 'punchCards',
          name: '/punchCards',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const PunchCardsPage(),
          ),
          routes: [
            GoRoute(
              path: 'punchedIn',
              name: '/punchCards/punchedIn',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: const PunchedInPage(),
              ),
            ),
            GoRoute(
              path: 'view',
              name: '/punchCards/view',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: ViewPunchCardPage(state.uri.queryParameters),
              ),
            ),
            GoRoute(
              path: 'edit',
              name: '/punchCards/edit',
              pageBuilder: (context, state) => NoTransitionPage(
                key: state.pageKey,
                name: state.name,
                child: WebEditPunchCardPage(state.uri.queryParameters),
              ),
            ),
          ],
        ),
        GoRoute(
          path: 'privacyPolicy',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            name: state.name,
            child: const PrivacyPolicyPage(),
          ),
        ),
      ],
    ),
  ],
);
