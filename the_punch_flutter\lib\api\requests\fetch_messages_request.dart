// File: lib/api/requests/fetch_messages_request_response.dart

import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:the_punch_flutter/misc/json_conversion.dart';

// Import your data models (Message, MessageGroup, GroupMember, etc.)
import '../../dataModel/data/message.dart';
import '../../dataModel/data/message_group.dart';
import '../../dataModel/data/group_member.dart';

// Import your base classes (SystemRequest, SystemResponse)
// which might already have their own @JsonSerializable annotations or fields
import 'system.dart';

part 'fetch_messages_request.g.dart';

// ------------------------ Request Class ------------------------
@JsonSerializable(includeIfNull: false, fieldRename: FieldRename.none)
class FetchMessagesRequest extends SystemRequest {
  /// Matches the C# property name 'TargetUserId'
  @JsonKey(name: 'TargetUserId')
  final String targetUserId;

  /// Matches the C# property name 'Request_ServerIP'
  @JsonKey(name: 'Request_ServerIP')
  final String serverIP;

  /// Matches the C# property name 'Request_DatabaseName'
  @JsonKey(name: 'Request_DatabaseName')
  final String databaseName;

  /// Matches the C# property name 'Request_SessionID'
  @JsonKey(name: 'Request_SessionID')
  final String sessionId;

  FetchMessagesRequest({
    required this.targetUserId,
    required this.serverIP,
    required this.databaseName,
    required this.sessionId,
  }) : super(
          // If your SystemRequest base constructor requires
          // "serverIP, databaseName, sessionId", pass them here
          serverIP: serverIP,
          databaseName: databaseName,
          sessionId: sessionId,
        );

  /// Creates a request with system details from [SystemRequest.create()]
  static Future<FetchMessagesRequest> create(String targetUserId) async {
    final systemRequest = await SystemRequest.create();
    return FetchMessagesRequest(
      targetUserId: targetUserId,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  // JSON (de)serialization boilerplate
  factory FetchMessagesRequest.fromJson(Map<String, dynamic> json) =>
      _$FetchMessagesRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$FetchMessagesRequestToJson(this);
}

// ------------------------ Response Class ------------------------
@JsonSerializable(includeIfNull: false, fieldRename: FieldRename.none)
class FetchMessagesResponse extends SystemResponse {
  /// The list of Messages the user is involved in
  @JsonKey(name: 'Messages')
  final List<Message> messages;

  /// The list of MessageGroups that correlate with the fetched messages
  @JsonKey(name: 'MessageGroups')
  final List<MessageGroup>? messageGroups;

  /// The list of GroupMembers for those MessageGroups
  @JsonKey(name: 'GroupMembers')
  final List<GroupMember>? groupMembers;

  FetchMessagesResponse({
    required this.messages,
    this.messageGroups,
    this.groupMembers,

    // These two come from SystemResponse
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory FetchMessagesResponse.fromJson(Map<String, dynamic> json) =>
      _$FetchMessagesResponseFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$FetchMessagesResponseToJson(this);
}
