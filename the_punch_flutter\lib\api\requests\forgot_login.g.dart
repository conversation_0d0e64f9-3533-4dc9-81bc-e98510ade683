// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'forgot_login.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ForgotLoginRequest _$ForgotLoginRequestFromJson(Map<String, dynamic> json) =>
    ForgotLoginRequest(
      organizationId: json['OrganizationId'] as String,
      email: json['Email'] as String,
      captchaResponse: json['CaptchaResponse'] as String,
    );

Map<String, dynamic> _$ForgotLoginRequestToJson(ForgotLoginRequest instance) =>
    <String, dynamic>{
      'OrganizationId': instance.organizationId,
      'Email': instance.email,
      'CaptchaResponse': instance.captchaResponse,
    };

ForgotLoginConfirmationRequest _$ForgotLoginConfirmationRequestFromJson(
        Map<String, dynamic> json) =>
    ForgotLoginConfirmationRequest(
      organizationId: json['OrganizationId'] as String,
      password: json['Password'] as String,
      confirmPassword: json['ConfirmPassword'] as String,
      confirmationKey: json['ConfirmationKey'] as String,
    );

Map<String, dynamic> _$ForgotLoginConfirmationRequestToJson(
        ForgotLoginConfirmationRequest instance) =>
    <String, dynamic>{
      'OrganizationId': instance.organizationId,
      'Password': instance.password,
      'ConfirmPassword': instance.confirmPassword,
      'ConfirmationKey': instance.confirmationKey,
    };
