import 'dart:async';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../dataModel/data/job_type.dart';
import '../dataModel/data/location.dart';
import '../dataModel/data/punch_card.dart';
import '../dataModel/data/user.dart';
import '../dataModel/data_model.dart';
import '../pages/view_model_mixin.dart';
import '../misc/extensions.dart';
import 'active_toggle.dart';
import 'calendar/calendar_widget.dart';
import 'package:collection/collection.dart';

class PunchCardsWidget extends StatelessWidget {
  final String employeeId;
  final ActiveToggleState? active;

  const PunchCardsWidget({super.key, required this.employeeId, this.active});

  @override
  Widget build(BuildContext context) {
    final viewModel = _ViewModel(employeeId: employeeId, active: active);
    return ChangeNotifierProvider.value(
        value: viewModel,
        child: Consumer<_ViewModel>(
            builder: (context, viewModel, _) => CalendarWidget(builder: (context, start, end) async => _getCalendarEvents(context, start, end, viewModel))));
  }

  Future<Iterable<CalendarEvent>> _getCalendarEvents(BuildContext context, DateTime start, DateTime end, _ViewModel viewModel) async {
    final theme = Theme.of(context);
    final caption = theme.textTheme.bodySmall;
    final activeTextStyle = caption?.copyWith(color: Colors.white);
    final inactiveTextStyle = caption?.copyWith(color: Colors.black87);
    final tooltipStyle = theme.textTheme.bodyLarge?.copyWith(color: theme.brightness == Brightness.dark ? Colors.black : Colors.white);

    final punchCards = (await viewModel.getPunchCards(start, end)).toList();
    punchCards.sort((a, b) => a.clockedIn.compareTo(b.clockedIn));
    return punchCards.map((e) => CalendarEvent(
          start: e.clockedIn,
          end: e.clockedOut ?? DateTime.now(),
          text: _toText(e, viewModel),
          detailedText: _toDetailString(e, viewModel),
          tooltip: _toTooltip(context, e, viewModel),
          tooltipTextStyle: tooltipStyle,
          color: e.isActive ? Colors.green : Colors.yellow,
          textStyle: e.isActive ? activeTextStyle : inactiveTextStyle,
          onTap: () => context.pushNamed('/punchCards/view', queryParameters: {'id': e.id}),
        ));
  }

  String _toText(PunchCard punchCard, _ViewModel viewModel) =>
      viewModel.punchCardLocationMap[punchCard.id]?.name ?? viewModel.jobTypeMap[punchCard.jobTypeId]?.name ?? '';

  String _toTooltip(BuildContext context, PunchCard punchCard, _ViewModel viewModel) {
    final locale = Localizations.localeOf(context);

    final startDate = punchCard.clockedIn.toFormattedDate(locale);
    final startTime = punchCard.clockedIn.toFormattedTime(locale);
    final endTime = punchCard.clockedOut?.toFormattedTime(locale) ?? DateTime.now();
    final employeeName = viewModel.employee?.name ?? '';
    final locationOrJobName = viewModel.punchCardLocationMap[punchCard.id]?.name ?? viewModel.jobTypeMap[punchCard.jobTypeId]?.name ?? '';
    return '$startDate\n$startTime - $endTime\n$employeeName\n$locationOrJobName';
  }

  String _toDetailString(PunchCard punchCard, _ViewModel viewModel) {
    final employeeName = viewModel.employee?.name ?? '';
    final locationOrJobName = viewModel.punchCardLocationMap[punchCard.id]?.name ?? viewModel.jobTypeMap[punchCard.jobTypeId]?.name ?? '';
    return '$employeeName\n$locationOrJobName';
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String employeeId;
  final ActiveToggleState? active;
  User? employee;

  DateTime start = DateTime.now();
  DateTime end = DateTime.now();
  Map<String, Location> locationMap = {};
  Map<String, Location> punchCardLocationMap = {};
  Map<String, JobType> jobTypeMap = {};

  _ViewModel({required this.employeeId, required this.active}) {
    addListenables([
      DataModel().punchCardModel,
      DataModel().locationModel,
      DataModel().userModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    employee = await DataModel().userModel.getById(employeeId);

    notifyListeners();
  }

  Future<Iterable<PunchCard>> getPunchCards(DateTime start, DateTime end) async {
    var punchCards = (await DataModel().punchCardModel.getBetweenByEmployeeId(employeeId, start, end));

    switch (active ?? ActiveToggleState.active) {
      case ActiveToggleState.active:
        punchCards = punchCards.where((e) => e.isActive);
        break;
      case ActiveToggleState.inactive:
        punchCards = punchCards.where((e) => !e.isActive);
        break;
      case ActiveToggleState.all:
        break;
    }

    if (punchCards.isEmpty) return punchCards;

    final scheduleIds = punchCards.map((e) => e.scheduleId).whereNotNull().toSet();
    final schedules = await DataModel().scheduleModel.getByIds(scheduleIds);
    final scheduleMap = {for (final e in schedules) e.id: e};

    final punchCardsWithLoction = punchCards.where((e) => (e.locationId ?? scheduleMap[e.scheduleId]?.locationId) != null);
    final locationIds = punchCardsWithLoction.map((e) => (e.locationId ?? scheduleMap[e.scheduleId]!.locationId)).toSet();
    locationIds.removeWhere((e) => locationMap.keys.contains(e));
    final locations = (await DataModel().locationModel.getByIds(locationIds));
    locationMap.addAll({for (final e in locations) e.id: e});
    punchCardLocationMap.addAll({for (final e in punchCardsWithLoction) e.id: locationMap[(e.locationId ?? scheduleMap[e.scheduleId]!.locationId)]!});

    final jobTypeIds = punchCards.map((e) => e.jobTypeId).toSet();
    final jobTypes = await DataModel().jobTypeModel.getByIds(jobTypeIds);
    jobTypeMap.addAll({for (final e in jobTypes) e.id: e});

    return punchCards;
  }
}
