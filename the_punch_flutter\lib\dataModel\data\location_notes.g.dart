// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'location_notes.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LocationNoteAdapter extends TypeAdapter<LocationNote> {
  @override
  final int typeId = 26;

  @override
  LocationNote read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocationNote(
      isDirty: fields[0] as bool,
      isActive: fields[1] as bool,
      createdOn: fields[2] as DateTime,
      createdByUserId: fields[3] as String?,
      lastChangedOn: fields[4] as DateTime?,
      lastChangedByUserId: fields[5] as String?,
      locationId: fields[7] as String,
      note: fields[8] as String,
    ).._id = fields[6] as String;
  }

  @override
  void write(BinaryWriter writer, LocationNote obj) {
    writer
      ..writeByte(9)
      ..writeByte(6)
      ..write(obj._id)
      ..writeByte(7)
      ..write(obj.locationId)
      ..writeByte(8)
      ..write(obj.note)
      ..writeByte(0)
      ..write(obj.isDirty)
      ..writeByte(1)
      ..write(obj.isActive)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdByUserId)
      ..writeByte(4)
      ..write(obj.lastChangedOn)
      ..writeByte(5)
      ..write(obj.lastChangedByUserId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocationNoteAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LocationNote _$LocationNoteFromJson(Map<String, dynamic> json) => LocationNote(
      id: json['Id'] as String?,
      isActive: json['IsActive'] as bool? ?? true,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
      locationId: idFromJson(json['LocationId'] as String),
      note: json['Note'] as String,
    );

Map<String, dynamic> _$LocationNoteToJson(LocationNote instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['LocationId'] = idToJson(instance.locationId);
  val['Note'] = instance.note;
  return val;
}
