import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../base_data.dart';
import '../../misc/json_conversion.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import 'package:uuid/uuid.dart';

part 'punch_card.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
@HiveType(typeId: 17)
class PunchCard extends BaseData {
  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  @JsonKey(fromJson: idFromJson)
  String userId;

  @HiveField(102)
  @JsonKey(fromJson: idFromJson)
  String jobTypeId;

  @HiveField(103)
  @JsonKey(fromJson: nullableIdFromJson)
  String? locationId;

  @HiveField(104)
  @JsonKey(fromJson: nullableIdFromJson)
  String? scheduleId;

  @HiveField(105)
  @JsonKey(fromJson: dateTimeFromJson, toJson: dateTimeToJson)
  DateTime clockedIn;

  @HiveField(106)
  @JsonKey(fromJson: nullableDateTimeFromJson, toJson: nullableDateTimeToJson)
  DateTime? clockedOut;

  @HiveField(107)
  @JsonKey(fromJson: nullableIdFromJson)
  String? previousLocationId;

  @HiveField(108)
  @JsonKey(fromJson: nullableIdFromJson)
  String? punchCardLinkId;

  Duration? get duration => clockedOut?.difference(clockedIn);

  PunchCard({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.userId,
    required this.jobTypeId,
    this.locationId,
    this.scheduleId,
    required this.clockedIn,
    this.clockedOut,
    this.previousLocationId,
    this.punchCardLinkId,
  });

  factory PunchCard.fromJson(Map<String, dynamic> json) {
    try {
      return _$PunchCardFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() => _$PunchCardToJson(this);

  factory PunchCard.from(PunchCard o) => PunchCard(
        isDirty: o.isDirty,
        isActive: o.isActive,
        createdOn: o.createdOn,
        createdByUserId: o.createdByUserId,
        lastChangedOn: o.lastChangedOn,
        lastChangedByUserId: o.lastChangedByUserId,
        id: o.id,
        userId: o.userId,
        jobTypeId: o.jobTypeId,
        clockedIn: o.clockedIn,
        locationId: o.locationId,
        scheduleId: o.scheduleId,
        clockedOut: o.clockedOut,
        previousLocationId: o.previousLocationId,
        punchCardLinkId: o.punchCardLinkId,
      );

  factory PunchCard.create() => PunchCard(
        id: const Uuid().v4(),
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
        userId: '',
        jobTypeId: '',
        clockedIn: ServerTimeState().utcTime,
      );
}
