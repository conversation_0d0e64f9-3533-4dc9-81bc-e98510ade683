import 'package:flutter/material.dart';

class ConstrainedDialog extends StatelessWidget {
  final String? title;
  final String? subTitle;
  final Widget child;
  final BoxConstraints? constraints;

  const ConstrainedDialog(
      {super.key,
      this.title,
      this.subTitle,
      required this.child,
      this.constraints});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline6 = theme.textTheme.titleLarge;
    final body1 = theme.textTheme.titleMedium;

    return Dialog(
      elevation: 0,
      child: ConstrainedBox(
        constraints: constraints ?? const BoxConstraints(maxWidth: 500),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (title != null) Text(title!, style: headline6),
            if (subTitle != null) Text(subTitle!, style: body1),
            child,
          ],
        ),
      ),
    );
  }
}
