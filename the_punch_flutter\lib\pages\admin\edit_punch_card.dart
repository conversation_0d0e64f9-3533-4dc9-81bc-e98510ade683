import 'dart:async';

import 'package:community_material_icon/community_material_icon.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../dataModel/data/user.dart';
import '../../dataModel/data_model.dart';
import '../../dataModel/data/alert.dart';
import '../../dataModel/data/job_type.dart';
import '../../dataModel/data/location.dart';
import '../../dataModel/data/punch_card.dart';
import '../../dataModel/data/schedule.dart';
import '../../dialogs/handheld/locations_dialog.dart';
import '../../dialogs/save_dialog.dart';
import '../../helpers/color_helper.dart';
import '../view_model_mixin.dart';
import '../../widgets/date_pill.dart';
import '../../widgets/icon_tile.dart';
import '../../misc/extensions.dart';
import 'package:dartx/dartx.dart';
import '../../widgets/my_body.dart';
import '../web/my_scaffold.dart';

class EditPunchCardPage extends StatelessWidget {
  EditPunchCardPage(Map<String, String> queryParms, {super.key})
      : punchCardId = queryParms['id'] ?? '';

  final _scaffoldKey = GlobalKey<ScaffoldState>();
  final String punchCardId;

  @override
  Widget build(BuildContext context) {
    final viewModel = _ViewModel(punchCardId);
    return ChangeNotifierProvider(
        create: (context) => viewModel,
        builder: (context, child) => MyScaffold(
              key: _scaffoldKey,
              title: AppLocalizations.of(context)!.editPunchCard,
              body: MyBody(header: _Header(), body: _Body()),
              floatingActionButton: FloatingActionButton.extended(
                  heroTag: 'uniqueTag7',

                  icon: const Icon(Icons.save),
                  label: Text(AppLocalizations.of(context)!.save),
                  onPressed: () {
                    final context = _scaffoldKey.currentContext;
                    if (context != null) {
                      unawaited(showDialog(
                          context: context,
                          builder: (context) => SaveDialog(save: () {
                                Navigator.pop(context);
                                viewModel.save();
                                GoRouter.of(context).canPop();
                              })));
                    }
                  }),
            ));
  }
}

class _Header extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onPrimary = theme.colorScheme.onPrimary;
    final primaryTextStyle =
        theme.textTheme.titleLarge?.copyWith(color: onPrimary);

    final punchCard =
        context.select<_ViewModel, PunchCard?>((v) => v.punchCard);
    final setDate = context
        .select<_ViewModel, Function(DateTime pickedDate)>((v) => v.setDate);
    final employee = context.select<_ViewModel, User?>((v) => v.employee);

    return IntrinsicHeight(
        child: Stack(children: [
      Align(
          alignment: Alignment.centerLeft,
          child: Padding(
              padding: const EdgeInsets.all(8),
              child: GestureDetector(
                onTap: () async {
                  if (punchCard == null) return;
                  final pickedDate = await showDatePicker(
                    context: context,
                    builder: (context, child) => Theme(
                      data: ThemeData.light().copyWith(
                        colorScheme: ColorScheme.light(
                          primary: ColorHelper.thePunchRed(),
                        ),
                      ),
                      child: child!,
                    ),
                    initialDate: punchCard.clockedIn.dateOnly,
                    firstDate: DateTime.utc(DateTime.now().year - 20),
                    lastDate: DateTime.utc(DateTime.now().year + 20),
                  );
                  if (pickedDate != null) await setDate(pickedDate);
                },
                child: DatePill(
                  date: punchCard?.clockedIn.dateOnly,
                ),
              ))),
      Align(child: Text(employee?.name ?? '', style: primaryTextStyle)),
    ]));
  }
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline6 = theme.textTheme.titleLarge;

    final hasPunchCard =
        context.select<_ViewModel, bool>((v) => v.punchCard != null);
    if (!hasPunchCard) return Container();

    final alertsNotEmpty =
        context.select<_ViewModel, bool>((v) => v.alerts.isNotEmpty);
    final isPunchedIn = context
        .select<_ViewModel, bool>((v) => v.punchCard!.clockedOut == null);

    return FocusScope(
      child: Center(
          child: Container(
              padding: const EdgeInsets.all(4),
              constraints: const BoxConstraints(minWidth: 250, maxWidth: 500),
              child: ListView(children: [
                Align(
                  child: (isPunchedIn)
                      ? Text(AppLocalizations.of(context)!.punchedIn,
                          style: headline6)
                      : Text(AppLocalizations.of(context)!.punchedOut,
                          style: headline6),
                ),
                _DateTile(),
                _PunchedInTile(),
                _PunchedOutTile(),
                _DurationTile(),
                _LocationTile(),
                _JobTypeTile(),
                if (alertsNotEmpty) _AlertsTile(),
                Container(height: 64),
              ]))),
    );
  }
}

class _DateTile extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final locale = Localizations.localeOf(context);
    final secondaryBodyText1 =
        theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.secondary);

    final clockedInDate = context
        .select<_ViewModel, DateTime>((v) => v.punchCard!.clockedIn.dateOnly);
    final setDate = context
        .select<_ViewModel, Function(DateTime pickedDate)>((v) => v.setDate);

    return GestureDetector(
      onTap: () async {
        final pickedDate = await showDatePicker(
          context: context,
          builder: (context, child) => Theme(
            data: ThemeData.light().copyWith(
              // Set the accent color of the app
              colorScheme: ColorScheme.light(
                primary: ColorHelper.thePunchRed(),
              ),
            ),
            child: child!,
          ),
          initialDate: clockedInDate,
          firstDate: DateTime.utc(DateTime.now().year - 20),
          lastDate: DateTime.utc(DateTime.now().year + 20),
        );
        if (pickedDate != null) await setDate(pickedDate);
      },
      child: IconCardTile(
        leading: const Icon(Icons.calendar_today),
        title:
            Text(AppLocalizations.of(context)!.date, style: secondaryBodyText1),
        body: Text(clockedInDate.toLongFormattedDateWithYear(locale)),
      ),
    );
  }
}

class _PunchedInTile extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);
    final theme = Theme.of(context);
    final secondaryBodyText1 =
        theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.secondary);

    final clockedIn =
        context.select<_ViewModel, DateTime>((v) => v.punchCard!.clockedIn);
    final setPunchedIn =
        context.select<_ViewModel, Future<void> Function(TimeOfDay pickedTime)>(
            (v) => v.setPunchedIn);

    return GestureDetector(
      onTap: () async {
        final pickedTime = await showTimePicker(
          context: context,
          initialTime: TimeOfDay.fromDateTime(clockedIn.toLocal()),
        );
        if (pickedTime != null) await setPunchedIn(pickedTime);
      },
      child: IconCardTile(
        leading: const Icon(CommunityMaterialIcons.clock_in),
        title: Text(AppLocalizations.of(context)!.punchedIn,
            style: secondaryBodyText1),
        body: Text(clockedIn.toFormattedTime(locale)),
      ),
    );
  }
}

class _PunchedOutTile extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);
    final theme = Theme.of(context);
    final secondaryBodyText1 =
        theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.secondary);

    final clockedOut =
        context.select<_ViewModel, DateTime?>((v) => v.punchCard!.clockedOut);
    final setPunchedOut =
        context.select<_ViewModel, Future<void> Function(TimeOfDay)>(
            (v) => v.setPunchedOut);
    final clearPunchedOut = context
        .select<_ViewModel, Future<void> Function()>((v) => v.clearPunchedOut);

    return GestureDetector(
      onTap: () async {
        final pickedTime = await showTimePicker(
          context: context,
          initialTime: TimeOfDay.fromDateTime(
              clockedOut?.toLocal() ?? DateTime.now().toLocal()),
        );
        if (pickedTime != null) await setPunchedOut(pickedTime);
      },
      child: IconCardTile(
        leading: const Icon(CommunityMaterialIcons.clock_out),
        title: Text(AppLocalizations.of(context)!.punchedOut,
            style: secondaryBodyText1),
        body: Text(clockedOut?.toFormattedTime(locale) ??
            AppLocalizations.of(context)!.punchedIn),
        trailing: IconButton(
          icon: const Icon(Icons.clear),
          onPressed: () async => await clearPunchedOut(),
        ),
      ),
    );
  }
}

class _DurationTile extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final secondaryBodyText1 =
        theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.secondary);

    final clockedIn =
        context.select<_ViewModel, DateTime>((v) => v.punchCard!.clockedIn);
    final clockedOut =
        context.select<_ViewModel, DateTime?>((v) => v.punchCard!.clockedOut);
    final setDuration =
        context.select<_ViewModel, Future<void> Function(Duration)>(
            (v) => v.setDuration);
    final clearPunchedOut = context
        .select<_ViewModel, Future<void> Function()>((v) => v.clearPunchedOut);

    return GestureDetector(
      onTap: () async {
        final duration =
            clockedOut?.difference(clockedIn).ceilMinutes ?? Duration.zero;
        final pickedTime = await showTimePicker(
          helpText: AppLocalizations.of(context)!.duration,
          context: context,
          initialTime: duration.timeOfDay,
          builder: (context, child) => MediaQuery(
            data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
            child: child!,
          ),
        );
        if (pickedTime != null) {
          await setDuration(pickedTime.duration);
        }
      },
      child: IconCardTile(
        leading: const Icon(Icons.timelapse),
        title: Text(AppLocalizations.of(context)!.duration,
            style: secondaryBodyText1),
        body: Text(clockedOut?.difference(clockedIn).ceilMinutes.toFormatted ??
            AppLocalizations.of(context)!.punchedIn),
        trailing: IconButton(
          icon: const Icon(Icons.clear),
          onPressed: () async => await clearPunchedOut(),
        ),
      ),
    );
  }
}

class _LocationTile extends StatelessWidget {
  void onTap(BuildContext context) {
    final schedule = context.select<_ViewModel, Schedule?>((v) => v.schedule);
    final setLocation =
        context.select<_ViewModel, Future<void> Function(String)>(
            (v) => v.setLocation);

    if (schedule != null) return;
    unawaited(showDialog(
        context: context,
        builder: (context) => LocationsDialog(onSelection: setLocation)));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final secondaryBodyText1 =
        theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.secondary);

    final schedule = context.select<_ViewModel, Schedule?>((v) => v.schedule);
    final location = context.select<_ViewModel, Location?>((v) => v.location);
    final clearLocation = context
        .select<_ViewModel, Future<void> Function()>((v) => v.clearLocation);

    if (location == null) return Container();

    return GestureDetector(
      onTap: () => onTap(context),
      child: IconCardTile(
        leading: const Icon(Icons.pin_drop),
        title: Text(AppLocalizations.of(context)!.location,
            style: secondaryBodyText1),
        body: Padding(
          padding: const EdgeInsets.only(right: 30),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(location.name),
              if (location.address1.isNotEmpty) Text(location.address1),
              if (location.address2.isNotEmpty) Text(location.address2),
              if (location.address3.isNotEmpty) Text(location.address3),
            ],
          ),
        ),
        trailing: (schedule == null)
            ? IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () async => await clearLocation(),
              )
            : Container(),
      ),
    );
  }
}

class _JobTypeTile extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final secondaryBodyText1 =
        theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.secondary);

    final jobTypeName =
        context.select<_ViewModel, String>((v) => v.jobType!.name);

    return IconCardTile(
      leading: const Icon(Icons.timelapse),
      title: Text(AppLocalizations.of(context)!.jobType,
          style: secondaryBodyText1),
      body: Text(jobTypeName),
    );
  }
}

class _AlertsTile extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final secondaryBodyText1 =
        theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.secondary);

    final alerts = context.select<_ViewModel, Iterable<Alert>>((v) => v.alerts);

    final alertsString = alerts.map((e) => e.name(context)).join(' ');
    return IconCardTile(
      leading: const Icon(Icons.notifications),
      title:
          Text(AppLocalizations.of(context)!.alerts, style: secondaryBodyText1),
      body: Text(alertsString),
      trailing: const IconButton(
        icon: Icon(Icons.clear),
        onPressed: null,
      ),
    );
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String id;
  PunchCard? punchCard;
  Schedule? schedule;
  Location? location;
  JobType? jobType;
  User? employee;
  Iterable<Alert> alerts = [];

  _ViewModel(this.id) {
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    punchCard = await DataModel().punchCardModel.getById(id);
    punchCard = PunchCard.from(punchCard!);

    employee = await DataModel().userModel.getById(punchCard!.userId);
    if (punchCard?.scheduleId != null) {
      schedule =
          await DataModel().scheduleModel.getById(punchCard!.scheduleId!);
    }
    if (punchCard?.locationId != null) {
      location =
          await DataModel().locationModel.getById(punchCard!.locationId!);
    } else if (schedule?.locationId != null) {
      location = await DataModel().locationModel.getById(schedule!.locationId);
    }
    jobType = await DataModel().jobTypeModel.getById(punchCard!.jobTypeId);
    alerts = (await DataModel().alertModel.getActiveByPunchCardId(id))
        .distinctBy((e) => e.alertTypeId);
    notifyListeners();
  }

  Future<void> setDate(DateTime date) async {
    if (punchCard == null) return;
    final daysOffset = date.dateOnly.difference(punchCard!.clockedIn.dateOnly);
    if (daysOffset.inDays == 0) return;
    punchCard!.clockedIn = punchCard!.clockedIn.add(daysOffset);
    if (punchCard!.clockedOut != null) {
      punchCard!.clockedOut = punchCard!.clockedOut!.add(daysOffset);
    }
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> setPunchedIn(TimeOfDay timeOfDay) async {
    if (punchCard == null) return;
    punchCard!.clockedIn = punchCard!.clockedIn.setTimeOfDay(timeOfDay).toUtc();
    if (punchCard!.clockedOut != null &&
        punchCard!.clockedIn >= punchCard!.clockedOut!) {
      punchCard!.clockedIn = punchCard!.clockedIn.add(const Duration(days: -1));
    }
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> setPunchedOut(TimeOfDay timeOfDay) async {
    if (punchCard == null) return;
    punchCard!.clockedOut =
        punchCard!.clockedIn.setTimeOfDay(timeOfDay).toUtc();
    if (punchCard!.clockedIn >= punchCard!.clockedOut!) {
      punchCard!.clockedOut =
          punchCard!.clockedOut!.add(const Duration(days: 1));
    }
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> clearPunchedOut() async {
    if (punchCard == null) return;
    punchCard!.clockedOut = null;
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> setDuration(Duration duration) async {
    if (punchCard == null) return;
    punchCard!.clockedOut = punchCard!.clockedIn.add(duration);
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> setLocation(String locationId) async {
    if (punchCard == null) return;
    punchCard!.locationId = locationId;
    location = await DataModel().locationModel.getById(locationId);
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> clearLocation() async {
    if (punchCard == null) return;
    punchCard!.locationId = null;
    location = null;
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> save() async {
    if (punchCard == null && !punchCard!.isDirty) return;
    await DataModel().punchCardModel.saveDirty([punchCard!]);
  }
}
