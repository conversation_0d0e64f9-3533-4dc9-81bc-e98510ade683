import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class CreateChatButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String text;
  final double width;

  const CreateChatButton({
    Key? key,
    this.onPressed,
    this.text = 'Create Chat',
    this.width = 277,
  }) : super(key: key);

@override
Widget build(BuildContext context) => SizedBox(
    width: width,
    child: Builder(
      builder: (BuildContext buttonContext) => OutlinedButton(
          onPressed: onPressed,
          
          style: OutlinedButton.styleFrom(
            side: const BorderSide(color: Color(0xFF4BA2E7), width: 2.5),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(100),
            ),
            padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
            backgroundColor: Colors.transparent,
          ),
          child: Text(
            text,
            style: const TextStyle(
              fontFamily: 'Poppins',
              fontSize: 14,
              fontWeight: FontWeight.w700,
              letterSpacing: -0.14,
              color: Color(0xFF4BA2E7),
            ),
          ),
        ),
    ),
  );

// Example usage:
// CreateChatButton(
//   onPressed: () {
//     print('Button pressed!');
//   },
// )
}