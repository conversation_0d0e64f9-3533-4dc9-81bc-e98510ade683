import 'package:flutter/material.dart';
import '../misc/app_localization.dart';

class MessageDialog extends StatelessWidget {
  final String? title;
  final String? message;

  const MessageDialog({super.key, this.title, this.message});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline6 = theme.textTheme.titleLarge;
    final body1 = theme.textTheme.bodyLarge;

    return Dialog(
        backgroundColor: Colors.transparent,
        child: Card(
          child: IntrinsicWidth(
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (title != null)
                    Padding(
                      padding: const EdgeInsets.all(8),
                      child: Text(title!, style: headline6),
                    ),
                  if (message != null)
                    Padding(
                      padding: const EdgeInsets.all(8),
                      child: Text(message!, style: body1),
                    ),
                  Align(
                      alignment: Alignment.bottomRight,
                      child: Padding(
                        padding: const EdgeInsets.all(8),
                        child: ElevatedButton(onPressed: () => Navigator.of(context).pop(), child: Text(AppLocalization.of(context).ok)),
                      )),
                ],
              ),
            ),
          ),
        ));
  }
}
