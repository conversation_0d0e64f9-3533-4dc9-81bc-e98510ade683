import 'package:flutter/material.dart';

class BookingCard extends StatelessWidget {
  final Booking booking;

  BookingCard({required this.booking});

  @override
  Widget build(BuildContext context) => Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 6),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        title: Text(
          booking.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text('${booking.timeRange}  •  ${booking.duration}'),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () {
          // Handle onTap event
        },
      ),
    );
}

class Booking {
  final String title;
  final String timeRange;
  final String duration;

  Booking({required this.title, required this.timeRange, required this.duration});
}