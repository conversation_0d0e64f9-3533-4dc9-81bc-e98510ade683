import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../helpers/color_helper.dart';
import '../misc/extensions.dart';

class DateTimePicker extends StatefulWidget {
  final Function(DateTime value)? onSelected;
  final String? hint;
  final DateTime? initialValue;

  const DateTimePicker(
      {super.key, this.onSelected, this.hint, this.initialValue});

  @override
  State<DateTimePicker> createState() => _DateTimePickerState();
}

class _DateTimePickerState extends State<DateTimePicker> {
  DateTime? dateTime;

  @override
  void initState() {
    dateTime = widget.initialValue;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);

    final theme = Theme.of(context);
    final hintStyle =
        theme.textTheme.bodyMedium?.copyWith(color: theme.focusColor);

    final defaultDateTime = DateTime.now();
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (dateTime != null)
          Text(dateTime!.toFormattedDateTime(locale))
        else
          Text(widget.hint ?? '', style: hintStyle),
        InkWell(
          onTap: () async {
            final pickedDate = await showDatePicker(
              context: context,
              builder: (context, child) => Theme(
                data: ThemeData.light().copyWith(
                  colorScheme: ColorScheme.light(
                    primary: ColorHelper.thePunchRed(),
                  ),
                ),
                child: child!,
              ),
              initialDate: dateTime ?? defaultDateTime,
              firstDate: (dateTime ?? defaultDateTime).addMonths(-12 * 20),
              lastDate: (dateTime ?? defaultDateTime).addMonths(12 * 20),
            );
            if (pickedDate != null) {
              setState(() => dateTime = DateTime(
                  pickedDate.year,
                  pickedDate.month,
                  pickedDate.day,
                  dateTime?.hour ?? 0,
                  dateTime?.minute ?? 0));
              widget.onSelected?.call(dateTime!);
            }
          },
          child: const Padding(
              padding: EdgeInsets.symmetric(horizontal: 8),
              child: SizedBox(
                  width: 20, height: 20, child: Icon(Icons.calendar_today))),
        ),
        InkWell(
          onTap: () async {
            final pickedTime = await showTimePicker(
              context: context,
              initialTime: (dateTime ?? defaultDateTime).timeOfDay,
            );
            if (pickedTime != null) {
              setState(() => dateTime = DateTime(
                  (dateTime ?? defaultDateTime).year,
                  (dateTime ?? defaultDateTime).month,
                  (dateTime ?? defaultDateTime).day,
                  pickedTime.hour,
                  pickedTime.minute));
              widget.onSelected?.call(dateTime!);
            }
          },
          child: const Padding(
              padding: EdgeInsets.symmetric(horizontal: 8),
              child:
                  SizedBox(width: 20, height: 20, child: Icon(Icons.schedule))),
        ),
      ],
    );
  }
}

class DateInputField extends StatelessWidget {
  final String labelText;
  final TextEditingController controller;
  final DateTime initialDate;
  final Function(DateTime) onDateSelected;

  const DateInputField({
    super.key,
    required this.labelText,
    required this.controller,
    required this.initialDate,
    required this.onDateSelected,
  });

  @override
  Widget build(BuildContext context) => TextField(
        controller: controller,
        onTap: () async {
          final pickedDate = await showDatePicker(
            context: context,
            builder: (context, child) => Theme(
              data: ThemeData.light().copyWith(
                colorScheme: ColorScheme.light(
                  primary: ColorHelper.thePunchRed(), // Primary color for the date picker
                ),
              ),
              child: child!,
            ),
            initialDate: initialDate,
            firstDate: DateTime.utc(DateTime.now().year - 20),
            lastDate: DateTime.now(),
          );
          if (pickedDate != null) {
            onDateSelected(pickedDate);
            final formattedDate = DateFormat('EEEE, MMMM d').format(pickedDate);
            controller.text = formattedDate;
          }
        },
        decoration: InputDecoration(
          labelText: labelText,
          hintText: 'Select a date',
          labelStyle: Theme.of(context).textTheme.titleMedium,
          // This style will be applied when the label is floating (i.e., when focused or not empty)
          floatingLabelStyle: const TextStyle(
            color: Colors.red, // Red label when focused or floating
            fontWeight: FontWeight.bold, // Bold label
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: const BorderSide(
              color: Colors.blue,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: const BorderSide(
              color: Colors.red, // Red border when focused
              width: 2,
            ),
          ),
          filled: false,
          suffixIcon: const Icon(Icons.calendar_today_rounded),
        ),
        style: const TextStyle(
          color: Colors.grey, // Light grey text color
          fontWeight: FontWeight.bold, // Bold text
        ),
      );
}
