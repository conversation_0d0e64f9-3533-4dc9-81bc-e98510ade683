import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:the_punch_flutter/dataModel/data/job_type.dart';
import 'package:the_punch_flutter/dataModel/data/location.dart';
import 'package:the_punch_flutter/dataModel/data/schedule.dart';
import 'package:the_punch_flutter/dataModel/data/user.dart';
import 'package:the_punch_flutter/dataModel/data_model.dart';
import 'package:the_punch_flutter/dataModel/models/punch_card_model.dart';
import 'package:the_punch_flutter/dataModel/models/punch_view_model.dart';
import 'package:the_punch_flutter/dataModel/models/user_model.dart';
import 'package:the_punch_flutter/dialogs/view_punch_card_dialog.dart';
import 'package:the_punch_flutter/helpers/check_session.dart';
import 'package:the_punch_flutter/helpers/color_helper.dart';
import 'package:the_punch_flutter/misc/calculate_distance.dart';
import 'package:the_punch_flutter/state/location_state.dart';
import 'package:the_punch_flutter/state/punch_state.dart';
import 'package:the_punch_flutter/widgets/padded_card.dart';
// ...existing imports...

class StartTaskDialog extends StatefulWidget {
  final PunchViewModel punchViewModel;

  const StartTaskDialog({required this.punchViewModel, Key? key})
      : super(key: key);

  @override
  _StartTaskDialogState createState() => _StartTaskDialogState();
}

class _StartTaskDialogState extends State<StartTaskDialog> {
  bool isLoading = true;
  Location? closestLocation;
  List<String> tasks = [
    'Special Event',
    'Inspection',
    'Administrative',
    'Training',
    'Project',
    'Customer Visit',
    'Employee Visit',
    'Tracking'
  ];
  List<Map<String, dynamic>> cleaningSchedules = [];
  bool showTasks = false;
  bool isStartingTask = false;

  @override
  void initState() {
    super.initState();
    _loadClosestLocationAndSchedules();
  }

  Future<void> _loadClosestLocationAndSchedules() async {
    setState(() {
      isLoading = true;
    });

    // Load current position
    await LocationState.updateCurrentLocation();
    final currentPosition = LocationState().currentPosition;

    if (currentPosition != null) {
      // Get all active locations
      final locations = (await DataModel().locationModel.active).toList();

      // Calculate distances from current position to each location
      Map<Location, double> distances = {};
      for (var location in locations) {
        final distance = calculateDistance(
          currentPosition.latitude,
          currentPosition.longitude,
          location.latitude,
          location.longitude,
        );
        distances[location] = distance;
      }

      // Filter locations within 1 mile (1609.34 meters)
      final withinOneMile =
          distances.entries.where((entry) => entry.value <= 1609.34).toList();

      // Find the closest location
      if (withinOneMile.isNotEmpty) {
        withinOneMile.sort((a, b) => a.value.compareTo(b.value));
        closestLocation = withinOneMile.first.key;

        // Load cleaning schedules for today's date at the current location
        await _loadCleaningSchedules(closestLocation!.id);
      }
    }

    setState(() {
      isLoading = false;
    });
  }

  Future<void> _loadCleaningSchedules(String locationId) async {
    final scheduleModel = DataModel().scheduleModel;

    // Fetch all schedules for the current location
    final allSchedules = await scheduleModel.getByLocationId(locationId);

    // Filter schedules that are for today
    final now = DateTime.now();
    final todaySchedules = allSchedules.where((schedule) {
      return schedule.startDateLocal.year == now.year &&
          schedule.startDateLocal.month == now.month &&
          schedule.startDateLocal.day == now.day;
    }).toList();

    // Sort the schedules by start time (earliest to latest)
    todaySchedules.sort((a, b) => a.startDateLocal.compareTo(b.startDateLocal));

    List<Map<String, dynamic>> scheduleInfoList = [];
    for (var schedule in todaySchedules) {
      final user = await UserModel().getById(schedule.userId);
      scheduleInfoList.add({
        'schedule': schedule,
        'user': user,
      });
    }

    // Instead of getting punch cards by linkId, we now pull all punch cards for this month.
    // Define the start and end of the current month
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 1);

    final punchCardModel = PunchCardModel();
    // Get all punch cards for the current month
    final monthlyPunchCards =
        await punchCardModel.getBetween(startOfMonth, endOfMonth);

    // Extract all scheduleIds from this month's punch cards
    final monthlyScheduleIds = monthlyPunchCards
        .where((p) => p.scheduleId != null)
        .map((p) => p.scheduleId!)
        .toSet();

    // Remove schedules that already have a punch card this month
    scheduleInfoList.removeWhere((info) {
      final schedule = info['schedule'] as Schedule;
      return monthlyScheduleIds.contains(schedule.id);
    });

    setState(() {
      cleaningSchedules = scheduleInfoList;
    });
  }

  void _onYesPressed() {
  // Navigator.push(
  //   context,
  //   MaterialPageRoute(builder: (context) => const StartTaskPage()),
  // );
   // context.pushReplacement('/employees/start-task');
   Navigator.of(context).pop();
   var location_name = closestLocation?.name;
   const lkey ="closest_location";
   context.push('/employees/start-task',extra: [{lkey:location_name}]);
  }

  void _onNoPressed() {
    Navigator.of(context).pop();
  }

  void _onTaskSelected(String task, {String? scheduleId}) async {
    // Map of task names to their corresponding JobType IDs
    Map<String, String> taskNameToId = {
      'Special Event': JobType.specialEventId,
      'Inspection': JobType.inspectionId,
      'Administrative': JobType.administrativeId,
      'Training': JobType.trainingId,
      'Project': JobType.projectId,
      'Customer Visit': JobType.customerVisitId,
      'Employee Visit': JobType.employeeVisitId,
      'Unscheduled': JobType.unscheduledId, // Added Unscheduled
      'Scheduled': JobType.scheduledId, // Added Scheduled
    };

    String? jobTypeId = taskNameToId[task];

    if (jobTypeId == null) return; // Safety check

    final sessionOk = await checkSession(context);
    if (!sessionOk) return;

    setState(() {
      isStartingTask = true;
    });

    await PunchState().managerTaskPunchIn(
      locationId: closestLocation?.id,
      jobTypeId: jobTypeId,
      scheduleId: scheduleId, // Pass the scheduleId here
    );

    Navigator.of(context).pop(); // Close the Start Task Dialog

    // Reopen the View Punch Card Dialog to fetch the latest punch card
    await showDialog(
      context: context,
      builder: (BuildContext context) => const ViewPunchCardDialog(),
    );
  }

  void _onUnscheduledCleaningSelected() {
    _onTaskSelected('Unscheduled');
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return AlertDialog(
        content: Container(
          height: 150,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Detecting Current Location...',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: ColorHelper.thePunchGray(),
                ),
              ),
              SizedBox(height: 20),
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  ColorHelper.thePunchBlue(),
                ),
              ),
            ],
          ),
        ),
      );
    } else if (isStartingTask) {
      // Show the "Starting Task..." dialog content
      return AlertDialog(
        content: Container(
          height: 150,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Starting Task...',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: ColorHelper.thePunchGray(),
                ),
              ),
              SizedBox(height: 20),
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  ColorHelper.thePunchBlue(),
                ),
              ),
            ],
          ),
        ),
      );
    } else if (showTasks) {
      return AlertDialog(
        title: Center(
          child: RichText(
            text: TextSpan(
              text: 'Start a Task at -> ',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 16,
                  ),
              children: [
                TextSpan(
                  text: closestLocation?.name ?? 'Current Location',
                  style: TextStyle(
                    color: ColorHelper.thePunchGray(),
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Cleaning Task Section
              Align(
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(0, 8, 8, 0),
                  child: Text(
                    'Cleaning Task:',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontSize: 16,
                          color: ColorHelper.thePunchGray(),
                        ),
                  ),
                ),
              ),
              // Unscheduled "pill"
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: GestureDetector(
                  onTap: _onUnscheduledCleaningSelected,
                  child: PaddedCard(
                    color: Colors.grey[300],
                    child: IntrinsicHeight(
                      child: Stack(
                        children: [
                          const Align(
                            alignment: Alignment.centerLeft,
                            child: Icon(Icons.pin_drop),
                          ),
                          Align(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 18, vertical: 4),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    'Unscheduled',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(
                                          color: ColorHelper.thePunchGray(),
                                        ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // Scheduled Tasks Section
              if (cleaningSchedules.isNotEmpty)
                ...cleaningSchedules.asMap().entries.map((entry) {
                  final index = entry.key + 1;
                  final scheduleInfo = entry.value;
                  final schedule = scheduleInfo['schedule'] as Schedule;
                  final user = scheduleInfo['user'] as User;
                  final startTime =
                      TimeOfDay.fromDateTime(schedule.startDateLocal)
                          .format(context);
                  final endTime = TimeOfDay.fromDateTime(schedule.endDateLocal)
                      .format(context);

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Added spacing before each schedule's time
                      SizedBox(height: 12.0), // Adjust the height as needed

                      // New Row: For: EmployeeName
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 3, 16, 0),
                        child: RichText(
                          text: TextSpan(
                            style: Theme.of(context)
                                .textTheme
                                .titleSmall
                                ?.copyWith(
                                  fontSize: 10,
                                ),
                            children: [
                              // TextSpan(
                              //   text: 'For: ',
                              //   style: TextStyle(color: Colors.grey, fontSize: 11),
                              // ),
                              TextSpan(
                                text: '${user.firstName} ${user.lastName}',
                                style: Theme.of(context)
                                    .textTheme
                                    .titleSmall
                                    ?.copyWith(
                                      color: ColorHelper.thePunchGray(),
                                      fontSize: 11,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      // Start and End Times
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16.0, vertical: 0.0),
                        child: RichText(
                          text: TextSpan(
                            style: Theme.of(context)
                                .textTheme
                                .titleSmall
                                ?.copyWith(
                                  fontSize: 10,
                                ),
                            children: [
                              TextSpan(
                                text: 'Start Time: ',
                                style:
                                    TextStyle(color: Colors.grey, fontSize: 10),
                              ),
                              TextSpan(
                                text: '$startTime ',
                                style: Theme.of(context)
                                    .textTheme
                                    .titleSmall
                                    ?.copyWith(
                                      color: ColorHelper.thePunchGray(),
                                      fontSize: 10,
                                    ),
                              ),
                              TextSpan(
                                text: '| End Time: ',
                                style:
                                    TextStyle(color: Colors.grey, fontSize: 10),
                              ),
                              TextSpan(
                                text: '$endTime',
                                style: Theme.of(context)
                                    .textTheme
                                    .titleSmall
                                    ?.copyWith(
                                      color: ColorHelper.thePunchGray(),
                                      fontSize: 10,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      // Scheduled Task "pill" with only 'Scheduled'
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 0.0),
                        child: GestureDetector(
                          onTap: () {
                            _onTaskSelected('Scheduled',
                                scheduleId: schedule.id);
                          },
                          child: PaddedCard(
                            color: const Color.fromARGB(255, 207, 250, 161),
                            child: IntrinsicHeight(
                              child: Stack(
                                children: [
                                  const Align(
                                    alignment: Alignment.centerLeft,
                                    child: Icon(Icons.pin_drop),
                                  ),
                                  Align(
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 18, vertical: 4),
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            'Scheduled',
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleMedium
                                                ?.copyWith(
                                                  color: ColorHelper
                                                      .thePunchGray(),
                                                ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                }).toList()
              else
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    'No Schedules Found Today.',
                    style: TextStyle(
                      fontSize: 12,
                    ),
                  ),
                ),

              // // Divider
              // Divider(
              //   color: Colors.grey,
              //   thickness: 0.5,
              // ),
              Align(
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(0, 20, 8, 0),
                  child: Text(
                    'Other Task:',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontSize: 16,
                          color: ColorHelper.thePunchGray(),
                        ),
                  ),
                ),
              ),
              // Other tasks
              ...tasks.map((task) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4.0),
                    child: GestureDetector(
                      onTap: () => _onTaskSelected(task),
                      child: PaddedCard(
                        color: Theme.of(context).colorScheme.primary,
                        child: IntrinsicHeight(
                          child: Stack(
                            children: [
                              Align(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 18, vertical: 5),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        task,
                                        style: Theme.of(context)
                                            .textTheme
                                            .titleMedium
                                            ?.copyWith(
                                              color: ColorHelper.thePunchGray(),
                                            ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  )),
              // Divider(
              //   color: Colors.grey,
              //   thickness: 0.5,
              // ),
            ],
          ),
        ),
      );
    } else if (closestLocation != null) {
      return AlertDialog(
        content: RichText(
          text: TextSpan(
            text: 'Would you like to start a Task at: ',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontSize: 18,
                ),
            children: [
              TextSpan(
                text: closestLocation!.name,
                style: TextStyle(
                  color: ColorHelper.thePunchBlue(),
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextSpan(
                text: '?',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontSize: 18,
                    ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: _onNoPressed,
            child: Text('No'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.blue,
              side: BorderSide(color: ColorHelper.thePunchGray()),
            ),
          ),
          TextButton(
            onPressed: _onYesPressed,
            child: Text('Yes'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.blue,
              side: BorderSide(color: ColorHelper.thePunchGray()),
            ),
          ),
        ],
      );
    } else {
      return AlertDialog(
        title: Text('No Nearby Locations'),
        content:
            Text('No locations found within 1 mile of your current location.'),
        actions: [
          TextButton(
            onPressed: _onNoPressed,
            child: Text('OK'),
          ),
        ],
      );
    }
  }
}
