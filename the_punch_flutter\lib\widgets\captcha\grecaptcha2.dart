// ignore_for_file: avoid_web_libraries_in_flutter

@JS()
library grecaptcha2;

import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'dart:html' as html;
// ignore: depend_on_referenced_packages
import 'package:js/js.dart';

@JS('grecaptcha.getResponse')
external Object getResponse();

@JS('grecaptcha.render')
external Object render(String id);

@JS('recaptchaCallback')
external set _recaptchaCallback(void Function(String) f);

@JS('recaptchaExpired')
external set _recaptchaExpired(void Function() f);

class GRecaptcha2 extends StatefulWidget {
  final Function(String) callback;

  const GRecaptcha2(this.callback, {super.key});

  @override
  State<GRecaptcha2> createState() => _GRecaptcha2State();
}

class _GRecaptcha2State extends State<GRecaptcha2> {
  String grecaptchaId = 'grecaptcha';

  _GRecaptcha2State();

  void _dartRecaptchaCallback(String value) {
    if (mounted) widget.callback(value);
  }

  void _dartRecaptchaExpired() {
    // widget.callback('');
  }

  @override
  void initState() {
    // ignore: undefined_prefixed_name, avoid_dynamic_calls
    ui.platformViewRegistry.registerViewFactory(grecaptchaId, (int viewId) {
      // return html.DivElement()..innerHtml = 'hello, world';
      _recaptchaCallback = allowInterop(_dartRecaptchaCallback);
      _recaptchaExpired = allowInterop(_dartRecaptchaExpired);
      return html.DivElement()
        ..attributes = {
          'id': 'g-recaptcha',
          'data-sitekey': '6LeczdMaAAAAABRXbuOQSYoL4yvq2g2PRkHfvJIH',
          'data-callback': 'recaptchaCallback',
          'data-expired-callback': 'recaptchaExpired'
        };
    });
    Future.delayed(const Duration(milliseconds: 1000), () => render('g-recaptcha'));
    super.initState();
  }

  @override
  Widget build(BuildContext context) => Center(
        child: SizedBox(width: 302, height: 85, child: HtmlElementView(viewType: grecaptchaId)),
      );
}
