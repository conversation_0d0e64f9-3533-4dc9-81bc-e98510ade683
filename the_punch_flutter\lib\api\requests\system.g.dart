// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'system.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SystemRequest _$SystemRequestFromJson(Map<String, dynamic> json) =>
    SystemRequest(
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$SystemRequestToJson(SystemRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
    };

SystemResponse _$SystemResponseFromJson(Map<String, dynamic> json) =>
    SystemResponse(
      json['ErrorCode'] as String?,
      nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$SystemResponseToJson(SystemResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  return val;
}
