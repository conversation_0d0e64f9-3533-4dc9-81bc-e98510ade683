// lib/local_report_inputs.dart

import 'package:flutter/material.dart';

/// Enum describing different input types
enum LocalParameterType {
  text,
  date,
  dateTime,
  boolean,
  dropdown,
  multipleChoice,
  float,
  integer,
}

/// Simple model for one parameter (input field).
class LocalParameter {
  final String name;        // e.g. 'startDate'
  final String prompt;      // e.g. 'Start Date'
  final LocalParameterType type;
  final List<MapEntry<String, String>> validValues; // for dropdown or multi-choice
  final String initialValue;

  LocalParameter({
    required this.name,
    required this.prompt,
    required this.type,
    this.validValues = const [],
    this.initialValue = '',
  });
}

/// Static definitions for each report ID.
/// For example, if 'report1' is selected, we want 3 inputs:
///  - Date, Text, Checkbox
/// If 'report2' is selected, we want a dropdown, etc.
class LocalReportDefinitions {
  static final Map<String, List<LocalParameter>> definitions = {
    'report1': [
      LocalParameter(
        name: 'startDate',
        prompt: 'Start Date',
        type: LocalParameterType.date,
      ),
      LocalParameter(
        name: 'employeeName',
        prompt: 'Employee Name',
        type: LocalParameterType.text,
      ),
      LocalParameter(
        name: 'includeInactive',
        prompt: 'Include Inactive?',
        type: LocalParameterType.boolean,
      ),
    ],
    'report2': [
      LocalParameter(
        name: 'department',
        prompt: 'Department',
        type: LocalParameterType.dropdown,
        validValues: [
          MapEntry('sales', 'Sales'),
          MapEntry('hr', 'Human Resources'),
          MapEntry('it', 'IT'),
        ],
      ),
      LocalParameter(
        name: 'limit',
        prompt: 'Limit (Integer)',
        type: LocalParameterType.integer,
        initialValue: '10',
      ),
    ],
    // Add more reports here...
  };
}
