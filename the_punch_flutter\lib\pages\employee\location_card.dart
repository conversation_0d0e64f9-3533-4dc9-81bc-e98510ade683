import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'dart:ui' as ui;
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:provider/provider.dart';
import 'package:the_punch_flutter/state/punch_state.dart';
import 'package:uuid/uuid.dart';

import '../../dataModel/data/geo_location.dart';
import '../../dataModel/data/location.dart';
import '../../dataModel/data/punch_card.dart';
import '../../dataModel/data/user.dart';
import '../../dataModel/data_model.dart';
import '../../dataModel/models/punch_view_model.dart';
import '../../helpers/color_helper.dart';
import '../../helpers/screen_helper.dart';
import '../../misc/app_localization.dart';
import '../../misc/extensions.dart';
import '../../widgets/map_widget.dart';
import '../view_model_mixin.dart';

class LocationCard extends StatelessWidget {
  const LocationCard({super.key});

  @override
  Widget build(BuildContext context) {
 final double containerWidth = MediaQuery.of(context).size.width * 0.5;
 final punchCard = context.watch<PunchState>().punchCard;

    return Container(
     

      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,

        children: [
          Container(
            width: containerWidth,
            
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 25),
                  decoration: BoxDecoration(
        color: Colors.grey[300], // Approximate background color
        borderRadius: BorderRadius.circular(10.0),
      ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [  
              _Location(locationId: punchCard?.locationId ?? '')
          ])),
          const SizedBox(height: 12.0),
          GestureDetector(
                onTap: ()=>{
                // I need to get the locationId to give to content.go so I can naivagte to location_notes page
                context.push('/employees/location-notes',extra:punchCard!.locationId)
                },
                child: Container(
                  width: containerWidth,
                  decoration: BoxDecoration(
                    color: ColorHelper.thePunchLightYellow(), // Approximate yellow background
                    borderRadius: BorderRadius.circular(20.0),
                  ),
                  padding: EdgeInsets.symmetric(vertical: 10, horizontal: 25),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Location Notes',
                        style: TextStyle(
                          color: ColorHelper.thePunchMustard(),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(width: 8.0),
                      Icon(
                        Symbols.add_notes, // Approximate icon
                        color: ColorHelper.thePunchMustard(),
                        size: 20.0,
                      ),
                    ],
                  ),
                ),
              )],
                ),
              );

}
}

class LocationMap extends StatelessWidget {
  const LocationMap({super.key});

  @override
  Widget build(BuildContext context) => Container(
      width: 150.0, // Approximate width
      height: 122.0, // Approximate height
     
      decoration: BoxDecoration(
        color: Colors.grey[400], // Placeholder for the map background
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
        MyMap(),
          // Replace this with a real map widget or image
          //  Center(
          //   child: Icon(
          //     Icons.map_outlined,
          //     size: 60.0,
          //     color: Colors.grey[600],
          //   ),
          // ),
          // Positioned(
          //   bottom: 20.0, // Approximate bottom position
          //   left: 30.0, // Approximate left position
          //   child: Stack(
          //     alignment: Alignment.center,
          //     children: [
          //       Icon(
          //         Icons.location_on,
          //         color: Colors.red[400],
          //         size: 30.0,
          //       ),
          //       const Icon(
          //         Icons.check,
          //         color: Colors.white,
          //         size: 16.0,
          //       ),
          //     ],
          //   ),
          // ),
        
        ],
      ),
    );
}

class CombinedLocationCard extends StatelessWidget {
  const CombinedLocationCard({super.key});

  @override
  Widget build(BuildContext context) => const Row(

      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(child: LocationMap()),
        SizedBox(width: 8.0),
        Expanded(child: LocationCard()),
      ],
    );
}

class _Location extends StatelessWidget {
  final String locationId;

  const _Location({required this.locationId});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final body1OnSecondary = theme.textTheme.bodyMedium?.copyWith(
        color: Colors.black, fontWeight: FontWeight.w800);
    final body1OnSurface = theme.textTheme.bodyMedium?.copyWith(
      color: Colors.black,
      fontSize: ScreenHelper.screenHeightPercentage(context, 1.2),
    );
  
    return Consumer<PunchViewModel>(builder: (context, viewModel, child) {
      if (viewModel.location == null) return Container();
      final location = viewModel.location!;
     
      return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(location.name,
              style: body1OnSecondary, textAlign: TextAlign.left,),
              SizedBox(height: 1.5),
          if (location.address1.isNotEmpty)
            Text(location.address1,
                style: body1OnSurface, textAlign: TextAlign.left),
                SizedBox(height: 1.5),
          if (location.address2.isNotEmpty)
            Text(location.address2,
                style: body1OnSurface, textAlign: TextAlign.left),
          if (location.address3.isNotEmpty)
            Text(location.address3,
                style: body1OnSurface, textAlign: TextAlign.left),
        ],
      );
    });
  }
}


class MyMap extends StatefulWidget {
  @override
  __MapState createState() => __MapState();
}

class __MapState extends State<MyMap> {
  String _mapStyle = '';
  BitmapDescriptor? _customIcon;
  GoogleMapController? _mapController;

  @override
  void initState() {
    super.initState();
    _loadMapStyle();
    _loadCustomMarker();
  }

  Future<void> _loadMapStyle() async {
    try {
      _mapStyle = await rootBundle.loadString('assets/map_style.json');
    } catch (e) {
      print('Error loading map style: $e');
    }
  }
Future<BitmapDescriptor> getResizedMarkerIcon(String assetPath, {int width = 70, int height = 70}) async {
  final ByteData data = await rootBundle.load(assetPath);
  final codec = await ui.instantiateImageCodec(
    data.buffer.asUint8List(),
    targetWidth: width,
    targetHeight: height,
  );
  final frame = await codec.getNextFrame();
  final ui.Image image = frame.image;

  final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
  final resizedBytes = byteData!.buffer.asUint8List();

  return BitmapDescriptor.fromBytes(resizedBytes);
}
  Future<void> _loadCustomMarker() async {
    try {
      final icon = await getResizedMarkerIcon('images/custom_location.png', width: 100, height: 100);
      setState(() {
        _customIcon = icon;
      });
    } catch (e) {
      print('Error loading custom marker: $e');
    }
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    if (_mapStyle.isNotEmpty) {
      _mapController?.setMapStyle(_mapStyle);
    }
  }

  @override
  Widget build(BuildContext context) => Consumer<PunchViewModel>(builder: (context, viewModel, child) {
      final Set<Marker> _markers = {};
      final Set<Circle> _circles = {};

      if (viewModel.location == null) return Container();
      final location = viewModel.location!;
      final LatLng center = LatLng(location.latitude, location.longitude);

      _markers.add(Marker(
        markerId: const MarkerId('location'),
        position: center, // Ensure the marker is at the center
        icon: _customIcon ?? BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        anchor: Offset(0.5, .5), // Adjusted to move the marker down by half its height
      ));

      _circles.add(
        Circle(
          circleId: const CircleId('radius'),
          center: center, // Ensure the circle is centered at the same position
          radius: 750, // Radius in meters
          fillColor: Colors.red.withOpacity(0.05),
          //strokeColor: Colors.red.withOpacity(0.4),
          strokeWidth: 0,
        ),
      );

      return GoogleMap(
        onMapCreated: _onMapCreated, // Apply the map style when the map is created
        zoomControlsEnabled: false,
        markers: _markers,
        circles: _circles,
        mapType: MapType.normal,
        initialCameraPosition: CameraPosition(
          target: center, // Ensure the map is focused on the center
          zoom: 13,
        ),
      );
    });
}

