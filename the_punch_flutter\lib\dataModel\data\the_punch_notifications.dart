import 'package:json_annotation/json_annotation.dart';
import '../../misc/json_conversion.dart';
import '../base_data.dart'; // Ensure this path is correct

part 'the_punch_notifications.g.dart'; // The generated part file

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class ThePunchNotification extends BaseData {
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  final String title; // Title of the notification
  final String message; // Message content
  final String? targetDeviceId; // Optional target device ID
  final String targetUserId; // Target user ID (required)
  
  @JsonKey(fromJson: dateTimeFromJson, toJson: dateTimeToJson)
  final DateTime sentOn; // Time the notification was sent

  @JsonKey(fromJson: nullableDateTimeFromJson, toJson: nullableDateTimeToJson)
  final DateTime? receivedOn; // Time the notification was received (nullable)

  ThePunchNotification({
    required this.id,
    required this.title,
    required this.message,
    this.targetDeviceId,
    required this.targetUserId,
    required this.sentOn,
    this.receivedOn, required super.createdByUserId, required super.createdOn,
  });

  /// Factory method to create an instance from JSON
  factory ThePunchNotification.fromJson(Map<String, dynamic> json) =>
      _$ThePunchNotificationFromJson(json);

  /// Converts the instance to JSON
  Map<String, dynamic> toJson() => _$ThePunchNotificationToJson(this);
}
