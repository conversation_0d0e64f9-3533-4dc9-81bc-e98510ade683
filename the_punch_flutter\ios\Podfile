# Uncomment this line to define a global platform for your project
platform :ios, '14.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))

  pod 'Firebase/Core'
  pod 'Firebase/Messaging'

  target 'RunnerTests' do
    inherit! :search_paths
  end
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)

    target.build_configurations.each do |config|
      # Enable only the permissions your app needs
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
        '$(inherited)',

        ## dart: PermissionGroup.calendar
        'PERMISSION_EVENTS=0', # Set to 1 if you need calendar access

        ## dart: PermissionGroup.reminders
        'PERMISSION_REMINDERS=0', # Set to 1 if you need reminders access

        ## dart: PermissionGroup.contacts
        'PERMISSION_CONTACTS=0', # Set to 1 if you need contacts access

        ## dart: PermissionGroup.camera
        'PERMISSION_CAMERA=1', # Set to 1 if you need camera access

        ## dart: PermissionGroup.microphone
        'PERMISSION_MICROPHONE=0', # Set to 1 if you need microphone access

        ## dart: PermissionGroup.speech
        'PERMISSION_SPEECH_RECOGNIZER=0', # Set to 1 if you need speech recognition

        ## dart: PermissionGroup.photos
        'PERMISSION_PHOTOS=1', # Set to 1 if you need photo library access

        ## dart: [PermissionGroup.location, PermissionGroup.locationAlways, PermissionGroup.locationWhenInUse]
        'PERMISSION_LOCATION=1', # Enable location permission
        'PERMISSION_LOCATION_WHENINUSE=0', # Enable background location if needed

        ## dart: PermissionGroup.notification
        'PERMISSION_NOTIFICATIONS=1', # Set to 1 if you use push notifications

        ## dart: PermissionGroup.mediaLibrary
        'PERMISSION_MEDIA_LIBRARY=0', # Set to 1 if you need media library access

        ## dart: PermissionGroup.sensors
        'PERMISSION_SENSORS=1', # Enable sensors for motion access

        ## dart: PermissionGroup.bluetooth
        'PERMISSION_BLUETOOTH=0', # Set to 1 if you use Bluetooth

        ## dart: PermissionGroup.appTrackingTransparency
        'PERMISSION_APP_TRACKING_TRANSPARENCY=0', # Enable if you need app tracking transparency

        ## dart: PermissionGroup.criticalAlerts
        'PERMISSION_CRITICAL_ALERTS=0', # Set to 1 if you use critical alerts
      ]
    end
  end
end
