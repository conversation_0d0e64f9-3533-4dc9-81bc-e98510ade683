import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../../dataModel/data_model.dart';
import '../../../dataModel/data/inspection.dart';
import '../../../dataModel/data/location.dart';
import '../../../dialogs/create_inspection_dialog.dart';
import '../../../helpers/color_helper.dart';
import '../../../helpers/screen_helper.dart';
import '../../../misc/closest_date.dart';
import '../../view_model_mixin.dart';
import '../../../widgets/date_bar.dart';
import '../../../widgets/padded_card.dart';
import '../../../misc/extensions.dart';
import '../../web/home/<USER>';
import '../../web/my_scaffold.dart';

class InspectionsPage extends StatelessWidget {
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  InspectionsPage({super.key});

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider(
        create: (context) => _ViewModel(),
        builder: (context, child) => MyScaffold(
          key: _scaffoldKey,
          title: AppLocalizations.of(context)!.inspections,
          body: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _Header(),
              Expanded(
                child: _Body(),
              )
            ],
          ),
          floatingActionButton: FloatingActionButton.extended(
              heroTag: 'uniqueTag5',

            backgroundColor: ColorHelper.thePunchRed(),
            icon: const Icon(
              Icons.remove_red_eye,
              color: Colors.white,
            ),
            label: Text(
              AppLocalizations.of(context)!.newInspection,
              style: Theme.of(context).textTheme.titleSmall,
            ),
            onPressed: () {
              final context = _scaffoldKey.currentContext;
              if (context != null) {
                unawaited(
                  showDialog(
                    context: context,
                    builder: (context) => CreateInspectionDialog(
                      inspectionCreated: (id, newInspection) =>
                          context.pushNamed(
                        '/inspections/edit',
                        queryParameters: {'id': id, 'anything': newInspection},
                      ),
                    ),
                  ),
                );
              }
            },
          ),
        ),
      );
}

class _Header extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
        builder: (context, viewModel, child) => DateBar(
            pipsByDate: viewModel.countByDates,
            initialDate: DateTime.now().startOfWeek,
            selectedDate: viewModel.selectedDate),
      );
}

class _Body extends StatefulWidget {
  @override
  _BodyState createState() => _BodyState();
}

class _BodyState extends State<_Body> {
  PageController? pageController;

  PageController _createPageController(_ViewModel viewModel) {
    final pageController = PageController(
        initialPage: viewModel.dates!.indexOf(viewModel.selectedDate.value));
    pageController.addListener(() {
      if (viewModel.dates == null) return;
      if (!pageController.hasClients) return;
      if (pageController.page == null) return;
      if (pageController.page!.toInt() != pageController.page) return;
      final date = viewModel.dates![pageController.page!.toInt()];
      if (viewModel.selectedDate.value != date) {
        viewModel.selectedDate.value = date;
      }
    });
    viewModel.selectedDate.addListener(() {
      if (viewModel.dates == null) return;
      if (!pageController.hasClients) return;
      final page = viewModel.dates!.indexOf(viewModel.selectedDate.value);
      if (page == -1) return;
      unawaited(pageController.animateToPage(
        page,
        duration: const Duration(milliseconds: 500),
        curve: Curves.ease,
      ));
    });
    return pageController;
  }

  @override
  Widget build(BuildContext context) {
    final data = context.watch<_ViewModel>();

    if (data.dates == null || data.dates!.isEmpty) return Container();
    final dates = data.dates!;
    pageController ??= _createPageController(data);
    if (pageController!.hasClients &&
        pageController!.page != dates.indexOf(data.selectedDate.value)) {
      pageController!.jumpToPage(dates.indexOf(data.selectedDate.value));
    }
    return PageView.builder(
      controller: pageController,
      itemCount: dates.length,
      itemBuilder: (context, index) => _BodyPage(dates[index], data),
    );
  }
}

class _BodyPage extends StatelessWidget {
  final DateTime date;
  final _ViewModel data;

  const _BodyPage(this.date, this.data);

  @override
  Widget build(BuildContext context) => FutureBuilder<_PageViewModel>(
        future: Future(
          () async => await data.getPageData(date),
        ),
        builder: (context, snapshot) {
          if (snapshot.data == null) return Container();
          final inspections = snapshot.data!.inspections;
          final completed = snapshot.data!.completed;
          return Center(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: ScreenHelper.screenWidth(context),
              ),
              child: ListView(
                children: [
                  for (var i = 0; i < inspections.length; i++)
                    _InspectionsTile(
                      inspection: inspections[i],
                      location: data.locationMap[inspections[i].locationId],
                      completed: completed[i],
                    ),
                  Container(height: 64),
                ],
              ),
            ),
          );
        },
      );
}

class _InspectionsTile extends StatelessWidget {
  const _InspectionsTile(
      {required this.inspection,
      required this.location,
      required this.completed});

  final Inspection inspection;
  final Location? location;
  final double completed;

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);
    final theme = Theme.of(context);
    final caption = theme.textTheme.bodyMedium;
    final body1 = theme.textTheme.titleLarge?.copyWith(
      fontSize: ScreenHelper.screenHeightPercentage(context, 2),
    );
    final body2 = theme.textTheme.bodyMedium?.copyWith(
      fontSize: ScreenHelper.screenHeightPercentage(context, 1.6),
    );

    return Padding(
      padding: const EdgeInsets.all(8),
      child: GestureDetector(
        onTap: () async => context.pushNamed('/inspections/edit',
            queryParameters: {'id': inspection.id}),
        child: PaddedCard(
          elevation: 1,
          child: Flex(
            direction: Axis.horizontal,
            children: [
              Flexible(
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(location?.name ?? 'unnkown location', style: body1),
                      Text(
                        inspection.name,
                        style: body2,
                      ),
                    ],
                  ),
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    inspection.dateTime.toFormattedTime(locale),
                    style: caption,
                  ),
                  Text(
                    completed.toPercent,
                    style: caption,
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final selectedDate = ValueNotifier<DateTime>(DateTime.now().dateOnly);

  List<DateTime>? dates;
  Map<DateTime, int> countByDates = {};
  Map<String, Location> locationMap = {};

  _ViewModel() {
    addListenables([
      DataModel().inspectionModel,
      DataModel().inspectionItemModel,
      DataModel().locationModel
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    final allLocalStarts = (await DataModel().inspectionModel.getActiveStarts())
        .map((e) => e.toLocal().dateOnly);
    countByDates = _getCountByDates(allLocalStarts);
    if (dates == null) {
      selectedDate.value = closestDateToToday(countByDates.keys);
    }
    dates = countByDates.keys.toList();
    dates!.sort((a, b) => a.compareTo(b));
    notifyListeners();
  }

  Map<DateTime, int> _getCountByDates(Iterable<DateTime> dates) {
    final map = <DateTime, int>{};
    for (final element in dates) {
      if (!map.containsKey(element)) {
        map[element] = 1;
      } else {
        map[element] = map[element]! + 1;
      }
    }
    return map;
  }

  Future<_PageViewModel> getPageData(DateTime date) async {
    final pageData = await _PageViewModel.create(date);

    final locationIds = pageData.inspections.map((e) => e.locationId).toSet();
    locationIds.removeAll(locationMap.keys);
    final locations = await DataModel().locationModel.getByIds(locationIds);
    locationMap.addAll({for (final e in locations) e.id: e});

    return pageData;
  }
}

class _PageViewModel {
  final List<Inspection> inspections;
  final List<double> completed;

  _PageViewModel._(this.inspections, this.completed);

  static Future<_PageViewModel> create(DateTime date) async {
    final startDateUtc = date.toUtc();
    final endDateUtc = date.addDays(1).toUtc();
    final inspections = (await DataModel()
            .inspectionModel
            .getActiveInspectionsBetween(startDateUtc, endDateUtc))
        .toList();
    inspections.sort((a, b) => a.dateTime.compareTo(b.dateTime));

    final completed = await Future.wait(inspections.map((e) async {
      final count =
          await DataModel().inspectionItemModel.countByInspectionId(e.id);
      final countCompleted = await DataModel()
          .inspectionItemModel
          .countCompletedByInspectionIds(e.id);
      if (count == 0) return 1.0;
      return countCompleted / count;
    }).toList());

    return _PageViewModel._(inspections, completed);
  }
}
