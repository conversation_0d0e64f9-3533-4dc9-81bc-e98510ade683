import 'package:flutter/material.dart';

import '../helpers/color_helper.dart';

class LabeledSwitch extends StatelessWidget {
  final Widget label;
  final bool value;
  final Function(bool value)? onChanged;

  const LabeledSwitch(
      {super.key, required this.label, required this.value, this.onChanged});

  @override
  Widget build(BuildContext context) => Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          label,
          ConstrainedBox(
            constraints: const BoxConstraints(maxHeight: 20),
            child: Switch(
              onChanged: onChanged,
              value: value,
              activeColor: ColorHelper.thePunchRed(),
            ),
          ),
        ],
      );
}
