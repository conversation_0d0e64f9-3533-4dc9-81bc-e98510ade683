import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';

class StorageManager {
  Future<String> get _localPath async {
    final directory = await getApplicationDocumentsDirectory();
    return directory.path;
  }

  Future<File> _fileForType(String type) async {
    final path = await _localPath;
    return File('$path/stashed_$type.json');
  }

  Future<File> writeData(String type, List<Map<String, dynamic>> data) async {
    final file = await _fileForType(type);
    return file.writeAsString(json.encode(data));
  }

  Future<List<Map<String, dynamic>>> readData(String type) async {
    try {
      final file = await _fileForType(type);
      String contents = await file.readAsString();
      List<dynamic> jsonResponse = json.decode(contents);
      return jsonResponse.map((data) => data as Map<String, dynamic>).toList();
    } catch (e) {
      return [];  // If encountering an issue reading file, return empty list
    }
  }

  Future<void> deleteData(String type) async {
    final file = await _fileForType(type);
    if (await file.exists()) {
      await file.delete();
    } else {
      print('File does not exist: No need to delete.');
    }
  }
}