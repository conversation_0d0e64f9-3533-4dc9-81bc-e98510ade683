import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../misc/json_conversion.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import 'package:uuid/uuid.dart';

import '../base_data.dart';

part 'inspection.g.dart';

@HiveType(typeId: 5)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class Inspection extends BaseData {
  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  String name;

  @HiveField(102)
  @JsonKey(fromJson: idFromJson)
  String locationId;

  @HiveField(103)
  @JsonKey(toJson: dateTimeToJson, fromJson: dateTimeFromJson)
  DateTime dateTime;

  Inspection({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.name,
    required this.locationId,
    required this.dateTime,
  });

  factory Inspection.fromJson(Map<String, dynamic> json) {
    try {
      return _$InspectionFromJson(json);
    } catch (e) {
      if (kDebugMode) print('${StackTrace.current}\n$e');
      rethrow;
    }
  }
  Map<String, dynamic> toJson() => _$InspectionToJson(this);

  factory Inspection.create() => Inspection(
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
        id: const Uuid().v4(),
        name: '',
        dateTime: ServerTimeState().utcTime,
        locationId: '',
      );
}

@HiveType(typeId: 6)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class InspectionArea extends BaseData {
  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  String name;

  @HiveField(102)
  @JsonKey(fromJson: idFromJson)
  String inspectionId;

  @HiveField(103)
  @JsonKey(defaultValue: 0)
  int order;

  InspectionArea({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.name,
    required this.inspectionId,
    required this.order,
  });

  factory InspectionArea.fromJson(Map<String, dynamic> json) {
    try {
      return _$InspectionAreaFromJson(json);
    } catch (e) {
      if (kDebugMode) print('${StackTrace.current}\n$e');
      rethrow;
    }
  }
  Map<String, dynamic> toJson() => _$InspectionAreaToJson(this);

  factory InspectionArea.create() => InspectionArea(
        id: const Uuid().v4(),
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
        name: '',
        inspectionId: '',
        order: 0,
      );
}

@HiveType(typeId: 7)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class InspectionItem extends BaseData {
  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  String name;

  @HiveField(102)
  @JsonKey(fromJson: idFromJson)
  String inspectionId;

  @HiveField(103)
  @JsonKey(fromJson: idFromJson)
  String inspectionAreaId;

  @HiveField(104)
  int? grade;

  @HiveField(105)
  String? note;

  @HiveField(106, defaultValue: 0)
  @JsonKey(defaultValue: 0)
  int order;

  InspectionItem({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.name,
    required this.inspectionId,
    required this.inspectionAreaId,
    this.grade,
    this.note,
    required this.order,
  });

  factory InspectionItem.fromJson(Map<String, dynamic> json) {
    try {
      return _$InspectionItemFromJson(json);
    } catch (e) {
      if (kDebugMode) print('${StackTrace.current}\n$e');
      rethrow;
    }
  }
  Map<String, dynamic> toJson() => _$InspectionItemToJson(this);

  factory InspectionItem.create() => InspectionItem(
        id: const Uuid().v4(),
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
        name: '',
        inspectionId: '',
        inspectionAreaId: '',
        order: 0,
      );
}

@HiveType(typeId: 8)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class InspectionImage extends BaseData {
  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  @JsonKey(fromJson: idFromJson)
  String inspectionId;

  @HiveField(102)
  @JsonKey(fromJson: idFromJson)
  String inspectionItemId;

  @HiveField(103, defaultValue: 0)
  @JsonKey(defaultValue: 0)
  int order;

  InspectionImage({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.inspectionId,
    required this.inspectionItemId,
    required this.order,
  });

  factory InspectionImage.fromJson(Map<String, dynamic> json) {
    try {
      return _$InspectionImageFromJson(json);
    } catch (e) {
      if (kDebugMode) print('${StackTrace.current}\n$e');
      rethrow;
    }
  }
  Map<String, dynamic> toJson() => _$InspectionImageToJson(this);

  factory InspectionImage.create() => InspectionImage(
        id: const Uuid().v4(),
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
        inspectionId: '',
        inspectionItemId: '',
        order: 0,
      );
}
