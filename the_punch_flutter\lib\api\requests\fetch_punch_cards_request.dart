import 'package:json_annotation/json_annotation.dart';
import '../../dataModel/data/punch_card.dart';
import '../../misc/json_conversion.dart';
import 'system.dart';

part 'fetch_punch_cards_request.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class FetchPunchCardsRequest extends SystemRequest {
  final DateTime startTime;
  final DateTime endTime;

  FetchPunchCardsRequest({
    required this.startTime,
    required this.endTime,
    required String serverIP,
    required String databaseName,
    required String sessionId,
  }) : super(serverIP: serverIP, databaseName: databaseName, sessionId: sessionId);

  static Future<FetchPunchCardsRequest> create(DateTime startTime, DateTime endTime) async {
    final systemRequest = await SystemRequest.create();
    return FetchPunchCardsRequest(
      startTime: startTime,
      endTime: endTime,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory FetchPunchCardsRequest.fromJson(Map<String, dynamic> json) =>
      _$FetchPunchCardsRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$FetchPunchCardsRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class FetchPunchCardsResponse extends SystemResponse {
  final List<PunchCard> punchCards;

  FetchPunchCardsResponse({
    required this.punchCards,
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory FetchPunchCardsResponse.fromJson(Map<String, dynamic> json) =>
      _$FetchPunchCardsResponseFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$FetchPunchCardsResponseToJson(this);
}
