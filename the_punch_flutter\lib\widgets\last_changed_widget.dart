import 'package:flutter/material.dart';
import '../dataModel/base_data.dart';
import '../dataModel/data/user.dart';
import '../dataModel/data_model.dart';
import '../misc/app_localization.dart';
import '../misc/extensions.dart';

class LastChangedWidget extends StatelessWidget {
  final BaseData? data;
  const LastChangedWidget({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    if (data == null) return Container();

    final locale = Localizations.localeOf(context);

    final theme = Theme.of(context);
    final disabled = theme.disabledColor;
    final style = theme.textTheme.bodySmall?.copyWith(color: disabled);

    return FutureBuilder<List<User?>>(
      future: Future(() async {
        final createdByUserId = data!.createdByUserId;
        final lastChangedByUserId = data!.lastChangedByUserId;
        return [(await DataModel().userModel.getById(createdByUserId ?? '')), (await DataModel().userModel.getById(lastChangedByUserId ?? ''))];
      }),
      builder: (context, userData) {
        final createdOn = data!.createdOn;
        final lastChangedOn = data!.lastChangedOn;
        final createdByUser = userData.data?[0];
        final lastChangedByUser = userData.data?[1];

        return Row(mainAxisSize: MainAxisSize.min, children: [
          Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              children: [
                Text(AppLocalization.of(context).created, style: style),
                Text(createdOn.toFormattedDateTimeWithYear(locale), style: style),
                if (createdByUser != null) Text(createdByUser.name, style: style),
              ],
            ),
          ),
          if (lastChangedOn != null)
            Padding(
                padding: const EdgeInsets.all(8),
                child: Column(children: [
                  Text(AppLocalization.of(context).lastUpdated, style: style),
                  Text(lastChangedOn.toFormattedDateTimeWithYear(locale), style: style),
                  Text(lastChangedByUser?.name ?? '', style: style),
                ]))
        ]);
      },
    );
  }
}
