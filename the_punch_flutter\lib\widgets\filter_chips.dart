import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../helpers/color_helper.dart';
import 'decorated_text_field.dart';

class FilterChipsNotifier<T> extends ChangeNotifier implements ValueListenable<Map<T, bool>> {
  Map<T, bool> _value;

  FilterChipsNotifier(Map<T, bool> value) : _value = value;

  void setEnabled(T t, bool enabled) {
    _value[t] = enabled;
    notifyListeners();
  }

  @override
  Map<T, bool> get value => _value;
  set value(Map<T, bool> newValue) {
    if (_value != newValue) {
      _value = newValue;
      notifyListeners();
    }
  }
}

class FilterChips<T> extends StatelessWidget {
  final FilterChipsNotifier<T> data;
  final String? hint;
  final BoxConstraints? constraints;
  final BorderRadius? borderRadius;

  const FilterChips(this.data, {super.key, this.hint, this.constraints, this.borderRadius});

  Rect _getItemRect(BuildContext context) {
    final navigator = Navigator.of(context);
    final itemBox = context.findRenderObject()! as RenderBox;
    return itemBox.localToGlobal(Offset.zero, ancestor: navigator.context.findRenderObject()) & itemBox.size;
  }

  Future<void> _showPopup(BuildContext context) async {
    final itemRect = _getItemRect(context);

    await Navigator.of(context).push(_FilterChipRoute(
      itemRect: itemRect,
      child: ValueListenableBuilder<Map<T, bool>>(
          valueListenable: data,
          builder: (context, map, _) => Card(
              elevation: 5,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  for (final e in map.entries)
                    GestureDetector(
                      onTap: () => data.setEnabled(e.key, !e.value),
                      child: Container(
                        color: e.value ? Theme.of(context).focusColor : null,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Text(e.key.toString()),
                        ),
                      ),
                    ),
                ],
              ))),
    ));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).copyWith(hintColor: ColorHelper.thePunchDesktopLightGray());
    
    return ValueListenableBuilder<Map<T, bool>>(
        valueListenable: data,
        builder: (context, map, _) {
          Widget? hintText;
          if (hint != null && !map.values.any((e) => e)) {
            hintText = IntrinsicHeight(
              child: Stack(children: [
                Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 16),
                      child: Text(hint ?? '', style: theme.textTheme.bodyMedium?.copyWith(color: theme.hintColor)),
                    )),
              ]),
            );
          }

          return GestureDetector(
            onTap: () => unawaited(_showPopup(context)),
            child: ConstrainedBox(
              constraints: constraints ?? const BoxConstraints(minHeight: 44, minWidth: 100),
              child: DecoratedBox(
                decoration: BoxDecoration(
                  border: Border.all(color: ColorHelper.thePunchAdminForm()),
                  borderRadius: borderRadius,
                ),
                child: hintText ??
                    Padding(
                      padding: const EdgeInsets.all(8),
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 4,
                        children: [
                          for (final e in map.entries.where((e) => e.value))
                            InputChip(
                              onDeleted: () => data.setEnabled(e.key, false),
                              label: Text(e.key.toString()),
                            )
                        ],
                      ),
                    ),
              ),
            ),
          );
        });
  }
}

class _FilterChipRoute extends PopupRoute {
  Rect itemRect;
  Widget child;
  _FilterChipRoute({required this.itemRect, required this.child});

  @override
  Color? get barrierColor => null;

  @override
  bool get barrierDismissible => true;

  @override
  String? get barrierLabel => null;

  @override
  Widget buildPage(BuildContext context, Animation<double> animation, Animation<double> secondaryAnimation) => Stack(
        children: [
          Positioned(
              top: itemRect.bottom,
              left: itemRect.left,
              width: itemRect.width,
              child: SizeTransition(sizeFactor: animation, child: child)),
        ],
      );

  @override
  Duration get transitionDuration => const Duration(milliseconds: 100);
}

class FilterChips2 extends StatefulWidget {
  final Function(Iterable<String> values) onSelected;
  final String? hint;
  final Iterable<String> labels;

  const FilterChips2({super.key, required this.labels, required this.onSelected, this.hint});

  @override
  State<FilterChips2> createState() => _FilterChips2State();
}

class _FilterChips2State extends State<FilterChips2> {
  late FilterChipsNotifier<String> data;

  Rect _getItemRect(BuildContext context) {
    final navigator = Navigator.of(context);
    final itemBox = context.findRenderObject()! as RenderBox;
    return itemBox.localToGlobal(Offset.zero, ancestor: navigator.context.findRenderObject()) & itemBox.size;
  }

  @override
  void initState() {
    data = FilterChipsNotifier({for (final e in widget.labels) e: false});
    super.initState();
    data.addListener(() {
      widget.onSelected(data.value.entries.where((e) => e.value).map((e) => e.key));
    });
  }

  @override
  Widget build(BuildContext context) => ValueListenableBuilder<Map<String, bool>>(
      valueListenable: data,
      builder: (context, map, _) => InkWell(
            onTap: () => unawaited(_showPopup(context)),
            child: DecoratedContainer(
              labelText: widget.hint,
              child: !map.values.any((e) => e)
                  ? null
                  : ConstrainedBox(
                      constraints: const BoxConstraints(maxHeight: 20),
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 4,
                        children: [
                          for (final e in map.entries.where((e) => e.value))
                            InputChip(
                              onDeleted: () => data.setEnabled(e.key, false),
                              label: Text(e.key),
                            )
                        ],
                      ),
                    ),
            ),
          ));

  Future<void> _showPopup(BuildContext context) async {
    final itemRect = _getItemRect(context);

    await Navigator.of(context).push(_FilterChipRoute(
      itemRect: itemRect,
      child: ValueListenableBuilder<Map<String, bool>>(
          valueListenable: data,
          builder: (context, map, _) => Card(
              elevation: 5,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  for (final e in map.entries)
                    GestureDetector(
                      onTap: () => data.setEnabled(e.key, !e.value),
                      child: Container(
                        color: e.value ? Theme.of(context).focusColor : null,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Text(e.key),
                        ),
                      ),
                    ),
                ],
              ))),
    ));
  }
}
