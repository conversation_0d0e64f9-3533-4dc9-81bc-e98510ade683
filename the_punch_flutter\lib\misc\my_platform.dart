import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:timezone/standalone.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

class MyPlatform {
  static bool get isWeb => kIsWeb;

  static bool get isAndroid => !kIsWeb && Platform.isAndroid;

  static bool get isIOS => !kIsWeb && Platform.isIOS;

  static bool get isMacOS => !kIsWeb && Platform.isMacOS;

  static bool get isWindows => !kIsWeb && Platform.isWindows;

  static bool get isHandheld => isAndroid || isIOS;

  static bool get isDesktop => kIsWeb || Platform.isWindows || Platform.isMacOS;

  static String get deviceIana {
    final preferedIanas = {
      'Pacific/Honolulu',
      'America/Anchorage',
      'America/Los_Angeles',
      'America/Phoenix',
      'America/Denver',
      'America/Chicago',
      'America/New_York',
    };

    final offset = DateTime(2020, 7, 7).timeZoneOffset.inMilliseconds;
    final dst = DateTime(2020).timeZoneOffset.inMilliseconds != offset;

    final locations = tz.timeZoneDatabase.locations.entries
        .where((e) => e.value.zones.any((f) => f.offset == offset && f.isDst == dst))
        .map((e) => e.key);

    final k = locations.toSet().intersection(preferedIanas);
    if (k.isNotEmpty) {
      return k.first;
    } else {
      return locations.first;
    }
  }
}
