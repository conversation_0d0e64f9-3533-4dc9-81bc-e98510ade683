import 'dart:async';

import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class AppContactInfo extends StatelessWidget {
  const AppContactInfo({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textStyle = theme.textTheme.bodyMedium;
    final titleStyle = theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold);
    final linkStyle = textStyle?.copyWith(color: Colors.blue, decoration: TextDecoration.underline);
    return Card(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _IconText(child: Text('Morris Consulting Services, Inc.', style: titleStyle)),
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const _IconText(
                  icon: Icon(Icons.pin_drop),
                  child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                    Text('720 Brooker Creek Blvd STE 203'),
                    Text('Oldsmar, FL 34677-2937'),
                  ])),
              InkWell(
                onTap: () => unawaited(launchUrl(Uri(scheme: 'http', path: 'www.morriscs.com'))),
                child:
                    _IconText(icon: const Icon(Icons.public), child: Text('http://www.morriscs.com', style: linkStyle)),
              ),
              const _IconText(icon: Icon(Icons.phone), child: Text('(*************')),
            ],
          ),
        ],
      ),
    );
  }
}

class _IconText extends StatelessWidget {
  final Widget? icon;
  final Widget child;

  const _IconText({this.icon, required this.child});

  @override
  Widget build(BuildContext context) => Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (icon != null) icon!,
          child,
        ],
      );
}
