import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../dataModel/data/language.dart';
import '../../dataModel/data/location_notes.dart';
import '../../dataModel/data/schedule_template.dart';
import '../../dataModel/data/user.dart';
import '../../dataModel/data/user_type.dart';
import '../../dataModel/data/geo_location.dart';
import '../../dataModel/data/inspection.dart';
import '../../dataModel/data/inspection_template.dart';
import '../../dataModel/data/job_type.dart';
import '../../dataModel/data/location.dart';
import '../../dataModel/data/location_contact.dart';
import '../../dataModel/data/message.dart';
import '../../dataModel/data/note.dart';
import '../../dataModel/data/permission.dart';
import '../../dataModel/data/punch_card.dart';
import '../../dataModel/data/schedule.dart';
import '../../dataModel/data/alert.dart';
import '../../dataModel/data/user_type_permission.dart';
import '../../misc/json_conversion.dart';

import 'system.dart';

part 'get_changes.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class GetChangesRequest extends SystemRequest {
  @JsonKey(fromJson: nullableDateTimeFromJson, toJson: nullableDateTimeToJson)
  DateTime? changedAfter;

  GetChangesRequest({
    required this.changedAfter,
    required super.serverIP,
    required super.databaseName,
    required super.sessionId,
  });

  static Future<GetChangesRequest> create(DateTime? changedAfter) async {
    final systemRequest = await SystemRequest.create();

    return GetChangesRequest(
      changedAfter: changedAfter,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory GetChangesRequest.fromJson(Map<String, dynamic> json) {
    try {
      return _$GetChangesRequestFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }
  @override
  Map<String, dynamic> toJson() => _$GetChangesRequestToJson(this);
}
// class GetChangesRequest extends SystemRequest {
//   @JsonKey(fromJson: nullableDateTimeFromJson, toJson: nullableDateTimeToJson)
//   DateTime? changedAfter;

//   List<String> tableNames;

//   GetChangesRequest({
//     required this.changedAfter,
//     required this.tableNames,
//     required super.serverIP,
//     required super.databaseName,
//     required super.sessionId,
//   });

//   static Future<GetChangesRequest> create(DateTime? changedAfter, List<String> tableNames) async {
//     final systemRequest = await SystemRequest.create();

//     return GetChangesRequest(
//       changedAfter: changedAfter,
//       tableNames: tableNames,
//       serverIP: systemRequest.serverIP,
//       databaseName: systemRequest.databaseName,
//       sessionId: systemRequest.sessionId,
//     );
//   }

//   factory GetChangesRequest.fromJson(Map<String, dynamic> json) {
//     try {
//       return _$GetChangesRequestFromJson(json);
//     } catch (e) {
//       if (kDebugMode) print('$e\n${StackTrace.current}');
//       rethrow;
//     }
//   }
//   @override
//   Map<String, dynamic> toJson() => _$GetChangesRequestToJson(this);
// }

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class GetChangesResponse extends SystemResponse {
  @JsonKey(defaultValue: [])
  List<Alert> alerts;
  @JsonKey(defaultValue: [])
  List<GeoLocation> geoLocations;
  @JsonKey(defaultValue: [])
  List<Inspection> inspections;
  @JsonKey(defaultValue: [])
  List<InspectionArea> inspectionAreas;
  @JsonKey(defaultValue: [])
  List<InspectionItem> inspectionItems;
  @JsonKey(defaultValue: [])
  List<InspectionImage> inspectionImages;
  @JsonKey(defaultValue: [])
  List<InspectionTemplate> inspectionTemplates;
  @JsonKey(defaultValue: [])
  List<InspectionTemplateArea> inspectionTemplateAreas;
  @JsonKey(defaultValue: [])
  List<InspectionTemplateItem> inspectionTemplateItems;
  @JsonKey(defaultValue: [])
  List<JobType> jobTypes;
  @JsonKey(defaultValue: [])
  List<Location> locations;
  @JsonKey(defaultValue: [])
  List<LocationContact> locationContacts;
  @JsonKey(defaultValue: [])
  List<LocationNote> locationNotes;
  @JsonKey(defaultValue: [])
  List<Message> messages;
  @JsonKey(defaultValue: [])
  List<Note> notes;
  @JsonKey(defaultValue: [])
  List<Permission> permissions;
  @JsonKey(defaultValue: [])
  List<PunchCard> punchCards;
  @JsonKey(defaultValue: [])
  List<Schedule> schedules;
  @JsonKey(defaultValue: [])
  List<ScheduleTemplate> scheduleTemplates;
  @JsonKey(defaultValue: [])
  List<Permission> userPermissions;
  @JsonKey(defaultValue: [])
  List<User> users;
  @JsonKey(defaultValue: [])
  List<UserType> userTypes;
  @JsonKey(defaultValue: [])
  List<UserTypePermission> userTypePermissions;
  @JsonKey(defaultValue: [])
  List<Language> languages;

  GetChangesResponse({
    this.alerts = const [],
    this.geoLocations = const [],
    this.inspections = const [],
    this.inspectionAreas = const [],
    this.inspectionItems = const [],
    this.inspectionImages = const [],
    this.inspectionTemplates = const [],
    this.inspectionTemplateAreas = const [],
    this.inspectionTemplateItems = const [],
    this.jobTypes = const [],
    this.locations = const [],
    this.locationContacts = const [],
    this.locationNotes = const [],
    this.messages = const [],
    this.notes = const [],
    this.permissions = const [],
    this.punchCards = const [],
    this.schedules = const [],
    this.scheduleTemplates = const [],
    this.users = const [],
    this.userTypes = const [],
    this.userPermissions = const [],
    this.userTypePermissions = const [],
    this.languages = const [],
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory GetChangesResponse.fromJson(Map<String, dynamic> json) => _$GetChangesResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetChangesResponseToJson(this);
}
