import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../api/api_model.dart';
import '../dataModel/data/location.dart';
import '../dataModel/data/schedule.dart';
import '../dataModel/data/user.dart';
import '../dataModel/data_model.dart';
import '../misc/app_localization.dart';
import '../pages/view_model_mixin.dart';
import '../misc/extensions.dart';
import '../state/permissions_state.dart';
import 'active_toggle.dart';
import 'calendar/calendar_widget.dart';
import 'menu/popup_menu_label.dart';

class MonthlySchedulesWidget extends StatelessWidget {
  final Iterable<String>? employeeIds;
  final Iterable<String>? locationIds;
  final ActiveToggleState? active;
  final bool showEmployeeNames;
  final bool scrollable;
  final dynamic selectedDate;

  const MonthlySchedulesWidget({
    super.key,
    this.employeeIds,
    this.locationIds,
    this.active,
    required this.showEmployeeNames,
    this.scrollable = false,
    this.selectedDate,
  });

  @override
  Widget build(BuildContext context) {
    final viewModel = _MonthlySchedulesViewModel(
        employeeIds: employeeIds, locationIds: locationIds, active: active);
    return ChangeNotifierProvider.value(
      value: viewModel,
      child: Consumer<_MonthlySchedulesViewModel>(
        builder: (context, viewModel, _) => Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  icon: Icon(Icons.arrow_back),
                  onPressed: viewModel.loadPreviousMonth,
                ),
                Text('${viewModel.selectedDate.month}/${viewModel.selectedDate.year}'),
                IconButton(
                  icon: Icon(Icons.arrow_forward),
                  onPressed: viewModel.loadNextMonth,
                ),
              ],
            ),
            Expanded(
              child: CalendarWidget(
                selectedDate: selectedDate,
                scrollable: scrollable,
                builder: (context, start, end) async => await _getCalendarEvents(
                    context, start, end, showEmployeeNames, viewModel),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<Iterable<CalendarEvent>> _getCalendarEvents(
      BuildContext context,
      DateTime start,
      DateTime end,
      bool showEmployeeNames,
      _MonthlySchedulesViewModel viewModel) async {
    final theme = Theme.of(context);
    final caption = theme.textTheme.bodySmall;
    final activeTextStyle = caption?.copyWith(color: Colors.white);
    final inactiveTextStyle = caption?.copyWith(color: Colors.black87);
    final tooltipStyle = theme.textTheme.bodyLarge?.copyWith(
        color:
            theme.brightness == Brightness.dark ? Colors.black : Colors.white);

    final schedules = (await viewModel.getSchedules(start, end)).toList();
    schedules.sort((a, b) => a.startDateUtc.compareTo(b.startDateUtc));
    return schedules.map((e) => CalendarEvent(
          start: e.startDateUtc,
          end: e.endDateUtc,
          text: _scheduleToString(e, showEmployeeNames, viewModel),
          detailedText: _scheduleToDetailString(e, viewModel),
          tooltip: _scheduleToTooltip(context, e, viewModel),
          tooltipTextStyle: tooltipStyle,
          color: e.isActive ? Colors.green : Colors.yellow,
          textStyle: e.isActive ? activeTextStyle : inactiveTextStyle,
          popupOnSelected: (value) {
            switch (value) {
              case 1:
                GoRouter.of(context).pushNamed('/schedules/view',
                    queryParameters: {'id': e.id});
                break;
              case 2:
                GoRouter.of(context).pushNamed('/schedules/edit',
                    queryParameters: {'id': e.id});
                break;
              case 3:
                context.pushNamed('/schedules/editRepeatingSchedule',
                    queryParameters: {
                      'id': e.scheduleTemplateId,
                      'startDate': e.startDateUtc.dateOnly.toIso8601String()
                    });
                break;
            }
          },
          popupItemBuilder: (context) => [
            PopupMenuLabel(
                child: Text(_scheduleToTooltip(context, e, viewModel))),
            PopupMenuItem<int>(
                value: 1,
                child: Text(AppLocalization.of(context).viewSchedule)),
            if (PermissionsState().editSchedules)
              PopupMenuItem<int>(
                  value: 2,
                  child: Text(AppLocalization.of(context).editThisSchedule)),
            if (PermissionsState().editSchedules &&
                e.startDateUtc >= DateTime.now().toUtc())
              PopupMenuItem<int>(
                  value: 3,
                  child:
                      Text(AppLocalization.of(context).editRepeatingSchedule)),
          ],
        ));
  }

  String _scheduleToTooltip(
      BuildContext context, Schedule schedule, _MonthlySchedulesViewModel viewModel) {
    final locale = Localizations.localeOf(context);

    final startDate = schedule.startDateUtc.toFormattedDate(locale);
    final startTime = schedule.startDateUtc.toFormattedTime(locale);
    final endTime = schedule.endDateUtc.toFormattedTime(locale);
    final employeeName = viewModel.employeeMap[schedule.userId]?.name ?? '';
    final locationName = viewModel.locationMap[schedule.locationId]?.name ?? '';
    return '$startDate\n$startTime - $endTime\n$employeeName\n$locationName';
  }

  String _scheduleToString(
      Schedule schedule, bool showEmployeeNames, _MonthlySchedulesViewModel viewModel) {
    if (showEmployeeNames) {
      return viewModel.employeeMap[schedule.userId]?.name ?? '';
    } else {
      return viewModel.locationMap[schedule.locationId]?.name ?? '';
    }
  }

  String _scheduleToDetailString(Schedule schedule, _MonthlySchedulesViewModel viewModel) =>
      '${viewModel.employeeMap[schedule.userId]?.name ?? ''}\n${viewModel.locationMap[schedule.locationId]?.name ?? ''}';
}

class _MonthlySchedulesViewModel extends ChangeNotifier with ViewModelMixin {
  final Iterable<String>? employeeIds;
  final Iterable<String>? locationIds;
  final ActiveToggleState? active;

  DateTime selectedDate = DateTime.now();
  Map<String, User> employeeMap = {};
  Map<String, Location> locationMap = {};
  List<Schedule> schedules = [];

  _MonthlySchedulesViewModel({
    required this.employeeIds,
    required this.locationIds,
    required this.active,
  }) {
    addListenables([
      DataModel().scheduleModel,
      DataModel().locationModel,
      DataModel().userModel,
    ]);
    loadSchedulesForMonth(selectedDate.month, selectedDate.year);
  }

  @override
  Future<void> refresh() async {
    await loadSchedulesForMonth(selectedDate.month, selectedDate.year);
  }

  Future<void> loadPreviousMonth() async {
    selectedDate = DateTime(selectedDate.year, selectedDate.month - 1);
    await loadSchedulesForMonth(selectedDate.month, selectedDate.year);
  }

  Future<void> loadNextMonth() async {
    selectedDate = DateTime(selectedDate.year, selectedDate.month + 1);
    await loadSchedulesForMonth(selectedDate.month, selectedDate.year);
  }

  Future<void> loadSchedulesForMonth(int month, int year) async {
    try {
      var schedulesResponse = await ApiModel().getSchedulesForMonth(month, year);
      schedules = schedulesResponse;  // assuming schedulesResponse is a list of Schedule objects
      notifyListeners();
    } catch (e) {
      print('Error loading schedules: $e');
    }
  }

  Future<Iterable<Schedule>> getSchedules(DateTime start, DateTime end) async {
    var filteredSchedules = schedules.where((schedule) =>
        schedule.startDateUtc.isAfter(start) &&
        schedule.endDateUtc.isBefore(end));

    if (employeeIds != null && employeeIds!.isNotEmpty) {
      filteredSchedules = filteredSchedules.where((e) => employeeIds!.contains(e.userId));
    }
    if (locationIds != null && locationIds!.isNotEmpty) {
      filteredSchedules = filteredSchedules.where((e) => locationIds!.contains(e.locationId));
    }
    switch (active ?? ActiveToggleState.active) {
      case ActiveToggleState.active:
        filteredSchedules = filteredSchedules.where((e) => e.isActive);
        break;
      case ActiveToggleState.inactive:
        filteredSchedules = filteredSchedules.where((e) => !e.isActive);
        break;
      case ActiveToggleState.all:
        break;
    }

    final employeeIds0 = filteredSchedules.map((e) => e.userId).toSet();
    employeeIds0.removeWhere((e) => employeeMap.keys.contains(e));
    final employees = await DataModel().userModel.getByIds(employeeIds0);
    employeeMap.addAll({for (final e in employees) e.id: e});

    final locationIds0 = filteredSchedules.map((e) => e.locationId).toSet();
    locationIds0.removeWhere((e) => locationMap.keys.contains(e));
    final locations = await DataModel().locationModel.getByIds(locationIds0);
    locationMap.addAll({for (final e in locations) e.id: e});

    return filteredSchedules;
  }
}