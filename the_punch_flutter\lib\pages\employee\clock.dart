import 'dart:async';
import 'dart:io';
import 'package:community_material_icon/community_material_icon.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../../dataModel/data/location_notes.dart';
import '../../api/api_model.dart';
import '../../dataModel/data/user.dart';
import '../../dataModel/data/user_type.dart';
import '../../dataModel/data_model.dart';
import '../../dataModel/data/job_type.dart';
import '../../dataModel/data/location.dart';
import '../../dataModel/data/punch_card.dart';
import '../../dataModel/data/schedule.dart';
import '../../dataModel/hive_db.dart';
import '../../dataModel/models/location_model.dart';
import '../../dataModel/models/location_notes_model.dart';
import '../../dataModel/models/punch_card_model.dart';
import '../../dataModel/models/schedule_model.dart';
import '../../dataModel/models/user_model.dart';
import '../../dataModel/models/user_type_model.dart';
import '../../dialogs/constrained_search_dialog.dart';
import '../../dialogs/contacts_dialog.dart';
import '../../dialogs/notes_dialog.dart';
import '../../helpers/check_session.dart';
import '../../helpers/color_helper.dart';
import '../../helpers/screen_helper.dart';
import '../../helpers/text_style_helper.dart';
import '../../misc/calculate_distance.dart';
import '../../services/location_background_service.dart';
import '../../services/notification_service.dart';
import '../../state/app_state.dart'; // <-- For local app version
import '../../state/location_state.dart';
import '../../widgets/padded_card.dart';
import '../view_model_mixin.dart';
import '../../state/login_state.dart';
import '../../state/permissions_state.dart';
import '../../state/server_time_state.dart';
import '../../widgets/animated_duration.dart';
import '../../widgets/date_pill.dart';
import '../../state/punch_state.dart';
import '../../misc/extensions.dart';
import '../web/my_scaffold.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../misc/my_platform.dart';
import '../../dataModel/models/punch_view_model.dart';
import '../../services/storage_manager_service.dart';
import 'package:url_launcher/url_launcher.dart'; // For launching native navigation


class ClockPage extends StatefulWidget {
  const ClockPage({super.key});

  @override
  _ClockPageState createState() => _ClockPageState();
}

class _ClockPageState extends State<ClockPage> {
  StreamSubscription<String?>? _geofenceSubscription;

  bool selectedisScheduled = false;
  String selectedlocationid = '';
  String? selectedScheduleId;
  bool locationSelected = false;
  final StorageManager _storageManager = StorageManager();

  @override
  void initState() {
    super.initState();
    
    _clearData();
    // Listen to the StreamController's stream for geofence alerts
    _geofenceSubscription =
        geoFenceStreamController.stream.listen(_handleGeoFenceBreach);

    if (Platform.isAndroid) {
      _checkAndShowBackgroundLocationDisclosure();
    }
  }
  
  Future<void> _clearData() async {
    // 1) Remove travel_ping & geo_fence from local storage
    await _storageManager.deleteData('travel_ping');
    await _storageManager.deleteData('geo_fence');

    // 2) Clear relevant Hive boxes
    final hiveDb = await HiveDb.database;
    await hiveDb.messages.clear();
    await hiveDb.groupMembers.clear();
    await hiveDb.messageGroups.clear();
  }

  Future<void> _checkAndShowBackgroundLocationDisclosure() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    // Check if we have already shown the disclosure
    final bool disclosureShown =
        prefs.getBool('background_location_disclosure_shown') ?? false;
    if (!disclosureShown) {
      // Show a modal (non-dismissible) dialog that provides the prominent disclosure
      await showDialog(
        context: context,
        barrierDismissible: false, // User must tap the button to dismiss
        builder: (BuildContext context) => AlertDialog(
          title: const Text('Background Location Access'),
          content: const Text(
            'The Punch stores your location in the background to provide continuous tracking while Punched-In even when you are not on the app.'
            ' Your location data is only used for safety/tracking purposes, and is protected in accordance with our privacy policy. '
            'Please tap "I Understand" to confirm that you agree to this usage.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                // Dismiss the dialog
                Navigator.of(context).pop();
              },
              child: const Text('I Understand'),
            ),
          ],
        ),
      );
      // Mark that the disclosure has been shown so it won't be shown again
      await prefs.setBool('background_location_disclosure_shown', true);
    }
  }

  void _handleGeoFenceBreach(String? geoFenceStatus) {
    // Print out the stream data for debugging
    print('GeoFence status stream emitted: $geoFenceStatus'); // Debugging

    ScaffoldMessenger.of(context).hideCurrentSnackBar();

    if (geoFenceStatus == 'true') {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                "WARNING: You exited the location area without Punching Out."),
            backgroundColor: Colors.red,
            duration: Duration(days: 365), // Effectively indefinite
          ),
        );
      }
    } else if (geoFenceStatus == 'false') {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text("Reconnected: You are back at the location."),
            backgroundColor: Colors.green,
          ),
        );
      }
    } else {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
    }
  }

  @override
  void dispose() {
    _geofenceSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => MultiProvider(
        providers: [
          ChangeNotifierProvider<PunchViewModel>(
            create: (context) => PunchViewModel()..initializeData(),
          ),
          ChangeNotifierProvider<PunchState>.value(
            value: PunchState(),
          ),
        ],
        child: Builder(
          builder: (context) => MyScaffold(
            showDrawer: false,
            showNotificationButton: true,
            title: AppLocalizations.of(context)!.clock,
            body: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const _Header(),
                Expanded(
                  child: _Body(onLocationSelected: _locationSelected),
                )
              ],
            ),
          ),
        ),
      );

  void _locationSelected(BuildContext context, bool isScheduled,
      String locationId, String? scheduleId) async {
    if (mounted) {
      setState(() {
        selectedisScheduled = isScheduled;
        selectedlocationid = locationId;
        selectedScheduleId = scheduleId;
        locationSelected = true;
      });
    }
    Navigator.pop(context);
  }
}

class _Header extends StatelessWidget {
  const _Header();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final titleLargeStyle = theme.textTheme.titleLarge?.copyWith();

    return SizedBox(
      width: double.infinity,
      child: Row(
        children: [
          Expanded(
            child: Column(
              children: [
                SizedBox(height: ScreenHelper.screenHeightPercentage(context, 2)),
                DateExt(date: DateTime.now()),
                SizedBox(height: ScreenHelper.screenHeightPercentage(context, 1)),

                // Show status
                Consumer<PunchViewModel>(
                  builder: (context, viewModel, child) {
                    if ((viewModel.punchCard == null && viewModel.prePageLoad) 
                        || viewModel.prePageLoad) {
                      return const SizedBox.shrink();
                    } 
                    else if (PunchState().isPunchedIn == true 
                             || PunchState().isTaskActive == true) {
                      // Status: Clocked In
                      return Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(
                              text: '${AppLocalizations.of(context)!.status}: ',
                              style: titleLargeStyle,
                            ),
                            TextSpan(
                              text: AppLocalizations.of(context)!.clockedIn,
                              style: titleLargeStyle?.copyWith(
                                color: ColorHelper.thePunchBlue(),
                              ),
                            ),
                          ],
                        ),
                      );
                    } 
                    else if (PunchState().isPunchedIn == false 
                             && PunchState().isTaskActive == false) {
                      // Status: Punched Out
                      return Column(
                        children: [
                          Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                  text: '${AppLocalizations.of(context)!.status}: ',
                                  style: titleLargeStyle,
                                ),
                                TextSpan(
                                  text: AppLocalizations.of(context)!.punchedOut,
                                  style: titleLargeStyle?.copyWith(
                                    color: ColorHelper.thePunchRed(),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 20),

                          // Directions Button goes HERE
                          OutlinedButton.icon(
                            icon: const Icon(Icons.directions),
                            label: const Text("Directions"),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.blue,
                              side: const BorderSide(color: Colors.blue),
                            ),
                            onPressed: () {
                              showDialog(
                                context: context,
                                builder: (_) => const _NavigationSearchDialog(),
                              );
                            },
                          ),
                        ],
                      );
                    } 
                    else {
                      return const SizedBox.shrink();
                    }
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}


class _Body extends StatelessWidget {
  final Function(BuildContext, bool, String, String?) onLocationSelected;

  const _Body({required this.onLocationSelected});

  @override
  Widget build(BuildContext context) => Consumer<PunchViewModel>(
        builder: (context, viewModel, child) =>
            FutureBuilder<ConnectivityResult>(
          future: Connectivity().checkConnectivity(),
          builder: (context, snapshot) {
            if (!snapshot.hasData) {
              // return const Center(
              //   child: Text('Checking connectivity...'),
              // );
            }

            if (snapshot.data == ConnectivityResult.none) {
              return Container(
                margin: const EdgeInsets.fromLTRB(0, 150, 0, 50),
                padding: const EdgeInsets.fromLTRB(50, 0, 50, 0),
                child: Column(
                  children: [
                    const Text(
                      'No internet connection, please make sure airplane mode is disabled & reconnect to the internet.',
                      style: TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 10),
                    TextButton(
                      onPressed: () {
                        context.read<PunchViewModel>().initializeData();
                      },
                      child: const Text(
                        'Retry',
                        style: TextStyle(
                          color: Colors.blue,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }

            if (viewModel.punchCard == null &&
                viewModel.prePageLoad == true &&
                viewModel.isLoading == false) {
              // Directly check permission status here
              if (viewModel.permissionStatus == null) {
                return Container(
                  margin: const EdgeInsets.fromLTRB(0, 50, 0, 50),
                  child: const Text(
                    '',
                    style: TextStyle(color: Colors.black),
                  ),
                );
              } else if (viewModel.permissionStatus !=
                  LocationPermission.always) {
                return Container(
                  margin: const EdgeInsets.fromLTRB(0, 150, 0, 50),
                  padding: const EdgeInsets.fromLTRB(50, 0, 50, 0),
                  child: Column(
                    children: [
                      const Text(
                        'Please set Location Permissions: "Always"',
                        style: TextStyle(color: Colors.black),
                      ),
                      const Text(
                        'in Settings > The Punch on your device.',
                        style: TextStyle(color: Colors.black),
                      ),
                      const SizedBox(height: 10),
                      TextButton(
                        onPressed: () {
                          context.read<PunchViewModel>().initializeData();
                        },
                        child: const Text(
                          'Refresh',
                          style: TextStyle(
                            color: Colors.blue,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              } else {
                return Container(
                  margin: const EdgeInsets.fromLTRB(0, 50, 0, 50),
                  child: const Text(
                    'Loading...',
                    style: TextStyle(color: Colors.black),
                  ),
                );
              }
            }

            if (viewModel.isLoading) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      viewModel.isPunchingIn
                          ? 'Punching In...'
                          : 'Punching Out...',
                      style: TextStyle(
                          fontSize: 18, color: ColorHelper.thePunchRed()),
                    ),
                    const SizedBox(height: 16),
                    const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
                    ),
                  ],
                ),
              );
            } else {
              // This is needed for if the user is punched in but turned off permissions.
              if (viewModel.permissionStatus != LocationPermission.always) {
                return Container(
                  margin: const EdgeInsets.fromLTRB(0, 150, 0, 50),
                  padding: const EdgeInsets.fromLTRB(50, 0, 50, 0),
                  child: Column(
                    children: [
                      const Text(
                        'Please set Location Permissions: "Always"',
                        style: TextStyle(color: Colors.black),
                      ),
                      const Text(
                        'in Settings > The Punch on your device.',
                        style: TextStyle(color: Colors.black),
                      ),
                      const SizedBox(height: 10),
                      TextButton(
                        onPressed: () async {
                          var permission =
                              await LocationState.checkAndRequestPermissions();
                          // Reload the data only if permission is still not 'always'
                          if (permission != LocationPermission.always) {
                            await context
                                .read<PunchViewModel>()
                                .initializeData();
                          }
                        },
                        child: const Text(
                          'Refresh',
                          style: TextStyle(
                            color: Colors.blue,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              } else {
                return Consumer<PunchState>(
                  builder: (context, punchState, child) => punchState
                          .isPunchedIn
                      ? const _PunchedInBody()
                      : _PunchedOutBody(onLocationSelected: onLocationSelected),
                );
              }
            }
          },
        ),
      );
}

class _PunchedOutBody extends StatefulWidget {
  final Function(BuildContext, bool, String, String?) onLocationSelected;

  const _PunchedOutBody({required this.onLocationSelected});

  @override
  _PunchedOutBodyState createState() => _PunchedOutBodyState();
}

class _PunchedOutBodyState extends State<_PunchedOutBody> {
  bool isPermissionsLoading = false;

  @override
  Widget build(BuildContext context) => Consumer<PunchViewModel>(
        builder: (context, viewModel, child) => Stack(
          children: [
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Center(
                    child: ElevatedButton(
                      onPressed: () {},
                      style: ElevatedButton.styleFrom(
                        shape: const CircleBorder(
                          side: BorderSide(color: Colors.red),
                        ),
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        elevation: 0,
                        padding: const EdgeInsets.all(10),
                      ),
                      child: ElevatedButton(
                        onPressed: () async {
                          // SIMPLY call _showPunchInDialog
                          // Let that method handle all checks & spinning
                          if (!isPermissionsLoading) {
                            await _showPunchInDialog(context);
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          shape: const CircleBorder(),
                          padding: const EdgeInsets.all(25),
                        ),
                        child: Text(
                          '${AppLocalizations.of(context)!.punch}\n${AppLocalizations.of(context)!.inW}',
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // If we're loading, put a spinner in the middle
            if (isPermissionsLoading)
              Center(
                child: SizedBox(
                  width: 110,
                  height: 105,
                  child: CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(ColorHelper.thePunchBlue()),
                    strokeWidth: 10.0,
                  ),
                ),
              ),
          ],
        ),
      );

  void _showNavigationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (_) => const _NavigationSearchDialog(),
    );
  }
  // In _PunchedOutBodyState:
/// Inside _PunchedOutBodyState:
Future<void> _showPunchInDialog(BuildContext context) async {
  setState(() => isPermissionsLoading = true);

  try {
    // 1) Check session
    final sessionOk = await checkSession(context);
    if (!sessionOk) return;

    // 2) Check internet
    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      // show "No internet" dialog...
      return;
    }

    // 3) Check min app version
    final serverMinVersion = await ApiModel().getMinimumVersion();
    // ... handle that logic ...

    // 4) Fetch location
    await LocationState.updateCurrentLocation();

    // 5) Refresh schedules, etc.
    final userId = LoginState.userId;
    final scheduleModel = context.read<ScheduleModel>();
    try {
      final updated = await scheduleModel.getUpdatedSchedulesByEmployeeId(userId);
      await scheduleModel.save(updated);
    } catch (e) {
      // handle error
    }

    // 6) Check motion-activity permission
    final motionGranted = await _checkMotionActivity(context);
    if (!motionGranted) return;

    // 7) Check notification permission
    final notificationsGranted = await _checkNotificationPermissions(context);
    if (!notificationsGranted) return;
  } finally {
    if (mounted) {
      setState(() => isPermissionsLoading = false);
    }
  }

  // 8) Show the location-selection dialog
  bool locationChosen = false;
  bool chosenIsScheduled = false;
  String? chosenLocationId;
  String? chosenScheduleId;

  await showDialog(
    context: context,
    builder: (_) => _PunchInDialogContent(
      onLocationSelected: (dialogCtx, isScheduled, locId, schedId) {
        locationChosen = true;
        chosenIsScheduled = isScheduled;
        chosenLocationId = locId;
        chosenScheduleId = schedId;
        Navigator.of(dialogCtx).pop();
      },
    ),
  );

  // 9) If user actually selected a location, do the punch in
  if (locationChosen && chosenLocationId != null && mounted) {
    // Check if user is manager/admin
    final userTypeId = await UserModel().getCurrentUserTypeId(LoginState.userId);
    final isManagerOrAdmin =
        userTypeId?.toUpperCase() == UserType.managerId.toUpperCase() ||
        userTypeId?.toUpperCase() == UserType.administratorId.toUpperCase();

    // If manager picked a *real* location (not "start of day"), 
    // show the "Start Task" dialog *before* punching in.
    if (isManagerOrAdmin && chosenLocationId != 'fake-location') {
      // Present the same "start task" dialog used by managers 
      // (but pass along chosenLocationId)
      final chosenLocation = await DataModel().locationModel.getById(chosenLocationId!);
      await showDialog(
        context: context,
        builder: (ctx) => _StartTaskDialog(
          punchViewModel: Provider.of<PunchViewModel>(context, listen: false),
          forcedLocation: chosenLocation,
        ),
      );
    } else {
      // Otherwise do your normal handlePunchIn logic
      await Provider.of<PunchViewModel>(context, listen: false).handlePunchIn(
        chosenIsScheduled,
        chosenLocationId!,
        chosenScheduleId,
      );
    }
  }
}

  /// Request or check Motion Activity permission
  Future<bool> _checkMotionActivity(BuildContext context) async {
    final status = await Permission.activityRecognition.request();

    if (!Platform.isAndroid) {
      // iOS does NOT have a dedicated activityRecognition permission
      return true;
    }

    // 2) Android only: request the permission
    if (status.isGranted) {
      return true;
    }
    
    // If not granted, direct user to settings
    bool userWentToSettings = false;
    await showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('Motion Activity Required'),
        content: const Text(
            'Please allow motion activity permissions in your device settings to accurately track location.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () async {
              userWentToSettings = await openAppSettings();
              Navigator.of(context).pop();
            },
            child: const Text('Go to Settings'),
          ),
        ],
      ),
    );

    if (userWentToSettings) {
      // Check again
      final recheckStatus = await Permission.activityRecognition.status;
      if (recheckStatus.isGranted) {
        return true;
      }
    }

    // If still denied
    await showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('You must allow Motion Activity'),
        content: const Text(
            'Motion Activity is required to properly track your location while punched in.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Refresh'),
          ),
        ],
      ),
    );
    return false;
  }

  /// Request/check Notification permission
  Future<bool> _checkNotificationPermissions(BuildContext context) async {
    final status = await Permission.notification.request();

    if (status.isGranted) {
      return true;
    }

    // If not granted, direct user to settings
    bool userWentToSettings = false;
    await showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('Notifications Required'),
        content: const Text(
            'Please allow notifications in your device settings so we can alert you about location and geofence updates.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () async {
              userWentToSettings = await openAppSettings();
              Navigator.of(context).pop();
            },
            child: const Text('Go to Settings'),
          ),
        ],
      ),
    );

    if (userWentToSettings) {
      // Check again
      final recheckStatus = await Permission.notification.status;
      if (recheckStatus.isGranted) {
        return true;
      }
    }

    // If still denied
    await showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('You must allow Notifications'),
        content: const Text(
            'Notifications are required to alert you about geofence or location events while punched in.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Refresh'),
          ),
        ],
      ),
    );
    return false;
  }
}
class _NavigationSearchDialog extends StatefulWidget {
  const _NavigationSearchDialog({Key? key}) : super(key: key);

  @override
  State<_NavigationSearchDialog> createState() => _NavigationSearchDialogState();
}

class _NavigationSearchDialogState extends State<_NavigationSearchDialog> {
  final TextEditingController _controller = TextEditingController();
  List<Location> _matchingLocations = [];
  bool _isSearching = false;

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _searchLocations(String query) async {
    // If fewer than 5 chars, clear results
    if (query.length < 5) {
      setState(() {
        _matchingLocations = [];
      });
      return;
    }

    setState(() => _isSearching = true);

    try {
      final allLocations = await DataModel().locationModel.active;
      final filtered = allLocations
          .where(
            (loc) => loc.name.toLowerCase().contains(query.toLowerCase().trim()),
          )
          .toList();

      setState(() {
        _matchingLocations = filtered;
      });
    } finally {
      if (mounted) setState(() => _isSearching = false);
    }
  }

  Future<void> _openNavigation(Location loc) async {
    final lat = loc.latitude;
    final lng = loc.longitude;
    final fallbackAddress = '${loc.address1} ${loc.address2} ${loc.address3}'.trim();

    // If coordinates are valid
    if (lat != 0 && lng != 0) {
      if (Platform.isAndroid) {
        final uri = Uri.parse('google.navigation:q=$lat,$lng');
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri);
          return;
        }
      } else if (Platform.isIOS) {
        final uri = Uri.parse('http://maps.apple.com/?daddr=$lat,$lng');
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri);
          return;
        }
      }
      // fallback universal link
      final fallbackUri = Uri.parse(
        'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng',
      );
      await launchUrl(fallbackUri, mode: LaunchMode.externalApplication);
    } else {
      // fallback textual address
      final fallbackUri = Uri.parse(
        'https://www.google.com/maps/dir/?api=1&destination=${Uri.encodeFull(fallbackAddress)}',
      );
      await launchUrl(fallbackUri, mode: LaunchMode.externalApplication);
    }
  }

  @override
  Widget build(BuildContext context) => AlertDialog(
      title: const Text(
        'Directions Search',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.black54,
        ),
        textAlign: TextAlign.center,
      ),
      content: SizedBox(
        width: 300,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // The text field with black text
            TextField(
              controller: _controller,
              onChanged: _searchLocations,
              style: const TextStyle(color: Colors.black), // Force black text
              decoration: InputDecoration(
                fillColor: Colors.grey[200], // light grey background
                filled: true,
                hintText: 'Search location...',
                hintStyle: const TextStyle(color: Colors.black54),
                prefixIcon: const Icon(Icons.search, color: Colors.black),
                border: const OutlineInputBorder(),
                isDense: true,
              ),
            ),
            const SizedBox(height: 12),

            if (_isSearching)
              const CircularProgressIndicator()
            else if (_matchingLocations.isNotEmpty)
              // Show the list of matches
              Flexible(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: _matchingLocations.length,
                  itemBuilder: (context, index) {
                    final loc = _matchingLocations[index];

                    // Let's build a multiline address:
                    final addressLines = <String>[];
                    if (loc.address1.isNotEmpty) addressLines.add(loc.address1);
                    if (loc.address2.isNotEmpty) addressLines.add(loc.address2);
                    //if (loc.address3.isNotEmpty) addressLines.add(loc.address3);

                    // "City, State Zip" if city/state/zip exist
                    String cityStateZip = '';
                    if (loc.city.isNotEmpty) cityStateZip += loc.city;
                    if (loc.state.isNotEmpty) {
                      cityStateZip += cityStateZip.isEmpty
                          ? loc.state
                          : ', ${loc.state}';
                    }
                    if (loc.zip.isNotEmpty) {
                      cityStateZip += cityStateZip.isEmpty
                          ? loc.zip
                          : ' ${loc.zip}';
                    }
                    if (cityStateZip.isNotEmpty) {
                      addressLines.add(cityStateZip);
                    }

                    return InkWell(
                      onTap: () async {
                        // Close the dialog first
                        Navigator.of(context).pop();
                        // Then open navigation
                        await _openNavigation(loc);
                      },
                      child: Container(
                        color: Colors.grey[200], // Grey background
                        margin: const EdgeInsets.symmetric(vertical: 2),
                        padding: const EdgeInsets.all(10),
                        child: Row(
                          children: [
                            // Left icon
                            const Icon(Icons.location_on, color: Colors.black),
                            const SizedBox(width: 8),

                            // Name + address lines
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    loc.name,
                                    style: const TextStyle(
                                      color: Colors.black,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14,
                                    ),
                                  ),
                                  // Print each address line
                                  for (final line in addressLines)
                                    Text(
                                      line,
                                      style: const TextStyle(
                                        color: Colors.black87,
                                        fontSize: 13,
                                      ),
                                    ),
                                ],
                              ),
                            ),

                            // Right arrow icon
                            const Icon(
                              Icons.arrow_forward_ios,
                              color: Colors.black45,
                              size: 16,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              )
            else if (_controller.text.length >= 3)
              // No results
              const Text(
                'No locations found.',
                style: TextStyle(color: Colors.redAccent, fontSize: 16),
              ),
          ],
        ),
      ),
      actions: [
        TextButton(
          style: TextButton.styleFrom(
            foregroundColor: Colors.blue,
            side: const BorderSide(color: Colors.blue),
          ),
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
}


/// Quick location-selection dialog
class _PunchInDialogContent extends StatefulWidget {
  final Function(BuildContext, bool, String, String?) onLocationSelected;

  const _PunchInDialogContent({required this.onLocationSelected});

  @override
  _PunchInDialogContentState createState() => _PunchInDialogContentState();
}

class _PunchInDialogContentState extends State<_PunchInDialogContent> {
  bool isLoading = true;
  bool isManagerOrAdmin = false;

  // For employees:
  List<Map<String, dynamic>> scheduledLocations = [];
  List<Location> nearbyLocations = [];
  Map<String, double> locationDistances = {};

  // For managers/admin:
  List<Location> managerNearbyLocations = [];
  Map<String, double> managerLocationDistances = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    await LocationState.updateCurrentLocation();
    final currentPosition = LocationState().currentPosition;
    final userId = LoginState.userId;
    final userTypeId = await UserModel().getCurrentUserTypeId(userId);

    setState(() {
      // Determine if user is manager or admin
      isManagerOrAdmin =
          (UserType.managerId.toUpperCase() == userTypeId?.toUpperCase()) ||
              (UserType.administratorId.toUpperCase() == userTypeId?.toUpperCase());
    });

    // If manager/admin, gather "detected locations" within ~2 miles
    if (currentPosition != null && isManagerOrAdmin) {
      final locations = (await DataModel().locationModel.active).toList();
      final distances = <String, double>{};
      for (final loc in locations) {
        final d = calculateDistance(
          currentPosition.latitude,
          currentPosition.longitude,
          loc.latitude,
          loc.longitude,
        );
        distances[loc.id] = d;
      }
      managerLocationDistances = distances;

      // Filter to within 2 miles
      final maxDistanceInMeters = 2.0 * 1609.34;
      final managerNearby = locations
          .where((loc) => distances[loc.id]! <= maxDistanceInMeters)
          .toList();
      managerNearby.sort((a, b) => distances[a.id]!.compareTo(distances[b.id]!));

      setState(() {
        managerNearbyLocations = managerNearby;
        isLoading = false;
      });
      return;
    }

    // Otherwise: employee logic
    if (currentPosition != null && !isManagerOrAdmin) {
      final locations = (await DataModel().locationModel.active).toList();
      final schedules = (await DataModel().scheduleModel.active).toList();

      final distances = <String, double>{};
      for (final loc in locations) {
        final meters = calculateDistance(
          currentPosition.latitude,
          currentPosition.longitude,
          loc.latitude,
          loc.longitude,
        );
        distances[loc.id] = meters;
      }
      locationDistances = distances;

      final maxDistanceInMiles = 2.0;
      final maxDistanceInMeters = maxDistanceInMiles * 1609.34;

      final today = DateTime.now();
      final scheduled = schedules
          .where((schedule) {
            final scheduleDate = schedule.startDateLocal;
            return scheduleDate.year == today.year &&
                scheduleDate.month == today.month &&
                scheduleDate.day == today.day;
          })
          .map((schedule) {
            final location = locations.firstWhere(
              (loc) => loc.id == schedule.locationId,
              orElse: () => Location.create(),
            );
            return {'schedule': schedule, 'location': location};
          })
          .toList();

      final scheduledLocationIds =
          scheduled.map((e) => e['location']!.id).toSet();

      final nearby = locations
          .where((loc) =>
              !scheduledLocationIds.contains(loc.id) &&
              distances[loc.id]! <= maxDistanceInMeters)
          .toList();
      nearby.sort((a, b) => distances[a.id]!.compareTo(distances[b.id]!));

      setState(() {
        scheduledLocations = scheduled;
        nearbyLocations = nearby;
        isLoading = false;
      });
    } else {
      // No current position found => stop
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.only(top: 45.0),
          child: SizedBox(
            width: 110,
            height: 105,
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                ColorHelper.thePunchBlue(),
              ),
              strokeWidth: 10.0,
            ),
          ),
        ),
      );
    }

    return AlertDialog(
      title: Center(
        child: Text(
          'Select Location:',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: ColorHelper.thePunchGray(),
              ),
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isManagerOrAdmin)
              _buildManagerLocationSection(context)
            else ...[
              _buildSection(
                'Scheduled Locations',
                scheduledLocations,
                locationDistances,
                context,
                true,
              ),
              _buildSection(
                'Nearby Locations',
                nearbyLocations,
                locationDistances,
                context,
                false,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildManagerLocationSection(BuildContext context) {
    final theme = Theme.of(context);
    final titleStyle = theme.textTheme.titleMedium;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // DETECTED LOCATIONS
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
          child: Text(
            'Detected Location:',
            style: titleStyle,
          ),
        ),
        if (managerNearbyLocations.isEmpty)
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              'No detected locations',
              style: TextStyle(fontSize: 12, color: Colors.red),
            ),
          )
        else
          ...managerNearbyLocations.map((loc) {
            final distance = managerLocationDistances[loc.id];
            return _LocationTile(
              location: loc,
              distance: distance,
              selected: () => widget.onLocationSelected(
                context,
                false, // isScheduled
                loc.id,
                null, // scheduleId
              ),
            );
          }).toList(),

        const SizedBox(height: 16),
        // OTHER
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
          child: Text(
            'Other:',
            style: titleStyle,
          ),
        ),
        _buildFakeLocationTile(context),
      ],
    );
  }

  Widget _buildSection(
    String title,
    List<dynamic> items,
    Map<String, double> locationDistances,
    BuildContext context,
    bool isScheduled,
  ) {
    final theme = Theme.of(context);
    final titleStyle = theme.textTheme.titleMedium;
    final timeLabelStyle =
        theme.textTheme.titleSmall?.copyWith(color: Colors.grey, fontSize: 8);

    if (isScheduled) {
      items.sort((a, b) => a['schedule']
          .startDateLocal
          .compareTo(b['schedule'].startDateLocal));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
          child: Text(
            title,
            style: titleStyle,
          ),
        ),
        if (items.isEmpty)
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.0),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text('None', style: TextStyle(fontSize: 12)),
            ),
          )
        else
          ...items.map((e) {
            final location = isScheduled ? e['location'] : e;
            final schedule = isScheduled ? e['schedule'] : null;
            final distance =
                locationDistances[isScheduled ? e['location'].id : e.id];

            final startTime = schedule?.startDateLocal != null
                ? TimeOfDay.fromDateTime(schedule.startDateLocal)
                    .format(context)
                : '';
            final endTime = schedule?.endDateLocal != null
                ? TimeOfDay.fromDateTime(schedule.endDateLocal).format(context)
                : '';

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (isScheduled)
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16.0, vertical: 0.0),
                    child: RichText(
                      text: TextSpan(
                        text: 'Start Time: $startTime  -  End Time: $endTime',
                        style: timeLabelStyle,
                      ),
                    ),
                  ),
                _LocationTile(
                  location: location,
                  distance: distance,
                  selected: () => widget.onLocationSelected(
                    context,
                    isScheduled,
                    location.id,
                    isScheduled ? schedule.id : null,
                  ),
                ),
                if (isScheduled) const SizedBox(height: 10),
              ],
            );
          }).toList(),
      ],
    );
  }

  Widget _buildFakeLocationTile(BuildContext context) => _LocationTile(
        location: Location(
          id: 'fake-location',
          name: 'Start of Day',
          address1: 'Current Location',
          address2: '',
          city: '',
          state: '',
          zip: '',
          country: '',
          phone: '',
          emailAddress: '',
          latitude: 0,
          longitude: 0,
          geoFenceRadius: 1000,
          timeZone: '',
          createdOn: DateTime.now(),
        ),
        distance: 0,
        selected: () => widget.onLocationSelected(
          context,
          false,
          'fake-location',
          null,
        ),
      );
}

class _LocationTile extends StatelessWidget {
  final Function() selected;
  final Location location;
  final double? distance;

  const _LocationTile({
    required this.location,
    required this.distance,
    required this.selected,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final secondaryBodyText1 = theme.textTheme.titleMedium?.copyWith(
      color: ColorHelper.thePunchGray(),
    );

    final isWithin250Feet = distance != null && distance! <= 250;
    final cardBackgroundColor = isWithin250Feet
        ? const Color.fromARGB(255, 207, 250, 161)
        : theme.colorScheme.primary;

    final distanceText = distance == null ? '' : distance!.metersToMiles;
    final Widget distanceDisplay = isWithin250Feet
        ? const Padding(
            padding: EdgeInsets.fromLTRB(0, 0, 5, 0),
            child: Column(children: [
              Text('Arrived', style: TextStyle(color: Colors.green))
            ]),
          )
        : Padding(
            padding: const EdgeInsets.fromLTRB(0, 0, 5, 0),
            child: Column(children: [
              Text(distanceText,
                  style: TextStyle(color: ColorHelper.thePunchRed()))
            ]),
          );

    return GestureDetector(
      onTap: () {
        if (isWithin250Feet) {
          selected();
        } else {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text('Too Far Away!',
                  style: TextStyle(color: ColorHelper.thePunchRed())),
              content: const Text("Please move closer to the location."),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text("OK"),
                ),
              ],
            ),
          );
        }
      },
      child: PaddedCard(
        color: cardBackgroundColor,
        child: IntrinsicHeight(
          child: Stack(
            children: [
              const Align(
                alignment: Alignment.centerLeft,
                child: Icon(Icons.pin_drop),
              ),
              Align(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 18),
                  child: Column(
                    children: [
                      Text(location.name, style: secondaryBodyText1),
                      if (location.address1.isNotEmpty)
                        Text(location.address1,
                            style: theme.textTheme.bodyMedium),
                      if (location.address2.isNotEmpty)
                        Text(location.address2,
                            style: theme.textTheme.bodyMedium),
                      if (location.address3.isNotEmpty)
                        Text(location.address3,
                            style: theme.textTheme.bodyMedium),
                    ],
                  ),
                ),
              ),
              Align(
                alignment: Alignment.bottomRight,
                child: distanceDisplay,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _PunchedInBody extends StatefulWidget {
  const _PunchedInBody();

  @override
  _PunchedInBodyState createState() => _PunchedInBodyState();
}

class _PunchedInBodyState extends State<_PunchedInBody> {
  Future<bool>? _isEmployeeFuture;
  bool isPunchingOut = false;

  @override
  void initState() {
    super.initState();
    _isEmployeeFuture = _checkIfEmployee();
  }

  Future<bool> _checkIfEmployee() async {
    final userId = LoginState.userId;
    final userTypeId = await UserModel().getCurrentUserTypeId(userId);
    return UserType.employeeId.toUpperCase() == userTypeId?.toUpperCase();
  }

  @override
  Widget build(BuildContext context) {
    final punchCard = context.watch<PunchState>().punchCard;

    return FutureBuilder<bool>(
      future: _isEmployeeFuture,
      builder: (context, snapshot) {
        final isEmployee = snapshot.data ?? false;

        return SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(height: ScreenHelper.screenHeightPercentage(context, 1)),

              // Row for Start/End Task, Location Notes, and View Punch Card
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // If not employee, show Start/End Task
                    if (!isEmployee)
                      Expanded(
                        child: Consumer<PunchState>(
                          builder: (context, punchState, child) {
                            if (punchState.isTaskActive) {
                              return _EndTaskButton(punchState: punchState);
                            } else {
                              return const _StartTaskButton();
                            }
                          },
                        ),
                      )
                    else
                      const Expanded(child: SizedBox()),

                    // Location Notes Button
                    if (punchCard?.locationId != null)
                      Expanded(
                        child: _LocationNotesButton(
                          locationId: punchCard!.locationId!,
                        ),
                      )
                    else
                      const Expanded(child: SizedBox()),

                    // If not employee, show Punch Card button
                    if (!isEmployee)
                      const Expanded(
                        child: ViewPunchCardButton(),
                      )
                    else
                      const Expanded(child: SizedBox()),
                  ],
                ),
              ),

              SizedBox(height: ScreenHelper.screenHeightPercentage(context, 2)),

              // Punch Card Circle
              Container(
                padding: EdgeInsets.all(
                  ScreenHelper.screenHeightPercentage(context, 6.8),
                ),
                decoration: BoxDecoration(
                  border: Border.all(
                    width: 5,
                    color: ColorHelper.thePunchBlue(),
                  ),
                  shape: BoxShape.circle,
                  color: ColorHelper.thePunchLightBlue(),
                ),
                child: Column(
                  children: [
                    if (punchCard?.jobTypeId != null)
                      _JobType(jobTypeId: punchCard!.jobTypeId),

                    SizedBox(height: ScreenHelper.screenHeightPercentage(context, 1.4)),
                    _Duration(),
                    SizedBox(height: ScreenHelper.screenHeightPercentage(context, 1.4)),

                    if (punchCard?.locationId != null)
                      _Location(locationId: punchCard!.locationId!),
                    if (punchCard?.scheduleId != null)
                      _Schedule(scheduleId: punchCard!.scheduleId!),

                    SizedBox(height: ScreenHelper.screenHeightPercentage(context, 1.4)),
                  ],
                ),
              ),

              SizedBox(height: ScreenHelper.screenHeightPercentage(context, 2)),

              // Punch Out Button
              Center(
                child: ElevatedButton(
                  onPressed: () => {},
                  style: ElevatedButton.styleFrom(
                    shape: const CircleBorder(
                      side: BorderSide(color: Colors.red),
                    ),
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    elevation: 0,
                    padding: const EdgeInsets.all(10),
                  ),
                  child: ElevatedButton(
                    onPressed: () async {
                      if (isPunchingOut) return;

                      if (!mounted) return;
                      setState(() {
                        isPunchingOut = true;
                      });

                      final sessionOk = await checkSession(context);
                      if (!mounted) return;
                      if (!sessionOk) {
                        setState(() {
                          isPunchingOut = false;
                        });
                        return;
                      }

                      var connectivityResult =
                          await Connectivity().checkConnectivity();
                      if (!mounted) return;
                      if (connectivityResult == ConnectivityResult.none) {
                        if (mounted) {
                          setState(() {
                            isPunchingOut = false;
                          });
                        }
                        return;
                      }

                      bool punchOutConfirmed = false;
                      await _showPunchOutDialog(context, (value) {
                        punchOutConfirmed = value;
                      });
                      if (!mounted) return;

                      if (punchOutConfirmed) {
                        ScaffoldMessenger.of(context).hideCurrentSnackBar();
                        await Provider.of<PunchViewModel>(
                          context,
                          listen: false,
                        ).handlePunchOut();
                      }

                      if (mounted) {
                        setState(() {
                          isPunchingOut = false;
                        });
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      shape: const CircleBorder(),
                      padding: const EdgeInsets.all(24),
                    ),
                    child: Text(
                      '${AppLocalizations.of(context)!.punch}\n${AppLocalizations.of(context)!.outW}',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.titleSmall,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _showPunchOutDialog(
    BuildContext context,
    ValueChanged<bool> onConfirm,
  ) async =>
      showDialog(
        context: context,
        builder: (BuildContext context) => AlertDialog(
          title: Text(
            '${AppLocalizations.of(context)!.punchStatus}:',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: ColorHelper.thePunchRed(),
                ),
          ),
          content: Text(
            AppLocalizations.of(context)!.confirmPunchOut,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontSize: ScreenHelper.screenHeightPercentage(context, 2),
                ),
          ),
          actions: [
            OutlinedButton.icon(
              style: OutlinedButton.styleFrom(
                side: BorderSide(
                  color: ColorHelper.thePunchRed(),
                ),
              ),
              icon: const Icon(Icons.cancel),
              label: const Text('No'),
              onPressed: () {
                onConfirm(false);
                Navigator.pop(context);
              },
            ),
            ElevatedButton.icon(
              icon: const Icon(
                CommunityMaterialIcons.clock_out,
                color: Colors.white,
              ),
              label: Text(
                AppLocalizations.of(context)!.clockOut,
                style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w700),
              ),
              onPressed: () {
                onConfirm(true);
                Navigator.pop(context);
              },
            ),
          ],
        ),
      );
}

class _LocationNotesButton extends StatelessWidget {
  final String locationId;

  const _LocationNotesButton({required this.locationId});

  @override
  Widget build(BuildContext context) => GestureDetector(
        onTap: () {
         // navigate to location page

         _showLocationNotesDialog(context, locationId);
        },
        child: Column(
          children: [
            Icon(
              Icons.announcement, // Notes icon
              size: 40,
              color: ColorHelper.thePunchGray(),
            ),
            const SizedBox(height: 4),
            Text(
              'Location Notes',
              style: TextStyle(
                color: ColorHelper.thePunchGray(),
                fontSize: 12,
              ),
            ),
          ],
        ),
      );

  Future<void> _showLocationNotesDialog(
      BuildContext context, String locationId) async {
    final notes = await LocationNoteModel().getByLocationIds([locationId]);

    await showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        content: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(50, 0, 50, 0),
            child: Column(
              children: [
                Text(
                  'Location Notes:',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 10),
                ...notes.asMap().entries.map((entry) {
                  final index = entry.key + 1;
                  final note = entry.value;
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '$index. ',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                note.note,
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                              const Divider(
                                  color: Colors.grey, thickness: 0.5),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

class _EndTaskDialog extends StatefulWidget {
  final PunchState punchState;

  const _EndTaskDialog({required this.punchState});

  @override
  _EndTaskDialogState createState() => _EndTaskDialogState();
}

class _EndTaskDialogState extends State<_EndTaskDialog> {
  bool isEndingTask = false;

  Future<void> _confirmEndTask() async {
    final sessionOk = await checkSession(context);
    if (!sessionOk) {
      Navigator.of(context, rootNavigator: true).pop();
      return;
    }

    setState(() {
      isEndingTask = true;
    });

    await widget.punchState.managerTaskPunchOut();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text("Task ended! Returning to Manager Tracking."),
        backgroundColor: ColorHelper.thePunchBlue(),
      ),
    );

    if (mounted) {
      Navigator.of(context, rootNavigator: true).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isEndingTask) {
      return AlertDialog(
        content: SizedBox(
          height: 150,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Ending Task...',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: ColorHelper.thePunchGray(),
                ),
              ),
              const SizedBox(height: 20),
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  ColorHelper.thePunchBlue(),
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      return AlertDialog(
        title: Text(
          'End Task',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: ColorHelper.thePunchRed(),
              ),
        ),
        content: Text(
          'Would you like to end the current task?',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: ColorHelper.thePunchLightGray(),
              ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: _confirmEndTask,
            child: const Text('Yes'),
          ),
        ],
      );
    }
  }
}

class _StartTaskButton extends StatefulWidget {
  const _StartTaskButton({Key? key}) : super(key: key);

  @override
  State<_StartTaskButton> createState() => _StartTaskButtonState();
}

class _StartTaskButtonState extends State<_StartTaskButton> {
  bool isStartingTask = false;

  @override
  Widget build(BuildContext context) => Consumer<PunchViewModel>(
        builder: (context, viewModel, child) => Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: GestureDetector(
                onTap: () async {
                  if (isStartingTask) return;
                  setState(() {
                    isStartingTask = true;
                  });

                  final sessionOk = await checkSession(context);
                  if (!sessionOk) {
                    setState(() {
                      isStartingTask = false;
                    });
                    return;
                  }

                 // _showStartTaskDialog(context, viewModel);
                 context.go('start-task');
                },
                child: Column(
                  children: [
                    Icon(
                      Icons.add_task,
                      size: 40,
                      color: ColorHelper.thePunchBlue(),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Start Task',
                      style: TextStyle(
                        color: ColorHelper.thePunchBlue(),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );

  void _showStartTaskDialog(BuildContext context, PunchViewModel viewModel) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) => _StartTaskDialog(punchViewModel: viewModel),
    ).then((_) {
      if (mounted) {
        setState(() {
          isStartingTask = false;
        });
      }
    });
  }
}

class _EndTaskButton extends StatefulWidget {
  final PunchState punchState;

  const _EndTaskButton({required this.punchState, Key? key}) : super(key: key);

  @override
  State<_EndTaskButton> createState() => _EndTaskButtonState();
}

class _EndTaskButtonState extends State<_EndTaskButton> {
  bool isEndingTaskButtonTap = false;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topLeft,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: GestureDetector(
          onTap: () async {
            if (isEndingTaskButtonTap) return;

            setState(() {
              isEndingTaskButtonTap = true;
            });

            final sessionOk = await checkSession(context);
            if (!sessionOk) {
              setState(() {
                isEndingTaskButtonTap = false;
              });
              return;
            }

            final punchViewModel =
                Provider.of<PunchViewModel>(context, listen: false);
            punchViewModel.clearLocationNotes();

            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => _EndTaskDialog(punchState: widget.punchState),
            ).then((_) {
              if (mounted) {
                setState(() {
                  isEndingTaskButtonTap = false;
                });
              }
            });
          },
          child: Column(
            children: [
              Icon(
                Icons.logout,
                size: 40,
                color: ColorHelper.thePunchRed(),
              ),
              const SizedBox(height: 4),
              Text(
                'End Task',
                style: TextStyle(
                  color: ColorHelper.thePunchRed(),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Future<void> _showViewPunchCardDialog(BuildContext context) async {
  showDialog(
    context: context,
    builder: (BuildContext context) => const ViewPunchCardDialog(),
  );
}

class ViewPunchCardDialog extends StatefulWidget {
  const ViewPunchCardDialog();

  @override
  _ViewPunchCardDialogState createState() => _ViewPunchCardDialogState();
}

class _ViewPunchCardDialogState extends State<ViewPunchCardDialog> {
  Timer? _timer;
  Duration _totalDuration = Duration.zero;
  Duration _taskDuration = Duration.zero;

  PunchCard? _firstPunchCard;
  List<PunchCard> _relatedPunchCards = [];

  @override
  void initState() {
    super.initState();
    _initializeDurations();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _initializeDurations() async {
    _firstPunchCard = await _getFirstPunchCard();
    _relatedPunchCards = await _fetchRelatedPunchCards();

    _totalDuration = _calculateTotalDuration();
    if (_firstPunchCard != null) {
      _taskDuration = DateTime.now().difference(_firstPunchCard!.clockedIn);
    }

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_firstPunchCard != null) {
          _taskDuration = DateTime.now().difference(_firstPunchCard!.clockedIn);
        }
        _totalDuration = _calculateTotalDuration();
      });
    });
  }

  Duration _calculateTotalDuration() {
    Duration total = Duration.zero;
    for (var card in _relatedPunchCards) {
      if (card.clockedIn != null && card.clockedOut != null) {
        total += card.clockedOut!.difference(card.clockedIn!);
      } else if (card.clockedIn != null && card.clockedOut == null) {
        total += DateTime.now().difference(card.clockedIn!);
      }
    }
    return total;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AlertDialog(
      title: Center(
        child: Text(
          'TOTAL TIME:',
          style: theme.textTheme.titleMedium?.copyWith(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: ColorHelper.thePunchLightGray(),
          ),
        ),
      ),
      content: SizedBox(
        width: double.infinity,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(50),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: ColorHelper.thePunchLightGray(),
                border: Border.all(
                  color: ColorHelper.thePunchGray(),
                  width: 5,
                ),
              ),
              child: _firstPunchCard != null
                  ? _buildTotalTimeDisplay()
                  : Text(
                      '...',
                      style: theme.textTheme.headlineMedium?.copyWith(
                        color: ColorHelper.thePunchGray(),
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
            ),
            const SizedBox(height: 16),
            _buildPunchCardsList(),
            const SizedBox(height: 20),
            if (_firstPunchCard != null)
              _TickingDuration(
                clockedInTime: _firstPunchCard!.clockedIn,
                theme: theme,
              ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: const Text('Exit'),
          style: TextButton.styleFrom(
            foregroundColor: Colors.blue,
            side: const BorderSide(color: Colors.blue),
          ),
        ),
      ],
    );
  }

  Widget _buildTotalTimeDisplay() {
    final hours = _totalDuration.inHours.toString().padLeft(2, '0');
    final minutes = (_totalDuration.inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (_totalDuration.inSeconds % 60).toString().padLeft(2, '0');

    return Text(
      '$hours:$minutes:$seconds',
      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            color: ColorHelper.thePunchGray(),
            fontWeight: FontWeight.bold,
          ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildPunchCardsList() {
    if (_relatedPunchCards.isEmpty) {
      return const Text(
        'No related punch cards found.',
        style: TextStyle(fontSize: 14, color: Colors.red),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              flex: 2,
              child: Text(
                'Task:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: ColorHelper.thePunchGray(),
                    ),
              ),
            ),
            Expanded(
              flex: 1,
              child: Text(
                'Duration:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: ColorHelper.thePunchGray(),
                    ),
              ),
            ),
          ],
        ),
        const Divider(),
        for (int i = 0; i < _relatedPunchCards.length; i++)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    DataModel().jobTypeModel.getJobTypeName(
                        _relatedPunchCards[i].jobTypeId.toUpperCase()),
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    i == 0
                        ? DurationExtensions(_taskDuration).toFormattedWithSeconds
                        : (_relatedPunchCards[i].clockedOut != null
                            ? DurationExtensions(_relatedPunchCards[i]
                                    .clockedOut!
                                    .difference(
                                        _relatedPunchCards[i].clockedIn!))
                                .toFormattedWithSeconds
                            : '...'),
                    style: i == 0
                        ? TextStyle(
                            fontSize: 12.0,
                            fontWeight: FontWeight.bold,
                            color: ColorHelper.thePunchLightGreen(),
                          )
                        : Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Future<PunchCard?> _getFirstPunchCard() async {
    final prefs = await SharedPreferences.getInstance();
    final linkId = prefs.getString('LinkId') ?? '';

    final punchCardModel = PunchCardModel();
    final allPunchCards = (await punchCardModel.getAllByLinkId(linkId)).toList();

    if (allPunchCards.isNotEmpty) {
      allPunchCards.sort((a, b) => b.clockedIn.compareTo(a.clockedIn));
      return allPunchCards.first;
    }
    return null;
  }

  Future<List<PunchCard>> _fetchRelatedPunchCards() async {
    final prefs = await SharedPreferences.getInstance();
    final linkId = prefs.getString('LinkId') ?? '';

    final punchCardModel = PunchCardModel();
    final allPunchCards = (await punchCardModel.getAllByLinkId(linkId)).toList();

    allPunchCards.sort((a, b) => b.clockedIn.compareTo(a.clockedIn));
    return allPunchCards;
  }
}

class ViewPunchCardButton extends StatelessWidget {
  const ViewPunchCardButton();

  @override
  Widget build(BuildContext context) => Align(
        alignment: Alignment.topRight,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: GestureDetector(
            onTap: () {
              _showViewPunchCardDialog(context);
            },
            child: Column(
              children: [
                Icon(
                  CommunityMaterialIcons.history,
                  size: 40,
                  color: ColorHelper.thePunchLightGray(),
                ),
                const SizedBox(height: 4),
                Text(
                  'Punch Card',
                  style: TextStyle(
                    color: ColorHelper.thePunchLightGray(),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
}

Future<PunchCard?> _getFirstPunchCard() async {
  final prefs = await SharedPreferences.getInstance();
  final linkId = prefs.getString('LinkId') ?? '';

  final punchCardModel = PunchCardModel();
  final allPunchCards = (await punchCardModel.getAllByLinkId(linkId)).toList();

  if (allPunchCards.isNotEmpty) {
    allPunchCards.sort((a, b) => b.clockedIn.compareTo(a.clockedIn));
    return allPunchCards.first;
  }
  return null;
}

class _TickingDuration extends StatefulWidget {
  final DateTime clockedInTime;
  final ThemeData theme;

  const _TickingDuration({
    required this.clockedInTime,
    required this.theme,
  });

  @override
  _TickingDurationState createState() => _TickingDurationState();
}

class _TickingDurationState extends State<_TickingDuration> {
  late Duration _elapsed;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _elapsed = DateTime.now().difference(widget.clockedInTime);
    _startTimer();
  }

  @override
  void didUpdateWidget(covariant _TickingDuration oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.clockedInTime != widget.clockedInTime) {
      _resetTimer();
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _elapsed = DateTime.now().difference(widget.clockedInTime);
        });
      }
    });
  }

  void _resetTimer() {
    _timer?.cancel();
    setState(() {
      _elapsed = Duration.zero;
    });
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final hours = _elapsed.inHours.toString().padLeft(2, '0');
    final minutes = (_elapsed.inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (_elapsed.inSeconds % 60).toString().padLeft(2, '0');

    return Text('');
  }
}

class _StartTaskDialog extends StatefulWidget {
  /// If the manager has already chosen a location (e.g. from a list),
  /// pass it here. If `null`, the dialog will detect your closest location.
  final Location? forcedLocation;

  final PunchViewModel punchViewModel;

  const _StartTaskDialog({
    required this.punchViewModel,
    this.forcedLocation,
    Key? key,
  }) : super(key: key);

  @override
  _StartTaskDialogState createState() => _StartTaskDialogState();
}

class _StartTaskDialogState extends State<_StartTaskDialog> {
  bool isLoading = true;
  bool showTasks = false;
  bool isStartingTask = false;

  Location? _closestLocation;
  List<Map<String, dynamic>> _cleaningSchedules = [];

  final List<String> _otherTasks = [
    'Special Event',
    'Inspection',
    'Administrative',
    'Training',
    'Project',
    'Customer Visit',
    'Employee Visit',
    // ...
  ];

  @override
  void initState() {
    super.initState();

    // If forcedLocation != null, we skip detection altogether.
    if (widget.forcedLocation != null) {
      _closestLocation = widget.forcedLocation;
      _loadCleaningSchedules(widget.forcedLocation!.id).then((_) {
        setState(() => isLoading = false);
      });
    } else {
      // Detect location as before
      _detectClosestLocation();
    }
  }

  Future<void> _detectClosestLocation() async {
    setState(() => isLoading = true);

    await LocationState.updateCurrentLocation();
    final currentPosition = LocationState().currentPosition;

    if (currentPosition != null) {
      final locations = await DataModel().locationModel.active;
      if (locations.isNotEmpty) {
        final distances = <Location, double>{};
        for (var location in locations) {
          final distance = calculateDistance(
            currentPosition.latitude,
            currentPosition.longitude,
            location.latitude,
            location.longitude,
          );
          distances[location] = distance;
        }

        final withinOneMile = distances.entries
            .where((entry) => entry.value <= 1609.34) // 1 mile ~ 1609.34m
            .toList();

        if (withinOneMile.isNotEmpty) {
          withinOneMile.sort((a, b) => a.value.compareTo(b.value));
          _closestLocation = withinOneMile.first.key;

          await _loadCleaningSchedules(_closestLocation!.id);
        }
      }
    }

    setState(() => isLoading = false);
  }

  Future<void> _loadCleaningSchedules(String locationId) async {
    final scheduleModel = DataModel().scheduleModel;
    final userModel = UserModel();
    final punchCardModel = PunchCardModel();
    final now = DateTime.now();

    final allSchedules = await scheduleModel.getByLocationId(locationId);
    final todaySchedules = allSchedules.where((schedule) {
      return schedule.startDateLocal.year == now.year &&
          schedule.startDateLocal.month == now.month &&
          schedule.startDateLocal.day == now.day;
    }).toList();

    todaySchedules.sort((a, b) => a.startDateLocal.compareTo(b.startDateLocal));

    final scheduleInfoList = <Map<String, dynamic>>[];
    for (var s in todaySchedules) {
      final user = await userModel.getById(s.userId);
      scheduleInfoList.add({'schedule': s, 'user': user});
    }

    // Filter out any schedules that have already been used in a punch card
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 1);
    final monthlyPunchCards =
        await punchCardModel.getBetween(startOfMonth, endOfMonth);
    final monthlyScheduleIds = monthlyPunchCards
        .where((p) => p.scheduleId != null)
        .map((p) => p.scheduleId!)
        .toSet();

    scheduleInfoList.removeWhere((info) {
      final s = info['schedule'] as Schedule;
      return monthlyScheduleIds.contains(s.id);
    });

    _cleaningSchedules = scheduleInfoList;
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return AlertDialog(
        content: SizedBox(
          height: 150,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                widget.forcedLocation == null
                    ? 'Detecting Current Location...'
                    : 'Loading...',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: ColorHelper.thePunchGray(),
                ),
              ),
              const SizedBox(height: 20),
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  ColorHelper.thePunchBlue(),
                ),
              ),
            ],
          ),
        ),
      );
    }

    // If we are actively starting the task
    if (isStartingTask) {
      return AlertDialog(
        content: SizedBox(
          height: 150,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Starting Task...',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: ColorHelper.thePunchGray(),
                ),
              ),
              const SizedBox(height: 20),
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  ColorHelper.thePunchBlue(),
                ),
              ),
            ],
          ),
        ),
      );
    }

    // If user clicked "Yes" to create a new task or we forcibly skip to tasks
    if (showTasks || widget.forcedLocation != null) {
      return _buildTaskSelectionDialog(context);
    }

    // If forcedLocation is null but we *didn't* find anything
    if (_closestLocation == null) {
      return AlertDialog(
        title: const Text('No Nearby Locations'),
        content: const Text(
          'No locations found within 1 mile of your current location.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      );
    }

    // We found a location but the user must confirm to see tasks
    return AlertDialog(
      content: Text(
        'Would you like to start a Task at: ${_closestLocation!.name}?',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 18),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('No'),
          style: TextButton.styleFrom(
            foregroundColor: Colors.blue,
            side: BorderSide(color: ColorHelper.thePunchGray()),
          ),
        ),
        TextButton(
          onPressed: () => setState(() => showTasks = true),
          child: const Text('Yes'),
          style: TextButton.styleFrom(
            foregroundColor: Colors.blue,
            side: BorderSide(color: ColorHelper.thePunchGray()),
          ),
        ),
      ],
    );
  }

  /// Renders the final list of tasks (scheduled vs. unscheduled).
  Widget _buildTaskSelectionDialog(BuildContext context) {
    return AlertDialog(
      title: Center(
        child: RichText(
          text: TextSpan(
            text: 'Start a Task at -> ',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontSize: 16,
                ),
            children: [
              TextSpan(
                text: _closestLocation?.name ?? 'Unknown Location',
                style: TextStyle(
                  color: ColorHelper.thePunchGray(),
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Cleaning Task
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(0, 8, 8, 0),
                child: Text(
                  'Cleaning Task:',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontSize: 16,
                        color: ColorHelper.thePunchGray(),
                      ),
                ),
              ),
            ),
            _buildTaskPill(
              label: 'Unscheduled',
              color: Colors.grey[300]!,
              icon: Icons.pin_drop,
              onTap: () => _onTaskSelected('Unscheduled'),
            ),
            if (_cleaningSchedules.isNotEmpty)
              ..._cleaningSchedules.map((info) {
                final schedule = info['schedule'] as Schedule;
                final user = info['user'] as User;

                final startTimeStr = TimeOfDay.fromDateTime(schedule.startDateLocal)
                    .format(context);
                final endTimeStr = TimeOfDay.fromDateTime(schedule.endDateLocal)
                    .format(context);

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
                      child: Text(
                        '${user.firstName} ${user.lastName}',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              color: ColorHelper.thePunchGray(),
                              fontSize: 11,
                            ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Text(
                        'Start: $startTimeStr | End: $endTimeStr',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              color: Colors.grey,
                              fontSize: 10,
                            ),
                      ),
                    ),
                    _buildTaskPill(
                      label: 'Scheduled',
                      color: const Color.fromARGB(255, 207, 250, 161),
                      icon: Icons.pin_drop,
                      onTap: () => _onTaskSelected('Scheduled', schedule.id),
                    ),
                  ],
                );
              }).toList()
            else
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 8.0),
                child: Text(
                  'No Schedules Found Today.',
                  style: TextStyle(fontSize: 12),
                ),
              ),
            // Other Tasks
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(0, 20, 8, 0),
                child: Text(
                  'Other Task:',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontSize: 16,
                        color: ColorHelper.thePunchGray(),
                      ),
                ),
              ),
            ),
            for (var task in _otherTasks)
              _buildTaskPill(
                label: task,
                color: Theme.of(context).colorScheme.primary,
                icon: null,
                onTap: () => _onTaskSelected(task),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTaskPill({
    required String label,
    required Color color,
    IconData? icon,
    required VoidCallback onTap,
  }) =>
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 4.0),
        child: GestureDetector(
          onTap: onTap,
          child: PaddedCard(
            color: color,
            child: IntrinsicHeight(
              child: Row(
                children: [
                  if (icon != null) ...[
                    Icon(icon, color: ColorHelper.thePunchGray()),
                    const SizedBox(width: 6),
                  ],
                  Expanded(
                    child: Text(
                      label,
                      textAlign: TextAlign.center,
                      style:
                          Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: ColorHelper.thePunchGray(),
                              ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

  /// Called when user taps a task pill
  Future<void> _onTaskSelected(String task, [String? scheduleId]) async {
    if (isStartingTask) return;
    setState(() => isStartingTask = true);

    // Check session
    final sessionOk = await checkSession(context);
    if (!sessionOk) {
      setState(() => isStartingTask = false);
      return;
    }

    // Map label -> jobTypeId
    final taskNameToId = <String, String>{
      'Special Event': JobType.specialEventId,
      'Inspection': JobType.inspectionId,
      'Administrative': JobType.administrativeId,
      'Training': JobType.trainingId,
      'Project': JobType.projectId,
      'Customer Visit': JobType.customerVisitId,
      'Employee Visit': JobType.employeeVisitId,
      'Unscheduled': JobType.unscheduledId,
      'Scheduled': JobType.scheduledId,
      // ...
    };

    final jobTypeId = taskNameToId[task] ?? JobType.unscheduledId;

    await PunchState().managerTaskPunchIn(
      locationId: _closestLocation?.id,
      jobTypeId: jobTypeId,
      scheduleId: scheduleId,
    );

    if (mounted) {
      Navigator.of(context).pop(); // Dismiss the dialog
    }
  }
}


class _Duration extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Center(
        child: StreamBuilder<String>(
          stream: PunchState().durationStream,
          builder: (context, snapshot) => Text(
            snapshot.data ?? '',
            textAlign: TextAlign.center,
            style: TextStyleHelper.bigBoldBlueTitle(context),
          ),
        ),
      );
}

class _Location extends StatelessWidget {
  final String locationId;

  const _Location({required this.locationId});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final body1OnSecondary = theme.textTheme.bodyMedium?.copyWith(
        color: ColorHelper.thePunchBlue(), fontWeight: FontWeight.w800);
    final body1OnSurface = theme.textTheme.bodyMedium?.copyWith(
      color: ColorHelper.thePunchBlue(),
      fontSize: ScreenHelper.screenHeightPercentage(context, 1.2),
    );

    return Consumer<PunchViewModel>(builder: (context, viewModel, child) {
      if (viewModel.location == null) return Container();
      final location = viewModel.location!;
      return Column(
        children: [
          Text(location.name, style: body1OnSecondary, textAlign: TextAlign.center),
          if (location.address1.isNotEmpty)
            Text(location.address1, style: body1OnSurface, textAlign: TextAlign.center),
          if (location.address2.isNotEmpty)
            Text(location.address2, style: body1OnSurface, textAlign: TextAlign.center),
          if (location.address3.isNotEmpty)
            Text(location.address3, style: body1OnSurface, textAlign: TextAlign.center),
        ],
      );
    });
  }
}

class _Schedule extends StatelessWidget {
  final String scheduleId;

  const _Schedule({required this.scheduleId});

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);
    final theme = Theme.of(context);
    final body1OnSecondary = theme.textTheme.bodyMedium?.copyWith(
        color: ColorHelper.thePunchBlue(), fontWeight: FontWeight.w800);
    final body1OnSurface = theme.textTheme.bodyMedium?.copyWith(
      color: ColorHelper.thePunchBlue(),
      fontSize: ScreenHelper.screenHeightPercentage(context, 1.2),
    );

    return Consumer<PunchViewModel>(
      builder: (context, viewModel, child) {
        if (viewModel.schedule == null || viewModel.location == null) {
          return Container();
        }
        final schedule = viewModel.schedule!;
        final location = viewModel.location!;
        final start = schedule.startDateUtc.toLocal().toFormattedTime(locale);
        final end = schedule.endDateUtc.toLocal().toFormattedTime(locale);
        return Column(
          children: [
            Text('${AppLocalizations.of(context)!.scheduled} $start - $end',
                style: body1OnSecondary),
          ],
        );
      },
    );
  }
}

class _JobType extends StatelessWidget {
  final String jobTypeId;

  const _JobType({required this.jobTypeId});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final body1OnSecondary =
        textTheme.headlineSmall?.copyWith(color: ColorHelper.thePunchBlue());

    return Consumer<PunchState>(
      builder: (context, punchState, child) => Container(
        constraints: const BoxConstraints(maxWidth: 140),
        decoration: BoxDecoration(
          border: Border.all(color: ColorHelper.thePunchBlue(), width: 1.5),
          borderRadius: const BorderRadius.all(
            Radius.circular(24),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(5),
          child: Center(
            child: Text(
              (punchState.currentTask?.toUpperCase() ?? 
               punchState.jobTypeName?.toUpperCase() ??
               'Unknown'),
              style: body1OnSecondary,
            ),
          ),
        ),
      ),
    );
  }
}

extension DurationExtensions on Duration {
  String get toFormatted {
    final hours = inHours.toString().padLeft(2, '0');
    final minutes = (inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (inSeconds % 60).toString().padLeft(2, '0');
    return '$hours:$minutes:$seconds';
  }

  String get toFormattedWithSeconds {
    final hours = inHours.toString().padLeft(2, '0');
    final minutes = (inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (inSeconds % 60).toString().padLeft(2, '0');
    return '$hours:$minutes:$seconds';
  }
}

/// Naive helper to compare localVersion (like "1.2.0") with serverMinVersion (like "1.2.0+").
bool _isAppVersionCompatible(String localVersion, String minRequiredVersion) {
  final cleanedMin = minRequiredVersion.replaceAll('+', '');
  final cleanedLocal = localVersion.replaceAll('+', '');

  final minParts = cleanedMin.split('.');
  final localParts = cleanedLocal.split('.');

  // If parse fails, treat as not compatible
  if (minParts.length < 2 || localParts.length < 2) {
    return false;
  }

  final minMajor = int.tryParse(minParts[0]) ?? 0;
  final minMinor = int.tryParse(minParts[1]) ?? 0;
  final minPatch = int.tryParse(minParts.length > 2 ? minParts[2] : '0') ?? 0;

  final localMajor = int.tryParse(localParts[0]) ?? 0;
  final localMinor = int.tryParse(localParts[1]) ?? 0;
  final localPatch = int.tryParse(localParts.length > 2 ? localParts[2] : '0') ?? 0;

  // Compare major
  if (localMajor > minMajor) return true;
  if (localMajor < minMajor) return false;

  // If major is equal, compare minor
  if (localMinor > minMinor) return true;
  if (localMinor < minMinor) return false;

  // If major & minor are equal, compare patch
  return localPatch >= minPatch;
}
