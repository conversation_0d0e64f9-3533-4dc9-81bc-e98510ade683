import 'package:flutter/material.dart';

class ListingComponent extends StatelessWidget {
  final List<ListingItem> items = [
    ListingItem(
      name: "<PERSON>",
      location: "Tampa, FL",
      icon: "https://dashboard.codeparrot.ai/api/image/Z_ZqcoVxqHvUClGi/where-to.png",
      status: const Color.fromRGBO(41, 52, 61, 0.04),
    ),
    ListingItem(
      name: "Ken Morris",
      location: "Tampa, FL",
      icon: "https://dashboard.codeparrot.ai/api/image/Z_ZqcoVxqHvUClGi/where-to-2.png",
      status: const Color.fromRGBO(253, 123, 23, 1),
      additionalStatus: const Color.fromRGBO(254, 216, 24, 1),
    ),
    ListingItem(
      name: "Ken Morris",
      location: "Tampa, FL",
      icon: "https://dashboard.codeparrot.ai/api/image/Z_ZqcoVxqHvUClGi/where-to-3.png",
      status: const Color.fromRGBO(41, 52, 61, 0.04),
      isHighlighted: true,
    ),
    ListingItem(
      name: "<PERSON>",
      location: "Tampa, FL",
      icon: "https://dashboard.codeparrot.ai/api/image/Z_ZqcoVxqHvUClGi/where-to-4.png",
      status: const Color.fromRGBO(254, 216, 24, 1),
    ),
    ListingItem(
      name: "Ken Morris",
      location: "Tampa, FL",
      icon: "https://dashboard.codeparrot.ai/api/image/Z_ZqcoVxqHvUClGi/where-to-5.png",
      status: const Color.fromRGBO(253, 23, 23, 1),
      additionalStatus1: const Color.fromRGBO(253, 123, 23, 1),
      additionalStatus2: const Color.fromRGBO(254, 216, 24, 1),
    ),
    ListingItem(
      name: "Ken Morris",
      location: "Tampa, FL",
      icon: "https://dashboard.codeparrot.ai/api/image/Z_ZqcoVxqHvUClGi/where-to-6.png",
      status: const Color.fromRGBO(41, 52, 61, 0.04),
    ),
    ListingItem(
      name: "Ken Morris",
      location: "Tampa, FL",
      icon: "https://dashboard.codeparrot.ai/api/image/Z_ZqcoVxqHvUClGi/where-to-7.png",
      status: const Color.fromRGBO(41, 52, 61, 0.04),
    ),
    ListingItem(
      name: "Ken Morris",
      location: "Tampa, FL",
      icon: "https://dashboard.codeparrot.ai/api/image/Z_ZqcoVxqHvUClGi/where-to-8.png",
      status: const Color.fromRGBO(179, 64, 35, 1),
    ),
    ListingItem(
      name: "Ken Morris",
      location: "Tampa, FL",
      icon: "https://dashboard.codeparrot.ai/api/image/Z_ZqcoVxqHvUClGi/where-to-9.png",
      status: const Color.fromRGBO(41, 52, 61, 0.04),
    ),
  ];

  @override
  Widget build(BuildContext context) => Container(
      decoration: BoxDecoration(
        border: Border.all(color: const Color.fromRGBO(235, 246, 255, 1)),
      ),
      padding: const EdgeInsets.fromLTRB(24, 40, 40, 0),
      child: Row(
        children: [
          Expanded(
            child: ListView.builder(
              itemCount: items.length,
              itemBuilder: (context, index) => _buildListItem(items[index]),
            ),
          ),
          Container(
            width: 6,
            height: 377,
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: NetworkImage('https://dashboard.codeparrot.ai/api/image/Z_ZqcoVxqHvUClGi/scrollba.png'),
                fit: BoxFit.cover,
              ),
            ),
          ),
        ],
      ),
    );

  Widget _buildListItem(ListingItem item) => Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      decoration: BoxDecoration(
        color: item.isHighlighted ? const Color.fromRGBO(218, 249, 225, 1) : Colors.transparent,
        borderRadius: item.isHighlighted ? BorderRadius.circular(8) : null,
        border: Border.all(color: const Color.fromRGBO(0, 0, 0, 0.08)),
      ),
      child: Row(
        children: [
          Image.network(item.icon, width: 20, height: 20),
          const SizedBox(width: 39.67),
          Text(
            item.name,
            style: const TextStyle(
              fontFamily: 'Poppins',
              fontSize: 11,
              fontWeight: FontWeight.w500,
              letterSpacing: -0.11,
              color: Color.fromRGBO(9, 31, 48, 1),
            ),
          ),
          const SizedBox(width: 83.33),
          Text(
            item.location,
            style: const TextStyle(
              fontFamily: 'Poppins',
              fontSize: 11,
              fontWeight: FontWeight.w500,
              letterSpacing: -0.11,
              color: Color.fromRGBO(9, 31, 48, 1),
            ),
          ),
          const Spacer(),
          if (item.additionalStatus2 != null)
            _buildStatusDot(item.additionalStatus2!),
          if (item.additionalStatus1 != null)
            _buildStatusDot(item.additionalStatus1!),
          if (item.additionalStatus != null)
            _buildStatusDot(item.additionalStatus!),
          _buildStatusDot(item.status),
        ],
      ),
    );

  Widget _buildStatusDot(Color color) {
    return Container(
      width: 14,
      height: 14,
      margin: const EdgeInsets.only(left: 4),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color,
      ),
    );
  }
}

class ListingItem {
  final String name;
  final String location;
  final String icon;
  final Color status;
  final Color? additionalStatus;
  final Color? additionalStatus1;
  final Color? additionalStatus2;
  final bool isHighlighted;

  ListingItem({
    required this.name,
    required this.location,
    required this.icon,
    required this.status,
    this.additionalStatus,
    this.additionalStatus1,
    this.additionalStatus2,
    this.isHighlighted = false,
  });
}
