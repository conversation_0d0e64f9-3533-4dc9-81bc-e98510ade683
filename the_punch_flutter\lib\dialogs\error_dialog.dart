import 'package:flutter/material.dart';
import '../misc/app_localization.dart';

class ErrorDialog extends StatelessWidget {
  final String? title;
  final String? message;
  final String? errorCode;

  const ErrorDialog({super.key, this.title, this.message, this.errorCode});

  @override
  Widget build(BuildContext context) {
    var text = AppLocalization.of(context).internalError;
    if (errorCode != null) text = AppLocalization.translate(context, errorCode!);
    if (message != null) text = message!;
    return AlertDialog(
      title: title != null ? Text(title!) : null,
      content: Text(text),
      actions: [
        ElevatedButton(
          onPressed: () => Navigator.pop(context),
          child: Text(AppLocalization.of(context).ok),
        ),
      ],
    );
  }
}
