import 'package:geolocator/geolocator.dart';
import 'package:path/path.dart';
import '../../api/api_model.dart';
import '../../state/location_ping_state.dart';
import '../../state/login_state.dart';
import '../../state/punch_state.dart';
import '../../state/server_time_state.dart';
import '../base_data.dart';
import '../data/travel_pings.dart';
import '../hive_db.dart';

class TravelPingModel extends BaseDataModel<TravelPing> {
  final LocationPingState _locationPingState = LocationPingState();

  // Method to synchronize travel pings from SharedPreferences to Hive database
  Future<void> synchronizePingsWithDatabase() async {
    List<Map<String, dynamic>> currentPings = await _locationPingState.getCurrentPunchTravelPings();
    List<TravelPing> pingsFromPrefs = currentPings.map((pingMap) => TravelPing.fromJson(pingMap)).toList();

    var db = await HiveDb.database;
    await db.travelPings.clear();

    for (TravelPing ping in pingsFromPrefs) {
      await db.travelPings.put(ping.id, ping);
      ping.isDirty = true;
    }
  }

  Future<List<TravelPing>> getAllPings() async {
    var db = await HiveDb.database;
    return db.travelPings.values.toList();
  }

  Future<void> savePings(Iterable<TravelPing> pings) async {
    var db = await HiveDb.database;
    for (var ping in pings) {
      await db.travelPings.put(ping.id, ping);
      ping.isDirty = true;
    }
  }

  Future<void> savePing(TravelPing ping) async {

      var db = await HiveDb.database;

      await db.travelPings.put(ping.id, ping);

      ping.isDirty = true;



      List<Map<String, dynamic>> currentPings = await _locationPingState.getCurrentPunchTravelPings();

      currentPings.add(ping.toJson());

      // print(currentPings);

      // print(ping.toJson());

      await _locationPingState.saveCurrentPunchTravelPings(currentPings);



      print('MyAppDebug:SENDING PING TO API');

      final response = await ApiModel().sendTravelPing(ping);



      if (response == null || response.errorCode != null || response.errorCode != '0') {

        print('MyAppDebug:Failed to save ping: ${response?.errorCode ?? "MyAppDebug:No Response from API"}');

      } else {

        print('MyAppDebug:PING SAVED!!');

        print(response);

        ping.isDirty = false;

      }



    }

  Future<Map<String, dynamic>?> getLastTravelPing(String userId) async {
    var allPings = await _locationPingState.getCurrentPunchTravelPings();
    var filteredPings = allPings.where((ping) => ping['userId'] == userId && ping['endTime'] != null).toList();

    if (filteredPings.isEmpty) return null;

    filteredPings.sort((a, b) => DateTime.parse(b['endTime']).compareTo(DateTime.parse(a['endTime'])));
    return filteredPings.first;
  }

Future<void> createAndSavePing(
  Position currentPosition,
  String userId,
  String? punchCardId,
  String? previousLat,
  String? previousLong,
  DateTime? lastPingEndTime,
  String polyline,
  DateTime pingTime, {
  /// Add a default for debugging if no source is provided
  String pingSource = 'UnknownPingSource',
}) async {
  if (currentPosition == null) {
    print('Error: Current position is null.');
    return;
  }

  TravelPing newTravelPing;

  if (punchCardId != null) {
    // If lastPingEndTime was null, use current server time
    lastPingEndTime ??= ServerTimeState().utcTime;

    print(
      '[createAndSavePing] Creating TravelPing with pingSource: $pingSource',
    );

    newTravelPing = TravelPing.create(
      punchCardId: punchCardId,
      userId: userId,
      startTime: lastPingEndTime,
      endTime: pingTime,
      startLat: previousLat.toString(),
      startLong: previousLong.toString(),
      endLat: currentPosition.latitude.toString(),
      endLong: currentPosition.longitude.toString(),
      polyline: polyline,

      // Ensure you have a `pingSource` field in TravelPing
      pingSource: pingSource, 
    );

    // Update location/ping info in storage
    await LocationPingState().saveUserId(userId);
    await LocationPingState().savePunchCardId(punchCardId);
    await LocationPingState().saveLastPingLocation(
      currentPosition.latitude.toString(),
      currentPosition.longitude.toString(),
    );
    await LocationPingState().saveLastPingEndTime(pingTime);

  } else {
    print("Error: Current punch card ID is null.");
    return;
  }

  // Finally, save (and/or send) the ping
  await savePing(newTravelPing);
}

  // Updated save method to take an Iterable<TravelPing>
  @override
  Future<void> save(Iterable<TravelPing> travelPings) async {
    await savePings(travelPings);
  }

  // Implementation of all getter for BaseDataModel
  @override
  Future<List<TravelPing>> get all async => await getAllPings();
}
