import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:the_punch_flutter/dataModel/models/location_notes_model.dart';

import '../../dataModel/data/location.dart';
import '../../dataModel/data/location_notes.dart';
import '../../dataModel/models/punch_view_model.dart';
import '../../state/punch_state.dart';
import '../web/my_scaffold.dart';

class LocationNotes extends StatefulWidget {
 
  final String locationId;

  const LocationNotes({super.key, required this.locationId});

  @override
  _LocationNotesState createState() => _LocationNotesState();
}

class _LocationNotesState extends State<LocationNotes> {
  List<dynamic> notes = []; // State to store the notes


  @override
  void initState() {
    super.initState();
    _getLocationNotes(context, widget.locationId);
    print('LLLLL ${widget.locationId}');
  }
  Future<void> _getLocationNotes(BuildContext context, String locationId) async {
    final fetchedNotes = await LocationNoteModel().getByLocationIds([locationId]);
    setState(() {
      notes = fetchedNotes; // Update the state with fetched notes
    });
  }
  @override
  Widget build(BuildContext context) => MultiProvider(
        providers: [
          ChangeNotifierProvider<PunchViewModel>(
            create: (context) => PunchViewModel()..initializeData(),
          ),
          ChangeNotifierProvider<PunchState>.value(
            value: PunchState(),
          ),
        ],
        child: Builder(
          builder: (context) => MyScaffold(
            showDrawer: false,
            showBackButton: true,
            showNotificationButton: false,
            title: AppLocalizations.of(context)!.clock,
            body: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _Header(location_notes: notes ),
              ],
            ),
          ),
        ),
      );
}

class _Header extends StatefulWidget {
  final List<dynamic> location_notes;
 

  const _Header({ required this.location_notes});

  @override
  State<_Header> createState() => _HeaderState();
}

class _HeaderState extends State<_Header> {
  Location? closestLocation;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final titleLargeStyle = theme.textTheme.titleLarge?.copyWith();
    return Container(
      width: double.infinity,
      child: Column(
        children: [
          SizedBox(height: 6),
  
          Padding(
            padding: EdgeInsets.all(8.0),
            child: Text(
              "Location Notes",
              style: TextStyle(fontSize: 30, color: Colors.black, fontWeight:FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          LocationNotesList(location_notes: widget.location_notes)
        ],
      ),
    );
  }
}


class LocationNotesList extends StatelessWidget {
 
  final List<dynamic> location_notes;

  const LocationNotesList({
    super.key,
    
    required this.location_notes,

  });

  @override
  Widget build(BuildContext context) {
    final allNotes = [...location_notes];

    return Column(
        children: [
          const SizedBox(height: 8),
          Text('${location_notes.length} notes', style: const TextStyle(fontSize: 16, color: Colors.grey)),
          const SizedBox(height: 16),
  SingleChildScrollView(
  child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [

      const SizedBox(height: 16),

      // 👇 You must constrain the height!
      SizedBox(
        height: 400, // or use MediaQuery to calculate dynamically
        child: GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          children:[ ...allNotes.map((note) => Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: const Color(0xFFF1F2F6),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          note.note,
                          style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
                        ),
                      )),
                  // Add Note Button --- KEEP HIDDEN UNTIL FUNCTIONALITY IS ADDED

                  // GestureDetector(
                  //   onTap: () {
                  //     // Add your navigation or logic here
                  //   },
                  //   child: Container(
                  //     decoration: BoxDecoration(
                  //       border: Border.all(color: Colors.blue),
                  //       borderRadius: BorderRadius.circular(12),
                  //     ),
                  //     child: const Center(
                  //       child: Column(
                  //         mainAxisSize: MainAxisSize.min,
                  //         children: [
                  //           Icon(Icons.add, color: Colors.blue),
                  //           SizedBox(height: 4),
                  //           Text(
                  //             "Add a Note",
                  //             style: TextStyle(
                  //               fontWeight: FontWeight.w600,
                  //               color: Colors.blue,
                  //             ),
                  //           ),
                  //         ],
                  //       ),
                  //     ),
                  //   ),
                  // ), 
                  ]
        ),
      ),
    ],
  ),
)

        ]
    )
    ; 
  }
}