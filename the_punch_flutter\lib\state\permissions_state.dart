import 'dart:async';
import 'package:flutter/foundation.dart';
import '../dataModel/data/user_type.dart';
import '../dataModel/data_model.dart';
import 'login_state.dart';

class PermissionsState extends ChangeNotifier {
  bool _isLoading = true;
  bool _isAdmin = false;

  bool _viewSchedules = false;
  bool _viewReports = false;
  bool _viewPunchCards = false;
  bool _viewNotes = false;
  bool _viewLocations = false;
  bool _viewJobTypes = false;
  bool _viewEmployees = false;
  bool _viewEmployeeTypes = false;
  bool _viewEmployeePayRates = false;
  bool _viewContacts = false;
  bool _viewContactTypes = false;
  bool _viewChecklists = false;
  bool _viewChatMessage = false;
  bool _viewAlerts = false;
  bool _editPermissions = false;
  bool _editSchedules = false;
  bool _editPunchCards = false;
  bool _editNotes = false;
  bool _editLocations = false;
  bool _editJobTypes = false;
  bool _editEmployees = false;
  bool _editEmployeeTypes = false;
  bool _editEmployeePayRates = false;
  bool _editContacts = false;
  bool _editContactTypes = false;
  bool _editChecklists = false;
  bool _punchIn = false;
  bool _sendChatMessage = false;

  bool get isLoading => _isLoading;
  bool get isAdmin => _isAdmin;
  bool get viewSchedules => _viewSchedules;
  bool get viewReports => _viewReports;
  bool get viewPunchCards => _viewPunchCards;
  bool get viewNotes => _viewNotes;
  bool get viewLocations => _viewLocations;
  bool get viewJobTypes => _viewJobTypes;
  bool get viewEmployees => _viewEmployees;
  bool get viewEmployeeTypes => _viewEmployeeTypes;
  bool get viewEmployeePayRates => _viewEmployeePayRates;
  bool get viewContacts => _viewContacts;
  bool get viewContactTypes => _viewContactTypes;
  bool get viewChecklists => _viewChecklists;
  bool get viewChatMessage => _viewChatMessage;
  bool get viewAlerts => _viewAlerts;
  bool get editPermissions => _editPermissions;
  bool get editSchedules => _editSchedules;
  bool get editPunchCards => _editPunchCards;
  bool get editNotes => _editNotes;
  bool get editLocations => _editLocations;
  bool get editJobTypes => _editJobTypes;
  bool get editEmployees => _editEmployees;
  bool get editEmployeeTypes => _editEmployeeTypes;
  bool get editEmployeePayRates => _editEmployeePayRates;
  bool get editContacts => _editContacts;
  bool get editContactTypes => _editContactTypes;
  bool get editChecklists => _editChecklists;
  bool get punchIn => _punchIn;
  bool get sendChatMessage => _sendChatMessage;

  static PermissionsState? _singleton;
  factory PermissionsState() {
    _singleton ??= PermissionsState._();
    return _singleton!;
  }

  PermissionsState._() {
    unawaited(initialize()); // Call the new public method
  }

  Future<void> initialize() async {
    DataModel().userPermissionModel.addListener(_refresh);
    (await LoginState.instance).addListener(_refresh);
    DataModel().userModel.addListener(_refresh);
    await _refresh();
  }

  Future<void> _refresh() async {
    _isLoading = true;
    notifyListeners();

    final employeeId = LoginState.userId;
    final employee = await DataModel().userModel.getById(employeeId);

    if (employee != null) {
      _isAdmin = employee.userTypeId == UserType.sysopId || employee.userTypeId == UserType.administratorId;
    }

    final keys = (await DataModel().userPermissionModel.active).map((e) => e.key);

    _viewSchedules = _isAdmin || keys.contains('CAN_VIEW_SCHEDULES');
    _viewReports = _isAdmin || keys.contains('CAN_VIEW_REPORTS');
    _viewPunchCards = _isAdmin || keys.contains('CAN_VIEW_PUNCH_CARDS');
    _viewNotes = _isAdmin || keys.contains('CAN_VIEW_NOTES');
    _viewLocations = _isAdmin || keys.contains('CAN_VIEW_LOCATIONS');
    _viewJobTypes = _isAdmin || keys.contains('CAN_VIEW_JOB_TYPES');
    _viewEmployees = _isAdmin || keys.contains('CAN_VIEW_EMPLOYEES');
    _viewEmployeeTypes = _isAdmin || keys.contains('CAN_VIEW_EMPLOYEE_TYPES');
    _viewEmployeePayRates = _isAdmin || keys.contains('CAN_VIEW_EMPLOYEE_PAY_RATES');
    _viewContacts = _isAdmin || keys.contains('CAN_VIEW_CONTACTS');
    _viewContactTypes = _isAdmin || keys.contains('CAN_VIEW_CONTACT_TYPES');
    _viewChecklists = _isAdmin || keys.contains('CAN_VIEW_CHECKLISTS');
    _viewChatMessage = _isAdmin || keys.contains('CAN_VIEW_CHAT_MESSAGE');
    _viewAlerts = _isAdmin || keys.contains('CAN_VIEW_ALERTS');
    _editPermissions = _isAdmin || keys.contains('CAN_EDIT_PERMISSIONS');
    _editSchedules = _isAdmin || keys.contains('CAN_EDIT_SCHEDULES');
    _editPunchCards = _isAdmin || keys.contains('CAN_EDIT_PUNCH_CARDS');
    _editNotes = _isAdmin || keys.contains('CAN_EDIT_NOTES');
    _editLocations = _isAdmin || keys.contains('CAN_EDIT_LOCATIONS');
    _editJobTypes = _isAdmin || keys.contains('CAN_EDIT_JOB_TYPES');
    _editEmployees = _isAdmin || keys.contains('CAN_EDIT_EMPLOYEES');
    _editEmployeeTypes = _isAdmin || keys.contains('CAN_EDIT_EMPLOYEE_TYPES');
    _editEmployeePayRates = _isAdmin || keys.contains('CAN_EDIT_EMPLOYEE_PAY_RATES');
    _editContacts = _isAdmin || keys.contains('CAN_EDIT_CONTACTS');
    _editContactTypes = _isAdmin || keys.contains('CAN_EDIT_CONTACT_TYPES');
    _editChecklists = _isAdmin || keys.contains('CAN_EDIT_CHECKLISTS');
    _punchIn = _isAdmin || keys.contains('CAN_PUNCH_IN');
    _sendChatMessage = _isAdmin || keys.contains('CAN_SEND_CHAT_MESSAGE');

    _isLoading = false;
    notifyListeners();
  }
}
