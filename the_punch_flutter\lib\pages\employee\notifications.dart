// File: lib/pages/notification_page.dart

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../api/api_model.dart'; // Adjust the path based on your project structure
import '../../api/requests/fetch_notifications_request.dart';
import '../../dataModel/data/the_punch_notifications.dart';
import '../../dataModel/models/user_model.dart';
import '../../helpers/color_helper.dart';
import '../../misc/extensions.dart';
import '../view_model_mixin.dart';
import '../web/my_scaffold.dart';
import '../../state/login_state.dart';

class NotificationPage extends StatefulWidget {
  const NotificationPage({super.key});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends State<NotificationPage> {
  late _NotificationViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    final userModel = Provider.of<UserModel>(context, listen: false);
    _viewModel = _NotificationViewModel(userModel);
  }

  @override
  void dispose() {
    _viewModel.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_NotificationViewModel>.value(
      value: _viewModel,
      child: MyScaffold(
        title: 'Notifications',
        showBackButton: true,
        body: Consumer<_NotificationViewModel>(
          builder: (context, viewModel, _) {
            if (viewModel.isLoading) {
              return const Center(child: CircularProgressIndicator(color: Colors.red));
            }

            if (viewModel.errorMessage != null) {
              return Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    viewModel.errorMessage!,
                    style: const TextStyle(color: Colors.red, fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
                ),
              );
            }

            if (viewModel.notifications.isEmpty) {
              return const Center(
                child: Text(
                  'No notifications.',
                  style: TextStyle(fontSize: 16),
                ),
              );
            }

            return RefreshIndicator(
              color:  Colors.red, // Spinner color
              backgroundColor: Colors.grey.shade200, // Background color
              onRefresh: viewModel.refresh,
              child: ListView(
                padding: const EdgeInsets.symmetric(vertical: 16.0),
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                    child: Text(
                      'Notifications:',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ),
                  ...viewModel.notifications.map((notification) {
                    final isRecent = isRecentNotification(notification.sentOn);
                    final titleColor =
                        Theme.of(context).textTheme.titleMedium?.color ?? Colors.black;

                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 16.0),
                      child: Card(
                        color: ColorHelper.thePunchDesktopLightGray(),
                        elevation: 2.0,
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (isRecent)
                                Container(
                                  width: 10,
                                  height: 10,
                                  margin: const EdgeInsets.only(right: 12.0, top: 4.0),
                                  decoration: BoxDecoration(
                                    color: ColorHelper.thePunchAccentRed(),
                                    shape: BoxShape.circle,
                                  ),
                                )
                              else
                                const SizedBox(width: 10, height: 10),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          notification.title,
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleMedium
                                              ?.copyWith(
                                                fontWeight: FontWeight.bold,
                                                color: ColorHelper.thePunchAccentRed(),
                                              ),
                                        ),
                                        Text(
                                          notification.sentOn.toFormattedDateTime(
                                            Localizations.localeOf(context),
                                          ),
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleMedium
                                              ?.copyWith(
                                                fontWeight: FontWeight.bold,
                                                color: titleColor,
                                              ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8.0),
                                    Text(
                                      notification.message,
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                            color: titleColor,
                                          ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ],
              ),
            );
          },
        ),
      ),
    );

  bool isRecentNotification(DateTime sentOn) {
    final now = DateTime.now();
    final oneHourAgo = now.subtract(const Duration(hours: 1));
    return sentOn.isAfter(oneHourAgo);
  }
}

class _NotificationViewModel extends ChangeNotifier with ViewModelMixin {
  final UserModel userModel;
  final ApiModel apiModel = ApiModel(); // Using singleton instance
  List<ThePunchNotification> notifications = [];
  bool isLoading = true;
  String? errorMessage;

  _NotificationViewModel(this.userModel) {
    addListenables([userModel]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    try {
      isLoading = true;
      errorMessage = null;
      notifyListeners();

      final userId = LoginState.userId;
      if (userId == null) {
        notifications = [];
        isLoading = false;
        notifyListeners();
        return;
      }

      // Fetch notifications using ApiModel's fetchNotifications
      final response = await apiModel.fetchNotifications(userId);

      if (response.errorCode != "0") {
        if (response.errorCode == "404") {
          // No notifications found
          notifications = [];
        } else {
          // Handle other errors
          throw ApiException(response.errorCode!);
        }
      } else {
        // Update the notifications list with the fetched data
        notifications = response.notifications;
      }

      // Sort notifications by date in descending order
      notifications.sort((a, b) => b.sentOn.compareTo(a.sentOn));
    } catch (e) {
      // Handle errors appropriately
      if (errorMessage == null) {
        // Avoid overwriting specific messages
        errorMessage = 'Failed to load notifications. Please try again later.';
      }
      debugPrint('Error fetching notifications: $e');
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }
}
