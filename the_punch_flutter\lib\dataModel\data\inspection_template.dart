import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../base_data.dart';
import '../../misc/json_conversion.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import 'package:uuid/uuid.dart';

part 'inspection_template.g.dart';

@HiveType(typeId: 9)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class InspectionTemplate extends BaseData {
  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  String name;

  @HiveField(102)
  @JsonKey(fromJson: nullableIdFromJson)
  String? locationId;

  InspectionTemplate({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.name,
    this.locationId,
  });

  factory InspectionTemplate.fromJson(Map<String, dynamic> json) => _$InspectionTemplateFromJson(json);
  Map<String, dynamic> toJson() => _$InspectionTemplateToJson(this);

  factory InspectionTemplate.create() => InspectionTemplate(
        id: const Uuid().v4(),
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
        name: '',
      );
}

@HiveType(typeId: 10)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class InspectionTemplateArea extends BaseData {
  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  String name;

  @HiveField(102)
  @JsonKey(fromJson: idFromJson)
  String inspectionTemplateId;

  @HiveField(103)
  @JsonKey(defaultValue: 0)
  int order;

  InspectionTemplateArea({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.name,
    required this.inspectionTemplateId,
    required this.order,
  });

  factory InspectionTemplateArea.fromJson(Map<String, dynamic> json) => _$InspectionTemplateAreaFromJson(json);
  Map<String, dynamic> toJson() => _$InspectionTemplateAreaToJson(this);

  factory InspectionTemplateArea.create() => InspectionTemplateArea(
        id: const Uuid().v4(),
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
        name: '',
        inspectionTemplateId: '',
        order: 0,
      );
}

@HiveType(typeId: 11)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class InspectionTemplateItem extends BaseData {
  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  String name;

  @HiveField(102)
  @JsonKey(fromJson: idFromJson)
  String inspectionTemplateId;

  @HiveField(103)
  @JsonKey(fromJson: idFromJson)
  String inspectionTemplateAreaId;

  @HiveField(104)
  @JsonKey(defaultValue: 0)
  int order;

  InspectionTemplateItem({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.name,
    required this.inspectionTemplateId,
    required this.inspectionTemplateAreaId,
    required this.order,
  });

  factory InspectionTemplateItem.fromJson(Map<String, dynamic> json) => _$InspectionTemplateItemFromJson(json);
  Map<String, dynamic> toJson() => _$InspectionTemplateItemToJson(this);

  factory InspectionTemplateItem.create() => InspectionTemplateItem(
        id: const Uuid().v4(),
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
        name: '',
        inspectionTemplateId: '',
        inspectionTemplateAreaId: '',
        order: 0,
      );
}
