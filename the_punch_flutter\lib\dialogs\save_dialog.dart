import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';

import '../helpers/color_helper.dart';

class SaveDialog extends StatelessWidget {
  final String? title;
  final String? message;
  final Function() save;

  const SaveDialog({super.key, this.title, this.message, required this.save});

  @override
  Widget build(BuildContext context) => AlertDialog(
        title: title != null ? Text(title!) : null,
        content:
            Text(message ?? AppLocalizations.of(context)!.saveConfirmation),
        actions: [
          OutlinedButton.icon(
            style: OutlinedButton.styleFrom(
              side: BorderSide(
                color: ColorHelper.thePunchRed(),
              ),
            ),
            icon: const Icon(Icons.cancel),
            label: Text(AppLocalizations.of(context)!.no),
            onPressed: () => Navigator.pop(context),
          ),
          ElevatedButton.icon(
            icon: const Icon(Icons.save),
            label: Text(AppLocalizations.of(context)!.save),
            onPressed: () {
              Navigator.pop(context);
              save();
            },
          ),
        ],
      );
}
