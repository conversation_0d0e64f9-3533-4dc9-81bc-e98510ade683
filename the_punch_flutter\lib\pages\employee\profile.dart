import 'dart:async';
import 'dart:io';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../dataModel/data/user.dart';
import '../../dataModel/models/user_model.dart';
import '../../dialogs/text_field_dialog.dart';
import '../../helpers/color_helper.dart';
import '../../misc/app_localization.dart';
import '../../dataModel/data_model.dart';
import '../../dialogs/logout_dialog.dart';
import '../../misc/my_platform.dart';
import '../../the_punch_app.dart';
import '../view_model_mixin.dart';
import '../../widgets/icon_tile.dart';
import '../../misc/extensions.dart';
import '../web/my_scaffold.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});
  @override
  Widget build(BuildContext context) => ChangeNotifierProvider(
        create: (context) => _ViewModel(),
        builder: (context, child) => MyScaffold(
          title: AppLocalizations.of(context)!.profile,
          body: _Body(),
        ),
      );
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final secondaryBodyText1 =
        theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w800);

    final viewModel = context.watch<_ViewModel>();

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Container(
          constraints: const BoxConstraints(minWidth: 200, maxWidth: 500),
          child: ListView(
            shrinkWrap: true,
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 16, top: 16),
                child: Text(
                  viewModel.name,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ),
              IconCardTile(
                title: Text(
                  AppLocalizations.of(context)!.organizationId,
                  style: secondaryBodyText1,
                ),
                body: Text(viewModel.organizationId),
              ),
              // IconCardTile(
              //   title: Text(AppLocalizations.of(context)!.employeeId, style: secondaryBodyText1),
              //   body: Text(viewModel.employeeId),
              // ),
              IconCardTile(
                leading: const Icon(Icons.account_circle),
                title: Text(
                  AppLocalizations.of(context)!.username,
                  style: secondaryBodyText1,
                ),
                body: Text(viewModel.username),
              ),
              IconCardTile(
                leading: const Icon(Icons.password),
                title: Text(
                  AppLocalizations.of(context)!.updatePassword,
                  style: secondaryBodyText1,
                ),
                trailing: Icon(
                  Icons.edit,
                  color: ColorHelper.thePunchRed(),
                ),
                onTap: () => context.go('/updatePassword'),
              ),
              IconCardTile(
                leading: const Icon(Icons.phone_android),
                title: Text(
                  AppLocalizations.of(context)!.phone,
                  style: secondaryBodyText1,
                ),
                body: Text(viewModel.phone),
                trailing: Icon(
                  Icons.edit,
                  color: ColorHelper.thePunchRed(),
                ),
                onTap: () => unawaited(onPhoneTapped(context)),
              ),
              IconCardTile(
                leading: const Icon(Icons.email),
                title: Text(
                  AppLocalizations.of(context)!.email,
                  style: secondaryBodyText1,
                ),
                body: Text(viewModel.email),
                trailing: Icon(
                  Icons.edit,
                  color: ColorHelper.thePunchRed(),
                ),
                onTap: () => unawaited(onEmailTapped(context)),
              ),
              // New Location Tracking toggle added here.
              if(MyPlatform.isWeb)
              LocationTrackingTile(textStyle: secondaryBodyText1),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: ElevatedButton.icon(
                      label: Text(
                        AppLocalizations.of(context)!.logout,
                        style: Theme.of(context).textTheme.titleSmall,
                      ),
                      icon: const Icon(
                        Icons.logout,
                        color: Colors.white,
                      ),
                      onPressed: () async {
                        // ThePunchApp.setLoggingOut(context, true);
                        await showDialog(
                          context: context,
                          builder: (context) => const LogoutDialog(),
                        ).then((_) {
                          // After the dialog is dismissed (whether canceled or confirmed),
                          // we revert the isLoggingOut flag.
                          // ThePunchApp.setLoggingOut(context, false);
                        });
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> onPhoneTapped(BuildContext context) async {
    final viewModel = context.read<_ViewModel>();
    await showDialog(
      context: context,
      builder: (context) => TextFieldDialog(
        initialValue: viewModel.phone,
        labelText: AppLocalization.of(context).phone,
        onChanged: viewModel.setPhone,
        validator: (value) => value == null || value.isEmpty || value.validatePhone
            ? null
            : 'Invalid phone number.',
      ),
    );
  }

  Future<void> onEmailTapped(BuildContext context) async {
    final viewModel = context.read<_ViewModel>();
    await showDialog(
      context: context,
      builder: (context) => TextFieldDialog(
        initialValue: viewModel.email,
        labelText: AppLocalization.of(context).emailAddress,
        onChanged: viewModel.setEmail,
        validator: (value) => value == null || value.isEmpty || value.validateEmail
            ? null
            : 'Invalid email address.',
      ),
    );
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  User? employee;
  String organizationId = '';
  String get name => employee?.name ?? '';
  String employeeId = '';
  String get phone => employee?.phone ?? '';
  String get email => employee?.emailAddress ?? '';
  String get username => employee?.username ?? '';

  _ViewModel() {
    addListenables([DataModel().userModel]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    final prefs = await SharedPreferences.getInstance();

    organizationId = prefs.getString('organizationId') ?? '';

    final employeeId = prefs.getString('userId');
    if (employeeId != null) {
      this.employeeId = employeeId;
      employee = await DataModel().userModel.getById(employeeId);
    }
    notifyListeners();
  }

  void setPhone(String value) {
    if (employee == null) return;
    employee!.phone = value;
    unawaited(UserModel().saveDirty([employee!]));
    notifyListeners();
  }

  void setEmail(String value) {
    if (employee == null) return;
    employee!.emailAddress = value;
    unawaited(UserModel().saveDirty([employee!]));
    notifyListeners();
  }
}

/// A new stateful widget for the Location Tracking toggle.
/// This switch does nothing other than visually toggle between on/off.
class LocationTrackingTile extends StatefulWidget {
  final TextStyle? textStyle;
  const LocationTrackingTile({Key? key, this.textStyle}) : super(key: key);

  @override
  State<LocationTrackingTile> createState() => _LocationTrackingTileState();
}

class _LocationTrackingTileState extends State<LocationTrackingTile> {
  bool isTracking = false;

  @override
  Widget build(BuildContext context) {
    return SwitchListTile(
      secondary: const Icon(Icons.location_on),
      title: Text(
        'Location Tracking',
        style: widget.textStyle,
      ),
      value: isTracking,
      onChanged: (bool value) {
        setState(() {
          isTracking = value;
        });
      },
    );
  }
}
