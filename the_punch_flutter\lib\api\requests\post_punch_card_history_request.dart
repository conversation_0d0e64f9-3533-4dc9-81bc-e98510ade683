import 'package:json_annotation/json_annotation.dart';
import '../../misc/json_conversion.dart';
import '../../dataModel/data/punch_card.dart';
import 'system.dart';

part 'post_punch_card_history_request.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class PostPunchCardHistoryRequest extends SystemRequest {
  final String punchCardId;
  final DateTime? clockedIn;
  final DateTime? clockedOut;
  final double? duration;
  final String? locationId;
  final String? jobTypeId;

  // New fields
  final String? userId;
  final String? createdByUserId;
  final DateTime? lastChangedOn;
  final String? lastChangedByUser;

  PostPunchCardHistoryRequest({
    required this.punchCardId,
    required this.clockedIn,
    required this.clockedOut,
    required this.duration,
    required this.locationId,
    required this.jobTypeId,
    required this.userId,
    required this.createdByUserId,
    required this.lastChangedOn,
    required this.lastChangedByUser,
    required String serverIP,
    required String databaseName,
    required String sessionId,
  }) : super(serverIP: serverIP, databaseName: databaseName, sessionId: sessionId);

  // Updated factory method
  static Future<PostPunchCardHistoryRequest> create(PunchCard punchCard) async {
    final systemRequest = await SystemRequest.create();
    
    double? decimalDuration;
    if (punchCard.duration != null) {
      decimalDuration = convertDurationToDecimalHours(punchCard.duration!);
    }

    return PostPunchCardHistoryRequest(
      punchCardId: punchCard.id,
      clockedIn: punchCard.clockedIn,
      clockedOut: punchCard.clockedOut,
      duration: decimalDuration,
      locationId: punchCard.locationId,
      jobTypeId: punchCard.jobTypeId,
      userId: punchCard.userId, 
      createdByUserId: punchCard.createdByUserId,
      lastChangedOn: punchCard.lastChangedOn,
      lastChangedByUser: punchCard.lastChangedByUserId,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory PostPunchCardHistoryRequest.fromJson(Map<String, dynamic> json) =>
      _$PostPunchCardHistoryRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() {
    final data = _$PostPunchCardHistoryRequestToJson(this);
    data.removeWhere((key, value) => value == null);
    return data;
  }
}


// Helper function to convert Duration to decimal hours
double convertDurationToDecimalHours(Duration duration) {
  final totalMinutes = duration.inMinutes.toDouble();
  final totalSeconds = duration.inSeconds.toDouble() % 60;
  
  // Convert total time to decimal hours
  return totalMinutes / 60 + (totalSeconds / 3600);
}


@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class PostPunchCardHistoryResponse extends SystemResponse {
  final String message;

  PostPunchCardHistoryResponse({
    required this.message,
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory PostPunchCardHistoryResponse.fromJson(Map<String, dynamic> json) {
    print('Response JSON: $json'); // Debug print
    return _$PostPunchCardHistoryResponseFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() => _$PostPunchCardHistoryResponseToJson(this);
}