import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocationPingState extends ChangeNotifier {
  // Keys for geo fence ping data
  static const _locationLatKey = 'locationLat';
  static const _locationLongKey = 'locationLong';
  static const _locationGeofenceKey = 'locationGeofence';

  // Keys for travel ping data
  static const _userIdKey = 'userId';
  static const _punchCardIdKey = 'punchCardId';
  static const _lastPingLatKey = 'lastPingLat';
  static const _lastPingLongKey = 'lastPingLong';
  static const _lastPingEndTimeKey = 'lastPingEndTime';
  static const _currentPunchTravelPingsKey = 'currentPunchTravelPings';
  static const _stashedPingsKey = 'stashedPings';

  // Key for manager status
  static const _isManagerKey = 'isManager';

  // Clear all preferences
  Future<void> clearAllPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  // Manager status methods
  Future<void> saveIsManager(bool isManager) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isManagerKey, isManager);
  }

  Future<bool> getIsManager() async {
    final prefs = await SharedPreferences.getInstance();
    // If the key does not exist, it returns false by default.
    return prefs.getBool(_isManagerKey) ?? false;
  }

  // Methods related to location
  Future<void> saveLocation(String? latitude, String? longitude) async {
    final prefs = await SharedPreferences.getInstance();
    if (latitude != null && longitude != null) {
      await prefs.setString(_locationLatKey, latitude);
      await prefs.setString(_locationLongKey, longitude);
    } else {
      await prefs.remove(_locationLatKey);
      await prefs.remove(_locationLongKey);
    }
  }

  Future<Map<String, String?>> getLocation() async {
    final prefs = await SharedPreferences.getInstance();
    String? latitude = prefs.getString(_locationLatKey);
    String? longitude = prefs.getString(_locationLongKey);
    return {
      "latitude": latitude,
      "longitude": longitude
    };
  }

  Future<void> saveLocationGeofence(Map<String, dynamic> geofenceData) async {
    final prefs = await SharedPreferences.getInstance();
    String encodedData = jsonEncode(geofenceData);
    print('Saving geofence data: $encodedData');
    await prefs.setString(_locationGeofenceKey, encodedData);
  }

  Future<Map<String, dynamic>?> getLocationGeofence() async {
    final prefs = await SharedPreferences.getInstance();
    String? encodedData = prefs.getString(_locationGeofenceKey);
    print('Retrieved geofence data: $encodedData');
    return encodedData == null ? null : Map<String, dynamic>.from(jsonDecode(encodedData));
  }

  // Methods related to travel pings
  Future<void> saveUserId(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userIdKey, userId);
  }

  Future<String?> getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userIdKey);
  }

  Future<void> savePunchCardId(String punchCardId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_punchCardIdKey, punchCardId);
  }

  Future<String?> getPunchCardId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_punchCardIdKey);
  }

  Future<void> saveLastPingLocation(String? latitude, String? longitude) async {
    final prefs = await SharedPreferences.getInstance();
    if (latitude != null && longitude != null) {
      await prefs.setString(_lastPingLatKey, latitude);
      await prefs.setString(_lastPingLongKey, longitude);
    } else {
      await prefs.remove(_lastPingLatKey);
      await prefs.remove(_lastPingLongKey);
    }
  }

  Future<Map<String, String?>> getLastPingLocation() async {
    final prefs = await SharedPreferences.getInstance();
    String? latitude = prefs.getString(_lastPingLatKey);
    String? longitude = prefs.getString(_lastPingLongKey);
    return {
      "latitude": latitude,
      "longitude": longitude
    };
  }

  Future<void> saveLastPingEndTime(DateTime endTime) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastPingEndTimeKey, endTime.toIso8601String());
  }

  Future<DateTime?> getLastPingEndTime() async {
    final prefs = await SharedPreferences.getInstance();
    String? endTimeString = prefs.getString(_lastPingEndTimeKey);
    if (endTimeString != null) {
      return DateTime.tryParse(endTimeString);
    }
    return null;
  }

  Future<void> saveCurrentPunchTravelPings(List<Map<String, dynamic>> pings) async {
    final prefs = await SharedPreferences.getInstance();
    String encodedData = jsonEncode(pings);
    await prefs.setString(_currentPunchTravelPingsKey, encodedData);
  }

  Future<List<Map<String, dynamic>>> getCurrentPunchTravelPings() async {
    final prefs = await SharedPreferences.getInstance();
    String? encodedData = prefs.getString(_currentPunchTravelPingsKey);
    return encodedData == null ? [] : List<Map<String, dynamic>>.from(jsonDecode(encodedData));
  }

  Future<void> saveStashedPings(List<Map<String, dynamic>> pings) async {
    final prefs = await SharedPreferences.getInstance();
    String encodedData = jsonEncode(pings);
    await prefs.setString(_stashedPingsKey, encodedData);
  }

  Future<List<Map<String, dynamic>>> getStashedPings() async {
    final prefs = await SharedPreferences.getInstance();
    String? encodedData = prefs.getString(_stashedPingsKey);
    return encodedData == null ? [] : List<Map<String, dynamic>>.from(jsonDecode(encodedData));
  }
}