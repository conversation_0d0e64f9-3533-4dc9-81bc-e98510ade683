import '../../api/api_model.dart';
import '../../api/sync_model.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import '../base_data.dart';
import '../data/group_member.dart';
import '../data_model.dart';
import '../hive_db.dart';

class GroupMemberModel extends BaseDataModel<GroupMember> {
  @override
  Future<Iterable<GroupMember>> get all async =>
      (await HiveDb.database).groupMembers.values;

  @override
  Future<void> save(Iterable<GroupMember> members) async {
    final box = (await HiveDb.database).groupMembers;
    await box.putAll({for (final m in members) m.id: m});
    notifyListeners();
  }

  /// **Add this clearAll method**
  Future<void> clearAll() async {
    final box = (await HiveDb.database).groupMembers;
    await box.clear();
    notifyListeners();
  }

  /// Just add this:
  Future<Iterable<GroupMember>> getByGroupId(String groupId) async {
    final allMembers = await all;
    return allMembers.where((gm) => gm.groupId == groupId);
  }

Future<void> editGroup(
  String groupId,
  List<String> addUserIds,
  List<String> removeUserIds,
) async {
  // 1. Call the API with both lists
  await ApiModel().editChatGroupMembers(groupId, addUserIds, removeUserIds);
  final fetchResponse = await ApiModel().fetchMessages(LoginState.userId);

  // --- Step 2: OVERWRITE local DB with fetched data ---
  // First, clear existing groups and group members:
  await DataModel().messageGroupModel.clearAll();
  await DataModel().groupMemberModel.clearAll();

  // Then add the freshly fetched data:
  // (If you also want to overwrite Messages, you can clear them too,
  //  but here we only overwrite groups & groupMembers as requested.)
  await DataModel().messageModel.updateMessages(fetchResponse.messages);
  await DataModel().messageGroupModel
      .updateMessageGroups(fetchResponse.messageGroups ?? []);
  await DataModel().groupMemberModel
      .updateGroupMembers(fetchResponse.groupMembers ?? []);
  // 2. (Optionally) add new local GroupMember records for each user in addUserIds
  //    ...
  //    final newMembers = addUserIds.map((userId) => GroupMember(
  //      id: something, 
  //      groupId: groupId, 
  //      userId: userId,
  //      ...
  //    ));
  //    await localBox.putAll( ... );

  // 3. Remove the locally stored groupMembers for those in removeUserIds
  // final box = (await HiveDb.database).groupMembers;
  // final keysToRemove = box.values
  //     .where((m) => m.groupId == groupId && removeUserIds.contains(m.userId))
  //     .map((m) => m.id)
  //     .toList();

  // await box.deleteAll(keysToRemove);
  notifyListeners();
}


  /// Merges a fetched list of groupMembers with what we have locally.
  Future<void> updateGroupMembers(Iterable<GroupMember> fetchedMembers) async {
    final db = await HiveDb.database;
    final localBox = db.groupMembers;

    // Create a map for quick local lookups
    final localMap = {for (final gm in localBox.values) gm.id: gm};

    final toSave = <String, GroupMember>{};

    for (final fetched in fetchedMembers) {
      final local = localMap[fetched.id];
      if (local == null) {
        // Brand new to local
        toSave[fetched.id] = fetched;
      } else {
        // Merge logic...
        final updated = GroupMember(
          id: fetched.id,
          groupId: fetched.groupId,
          userId: fetched.userId,
          createdByUserId: local.createdByUserId!,
          createdOn: local.createdOn,
          isDirty: local.isDirty || fetched.isDirty,
          isActive: local.isActive,
          lastChangedOn:
              _mostRecentDateTime(local.lastChangedOn, fetched.lastChangedOn),
          lastChangedByUserId:
              local.lastChangedByUserId ?? fetched.lastChangedByUserId,
        );
        toSave[updated.id] = updated;
      }
    }

    if (toSave.isNotEmpty) {
      await localBox.putAll(toSave);
      notifyListeners();
    }
  }

  DateTime _mostRecentDateTime(DateTime? dt1, DateTime? dt2) {
    if (dt1 == null && dt2 == null) {
      return DateTime.fromMillisecondsSinceEpoch(0);
    } else if (dt1 == null) {
      return dt2!;
    } else if (dt2 == null) {
      return dt1;
    } else {
      return dt1.isAfter(dt2) ? dt1 : dt2;
    }
  }
}
