import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';

class RemoveDialog extends StatelessWidget {
  final String? title;
  final String? message;
  final Function() remove;

  const RemoveDialog({super.key, this.title, this.message, required this.remove});

  @override
  Widget build(BuildContext context) => AlertDialog(
      title: title != null ? Text(title!) : null,
      content: Text(message ?? 'Are you sure you want to remove this item?'),
      actions: [
        OutlinedButton.icon(
          icon: const Icon(Icons.cancel),
          label: Text(AppLocalizations.of(context)!.no),
          onPressed: () => Navigator.pop(context),
        ),
        ElevatedButton.icon(
          icon: const Icon(Icons.remove_circle),
          label: const Text('Remove'),
          onPressed: () {
            Navigator.pop(context);
            remove();
          },
        ),
      ],
    );
}
