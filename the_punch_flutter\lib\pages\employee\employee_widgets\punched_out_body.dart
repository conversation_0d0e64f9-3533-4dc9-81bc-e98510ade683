import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:material_symbols_icons/material_symbols_icons.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../../dataModel/data/location.dart';

import '../../../dataModel/data/user_type.dart';
import '../../../dataModel/data_model.dart';

import '../../../dataModel/models/punch_view_model.dart';
import '../../../dataModel/models/user_model.dart';
import '../../../dialogs/end_task_dialog.dart';
import '../../../dialogs/view_punch_card_dialog.dart';
import '../../../helpers/check_session.dart';
import '../../../helpers/color_helper.dart';
import '../../../helpers/screen_helper.dart';
import '../../../misc/calculate_distance.dart';
import '../../../widgets/buttons/attachment_button.dart';
import '../clock2.dart';
import 'punched_in_body.dart';
import '../../../state/location_state.dart';
import '../../../state/login_state.dart';
import '../../../state/punch_state.dart';
import '../../../widgets/jobType.dart';
import '../../../widgets/location_tile.dart';
import '../../../widgets/location_notes.dart';
import '../../../widgets/progress_bar/progress_bar.dart';
// ...existing imports...

class PunchedOutBody extends StatefulWidget {
  final Function(BuildContext, bool, String, String?) onLocationSelected;

  const PunchedOutBody({required this.onLocationSelected});

  @override
  _PunchedOutBodyState createState() => _PunchedOutBodyState();
}

class _PunchedOutBodyState extends State<PunchedOutBody> {
  bool isPermissionsLoading = false;
 Future<bool>? _isEmployeeFuture;
  Future<void> _showPunchInDialog(BuildContext context) async {
    if (mounted) {
      setState(() {
        isPermissionsLoading = true;
      });
    }

    LocationPermission permission =
        await LocationState.checkAndRequestPermissions();

    if (mounted) {
      setState(() {
        isPermissionsLoading = false;
      });
    }

    if (permission != LocationPermission.always) {
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        await showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Permissions Required'),
            content: const Text(
                'Please enable location permissions to be able to punch in.'),
            actions: [
              TextButton(
                child: const Text('OK'),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        );
        await context.read<PunchViewModel>().initializeData();
      } else {
        await showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Permissions Denied'),
            content: const Text(
                'Please set Location Permissions to "Always" in Settings > The Punch on your device.'),
            actions: [
              TextButton(
                child: const Text('OK'),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        );
        await context.read<PunchViewModel>().initializeData();
      }
    } else {
      await showDialog(
        context: context,
        builder: (BuildContext context) => _PunchInDialogContent(
            onLocationSelected: widget.onLocationSelected),
      );
    }
  }
  Future<bool> _checkIfEmployee() async {
    final userId = LoginState.userId;
    final userTypeId = await UserModel().getCurrentUserTypeId(userId);
    // If the user's userTypeId equals the employee userTypeId, we consider them an employee
    return UserType.employeeId.toUpperCase() == userTypeId?.toUpperCase();
  }
@override
void initState() {
    super.initState();
    _isEmployeeFuture = _checkIfEmployee();
}

@override
Widget build(BuildContext context) => FutureBuilder<bool>(
      future: _isEmployeeFuture,
      builder: (context,snapshot) { 
         final isEmployee = snapshot.data ?? false;
         final punchCard = context.watch<PunchState>().punchCard;
        return Consumer<PunchViewModel>(
     
      builder: (context, viewModel, child) => 
      // Expanded( // Changed to Column
      //   child: 
          SingleChildScrollView(
           
          //padding: EdgeInsets.all(ScreenHelper.screenHeightPercentage(context, 6.8)),
          child: Column(
             children: [
              Container(
                padding: EdgeInsets.all(
                  ScreenHelper.screenHeightPercentage(context, 2.8),
                ),
              ),
              Column(children: [
                
                 ProgressBar(
                  duration: Duration(minutes: 0, seconds:0) ,
                  durationWidget:  Text(
                      "0:00",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 35,
                        
                        fontWeight: FontWeight.w900,
                        color: ColorHelper.thePunchLightGray(),
                      )),
                  locationId:punchCard?.locationId?? '', 
                  jobTypeWidget:  JobTypeW(jobTypeId: punchCard?.jobTypeId?? '')),
                 SizedBox(height:  ScreenHelper.screenHeightPercentage(context, 4)),
                 SizedBox(
                 //   width: 300.0,  // Example width
                    height: 200.0, // Example height
                    child:                  Stack(
                  children: [
                    //Punch In Button
                    Positioned(
                      top: 10,
                      left:ScreenHelper.screenWidthPercentage(context, 50) - 65,
                      child: Container(
                          width: 130,
                          height: 130,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: ColorHelper.thePunchLightRed(),
                          ),
                          child: 
                          // Center(
                          //   child: 
                          //     ElevatedButton(
                          //     onPressed: () => {},
                          //     style: ElevatedButton.styleFrom(
                          //       shape: const CircleBorder(
                          //       // side: BorderSide(color: Colors.red),
                          //       ),
                          //       backgroundColor: Colors.transparent,
                          //       elevation: 0,
                          //       padding: const EdgeInsets.all(0),
                          //     ),
                          //     child: 
                        ElevatedButton(
                        onPressed: () async {
                          if (isPermissionsLoading) return;

                          if (mounted) {
                            setState(() {
                              isPermissionsLoading = true;
                            });
                          }

                          // 2A. Check session first
                          final sessionOk = await checkSession(context);
                          if (!sessionOk) {
                            // Session was forced out, bail out
                            return;
                          }

                          var connectivityResult = await (Connectivity().checkConnectivity());
                          if (connectivityResult == ConnectivityResult.none) {
                            if (mounted) {
                              setState(() {
                                isPermissionsLoading = false;
                              });
                            }
                            await showDialog(
                              context: context,
                              builder: (BuildContext context) => AlertDialog(
                                content: const Text(
                                    "No Internet Connection, Please Reconnect and Try Again"),
                                actions: [
                                  TextButton(
                                    child: const Text("OK"),
                                    onPressed: () {
                                      Navigator.of(context).pop();
                                    },
                                  ),
                                ],
                              ),
                            );
                           } else {
                            // Continue your normal flow
                            await _showPunchInDialog(context);
                            final clockMainState = context.findAncestorStateOfType<ClockMainState>();
                            if (clockMainState?.locationSelected ?? false) {
                              await Provider.of<PunchViewModel>(context, listen: false).handlePunchIn(
                                clockMainState!.selectedisScheduled,
                                clockMainState.selectedlocationid,
                                clockMainState.selectedScheduleId,
                              );
                              clockMainState.locationSelected = false;
                            }
                          }

                          if (mounted) {
                            setState(() {
                              isPermissionsLoading = false;
                            });
                          }
                         },
                          style: ElevatedButton.styleFrom(
                            shape: const CircleBorder(),
                            padding: const EdgeInsets.all(40),
                            backgroundColor: ColorHelper.thePunchPink(),
                          ),
                          child: Text(
                            '${AppLocalizations.of(context)!.punch}\n${AppLocalizations.of(context)!.inW}',
                            textAlign: TextAlign.center,
                            style: TextStyle(color: Colors.white, fontSize: 16),
                          ),
                    //    ),
                      ),
                       ),
                    //   ),
                    // )
                    ),

                    Positioned(
                      top: 105,
                      left:ScreenHelper.screenWidthPercentage(context, 0)+ 25 ,
                      child: isEmployee ? const ViewPunchCardButton() : const SizedBox(),

                    )  ,
                    if(!isEmployee)
                    Positioned(
                      top: 95,
                      right:ScreenHelper.screenWidthPercentage(context, 0) + 25,
                      child: Container(
                          width: 105,
                          height: 105,
                          decoration: BoxDecoration(
                            color:ColorHelper.thePunchDesktopLightGray(),
                            shape: BoxShape.circle,
                            
                          ),
                          child:  Consumer<PunchState>(
                          builder: (context, punchState, child) {
                            if (punchState.isTaskActive) {
                              return EndTaskButton(punchState: punchState);
                            } else {
                              return StartTaskButton();
                            }
                          },
                          ),
                      ),
                    ),                  
                  ]
                ),
              
                  )
                ]),

            ],
          ),
          ),

        
     // ),
    
      );
   
     }  
    );
}


class _PunchInDialogContent extends StatefulWidget {
  final Function(BuildContext, bool, String, String?) onLocationSelected;

  const _PunchInDialogContent({required this.onLocationSelected});

  @override
  _PunchInDialogContentState createState() => _PunchInDialogContentState();
}


class _PunchInDialogContentState extends State<_PunchInDialogContent> {
  bool isLoading = true;
  bool isManagerOrAdmin = false;

  // For employees:
  List<Map<String, dynamic>> scheduledLocations = [];
  List<Location> nearbyLocations = [];
  Map<String, double> locationDistances = {};

  // For managers/admin:
  List<Location> managerNearbyLocations = [];
  Map<String, double> managerLocationDistances = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    await LocationState.updateCurrentLocation();
    final currentPosition = LocationState().currentPosition;
    final userId = LoginState.userId;
    final userTypeId = await UserModel().getCurrentUserTypeId(userId);

    setState(() {
      // Determine if user is manager or admin
      isManagerOrAdmin = (UserType.managerId.toUpperCase() == userTypeId?.toUpperCase()) ||
          (UserType.administratorId.toUpperCase() == userTypeId?.toUpperCase());
    });

    // If manager/admin, gather "detected locations" within ~2 miles
    if (currentPosition != null && isManagerOrAdmin) {
      final locations = (await DataModel().locationModel.active).toList();
      final distances = <String, double>{};
      for (final loc in locations) {
        final d = calculateDistance(
          currentPosition.latitude,
          currentPosition.longitude,
          loc.latitude,
          loc.longitude,
        );
        distances[loc.id] = d;
      }
      managerLocationDistances = distances;

      // Filter to within 2 miles
      final maxDistanceInMeters = 2.0 * 1609.34;
      final managerNearby = locations
          .where((loc) => distances[loc.id]! <= maxDistanceInMeters)
          .toList();
      managerNearby.sort((a, b) => distances[a.id]!.compareTo(distances[b.id]!));

      setState(() {
        managerNearbyLocations = managerNearby;
        isLoading = false;
      });
      return;
    }

    // Otherwise: employee logic
    if (currentPosition != null && !isManagerOrAdmin) {
      final locations = (await DataModel().locationModel.active).toList();
      final schedules = (await DataModel().scheduleModel.active).toList();

      final distances = <String, double>{};
      for (final loc in locations) {
        final meters = calculateDistance(
          currentPosition.latitude,
          currentPosition.longitude,
          loc.latitude,
          loc.longitude,
        );
        distances[loc.id] = meters;
      }
      locationDistances = distances;

      final maxDistanceInMiles = 2.0;
      final maxDistanceInMeters = maxDistanceInMiles * 1609.34;

      final today = DateTime.now();
      final scheduled = schedules
          .where((schedule) {
            final scheduleDate = schedule.startDateLocal;
            return scheduleDate.year == today.year &&
                scheduleDate.month == today.month &&
                scheduleDate.day == today.day;
          })
          .map((schedule) {
            final location = locations.firstWhere(
              (loc) => loc.id == schedule.locationId,
              orElse: () => Location.create(),
            );
            return {'schedule': schedule, 'location': location};
          })
          .toList();

      final scheduledLocationIds =
          scheduled.map((e) => e['location']!.id).toSet();

      final nearby = locations
          .where((loc) =>
              !scheduledLocationIds.contains(loc.id) &&
              distances[loc.id]! <= maxDistanceInMeters)
          .toList();
      nearby.sort((a, b) => distances[a.id]!.compareTo(distances[b.id]!));

      setState(() {
        scheduledLocations = scheduled;
        nearbyLocations = nearby;
        isLoading = false;
      });
    } else {
      // No current position found => stop
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.only(top: 45.0),
          child: SizedBox(
            width: 110,
            height: 105,
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                ColorHelper.thePunchBlue(),
              ),
              strokeWidth: 10.0,
            ),
          ),
        ),
      );
    }

    return AlertDialog(
      title: Center(
        child: Text(
          'Select Location:',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: ColorHelper.thePunchGray(),
              ),
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isManagerOrAdmin)
              _buildManagerLocationSection(context)
            else ...[
              _buildSection(
                'Scheduled Locations',
                scheduledLocations,
                locationDistances,
                context,
                true,
              ),
              _buildSection(
                'Nearby Locations',
                nearbyLocations,
                locationDistances,
                context,
                false,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// For manager/admin: Show "Detected Location(s)" + "No detected locations" if none,
  /// and also the "Other" section with "Start of Day".
  Widget _buildManagerLocationSection(BuildContext context) {
    final theme = Theme.of(context);
    final titleStyle = theme.textTheme.titleMedium;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // DETECTED LOCATIONS
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
          child: Text(
            'Detected Location:',
            style: titleStyle,
          ),
        ),
        if (managerNearbyLocations.isEmpty)
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              'No detected locations',
              style: TextStyle(fontSize: 12, color: Colors.red),
            ),
          )
        else
          ...managerNearbyLocations.map((loc) {
            final distance = managerLocationDistances[loc.id];
            return LocationTile(
              location: loc,
              distance: distance,
              selected: () => widget.onLocationSelected(
                context,
                false,  // isScheduled
                loc.id,
                null,    // scheduleId
              ),
            );
          }).toList(),

        const SizedBox(height: 16),
        // OTHER
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
          child: Text(
            'Other:',
            style: titleStyle,
          ),
        ),
        _buildFakeLocationTile(context),
      ],
    );
  }

  /// The same "Scheduled/Nearby Locations" logic for employees
  Widget _buildSection(
    String title,
    List<dynamic> items,
    Map<String, double> locationDistances,
    BuildContext context,
    bool isScheduled,
  ) {
    final theme = Theme.of(context);
    final titleStyle = theme.textTheme.titleMedium;
    final timeLabelStyle =
        theme.textTheme.titleSmall?.copyWith(color: Colors.grey, fontSize: 8);

    if (isScheduled) {
      items.sort((a, b) => a['schedule']
          .startDateLocal
          .compareTo(b['schedule'].startDateLocal));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
          child: Text(
            title,
            style: titleStyle,
          ),
        ),
        if (items.isEmpty)
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.0),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text('None', style: TextStyle(fontSize: 12)),
            ),
          )
        else
          ...items.map((e) {
            final location = isScheduled ? e['location'] : e;
            final schedule = isScheduled ? e['schedule'] : null;
            final distance = locationDistances[
                isScheduled ? e['location'].id : e.id];

            final startTime = schedule?.startDateLocal != null
                ? TimeOfDay.fromDateTime(schedule.startDateLocal)
                    .format(context)
                : '';
            final endTime = schedule?.endDateLocal != null
                ? TimeOfDay.fromDateTime(schedule.endDateLocal)
                    .format(context)
                : '';

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (isScheduled)
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16.0, vertical: 0.0),
                    child: RichText(
                      text: TextSpan(
                        text: 'Start Time: $startTime  -  End Time: $endTime',
                        style: timeLabelStyle,
                      ),
                    ),
                  ),
                LocationTile(
                  location: location,
                  distance: distance,
                  selected: () => widget.onLocationSelected(
                    context,
                    isScheduled,
                    location.id,
                    isScheduled ? schedule.id : null,
                  ),
                ),
                if (isScheduled) const SizedBox(height: 10),
              ],
            );
          }).toList(),
      ],
    );
  }

  /// For managers, "Start of Day" tile under "Other:"
  Widget _buildFakeLocationTile(BuildContext context) => LocationTile(
      location: Location(
        id: 'fake-location',
        name: 'Start of Day',
        address1: 'Current Location',
        address2: '',
        city: '',
        state: '',
        zip: '',
        country: '',
        phone: '',
        emailAddress: '',
        latitude: 0,
        longitude: 0,
        geoFenceRadius: 1000,
        timeZone: '',
        createdOn: DateTime.now(),
      ),
      distance: 0,
      selected: () => widget.onLocationSelected(
        context,
        false,
        'fake-location',
        null,
      ),
    );
}


class EndTaskButton extends StatelessWidget {
  final PunchState punchState;

  const EndTaskButton({required this.punchState});

  @override
  Widget build(BuildContext context) => Align(
        alignment: Alignment.center, // Aligns to the same top-left position
        child: Padding(
          padding: const EdgeInsets.all(16.0), // Adjust padding as needed
          child: GestureDetector(
            onTap: () async {
              // 5A. Check session
                  final sessionOk = await checkSession(context);
                  if (!sessionOk) return;

                  final punchViewModel =
                      Provider.of<PunchViewModel>(context, listen: false);
                  punchViewModel.clearLocationNotes();
                  await showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) => EndTaskDialog(punchState: punchState),
                  );
                },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Symbols.check_circle_outline, // Material icon for End Task
                  size: 20, // Adjust icon size
                  color: ColorHelper.thePunchBlue(), // Red color for End Task
                ),
                const SizedBox(height: 4), // Spacing between icon and text
                Text(
                  'End Task',
                  style: TextStyle(
                    color: ColorHelper.thePunchBlue(), // Match icon color
                    fontSize: 12, // Smaller font size for text
                    fontWeight: FontWeight.bold
                  ),
                ),
              ],
            ),
          ),
        ),
      );
}



