import 'package:collection/collection.dart';

class WindowsTimeZone {
  String name;
  String iana;
  int offset;

  WindowsTimeZone({required this.name, required this.iana, required this.offset});

  static String? ianaToName(String iana) => windowsTimeZones.firstWhereOrNull((e) => e.iana == iana)?.name;

  static Iterable<WindowsTimeZone> windowsTimeZones = [
    // WindowsTimeZone(name: name, iana: iana, offset: offset),
    WindowsTimeZone(name: '(UTC-12:00) International Date Line West', iana: 'Etc/GMT+12', offset: -43200000),
    WindowsTimeZone(name: '(UTC-11:00) Coordinated Universal Time-11', iana: 'Etc/GMT+11', offset: -39600000),
    WindowsTimeZone(name: '(UTC-10:00) Aleutian Islands', iana: 'America/Adak', offset: -36000000),
    WindowsTimeZone(name: '(UTC-10:00) Hawaii', iana: 'Pacific/Honolulu', offset: -36000000),
    WindowsTimeZone(name: '(UTC-09:30) Marquesas Islands', iana: 'Pacific/Marquesas', offset: -34200000),
    WindowsTimeZone(name: '(UTC-09:00) Alaska', iana: 'America/Anchorage', offset: -32400000),
    WindowsTimeZone(name: '(UTC-09:00) Coordinated Universal Time-09', iana: 'Etc/GMT+9', offset: -32400000),
    WindowsTimeZone(name: '(UTC-08:00) Baja California', iana: 'America/Tijuana', offset: -28800000),
    WindowsTimeZone(name: '(UTC-08:00) Coordinated Universal Time-08', iana: 'Etc/GMT+8', offset: -28800000),
    WindowsTimeZone(name: '(UTC-08:00) Pacific Time (US & Canada)', iana: 'America/Los_Angeles', offset: -28800000),
    WindowsTimeZone(name: '(UTC-07:00) Arizona', iana: 'America/Phoenix', offset: -25200000),
    WindowsTimeZone(name: '(UTC-07:00) Chihuahua, La Paz, Mazatlan', iana: 'America/Chihuahua', offset: -25200000),
    WindowsTimeZone(name: '(UTC-07:00) Mountain Time (US & Canada)', iana: 'America/Denver', offset: -25200000),
    WindowsTimeZone(name: '(UTC-07:00) Yukon', iana: 'America/Whitehorse', offset: -25200000),
    WindowsTimeZone(name: '(UTC-06:00) Central America', iana: 'America/Guatemala', offset: -21600000),
    WindowsTimeZone(name: '(UTC-06:00) Central Time (US & Canada)', iana: 'America/Chicago', offset: -21600000),
    WindowsTimeZone(name: '(UTC-06:00) Easter Island', iana: 'Pacific/Easter', offset: -21600000),
    WindowsTimeZone(
        name: '(UTC-06:00) Guadalajara, Mexico City, Monterrey', iana: 'America/Mexico_City', offset: -21600000),
    WindowsTimeZone(name: '(UTC-06:00) Saskatchewan', iana: 'America/Regina', offset: -21600000),
    WindowsTimeZone(name: '(UTC-05:00) Bogota, Lima, Quito, Rio Branco', iana: 'America/Bogota', offset: -18000000),
    WindowsTimeZone(name: '(UTC-05:00) Chetumal', iana: 'America/Cancun', offset: -18000000),
    WindowsTimeZone(name: '(UTC-05:00) Eastern Time (US & Canada)', iana: 'America/New_York', offset: -18000000),
    WindowsTimeZone(name: '(UTC-05:00) Haiti', iana: 'America/Port-au-Prince', offset: -18000000),
    WindowsTimeZone(name: '(UTC-05:00) Havana', iana: 'America/Havana', offset: -18000000),
    WindowsTimeZone(name: '(UTC-05:00) Indiana (East)', iana: 'America/Indiana/Indianapolis', offset: -18000000),
    WindowsTimeZone(name: '(UTC-05:00) Turks and Caicos', iana: 'America/Grand_Turk', offset: -18000000),
    WindowsTimeZone(name: '(UTC-04:00) Asuncion', iana: 'America/Asuncion', offset: -14400000),
    WindowsTimeZone(name: '(UTC-04:00) Atlantic Time (Canada)', iana: 'America/Halifax', offset: -14400000),
    WindowsTimeZone(name: '(UTC-04:00) Caracas', iana: 'America/Caracas', offset: -14400000),
    WindowsTimeZone(name: '(UTC-04:00) Cuiaba', iana: 'America/Cuiaba', offset: -14400000),
    WindowsTimeZone(
        name: '(UTC-04:00) Georgetown, La Paz, Manaus, San Juan', iana: 'America/La_Paz', offset: -14400000),
    WindowsTimeZone(name: '(UTC-04:00) Santiago', iana: 'America/Santiago', offset: -14400000),
    WindowsTimeZone(name: '(UTC-03:30) Newfoundland', iana: 'America/St_Johns', offset: -12600000),
    WindowsTimeZone(name: '(UTC-03:00) Araguaina', iana: 'America/Araguaina', offset: -10800000),
    WindowsTimeZone(name: '(UTC-03:00) Brasilia', iana: 'America/Sao_Paulo', offset: -10800000),
    WindowsTimeZone(name: '(UTC-03:00) Cayenne, Fortaleza', iana: 'America/Cayenne', offset: -10800000),
    WindowsTimeZone(
        name: '(UTC-03:00) City of Buenos Aires', iana: 'America/Argentina/Buenos_Aires', offset: -10800000),
    WindowsTimeZone(name: '(UTC-03:00) Greenland', iana: 'America/Nuuk', offset: -10800000),
    WindowsTimeZone(name: '(UTC-03:00) Montevideo', iana: 'America/Montevideo', offset: -10800000),
    WindowsTimeZone(name: '(UTC-03:00) Punta Arenas', iana: 'America/Punta_Arenas', offset: -10800000),
    WindowsTimeZone(name: '(UTC-03:00) Saint Pierre and Miquelon', iana: 'America/Miquelon', offset: -10800000),
    WindowsTimeZone(name: '(UTC-03:00) Salvador', iana: 'America/Bahia', offset: -10800000),
    WindowsTimeZone(name: '(UTC-02:00) Coordinated Universal Time-02', iana: 'Etc/GMT+2', offset: -7200000),
    WindowsTimeZone(name: '(UTC-02:00) Mid-Atlantic - Old', iana: 'Etc/GMT+2', offset: -7200000),
    WindowsTimeZone(name: '(UTC-01:00) Azores', iana: 'Atlantic/Azores', offset: -3600000),
    WindowsTimeZone(name: '(UTC-01:00) Cabo Verde Is.', iana: 'Atlantic/Cape_Verde', offset: -3600000),
    WindowsTimeZone(name: '(UTC) Coordinated Universal Time', iana: 'Etc/UTC', offset: 0),
    WindowsTimeZone(name: '(UTC+00:00) Dublin, Edinburgh, Lisbon, London', iana: 'Europe/London', offset: 0),
    WindowsTimeZone(name: '(UTC+00:00) Monrovia, Reykjavik', iana: 'Atlantic/Reykjavik', offset: 0),
    WindowsTimeZone(name: '(UTC+00:00) Sao Tome', iana: 'Africa/Sao_Tome', offset: 0),
    WindowsTimeZone(name: '(UTC+01:00) Casablanca', iana: 'Africa/Casablanca', offset: 0),
    WindowsTimeZone(
        name: '(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna', iana: 'Europe/Berlin', offset: 3600000),
    WindowsTimeZone(
        name: '(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague',
        iana: 'Europe/Budapest',
        offset: 3600000),
    WindowsTimeZone(name: '(UTC+01:00) Brussels, Copenhagen, Madrid, Paris', iana: 'Europe/Paris', offset: 3600000),
    WindowsTimeZone(name: '(UTC+01:00) Sarajevo, Skopje, Warsaw, Zagreb', iana: 'Europe/Warsaw', offset: 3600000),
    WindowsTimeZone(name: '(UTC+01:00) West Central Africa', iana: 'Africa/Lagos', offset: 3600000),
    WindowsTimeZone(name: '(UTC+02:00) Amman', iana: 'Asia/Amman', offset: 7200000),
    WindowsTimeZone(name: '(UTC+02:00) Athens, Bucharest', iana: 'Europe/Bucharest', offset: 7200000),
    WindowsTimeZone(name: '(UTC+02:00) Beirut', iana: 'Asia/Beirut', offset: 7200000),
    WindowsTimeZone(name: '(UTC+02:00) Cairo', iana: 'Africa/Cairo', offset: 7200000),
    WindowsTimeZone(name: '(UTC+02:00) Chisinau', iana: 'Europe/Chisinau', offset: 7200000),
    WindowsTimeZone(name: '(UTC+02:00) Damascus', iana: 'Asia/Damascus', offset: 7200000),
    WindowsTimeZone(name: '(UTC+02:00) Gaza, Hebron', iana: 'Asia/Hebron', offset: 7200000),
    WindowsTimeZone(name: '(UTC+02:00) Harare, Pretoria', iana: 'Africa/Johannesburg', offset: 7200000),
    WindowsTimeZone(
        name: '(UTC+02:00) Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius', iana: 'Europe/Kiev', offset: 7200000),
    WindowsTimeZone(name: '(UTC+02:00) Jerusalem', iana: 'Asia/Jerusalem', offset: 7200000),
    WindowsTimeZone(name: '(UTC+02:00) Kaliningrad', iana: 'Europe/Kaliningrad', offset: 7200000),
    WindowsTimeZone(name: '(UTC+02:00) Khartoum', iana: 'Africa/Khartoum', offset: 7200000),
    WindowsTimeZone(name: '(UTC+02:00) Tripoli', iana: 'Africa/Tripoli', offset: 7200000),
    WindowsTimeZone(name: '(UTC+02:00) Windhoek', iana: 'Africa/Windhoek', offset: 7200000),
    WindowsTimeZone(name: '(UTC+03:00) Baghdad', iana: 'Asia/Baghdad', offset: 10800000),
    WindowsTimeZone(name: '(UTC+03:00) Istanbul', iana: 'Europe/Istanbul', offset: 10800000),
    WindowsTimeZone(name: '(UTC+03:00) Kuwait, Riyadh', iana: 'Asia/Riyadh', offset: 10800000),
    WindowsTimeZone(name: '(UTC+03:00) Minsk', iana: 'Europe/Minsk', offset: 10800000),
    WindowsTimeZone(name: '(UTC+03:00) Moscow, St. Petersburg', iana: 'Europe/Moscow', offset: 10800000),
    WindowsTimeZone(name: '(UTC+03:00) Nairobi', iana: 'Africa/Nairobi', offset: 10800000),
    WindowsTimeZone(name: '(UTC+03:00) Volgograd', iana: 'Europe/Volgograd', offset: 10800000),
    WindowsTimeZone(name: '(UTC+03:30) Tehran', iana: 'Asia/Tehran', offset: 12600000),
    WindowsTimeZone(name: '(UTC+04:00) Abu Dhabi, Muscat', iana: 'Asia/Dubai', offset: 14400000),
    WindowsTimeZone(name: '(UTC+04:00) Astrakhan, Ulyanovsk', iana: 'Europe/Astrakhan', offset: 14400000),
    WindowsTimeZone(name: '(UTC+04:00) Baku', iana: 'Asia/Baku', offset: 14400000),
    WindowsTimeZone(name: '(UTC+04:00) Izhevsk, Samara', iana: 'Europe/Samara', offset: 14400000),
    WindowsTimeZone(name: '(UTC+04:00) Port Louis', iana: 'Indian/Mauritius', offset: 14400000),
    WindowsTimeZone(name: '(UTC+04:00) Saratov', iana: 'Europe/Saratov', offset: 14400000),
    WindowsTimeZone(name: '(UTC+04:00) Tbilisi', iana: 'Asia/Tbilisi', offset: 14400000),
    WindowsTimeZone(name: '(UTC+04:00) Yerevan', iana: 'Asia/Yerevan', offset: 14400000),
    WindowsTimeZone(name: '(UTC+04:30) Kabul', iana: 'Asia/Kabul', offset: 16200000),
    WindowsTimeZone(name: '(UTC+05:00) Ashgabat, Tashkent', iana: 'Asia/Tashkent', offset: 18000000),
    WindowsTimeZone(name: '(UTC+05:00) Ekaterinburg', iana: 'Asia/Yekaterinburg', offset: 18000000),
    WindowsTimeZone(name: '(UTC+05:00) Islamabad, Karachi', iana: 'Asia/Karachi', offset: 18000000),
    WindowsTimeZone(name: '(UTC+05:00) Qyzylorda', iana: 'Asia/Qyzylorda', offset: 18000000),
    WindowsTimeZone(name: '(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi', iana: 'Asia/Kolkata', offset: 19800000),
    WindowsTimeZone(name: '(UTC+05:30) Sri Jayawardenepura', iana: 'Asia/Colombo', offset: 19800000),
    WindowsTimeZone(name: '(UTC+05:45) Kathmandu', iana: 'Asia/Kathmandu', offset: 20700000),
    WindowsTimeZone(name: '(UTC+06:00) Astana', iana: 'Asia/Almaty', offset: 21600000),
    WindowsTimeZone(name: '(UTC+06:00) Dhaka', iana: 'Asia/Dhaka', offset: 21600000),
    WindowsTimeZone(name: '(UTC+06:00) Omsk', iana: 'Asia/Omsk', offset: 21600000),
    WindowsTimeZone(name: '(UTC+06:30) Yangon (Rangoon)', iana: 'Asia/Yangon', offset: 23400000),
    WindowsTimeZone(name: '(UTC+07:00) Bangkok, Hanoi, Jakarta', iana: 'Asia/Bangkok', offset: 25200000),
    WindowsTimeZone(name: '(UTC+07:00) Barnaul, Gorno-Altaysk', iana: 'Asia/Barnaul', offset: 25200000),
    WindowsTimeZone(name: '(UTC+07:00) Hovd', iana: 'Asia/Hovd', offset: 25200000),
    WindowsTimeZone(name: '(UTC+07:00) Krasnoyarsk', iana: 'Asia/Krasnoyarsk', offset: 25200000),
    WindowsTimeZone(name: '(UTC+07:00) Novosibirsk', iana: 'Asia/Novosibirsk', offset: 25200000),
    WindowsTimeZone(name: '(UTC+07:00) Tomsk', iana: 'Asia/Tomsk', offset: 25200000),
    WindowsTimeZone(name: '(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi', iana: 'Asia/Shanghai', offset: 28800000),
    WindowsTimeZone(name: '(UTC+08:00) Irkutsk', iana: 'Asia/Irkutsk', offset: 28800000),
    WindowsTimeZone(name: '(UTC+08:00) Kuala Lumpur, Singapore', iana: 'Asia/Singapore', offset: 28800000),
    WindowsTimeZone(name: '(UTC+08:00) Perth', iana: 'Australia/Perth', offset: 28800000),
    WindowsTimeZone(name: '(UTC+08:00) Taipei', iana: 'Asia/Taipei', offset: 28800000),
    WindowsTimeZone(name: '(UTC+08:00) Ulaanbaatar', iana: 'Asia/Ulaanbaatar', offset: 28800000),
    WindowsTimeZone(name: '(UTC+08:45) Eucla', iana: 'Australia/Eucla', offset: 31500000),
    WindowsTimeZone(name: '(UTC+09:00) Chita', iana: 'Asia/Chita', offset: 32400000),
    WindowsTimeZone(name: '(UTC+09:00) Osaka, Sapporo, Tokyo', iana: 'Asia/Tokyo', offset: 32400000),
    WindowsTimeZone(name: '(UTC+09:00) Pyongyang', iana: 'Asia/Pyongyang', offset: 32400000),
    WindowsTimeZone(name: '(UTC+09:00) Seoul', iana: 'Asia/Seoul', offset: 32400000),
    WindowsTimeZone(name: '(UTC+09:00) Yakutsk', iana: 'Asia/Yakutsk', offset: 32400000),
    WindowsTimeZone(name: '(UTC+09:30) Adelaide', iana: 'Australia/Adelaide', offset: 34200000),
    WindowsTimeZone(name: '(UTC+09:30) Darwin', iana: 'Australia/Darwin', offset: 34200000),
    WindowsTimeZone(name: '(UTC+10:00) Brisbane', iana: 'Australia/Brisbane', offset: 36000000),
    WindowsTimeZone(name: '(UTC+10:00) Canberra, Melbourne, Sydney', iana: 'Australia/Sydney', offset: 36000000),
    WindowsTimeZone(name: '(UTC+10:00) Guam, Port Moresby', iana: 'Pacific/Port_Moresby', offset: 36000000),
    WindowsTimeZone(name: '(UTC+10:00) Hobart', iana: 'Australia/Hobart', offset: 36000000),
    WindowsTimeZone(name: '(UTC+10:00) Vladivostok', iana: 'Asia/Vladivostok', offset: 36000000),
    WindowsTimeZone(name: '(UTC+10:30) Lord Howe Island', iana: 'Australia/Lord_Howe', offset: 37800000),
    WindowsTimeZone(name: '(UTC+11:00) Bougainville Island', iana: 'Pacific/Bougainville', offset: 39600000),
    WindowsTimeZone(name: '(UTC+11:00) Chokurdakh', iana: 'Asia/Srednekolymsk', offset: 39600000),
    WindowsTimeZone(name: '(UTC+11:00) Magadan', iana: 'Asia/Magadan', offset: 39600000),
    WindowsTimeZone(name: '(UTC+11:00) Norfolk Island', iana: 'Pacific/Norfolk', offset: 39600000),
    WindowsTimeZone(name: '(UTC+11:00) Sakhalin', iana: 'Asia/Sakhalin', offset: 39600000),
    WindowsTimeZone(name: '(UTC+11:00) Solomon Is., New Caledonia', iana: 'Pacific/Guadalcanal', offset: 39600000),
    WindowsTimeZone(name: '(UTC+12:00) Anadyr, Petropavlovsk-Kamchatsky', iana: 'Asia/Kamchatka', offset: 43200000),
    WindowsTimeZone(name: '(UTC+12:00) Auckland, Wellington', iana: 'Pacific/Auckland', offset: 43200000),
    WindowsTimeZone(name: '(UTC+12:00) Coordinated Universal Time+12', iana: 'Etc/GMT-12', offset: 43200000),
    WindowsTimeZone(name: '(UTC+12:00) Fiji', iana: 'Pacific/Fiji', offset: 43200000),
    WindowsTimeZone(name: '(UTC+12:00) Petropavlovsk-Kamchatsky - Old', iana: 'Asia/Kamchatka', offset: 43200000),
    WindowsTimeZone(name: '(UTC+12:45) Chatham Islands', iana: 'Pacific/Chatham', offset: 45900000),
    WindowsTimeZone(name: '(UTC+13:00) Coordinated Universal Time+13', iana: 'Etc/GMT-13', offset: 46800000),
    WindowsTimeZone(name: "(UTC+13:00) Nuku'alofa", iana: 'Pacific/Tongatapu', offset: 46800000),
    WindowsTimeZone(name: '(UTC+13:00) Samoa', iana: 'Pacific/Apia', offset: 46800000),
    WindowsTimeZone(name: '(UTC+14:00) Kiritimati Island', iana: 'Pacific/Kiritimati', offset: 50400000),
  ];
}
