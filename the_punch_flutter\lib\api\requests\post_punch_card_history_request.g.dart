// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'post_punch_card_history_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PostPunchCardHistoryRequest _$PostPunchCardHistoryRequestFromJson(
        Map<String, dynamic> json) =>
    PostPunchCardHistoryRequest(
      punchCardId: json['PunchCardId'] as String,
      clockedIn: json['ClockedIn'] == null
          ? null
          : DateTime.parse(json['ClockedIn'] as String),
      clockedOut: json['ClockedOut'] == null
          ? null
          : DateTime.parse(json['ClockedOut'] as String),
      duration: (json['Duration'] as num?)?.toDouble(),
      locationId: json['LocationId'] as String?,
      jobTypeId: json['JobTypeId'] as String?,
      userId: json['UserId'] as String?,
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: json['LastChangedOn'] == null
          ? null
          : DateTime.parse(json['LastChangedOn'] as String),
      lastChangedByUser: json['LastChangedByUser'] as String?,
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$PostPunchCardHistoryRequestToJson(
    PostPunchCardHistoryRequest instance) {
  final val = <String, dynamic>{
    'Request_ServerIP': instance.serverIP,
    'Request_DatabaseName': instance.databaseName,
    'Request_SessionID': instance.sessionId,
    'PunchCardId': instance.punchCardId,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ClockedIn', instance.clockedIn?.toIso8601String());
  writeNotNull('ClockedOut', instance.clockedOut?.toIso8601String());
  writeNotNull('Duration', instance.duration);
  writeNotNull('LocationId', instance.locationId);
  writeNotNull('JobTypeId', instance.jobTypeId);
  writeNotNull('UserId', instance.userId);
  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', instance.lastChangedOn?.toIso8601String());
  writeNotNull('LastChangedByUser', instance.lastChangedByUser);
  return val;
}

PostPunchCardHistoryResponse _$PostPunchCardHistoryResponseFromJson(
        Map<String, dynamic> json) =>
    PostPunchCardHistoryResponse(
      message: json['Message'] as String,
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$PostPunchCardHistoryResponseToJson(
    PostPunchCardHistoryResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['Message'] = instance.message;
  return val;
}
