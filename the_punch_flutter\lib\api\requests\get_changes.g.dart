// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_changes.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetChangesRequest _$GetChangesRequestFromJson(Map<String, dynamic> json) =>
    GetChangesRequest(
      changedAfter: nullableDateTimeFromJson(json['ChangedAfter']),
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$GetChangesRequestToJson(GetChangesRequest instance) {
  final val = <String, dynamic>{
    'Request_ServerIP': instance.serverIP,
    'Request_DatabaseName': instance.databaseName,
    'Request_SessionID': instance.sessionId,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ChangedAfter', nullableDateTimeToJson(instance.changedAfter));
  return val;
}

GetChangesResponse _$GetChangesResponseFromJson(Map<String, dynamic> json) =>
    GetChangesResponse(
      alerts: (json['Alerts'] as List<dynamic>?)
              ?.map((e) => Alert.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      geoLocations: (json['GeoLocations'] as List<dynamic>?)
              ?.map((e) => GeoLocation.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      inspections: (json['Inspections'] as List<dynamic>?)
              ?.map((e) => Inspection.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      inspectionAreas: (json['InspectionAreas'] as List<dynamic>?)
              ?.map((e) => InspectionArea.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      inspectionItems: (json['InspectionItems'] as List<dynamic>?)
              ?.map((e) => InspectionItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      inspectionImages: (json['InspectionImages'] as List<dynamic>?)
              ?.map((e) => InspectionImage.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      inspectionTemplates: (json['InspectionTemplates'] as List<dynamic>?)
              ?.map(
                  (e) => InspectionTemplate.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      inspectionTemplateAreas: (json['InspectionTemplateAreas']
                  as List<dynamic>?)
              ?.map((e) =>
                  InspectionTemplateArea.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      inspectionTemplateItems: (json['InspectionTemplateItems']
                  as List<dynamic>?)
              ?.map((e) =>
                  InspectionTemplateItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      jobTypes: (json['JobTypes'] as List<dynamic>?)
              ?.map((e) => JobType.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      locations: (json['Locations'] as List<dynamic>?)
              ?.map((e) => Location.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      locationContacts: (json['LocationContacts'] as List<dynamic>?)
              ?.map((e) => LocationContact.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      locationNotes: (json['LocationNotes'] as List<dynamic>?)
              ?.map((e) => LocationNote.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      messages: (json['Messages'] as List<dynamic>?)
              ?.map((e) => Message.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      notes: (json['Notes'] as List<dynamic>?)
              ?.map((e) => Note.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      permissions: (json['Permissions'] as List<dynamic>?)
              ?.map((e) => Permission.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      punchCards: (json['PunchCards'] as List<dynamic>?)
              ?.map((e) => PunchCard.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      schedules: (json['Schedules'] as List<dynamic>?)
              ?.map((e) => Schedule.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      scheduleTemplates: (json['ScheduleTemplates'] as List<dynamic>?)
              ?.map((e) => ScheduleTemplate.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      users: (json['Users'] as List<dynamic>?)
              ?.map((e) => User.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      userTypes: (json['UserTypes'] as List<dynamic>?)
              ?.map((e) => UserType.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      userPermissions: (json['UserPermissions'] as List<dynamic>?)
              ?.map((e) => Permission.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      userTypePermissions: (json['UserTypePermissions'] as List<dynamic>?)
              ?.map(
                  (e) => UserTypePermission.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      languages: (json['Languages'] as List<dynamic>?)
              ?.map((e) => Language.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$GetChangesResponseToJson(GetChangesResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['Alerts'] = instance.alerts;
  val['GeoLocations'] = instance.geoLocations;
  val['Inspections'] = instance.inspections;
  val['InspectionAreas'] = instance.inspectionAreas;
  val['InspectionItems'] = instance.inspectionItems;
  val['InspectionImages'] = instance.inspectionImages;
  val['InspectionTemplates'] = instance.inspectionTemplates;
  val['InspectionTemplateAreas'] = instance.inspectionTemplateAreas;
  val['InspectionTemplateItems'] = instance.inspectionTemplateItems;
  val['JobTypes'] = instance.jobTypes;
  val['Locations'] = instance.locations;
  val['LocationContacts'] = instance.locationContacts;
  val['LocationNotes'] = instance.locationNotes;
  val['Messages'] = instance.messages;
  val['Notes'] = instance.notes;
  val['Permissions'] = instance.permissions;
  val['PunchCards'] = instance.punchCards;
  val['Schedules'] = instance.schedules;
  val['ScheduleTemplates'] = instance.scheduleTemplates;
  val['UserPermissions'] = instance.userPermissions;
  val['Users'] = instance.users;
  val['UserTypes'] = instance.userTypes;
  val['UserTypePermissions'] = instance.userTypePermissions;
  val['Languages'] = instance.languages;
  return val;
}
