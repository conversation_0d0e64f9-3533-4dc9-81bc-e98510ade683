// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserAdapter extends TypeAdapter<User> {
  @override
  final int typeId = 3;

  @override
  User read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return User(
      isDirty: fields[0] as bool,
      isActive: fields[1] as bool,
      createdOn: fields[2] as DateTime,
      createdByUserId: fields[3] as String?,
      lastChangedOn: fields[4] as DateTime?,
      lastChangedByUserId: fields[5] as String?,
      id: fields[100] as String,
      username: fields[101] as String?,
      phone: fields[104] as String?,
      emailAddress: fields[105] as String?,
      firstName: fields[102] as String,
      lastName: fields[103] as String,
      userTypeId: fields[106] as String,
      isContact: fields[107] as bool,
      payRate: fields[108] as double?,
      payRateFrequency: fields[109] as int,
      languageKey: fields[110] as String,
      payrollId: fields[111] as String?,
      managedByIds: fields[112] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, User obj) {
    writer
      ..writeByte(19)
      ..writeByte(100)
      ..write(obj.id)
      ..writeByte(101)
      ..write(obj.username)
      ..writeByte(102)
      ..write(obj.firstName)
      ..writeByte(103)
      ..write(obj.lastName)
      ..writeByte(104)
      ..write(obj.phone)
      ..writeByte(105)
      ..write(obj.emailAddress)
      ..writeByte(106)
      ..write(obj.userTypeId)
      ..writeByte(107)
      ..write(obj.isContact)
      ..writeByte(112)
      ..write(obj.managedByIds)
      ..writeByte(108)
      ..write(obj.payRate)
      ..writeByte(109)
      ..write(obj.payRateFrequency)
      ..writeByte(110)
      ..write(obj.languageKey)
      ..writeByte(111)
      ..write(obj.payrollId)
      ..writeByte(0)
      ..write(obj.isDirty)
      ..writeByte(1)
      ..write(obj.isActive)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdByUserId)
      ..writeByte(4)
      ..write(obj.lastChangedOn)
      ..writeByte(5)
      ..write(obj.lastChangedByUserId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFromJson(Map<String, dynamic> json) => User(
      isActive: json['IsActive'] as bool? ?? true,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
      id: idFromJson(json['Id']),
      username: json['Username'] as String?,
      phone: json['Phone'] as String?,
      emailAddress: json['EmailAddress'] as String?,
      firstName: json['FirstName'] as String,
      lastName: json['LastName'] as String,
      userTypeId: idFromJson(json['UserTypeId']),
      isContact: json['IsContact'] as bool,
      password: json['Password'] as String?,
      confirmPassword: json['ConfirmPassword'] as String?,
      payRate: (json['PayRate'] as num?)?.toDouble(),
      payRateFrequency: (json['PayRate_Frequency'] as num).toInt(),
      languageKey: json['LanguageKey'] as String,
      payrollId: json['PayrollID'] as String?,
      managedByIds: json['ManagedByIds'] as String?,
    );

Map<String, dynamic> _$UserToJson(User instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  writeNotNull('Username', instance.username);
  val['FirstName'] = instance.firstName;
  val['LastName'] = instance.lastName;
  writeNotNull('Phone', instance.phone);
  writeNotNull('EmailAddress', instance.emailAddress);
  val['UserTypeId'] = instance.userTypeId;
  val['IsContact'] = instance.isContact;
  writeNotNull('ManagedByIds', instance.managedByIds);
  writeNotNull('Password', instance.password);
  writeNotNull('ConfirmPassword', instance.confirmPassword);
  writeNotNull('PayRate', instance.payRate);
  val['PayRate_Frequency'] = instance.payRateFrequency;
  val['LanguageKey'] = instance.languageKey;
  writeNotNull('PayrollID', instance.payrollId);
  return val;
}
