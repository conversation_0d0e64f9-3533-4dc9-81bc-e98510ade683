import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import '../../dataModel/data/user.dart';
import '../../dataModel/data_model.dart';
import '../constrained_search_dialog.dart';
import '../message_dialog.dart';
import '../../misc/change_notification_builder.dart';
import '../../pages/view_model_mixin.dart';
import '../../widgets/padded_card.dart';

class ExpandedContactsDialog extends StatelessWidget {
  final Iterable<String> ignoreUserIds;
  final Function(String) onSelection;
  final search = ValueNotifier<String>('');

  ExpandedContactsDialog({super.key, required this.onSelection, this.ignoreUserIds = const []});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline6 = theme.textTheme.titleLarge;

    return ChangeNotifierBuilder<_ViewModel>(
      create: (context) => _ViewModel(ignoreContacts: ignoreUserIds),
      builder: (context, viewModel, child) {
        if (viewModel.contacts.isEmpty) return const MessageDialog(message: 'No available contacts.');
        return ConstrainedSearchDialog(
          autofocus: true,
          title: Text(AppLocalizations.of(context)!.contacts, style: headline6),
          builder: (search) {
            final visibleContacts = viewModel.contacts.where((e) => _contactMatches(e, search));
            return visibleContacts.map((e) => _Tile(contact: e, contactSelected: onSelection));
          },
        );
      },
    );
  }

  bool _contactMatches(User contact, String search) {
    if (contact.name.toLowerCase().contains(search.toLowerCase())) return true;
    if (contact.phone != null && contact.phone!.contains(search)) return true;
    if (contact.emailAddress != null && contact.emailAddress!.toLowerCase().contains(search.toLowerCase())) {
      return true;
    }
    return false;
  }
}

class _Tile extends StatelessWidget {
  const _Tile({required this.contactSelected, required this.contact});

  final User contact;
  final Function(String) contactSelected;

  @override
  Widget build(BuildContext context) => GestureDetector(
        onTap: () {
          Navigator.of(context).pop();
          contactSelected(contact.id);
        },
        child: PaddedCard(
          child: Center(
              child: Column(
            children: [
              Text(contact.name),
              if (contact.phone != null) Text(contact.phone!),
              if (contact.emailAddress != null) Text(contact.emailAddress!),
            ],
          )),
        ),
      );
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  Iterable<User> contacts = [];

  Iterable<String> ignoreContacts;
  _ViewModel({required this.ignoreContacts}) {
    addListenables([DataModel().userModel]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    contacts = (await DataModel().userModel.activeContacts).where((e) => !ignoreContacts.contains(e.id));
    notifyListeners();
  }
}
