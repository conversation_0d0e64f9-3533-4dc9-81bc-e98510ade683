<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="app.thepunch.mobile">

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>

    <application
        android:usesCleartextTraffic="true"
        android:label="The Punch"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        android:enableOnBackInvokedCallback="true"
        tools:replace="android:label">

        <!-- Add your flutter_background_geolocation license key here -->
        <meta-data android:name="com.transistorsoft.locationmanager.license"
            android:value="bb99d6ea9979686c6bf181d68a5ccc838c8e1b06ac8ef8bc2aa52cef7d7a5a42" />
        <meta-data android:name="com.google.android.geo.API_KEY" android:value="AIzaSyBVO_QhmNJNr2ih4kBewpVCOrlEJ72fPXw"/>
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize"
            android:screenOrientation="portrait">  <!-- This line locks it to portrait -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

        <!-- <service
            android:name="com.almoullim.background_service.BackgroundService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="location" /> -->
    </application>
</manifest>
