import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../helpers/screen_helper.dart';
import '../helpers/text_style_helper.dart';
import '../state/punch_state.dart';
// ...existing imports...

class DurationWidget extends StatelessWidget {
  
  @override
  Widget build(BuildContext context) => Center(
        child: Column( // Use a Column to arrange the text vertically
        mainAxisAlignment: MainAxisAlignment.center,
          children: [
            StreamBuilder<String>(
              stream: PunchState().durationStream,
              builder: (context, snapshot){ 
                final punchCard = context.watch<PunchState>().punchCard;
                 return Text(
                snapshot.data ?? '',
                textAlign: TextAlign.center,
                style: TextStyleHelper.bigBlackTitle(context)
                    .copyWith(fontSize: 35, color: PunchState().isPunchedIn  ? Colors.black : Colors.black54),
              ); 
              }
            ),
            const Text( // Added Text widget below the StreamBuilder
              '12hrs', // The text you want to display
              style: TextStyle( // Style the "12hrs" text as needed
                fontSize: 11, // Example font size
                color: Colors.grey, // Example color
              ),
            ),
            SizedBox(height: ScreenHelper.screenHeightPercentage(context, 1)),          ],
        ),
      );
}
