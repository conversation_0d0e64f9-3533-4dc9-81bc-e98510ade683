import 'package:community_material_icon/community_material_icon.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import 'package:the_punch_flutter/dataModel/data/user_type.dart';
import 'package:the_punch_flutter/dataModel/models/punch_view_model.dart';
import 'package:the_punch_flutter/dataModel/models/user_model.dart';
import 'package:the_punch_flutter/dialogs/start_task_dialog.dart';
import 'package:the_punch_flutter/dialogs/view_punch_card_dialog.dart';
import 'package:the_punch_flutter/helpers/check_session.dart';
import 'package:the_punch_flutter/helpers/color_helper.dart';
import 'package:the_punch_flutter/helpers/screen_helper.dart';
import 'package:the_punch_flutter/pages/employee/clock2.dart';
import 'package:the_punch_flutter/state/login_state.dart';
import 'package:the_punch_flutter/state/punch_state.dart';
import 'package:the_punch_flutter/widgets/duration_display.dart';
import 'package:the_punch_flutter/widgets/location_notes.dart';
import 'package:the_punch_flutter/widgets/progress_bar/progress_bar.dart';
import '../../../widgets/buttons/attachment_button.dart';
import '../../../widgets/jobType.dart';

class _PunchedInBody extends StatefulWidget {
  const _PunchedInBody();

  @override
  _PunchedInBodyState createState() => _PunchedInBodyState();
}

class _PunchedInBodyState extends State<_PunchedInBody> {
  Future<bool>? _isEmployeeFuture;

  @override
  void initState() {
    super.initState();
    _isEmployeeFuture = _checkIfEmployee();
  }

  Future<bool> _checkIfEmployee() async {
    final userId = LoginState.userId;
    final userTypeId = await UserModel().getCurrentUserTypeId(userId);
    // If the user's userTypeId equals the employee userTypeId, we consider them an employee
    return UserType.employeeId.toUpperCase() == userTypeId?.toUpperCase();
  }

  @override
  Widget build(BuildContext context) {
    final punchCard = context.watch<PunchState>().punchCard;

    return FutureBuilder<bool>(
      future: _isEmployeeFuture,
      builder: (context, snapshot) {
        final isEmployee = snapshot.data ?? false;
        
        return SingleChildScrollView(
          child: Column(
             children: [
              Container(
                padding: EdgeInsets.all(
                  ScreenHelper.screenHeightPercentage(context, 2.8),
                ),
              ),
              Column(children: [
                
                 ProgressBar(
                  duration: Duration(minutes: 0, seconds:0) ,
                  durationWidget: DurationWidget(),
                  locationId:punchCard?.locationId?? '', 
                  jobTypeWidget:  JobTypeW(jobTypeId: punchCard!.jobTypeId?? '')),
                 SizedBox(height:  ScreenHelper.screenHeightPercentage(context, 4)),
                 SizedBox(
                 //   width: 300.0,  // Example width
                    height: 200.0, // Example height
                    child:                  Stack(
                  children: [
                    Positioned(
                      top: 10,
                      left:ScreenHelper.screenWidthPercentage(context, 50) - 65,
                      child: Container(
                          width: 130,
                          height: 130,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: ColorHelper.thePunchLightRed(),
                          ),
                          child: 
                          // Center(
                          //   child: 
                          //     ElevatedButton(
                          //     onPressed: () => {},
                          //     style: ElevatedButton.styleFrom(
                          //       shape: const CircleBorder(
                          //       // side: BorderSide(color: Colors.red),
                          //       ),
                          //       backgroundColor: Colors.transparent,
                          //       elevation: 0,
                          //       padding: const EdgeInsets.all(0),
                          //     ),
                          //     child: 
                              ElevatedButton(
                              onPressed: () async {
                                  // 3A. Check session first
                                  final sessionOk = await checkSession(context);
                                  if (!sessionOk) {
                                    // Session was forced out, bail out
                                    return;
                                  }

                                  var connectivityResult = await (Connectivity().checkConnectivity());
                                  if (connectivityResult == ConnectivityResult.none) {
                                    await showDialog(
                                      context: context,
                                      builder: (BuildContext context) => AlertDialog(
                                        content: const Text(
                                            "No Internet Connection, Please Reconnect and Try Again"),
                                        actions: [
                                          TextButton(
                                            child: const Text("OK"),
                                            onPressed: () {
                                              Navigator.of(context).pop();
                                            },
                                          ),
                                        ],
                                      ),
                                    );
                                  } else {
                                    bool punchOutConfirmed = false;
                                    await _showPunchOutDialog(context, (value) => punchOutConfirmed = value);
                                    if (punchOutConfirmed) {
                                      ScaffoldMessenger.of(context).hideCurrentSnackBar();
                                      await Provider.of<PunchViewModel>(context, listen: false).handlePunchOut();
                                      punchOutConfirmed = false;
                                    }
                                  }
                                },

                                style: ElevatedButton.styleFrom(
                                  shape: const CircleBorder(),
                                padding: const EdgeInsets.all(40),
                                backgroundColor: ColorHelper.thePunchLightPink(),
                                
                                
                                ),
                                child: Text(
                                  '${AppLocalizations.of(context)!.punch}\n${AppLocalizations.of(context)!.outW}',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(color: ColorHelper.thePunchRed(), fontSize: 16, fontWeight: FontWeight.bold)
                                // style: Theme.of(context).textTheme.bodyLarge,
                              
                                ),
                              ),
                            ),
                    //   ),
                    // )
                    ),
                    Positioned(
                      top: 10,
                      right:ScreenHelper.screenWidthPercentage(context, 0) + 15,
                      child: Container(
                          width: 120,
                          height: 80,

                          child: Center(
                            child:  LocationNotesButton(
                          locationId: punchCard?.locationId?? '',
                        ) 
                             ),
                      ),
                    ),
                    // View Punch Card Button
                    if(!isEmployee)
                    Positioned(
                      top: 105,
                      left:ScreenHelper.screenWidthPercentage(context, 0)+ 25 ,
                      child: !isEmployee ? const ViewPunchCardButton() : const SizedBox(),

                    )  ,
                    // Start/ End Task Button
                    if(!isEmployee)
                    Positioned(
                      top: 95,
                      right:ScreenHelper.screenWidthPercentage(context, 0) + 25,
                      child: Container(
                          width: 105,
                          height: 105,
                          decoration: BoxDecoration(
                            color:PunchState().isTaskActive ? ColorHelper.thePunchLightBlue(): ColorHelper.thePunchBlue(),
                            shape: BoxShape.circle,
                            
                          ),
                          child: !isEmployee ? Consumer<PunchState>(
                          builder: (context, punchState, child) {
                            if (punchState.isTaskActive) {
                              return _EndTaskButton(punchState: punchState);
                            } else {
                              return StartTaskButton();
                            }
                          },
                          ): Expanded(child: SizedBox()),
                      ),
                    ),                  
                    //Location Notes Button
                    Positioned(
                      top: 20,
                      right:ScreenHelper.screenWidthPercentage(context, 0) + 50,
                      child:  Center(
                            child:  LocationNotesButton(
                          locationId: punchCard?.locationId?? '',
                        ) 
                             
                      ),
                    ),
                    //Attachment Button
                    Positioned(
                      top: 20,
                      left:ScreenHelper.screenWidthPercentage(context, 0)+ 25 ,
                      child: const AttachmentButton(), 

                    )  ,
                
                
                  ]
                ),
              
                  )
                ]),

            ],
          ),
        );
      },
    );
  }

  Future<void> _showPunchOutDialog(
    BuildContext context,
    ValueChanged<bool> onConfirm,
  ) async =>
      showDialog(
        context: context,
        builder: (BuildContext context) => AlertDialog(
          title: Text(
            '${AppLocalizations.of(context)!.punchStatus}:',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: ColorHelper.thePunchRed(),
                ),
          ),
          content: Text(
            AppLocalizations.of(context)!.confirmPunchOut,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontSize: ScreenHelper.screenHeightPercentage(context, 2),
                ),
          ),
          actions: [
            OutlinedButton.icon(
              style: OutlinedButton.styleFrom(
                side: BorderSide(
                  color: ColorHelper.thePunchRed(),
                ),
              ),
              icon: const Icon(Icons.cancel),
              label: const Text('No'),
              onPressed: () {
                onConfirm(false);
                Navigator.pop(context);
              },
            ),
            ElevatedButton.icon(
              icon: const Icon(
                CommunityMaterialIcons.clock_out,
                color: Colors.white,
              ),
              label: Text(AppLocalizations.of(context)!.clockOut,
                  style: const TextStyle(
                      color: Colors.white, fontWeight: FontWeight.w700)),
              onPressed: () {
                onConfirm(true);
                Navigator.pop(context);
              },
            ),
          ],
        ),
      );
}

class _EndTaskButton extends StatelessWidget {
  final PunchState punchState;

  const _EndTaskButton({required this.punchState});

  @override
  Widget build(BuildContext context) => Align(
        alignment: Alignment.center, // Aligns to the same top-left position
        child: Padding(
          padding: const EdgeInsets.all(16.0), // Adjust padding as needed
          child: GestureDetector(
            onTap: () async {
              // 5A. Check session
                  final sessionOk = await checkSession(context);
                  if (!sessionOk) return;

                  final punchViewModel =
                      Provider.of<PunchViewModel>(context, listen: false);
                  punchViewModel.clearLocationNotes();
                  await showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) => _EndTaskDialog(punchState: punchState),
                  );
                },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Symbols.check_circle_outline, // Material icon for End Task
                  size: 20, // Adjust icon size
                  color: ColorHelper.thePunchBlue(), // Red color for End Task
                ),
                const SizedBox(height: 4), // Spacing between icon and text
                Text(
                  'End Task',
                  style: TextStyle(
                    color: ColorHelper.thePunchBlue(), // Match icon color
                    fontSize: 12, // Smaller font size for text
                    fontWeight: FontWeight.bold
                  ),
                ),
              ],
            ),
          ),
        ),
      );
}
class _EndTaskDialog extends StatefulWidget {
  final PunchState punchState;

  const _EndTaskDialog({required this.punchState});

  @override
  _EndTaskDialogState createState() => _EndTaskDialogState();
}

class _EndTaskDialogState extends State<_EndTaskDialog> {
  bool isEndingTask = false;

  Future<void> _confirmEndTask() async {

    final sessionOk = await checkSession(context);
    if (!sessionOk) {
      Navigator.of(context, rootNavigator: true).pop();
      return;
    }

    setState(() {
      isEndingTask = true;
    });

    // Perform the punch-out operation
    await widget.punchState.managerTaskPunchOut();

    // Optionally, show a confirmation SnackBar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text("Task ended! Returning to Manager Tracking."),
        backgroundColor: ColorHelper.thePunchBlue(),
      ),
    );

    // Close the dialog after completing the task
    if (mounted) {
      Navigator.of(context, rootNavigator: true).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isEndingTask) {
      // Display a loading indicator while ending the task
      return AlertDialog(
        content: Container(
          height: 150,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Ending Task...',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: ColorHelper.thePunchGray(),
                ),
              ),
              SizedBox(height: 20),
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  ColorHelper.thePunchBlue(),
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      // Confirmation dialog before ending the task
      return AlertDialog(
        title: Text(
          'End Task',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: ColorHelper.thePunchRed(),
              ),
        ),
        content: Text(
          'Would you like to end the current task?',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: ColorHelper.thePunchLightGray(),
              ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('No'),
          ),
          TextButton(
            onPressed: _confirmEndTask,
            child: Text('Yes'),
          ),
        ],
      );
    }
  }
}

class StartTaskButton extends StatelessWidget {

  @override
Widget build(BuildContext context) => Consumer<PunchViewModel>(
  builder: (context, viewModel, child) => 
    Opacity(opacity:PunchState().isPunchedIn?1:.5,child:
        GestureDetector(
        
          onTap: () async {
            // 4A. Check session first
            final sessionOk = await checkSession(context);
            if (!sessionOk) {
              // Session was forced out, bail out
              return;
            }
            if(PunchState().isPunchedIn)
            _showStartTaskDialog(context, viewModel);
          },
          child: Container( // Container now wraps both Icon and Text
          //                           width: 105,
          //                 height: 105,
          // //width: ScreenHelper.screenWidth(context)*.30, 
          //   decoration:  BoxDecoration(
          //                   color:ColorHelper.thePunchBlue(),
          //                   shape: BoxShape.circle,
                            
          //                 ),
            child: const Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.add_task,
                  size: 25,
                  color: Colors.white,
                ),
                SizedBox(height: 3),
                Text(
                  'Start Task',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold
                  ),
                ),
              ],
            ),
          ),
        ),
     ),
    );


  void _showStartTaskDialog(BuildContext context, PunchViewModel viewModel) {
    showDialog(
      context: context,
      builder: (BuildContext context) =>
          StartTaskDialog(punchViewModel: viewModel),
    );
  }
}
