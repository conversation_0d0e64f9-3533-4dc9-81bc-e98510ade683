import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import '../../dataModel/data/location.dart';
import '../../dataModel/data_model.dart';
import '../constrained_search_dialog.dart';
import '../../misc/change_notification_builder.dart';
import '../../pages/view_model_mixin.dart';
import '../../widgets/padded_card.dart';

class ExpandedLocationsDialog extends StatelessWidget {
  final Function(String) onSelection;
  final String? nullSelectionName;
  final Iterable<String>? ignoreLocationIds;

  const ExpandedLocationsDialog({super.key, required this.onSelection, this.nullSelectionName, this.ignoreLocationIds});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline6 = theme.textTheme.titleLarge;

    return ChangeNotifierBuilder<_ViewModel>(
        create: (context) => _ViewModel(ignoreLocationIds: ignoreLocationIds),
        builder: (context, viewModel, child) {
          if (viewModel.locations.isEmpty) return Container();
          return ConstrainedSearchDialog(
              autofocus: true,
              title: Text(AppLocalizations.of(context)!.locations, style: headline6),
              builder: (search) {
                final visibleLocations = <Location>[];
                if (nullSelectionName != null) {
                  visibleLocations.add(Location.create()
                    ..id = ''
                    ..name = nullSelectionName!);
                }
                visibleLocations.addAll(viewModel.locations.where((e) => _locationMatches(e, search)));
                return visibleLocations.map((e) => _Tile(location: e, locationSelected: onSelection));
              });
        });
  }

  bool _locationMatches(Location location, String search) {
    if (location.name.toLowerCase().contains(search.toLowerCase())) return true;
    if (location.address1.toLowerCase().contains(search.toLowerCase())) return true;
    if (location.address2.toLowerCase().contains(search.toLowerCase())) return true;
    if (location.city.toLowerCase().contains(search.toLowerCase())) return true;
    if (location.state.toLowerCase().contains(search.toLowerCase())) return true;
    if (location.zip.toLowerCase().contains(search.toLowerCase())) return true;
    return false;
  }
}

class _Tile extends StatelessWidget {
  const _Tile({required this.locationSelected, required this.location});

  final Location location;
  final Function(String) locationSelected;

  @override
  Widget build(BuildContext context) => GestureDetector(
        onTap: () {
          Navigator.of(context).pop();
          locationSelected(location.id);
        },
        child: PaddedCard(
          child: Center(
              child: Column(
            children: [
              Text(location.name),
              if (location.address1.isNotEmpty) Text(location.address1),
              if (location.address2.isNotEmpty) Text(location.address2),
              if (location.address3.isNotEmpty) Text(location.address3),
            ],
          )),
        ),
      );
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  Iterable<Location> locations = [];
  final Iterable<String>? ignoreLocationIds;

  _ViewModel({required this.ignoreLocationIds}) {
    addListenables([DataModel().locationModel]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    locations = await DataModel().locationModel.active;
    if (ignoreLocationIds != null) locations = locations.where((e) => !ignoreLocationIds!.contains(e.id));
    notifyListeners();
  }
}
