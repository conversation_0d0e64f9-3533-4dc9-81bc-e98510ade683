import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:the_punch_flutter/helpers/color_helper.dart';
import 'package:the_punch_flutter/state/punch_state.dart';

class JobTypeW extends StatelessWidget {
  final String jobTypeId;

  const JobTypeW({required this.jobTypeId});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final body1OnSecondary =
        textTheme.headlineSmall?.copyWith(color: ColorHelper.thePunchBlue());

    return Consumer<PunchState>(
      builder: (context, punchState, child) => Container(
        constraints: const BoxConstraints(maxWidth: 240),
        decoration: const BoxDecoration(
         // border: Border.all(color: ColorHelper.thePunchBlue(), width: 1.5),
          // borderRadius: const BorderRadius.all(
          //   Radius.circular(24),
          // ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(5),
          child: Center(
            child: Text(
              (punchState.currentTask?.toUpperCase() ??
                  punchState.jobTypeName?.toUpperCase() ??
                  'UNKNOWN123'),
              style: body1OnSecondary,
            ),
          ),
        ),
      ),
    );
  }
}

