// ignore_for_file: use_build_context_synchronously

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../api/api_model.dart';
import '../../dialogs/busy_dialog.dart';
import '../../dialogs/error_dialog.dart';
import '../../dialogs/message_dialog.dart';
import '../../helpers/screen_helper.dart';
import '../../misc/app_localization.dart';
import '../../misc/logging.dart';
import '../../widgets/decorated_text_field.dart';
import '../../widgets/padded_card.dart';
import '../web/my_scaffold.dart';

class ForgotLoginConfirmationPage extends StatelessWidget {
  final String organizationId;
  final String username;
  final String confirmationKey;

  ForgotLoginConfirmationPage(Map<String, String> queryParms, {super.key})
      : organizationId = queryParms['organizationId'] ?? '',
        username = queryParms['username'] ?? '',
        confirmationKey = queryParms['confirmationKey'] ?? '';

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider(
        create: (context) =>
            _ViewModel(organizationId, username, confirmationKey),
        child: MyScaffold(
          enableBottomBar: false,
          showLoggedOutDrawer: true,
          showDesktopHeader: false,
          title: AppLocalization.of(context).forgotLogin,
          body: const Center(
            child: Padding(
              padding: EdgeInsets.all(8),
              child: _Body(),
            ),
          ),
        ),
      );
}

class _Body extends StatelessWidget {
  const _Body();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline6 = theme.textTheme.titleLarge;
    return Consumer<_ViewModel>(
      builder: (context, viewModel, _) => ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 500),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            PaddedCard(
              child: Flex(
                mainAxisSize: MainAxisSize.min,
                direction: Axis.vertical,
                children: [
                  Text(AppLocalization.of(context).forgotLogin,
                      style: headline6),
                  SizedBox(
                    height: ScreenHelper.screenHeightPercentage(context, 2),
                  ),
                  DecoratedText(
                    padding: const EdgeInsets.all(8),
                    text: viewModel.username,
                    labelText: AppLocalization.of(context).username,
                  ),
                  DecoratedTextField(
                    padding: const EdgeInsets.all(8),
                    initialValue: viewModel.password,
                    obscureText: true,
                    labelText: AppLocalization.of(context).newPassword,
                    onChanged: (value) => viewModel.setPassword(value),
                    validator: (value) => !viewModel.validatePassword
                        ? AppLocalization.of(context).passwordRequired
                        : ' ',
                  ),
                  DecoratedTextField(
                    padding: const EdgeInsets.all(8),
                    initialValue: viewModel.confirmPassword,
                    obscureText: true,
                    labelText: AppLocalization.of(context).confirmPassword,
                    onChanged: (value) => viewModel.setConfirmPassword(value),
                    validator: (value) => viewModel.confirmPassword.isEmpty
                        ? AppLocalization.of(context).confirmPasswordRequired
                        : !viewModel.validateConfirmPassword
                            ? AppLocalization.of(context)
                                .confirmPasswordNotMatchPassword
                            : ' ',
                  ),
                ],
              ),
            ),
            ElevatedButton(
              onPressed: !viewModel.validateRequest
                  ? null
                  : () => unawaited(
                        _completeRequest(context, viewModel),
                      ),
              child: Text(AppLocalization.of(context).send),
            ),
          ],
        ),
      ),
    );
  }
}

Future<void> _completeRequest(
    BuildContext context, _ViewModel viewModel) async {
  await showDialog(
      context: context,
      builder: (context) {
        final router = GoRouter.of(context);
        return BusyDialog(future: () async {
          try {
            await ApiModel().forgotLoginConfirmation(
              organizationId: viewModel.organizationId,
              password: viewModel.password,
              confirmPassword: viewModel.confirmPassword,
              confirmationKey: viewModel.confirmationKey,
            );

            final prefs = await SharedPreferences.getInstance();
            await prefs.setString('organizationId', viewModel.organizationId);
            await prefs.setString('username', viewModel.username);
            if (!context.mounted) return;
            await showDialog(
                context: context,
                builder: (context) => MessageDialog(
                      title: AppLocalization.of(context).forgotLogin,
                      message: 'Password reset.',
                    ));
            router.go('/login');
          } on ApiException catch (e, stack) {
            await logApiException(e, stack);
            if (!context.mounted) return;
            await showDialog(
                context: context,
                builder: (context) => ErrorDialog(errorCode: e.errorCode));
          } on Exception catch (e, stack) {
            await logException(e, stack);
            if (!context.mounted) return;
            await showDialog(
                context: context, builder: (context) => const ErrorDialog());
          }
        });
      });
}

class _ViewModel extends ChangeNotifier {
  String organizationId;
  String username;
  String password = '';
  String confirmPassword = '';
  String confirmationKey;

  _ViewModel(this.organizationId, this.username, this.confirmationKey);

  bool get validatePassword => password.isNotEmpty;
  bool get validateConfirmPassword =>
      confirmPassword.isNotEmpty && password == confirmPassword;
  bool get validateRequest => validatePassword && validateConfirmPassword;

  void setPassword(String value) {
    password = value;
    notifyListeners();
  }

  void setConfirmPassword(String value) {
    confirmPassword = value;
    notifyListeners();
  }
}
