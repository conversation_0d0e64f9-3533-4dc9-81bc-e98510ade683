// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:printing/printing.dart';
import 'package:provider/provider.dart';
import 'package:universal_html/html.dart' as html;
import 'package:universal_html/js.dart' as js;

import '../../../api/api_model.dart';
import '../../../api/report_model.dart';
import '../../../api/requests/reports.dart';
import '../../../dialogs/error_dialog.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/app_localization.dart';
import '../../../misc/extensions.dart';
import '../../../misc/logging.dart';
import '../../../misc/my_platform.dart';
import '../../view_model_mixin.dart';
import '../../../widgets/date_time_picker.dart';
import '../../../widgets/decorated_text_field.dart';
import '../../../widgets/filter_chips.dart';
import '../my_scaffold.dart';

/// A simple Parameter model
class Parameter {
  final String name;
  final String prompt;
  final String uiControl;
  String value;
  DateTime? dateTime;
  Map<String, String> validValues;

  Parameter({
    required this.name,
    required this.prompt,
    required this.uiControl,
    this.value = '',
    this.validValues = const {},
    this.dateTime,
  });
}

/// A Report model representing each report's details
class Report {
  final String reportId;
  final String reportName;
  final List<Parameter> reportInputs;
  // Add other properties as needed

  Report({
    required this.reportId,
    required this.reportName,
    required this.reportInputs,
    // Initialize other properties
  });
}

/// A Manager class to handle all reports
class ReportManager {
  // Singleton pattern
  ReportManager._privateConstructor();

  static final ReportManager _instance = ReportManager._privateConstructor();

  factory ReportManager() {
    return _instance;
  }

  // Define all reports here
  final List<Report> reports = [
    Report(
      reportId: 'db46e81f-3b58-4d9b-91b8-2ced772a92f2',
      reportName: 'Employee Hours Worked',
      reportInputs: [
        Parameter(
          name: 'weekDate',
          prompt: 'Week Date',
          uiControl: 'Date',
        ),
      ],
    ),
    Report(
      reportId: 'fbb7bb12-3c62-4716-b7f8-3d34b6f4a8ea',
      reportName: 'Active Employee Locations',
      reportInputs: [
        Parameter(
          name: 'dateOfDay',
          prompt: 'Date Of Day',
          uiControl: 'DateTime',
        ),
        Parameter(
          name: 'utcOffset',
          prompt: 'UTC Offset',
          uiControl: 'Integer',
        ),
      ],
    ),
    Report(
      reportId: '3a5e1066-f227-4d99-b52a-5fbcdeafe985',
      reportName: 'Location Hours',
      reportInputs: [
        Parameter(
          name: 'weekDate',
          prompt: 'Week Date',
          uiControl: 'Date',
        ),
      ],
    ),
    Report(
      reportId: 'cd3b5463-ebec-4e89-bfc7-67a0d522c9df',
      reportName: 'User Sessions',
      reportInputs: [
        Parameter(
          name: 'startDate',
          prompt: 'Start Date',
          uiControl: 'Date',
        ),
        Parameter(
          name: 'endDate',
          prompt: 'End Date',
          uiControl: 'Date',
        ),
      ],
    ),
    Report(
      reportId: '355eb3ef-9103-41a0-a751-eaf246ac05f6',
      reportName: 'Active Managers',
      reportInputs: [
        Parameter(
          name: 'startDate',
          prompt: 'Start Date',
          uiControl: 'Date',
        ),
        Parameter(
          name: 'endDate',
          prompt: 'End Date',
          uiControl: 'Date',
        ),
        Parameter(
          name: 'utcOffset',
          prompt: 'UTC Offset',
          uiControl: 'Integer',
        ),
      ],
    ),
    //     Report(
    //   reportId: 'db36e81f-3b58-4d9b-91b8-2ced772a92f2',
    //   reportName: 'Test report',
    //   reportInputs: [
    //     Parameter(
    //       name: 'String',
    //       prompt: 'String',
    //       uiControl: 'String',
    //     ),
    //   ],
    // ),
    // Add more reports as needed
  ];

  /// Retrieves a report by its ID
  Report? getReportById(String reportId) {
    return reports.firstWhere(
      (report) => report.reportId == reportId,
      orElse: () => reports.first, // Fallback to the first report if not found
    );
  }
}

/// The ViewModel that manages report parameters and interactions
class _ViewModel extends ChangeNotifier with ViewModelMixin {
  String id;

  List<Parameter> parameters = [];
  Uint8List reportData = Uint8List(0);
  String fileName = '';
  String csvContent = '';
  Uint8List excelData = Uint8List(0);

  // Access the singleton ReportManager
  final ReportManager reportManager = ReportManager();

  _ViewModel(this.id) {
    debugPrint("RunReportPage ViewModel constructor: id=$id");
    selectReport(id);
  }

  @override
  Future<void> refresh() async {
    // Refresh the current report
    selectReport(id);
  }

  void selectReport(String reportKey) {
    debugPrint("selectReport called with reportKey=$reportKey");

    // Fetch the report using ReportManager
    final report = reportManager.getReportById(reportKey);

    if (report == null) {
      debugPrint(
          "reportKey not found, defaulting to '${reportManager.reports.first.reportName}'");
      id = reportManager.reports.first.reportId;
    } else {
      id = report.reportId;
    }

    // Make a fresh copy of the desired parameters
    final originalParams = reportManager.getReportById(id)?.reportInputs ?? [];
    parameters = originalParams
        .map((orig) => Parameter(
              name: orig.name,
              prompt: orig.prompt,
              uiControl: orig.uiControl,
              validValues: Map.of(orig.validValues),
            ))
        .toList();

    reportData = Uint8List(0);
    fileName = '';
    csvContent = '';
    excelData = Uint8List(0);
    notifyListeners();
  }

  // Access param values
  String getParameter(String name) => parameters
      .firstWhere((p) => p.name == name, orElse: () => parameters.first)
      .value;

  // Set param value
void setParameter(String name, String value) {
  debugPrint('Setting parameter: $name to $value');
  final param = parameters.firstWhere((p) => p.name == name, orElse: () => parameters.first);
  param.value = value;

  reportData = Uint8List(0);
  debugPrint('PDF data cleared');

  notifyListeners();
}

  // Access param's DateTime
  DateTime? getDateTimeParameter(String name) => parameters
      .firstWhere((p) => p.name == name, orElse: () => parameters.first)
      .dateTime;

  // Set param's DateTime
void setDateTimeParameter(String name, String displayValue, DateTime dt) {
  final param = parameters.firstWhere((p) => p.name == name, orElse: () => parameters.first);
  param.value = displayValue;
  param.dateTime = dt;

  // Clear the PDF data
  reportData = Uint8List(0);

  notifyListeners();
}

  // MultipleChoice
void setMultiSelectValues(String name, Iterable<String> labels) {
  final param = parameters.firstWhere((p) => p.name == name, orElse: () => parameters.first);
  final selectedKeys = param.validValues.entries
      .where((entry) => labels.contains(entry.value))
      .map((entry) => entry.key);
  param.value = selectedKeys.join(',');

  // Clear the PDF data
  reportData = Uint8List(0);

  notifyListeners();
}


  List<String> getMultiSelectValues(String name) {
    final param = parameters.firstWhere((p) => p.name == name,
        orElse: () => parameters.first);
    if (param.value.isEmpty) return [];
    final keys = param.value.split(',');
    return param.validValues.entries
        .where((entry) => keys.contains(entry.key))
        .map((entry) => entry.value)
        .toList();
  }

  //--- RUN REPORT -> server PDF
  Future<void> runReport() async {
    debugPrint("runReport called for id=$id");
    final paramValues = <String, String>{};
    for (final p in parameters) {
      if ((p.uiControl == 'Date' || p.uiControl == 'DateTime') &&
          p.dateTime != null) {
        if (p.uiControl == 'Date') {
          paramValues[p.name] = p.dateTime!.toIso8601String().split('T').first;
        } else {
          paramValues[p.name] = p.dateTime!.toIso8601String();
        }
      } else {
        paramValues[p.name] = p.value;
      }
    }

    try {
      final response = await ReportModel().runReport(id, paramValues, 'pdf');
      reportData = base64Decode(response.reportData);
      fileName = response.fileName;
      notifyListeners();
    } catch (e) {
      // Handle exceptions appropriately
      rethrow;
    }
  }

  //--- RUN PAYROLL -> server CSV
  Future<void> runPayroll() async {
    debugPrint("runPayroll called for id=$id");
    final paramValues = <String, String>{};
    for (final p in parameters) {
      if ((p.uiControl == 'Date' || p.uiControl == 'DateTime') &&
          p.dateTime != null) {
        if (p.uiControl == 'Date') {
          paramValues[p.name] = p.dateTime!.toIso8601String().split('T').first;
        } else {
          paramValues[p.name] = p.dateTime!.toIso8601String();
        }
      } else {
        paramValues[p.name] = p.value;
      }
    }

    try {
      final response = await ReportModel().runPayroll(id, paramValues, 'csv');
      csvContent = response.reportData;
      fileName = response.fileName;
      download(csvContent, fileName, mimeType: 'text/csv');
      notifyListeners();
    } catch (e) {
      // Handle exceptions appropriately
      rethrow;
    }
  }

  //--- EXPORT EXCEL -> server Excel
  Future<void> exportExcel() async {
    debugPrint("exportExcel called for id=$id");
    final paramValues = <String, String>{};
    for (final p in parameters) {
      if ((p.uiControl == 'Date' || p.uiControl == 'DateTime') &&
          p.dateTime != null) {
        if (p.uiControl == 'Date') {
          paramValues[p.name] = p.dateTime!.toIso8601String().split('T').first;
        } else {
          paramValues[p.name] = p.dateTime!.toIso8601String();
        }
      } else {
        paramValues[p.name] = p.value;
      }
    }

    try {
      // final response = await ReportModel().exportExcel(id, paramValues, 'xlsx');
      // excelData = base64Decode(response.reportData);
      // final excelFileName = response.fileName;
      // downloadExcel(excelData, excelFileName, mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      // notifyListeners();
    } catch (e) {
      // Handle exceptions appropriately
      rethrow;
    }
  }

  // CSV and Excel download for web
  void download(String content, String fileName,
      {String mimeType = 'text/csv'}) async {
    final blob = html.Blob([content], mimeType);
    final url = html.Url.createObjectUrl(blob);
    final doc = js.context['document'] as html.HtmlDocument;
    final link = html.AnchorElement(href: url)
      ..download = fileName
      ..style.display = 'none';
    doc.body?.append(link);
    link.click();
    link.remove();
    html.Url.revokeObjectUrl(url);
  }

  void downloadExcel(Uint8List bytes, String fileName,
      {String mimeType =
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'}) async {
    final blob = html.Blob([bytes], mimeType);
    final url = html.Url.createObjectUrl(blob);
    final doc = js.context['document'] as html.HtmlDocument;
    final link = html.AnchorElement(href: url)
      ..download = fileName
      ..style.display = 'none';
    doc.body?.append(link);
    link.click();
    link.remove();
    html.Url.revokeObjectUrl(url);
  }
}

/// The page that receives an `id` and loads the correct parameters.
/// There's **no** top bar with multiple buttons; it simply uses the local map to determine which report's inputs to show.
class RunReportPage extends StatelessWidget {
  final String id;

  /// For example, from `ReportsPage`:
  ///   context.pushNamed('/reports/runReports', queryParameters: {'id': 'REPORT_LOCATIONS_EMPLOYEES_ACTIVE'});
  /// Then `id` will be 'REPORT_LOCATIONS_EMPLOYEES_ACTIVE'
  RunReportPage(Map<String, String> queryParms, {super.key})
      : id = queryParms['id'] ?? '';

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider(
        create: (_) => _ViewModel(id),
        child: Scaffold(
          appBar: AppBar(
            title: Text(AppLocalization.of(context).reports),
            backgroundColor: ColorHelper.thePunchRed(),
            // Conditionally show leading icon if on small screen
            leading: MediaQuery.of(context).size.width < 800
                ? Builder(
                    builder: (context) => IconButton(
                      icon: const Icon(Icons.menu),
                      onPressed: () => Scaffold.of(context).openDrawer(),
                    ),
                  )
                : null,
          ),
          drawer:
              MediaQuery.of(context).size.width < 800 ? const _Sidebar() : null,
          body: const _Body(),
        ),
      );
}

/// The main body showing:
/// - A sidebar with report buttons (on large screens)
/// - A main content area with parameter inputs, action buttons, and PDF preview
class _Body extends StatelessWidget {
  const _Body();

  // Define custom colors
  static const Color selectedBackgroundColor = Color(0xFFFFCDD2); // Light Red
  static const Color selectedTextColor = Colors.red; // Red
  static const Color hoverBackgroundColor =
      Color(0xFFFFEBEE); // Lighter Red for hover

  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
        builder: (context, viewModel, _) => LayoutBuilder(
          builder: (context, constraints) {
            // Determine if the screen is wide enough to display inputs in a row
            bool isWide = constraints.maxWidth > 800;

            return Row(
              children: [
                // Sidebar (only on large screens)
                if (isWide) const _Sidebar(),
                // Main Content
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          // Card with inputs & action buttons
                          Card(
                            elevation: 4,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  // Display parameter widgets with dividers
                                  isWide
                                      ? Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: _buildWideInputs(
                                              context, viewModel),
                                        )
                                      : Column(
                                          children:
                                              viewModel.parameters.map((p) {
                                            Widget widget;
                                            switch (p.uiControl) {
                                              case 'Boolean':
                                                widget =
                                                    buildBoolean(p, viewModel);
                                                break;
                                              case 'Date':
                                                widget = buildDate(
                                                    context, p, viewModel);
                                                break;
                                              case 'DateTime':
                                                widget = buildDateTime(
                                                    context, p, viewModel);
                                                break;
                                              case 'String':
                                                widget =
                                                    buildString(p, viewModel);
                                                break;
                                              case 'Dropdown':
                                                widget =
                                                    buildDropdown(p, viewModel);
                                                break;
                                              case 'MultipleChoice':
                                                widget = buildMultipleChoice(
                                                    context, p, viewModel);
                                                break;
                                              case 'Float':
                                                widget =
                                                    buildFloat(p, viewModel);
                                                break;
                                              case 'Integer':
                                                widget =
                                                    buildInteger(p, viewModel);
                                                break;
                                              default:
                                                widget =
                                                    buildString(p, viewModel);
                                            }
                                            return Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 8.0),
                                              child: Column(
                                                children: [
                                                  widget,
                                                  const SizedBox(height: 8),
                                                  const Divider(thickness: 1),
                                                ],
                                              ),
                                            );
                                          }).toList(),
                                        ),
                                  const SizedBox(height: 16),
                                  // Action buttons row
                                  Wrap(
                                    spacing: 12,
                                    runSpacing: 12,
                                    alignment: WrapAlignment.end,
                                    children: [
                                      // RUN REPORT (PDF)
                                      ElevatedButton.icon(
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor:
                                              Colors.green.shade700,
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 12, horizontal: 16),
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                        ),
                                        icon: const Icon(Icons.play_arrow),
                                        label: Text(
                                          AppLocalization.of(context)
                                              .runReport
                                              .toUpperCase(),
                                          style: const TextStyle(
                                              fontWeight: FontWeight.bold),
                                        ),
                                        onPressed: () async {
                                          try {
                                            await viewModel.runReport();
                                          } on ApiException catch (e, stack) {
                                            await logApiException(e, stack);
                                            if (!context.mounted) return;
                                            await showDialog(
                                              context: context,
                                              builder: (ctx) => ErrorDialog(
                                                  errorCode: e.errorCode),
                                            );
                                          } on Exception catch (e, stack) {
                                            await logException(e, stack);
                                            if (!context.mounted) return;
                                            await showDialog(
                                              context: context,
                                              builder: (ctx) =>
                                                  const ErrorDialog(),
                                            );
                                          }
                                        },
                                      ),
                                      // RUN PAYROLL (CSV)
                                      ElevatedButton.icon(
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: const Color.fromARGB(
                                              255, 207, 56, 45),
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 12, horizontal: 16),
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                        ),
                                        icon: const Icon(Icons.download),
                                        label: Text(AppLocalization.of(context)
                                            .downloadPayroll),
                                        onPressed: () async {
                                          try {
                                            await viewModel.runPayroll();
                                          } on ApiException catch (e, stack) {
                                            await logApiException(e, stack);
                                            if (!context.mounted) return;
                                            await showDialog(
                                              context: context,
                                              builder: (ctx) => ErrorDialog(
                                                  errorCode: e.errorCode),
                                            );
                                          } on Exception catch (e, stack) {
                                            await logException(e, stack);
                                            if (!context.mounted) return;
                                            await showDialog(
                                              context: context,
                                              builder: (ctx) =>
                                                  const ErrorDialog(),
                                            );
                                          }
                                        },
                                      ),
                                      // EXPORT EXCEL
                                      ElevatedButton.icon(
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor:
                                              Colors.orange.shade700,
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 12, horizontal: 16),
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                        ),
                                        icon: const Icon(Icons.grid_on),
                                        label: const Text(
                                          'EXPORT EXCEL',
                                          style: TextStyle(
                                              fontWeight: FontWeight.bold),
                                        ),
                                        onPressed: () async {
                                          try {
                                            await viewModel.exportExcel();
                                          } on ApiException catch (e, stack) {
                                            await logApiException(e, stack);
                                            if (!context.mounted) return;
                                            await showDialog(
                                              context: context,
                                              builder: (ctx) => ErrorDialog(
                                                  errorCode: e.errorCode),
                                            );
                                          } on Exception catch (e, stack) {
                                            await logException(e, stack);
                                            if (!context.mounted) return;
                                            await showDialog(
                                              context: context,
                                              builder: (ctx) =>
                                                  const ErrorDialog(),
                                            );
                                          }
                                        },
                                      ),
                                      // CANCEL/BACK
                                      OutlinedButton.icon(
                                        style: OutlinedButton.styleFrom(
                                          foregroundColor: Colors.black87,
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 12, horizontal: 16),
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                          side: const BorderSide(
                                              color: Colors.black26),
                                        ),
                                        icon: const Icon(Icons.arrow_back),
                                        onPressed: () =>
                                            Navigator.of(context).pop(),
                                        label: const Text('Cancel/Back'),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 24),
                          // PDF preview below
                          Card(
                            elevation: 4,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Container(
                                height:
                                    700, // Adjusted height for a taller PDF preview
                                child: _buildPdfPreview(context, viewModel),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      );

  /// Builds inputs for wide layout with vertical dividers
  List<Widget> _buildWideInputs(BuildContext context, _ViewModel viewModel) {
    List<Widget> widgets = [];
    for (int i = 0; i < viewModel.parameters.length; i++) {
      final p = viewModel.parameters[i];
      Widget widget;
      switch (p.uiControl) {
        case 'Boolean':
          widget = buildBoolean(p, viewModel);
          break;
        case 'Date':
          widget = buildDate(context, p, viewModel);
          break;
        case 'DateTime':
          widget = buildDateTime(context, p, viewModel);
          break;
        case 'String':
          widget = buildString(p, viewModel);
          break;
        case 'Dropdown':
          widget = buildDropdown(p, viewModel);
          break;
        case 'MultipleChoice':
          widget = buildMultipleChoice(context, p, viewModel);
          break;
        case 'Float':
          widget = buildFloat(p, viewModel);
          break;
        case 'Integer':
          widget = buildInteger(p, viewModel);
          break;
        default:
          widget = buildString(p, viewModel);
      }

      widgets.add(Expanded(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: widget,
        ),
      ));

      // Add vertical divider if not the last widget
      if (i != viewModel.parameters.length - 1) {
        widgets.add(Container(
          height: 60,
          width: 1,
          color: Colors.grey.shade300,
        ));
      }
    }
    return widgets;
  }

Widget _buildPdfPreview(BuildContext context, _ViewModel vm) {
  debugPrint('Building PDF preview, reportData size: ${vm.reportData.length}');
  if (vm.reportData.isEmpty) {
    debugPrint('No PDF data to display');
    return Center(
      child: Text(
        'No PDF to show yet.\nRun a report to preview!',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Colors.grey.shade600,
          fontSize: 16,
        ),
      ),
    );
  }

  return Column(
    crossAxisAlignment: CrossAxisAlignment.stretch,
    children: [
      Expanded(
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(6),
            child: PdfPreview(
              build: (format) => vm.reportData,
              useActions: false,
            ),
          ),
        ),
      ),
      const SizedBox(height: 8),
      if (MyPlatform.isWeb)
        Padding(
          padding: const EdgeInsets.all(8),
          child: ElevatedButton.icon(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade300,
              foregroundColor: Colors.black87,
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            icon: const Icon(Icons.download),
            label: Text(AppLocalization.of(context).downloadReport),
            onPressed: () => _downloadPdf(vm.reportData, vm.fileName),
          ),
        ),
    ],
  );
}

  //--- Builders for each parameter type ---

  Widget buildBoolean(Parameter p, _ViewModel vm) => Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade200,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            const Icon(Icons.check_box, color: Colors.grey),
            const SizedBox(width: 8),
            Expanded(
              child: Row(
                children: [
                  Checkbox(
                    value: vm.getParameter(p.name) == 'true',
                    onChanged: (val) => vm.setParameter(p.name, val.toString()),
                  ),
                  Text(
                    p.prompt,
                    style: const TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
          ],
        ),
      );

  Widget buildDate(BuildContext context, Parameter p, _ViewModel vm) {
    final locale = Localizations.localeOf(context);
    final TextEditingController _controller =
        TextEditingController(text: p.value);

    // Listen to parameter changes and update the controller accordingly
    // This ensures that the TextField reflects the latest value
    vm.addListener(() {
      if (_controller.text != p.value) {
        _controller.text = p.value;
      }
    });

    return Container(
      padding: const EdgeInsets.all(8),
      child: Row(
        children: [
          const Icon(Icons.calendar_today, color: Colors.grey),
          const SizedBox(width: 8),
          Expanded(
            child: InkWell(
              onTap: () async {
                final picked = await showDatePicker(
                  context: context,
                  builder: (ctx, child) => Theme(
                    data: ThemeData.light().copyWith(
                      colorScheme: ColorScheme.light(
                        primary: ColorHelper.thePunchRed(),
                      ),
                    ),
                    child: child!,
                  ),
                  initialDate: p.dateTime ?? DateTime.now(),
                  firstDate:
                      DateTime.now().subtract(const Duration(days: 3650)),
                  lastDate: DateTime.now().add(const Duration(days: 3650)),
                );
                if (picked != null) {
                  vm.setDateTimeParameter(
                    p.name,
                    picked.toFormattedDateWithYear(locale),
                    picked,
                  );
                }
              },
              child: TextField(
                controller: _controller,
                decoration: InputDecoration(
                  labelText: p.prompt,
                  suffixIcon: const Icon(
                    Icons.calendar_today,
                    size: 20,
                  ),
                  border: const OutlineInputBorder(),
                ),
                readOnly: true,
                onTap: () async {
                  final picked = await showDatePicker(
                    context: context,
                    builder: (ctx, child) => Theme(
                      data: ThemeData.light().copyWith(
                        colorScheme: ColorScheme.light(
                          primary: ColorHelper.thePunchRed(),
                        ),
                      ),
                      child: child!,
                    ),
                    initialDate: p.dateTime ?? DateTime.now(),
                    firstDate:
                        DateTime.now().subtract(const Duration(days: 3650)),
                    lastDate: DateTime.now().add(const Duration(days: 3650)),
                  );
                  if (picked != null) {
                    vm.setDateTimeParameter(
                      p.name,
                      picked.toFormattedDateWithYear(locale),
                      picked,
                    );
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildDateTime(BuildContext context, Parameter p, _ViewModel vm) {
    final locale = Localizations.localeOf(context);
    final TextEditingController _controller =
        TextEditingController(text: p.value);

    // Listen to parameter changes and update the controller accordingly
    // This ensures that the TextField reflects the latest value
    vm.addListener(() {
      if (_controller.text != p.value) {
        _controller.text = p.value;
      }
    });

    return Container(
      padding: const EdgeInsets.all(8),
      child: Row(
        children: [
          const Icon(Icons.access_time, color: Colors.grey),
          const SizedBox(width: 8),
          Expanded(
            child: DateTimePicker(
              hint: p.prompt,
              initialValue: vm.getDateTimeParameter(p.name),
              onSelected: (val) {
                vm.setDateTimeParameter(
                  p.name,
                  val.toFormattedDateTimeWithYear(locale),
                  val,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget buildString(Parameter p, _ViewModel vm) => Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade200,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            const Icon(Icons.text_fields, color: Colors.grey),
            const SizedBox(width: 8),
            Expanded(
              child: DecoratedTextField(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                initialValue: vm.getParameter(p.name),
                labelText: p.prompt,
                onChanged: (val) => vm.setParameter(p.name, val),
              ),
            ),
          ],
        ),
      );

  Widget buildDropdown(Parameter p, _ViewModel vm) {
    if (p.validValues.isEmpty) return const SizedBox();
    final current = p.value.isNotEmpty ? p.value : p.validValues.keys.first;

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(Icons.arrow_drop_down_circle, color: Colors.grey),
          const SizedBox(width: 8),
          Expanded(
            child: DecoratedContainer(
              labelText: p.prompt,
              centered: false,
              child: DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  border: InputBorder.none,
                ),
                isExpanded: true,
                onChanged: (val) {
                  if (val != null) vm.setParameter(p.name, val);
                },
                value: current,
                items: p.validValues.entries
                    .map((e) => DropdownMenuItem<String>(
                          value: e.key,
                          child: Text(e.value),
                        ))
                    .toList(),
                icon: const Icon(Icons.arrow_drop_down),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildMultipleChoice(BuildContext context, Parameter p, _ViewModel vm) {
    if (p.validValues.isEmpty) return const SizedBox();
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(Icons.select_all, color: Colors.grey),
          const SizedBox(width: 8),
          Expanded(
            child: FilterChips2(
              onSelected: (vals) => vm.setMultiSelectValues(p.name, vals),
              hint: p.prompt,
              labels: p.validValues.values,
            ),
          ),
        ],
      ),
    );
  }

  Widget buildFloat(Parameter p, _ViewModel vm) => Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade200,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            const Icon(Icons.attach_money, color: Colors.grey),
            const SizedBox(width: 8),
            Expanded(
              child: DecoratedTextField(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                initialValue: vm.getParameter(p.name),
                labelText: p.prompt,
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                onChanged: (val) => vm.setParameter(p.name, val),
                inputFormatters: <TextInputFormatter>[
                  FilteringTextInputFormatter.allow(
                      RegExp(r'^[+-]?[0-9]*\.?[0-9]*')),
                ],
              ),
            ),
          ],
        ),
      );

  Widget buildInteger(Parameter p, _ViewModel vm) => Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade200,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            const Icon(Icons.alarm_sharp, color: Colors.grey),
            const SizedBox(width: 8),
            Expanded(
              child: DecoratedTextField(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                initialValue: vm.getParameter(p.name),
                labelText: p.prompt,
                keyboardType: TextInputType.number,
                onChanged: (val) => vm.setParameter(p.name, val),
                inputFormatters: <TextInputFormatter>[
                  FilteringTextInputFormatter.allow(RegExp(r'^[+-]?[0-9]*')),
                ],
              ),
            ),
          ],
        ),
      );

  // Download PDF (web only)
  void _downloadPdf(Uint8List bytes, String fileName) async {
    final blobFile = html.Blob([bytes], 'application/pdf');
    final pdfUrl = html.Url.createObjectUrl(blobFile);
    final doc = js.context['document'] as html.HtmlDocument;
    final link = html.AnchorElement(href: pdfUrl)
      ..download = fileName
      ..style.display = 'none';
    doc.body?.append(link);
    link.click();
    link.remove();
    html.Url.revokeObjectUrl(pdfUrl);
  }
}

/// A helper container to decorate widgets
class DecoratedContainer extends StatelessWidget {
  final String labelText;
  final bool centered;
  final Widget child;

  const DecoratedContainer({
    Key? key,
    required this.labelText,
    this.centered = false,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => Column(
        crossAxisAlignment:
            centered ? CrossAxisAlignment.center : CrossAxisAlignment.start,
        children: [
          Text(
            labelText,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: child,
          ),
        ],
      );
}

/// Sidebar Widget
class _Sidebar extends StatelessWidget {
  const _Sidebar();

  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
        builder: (context, viewModel, _) => Container(
          width: 250,
          color: Colors.grey.shade100,
          child: Column(
            children: [
              const SizedBox(height: 20),
              // Sidebar Header
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  AppLocalization.of(context).recurring,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Divider(),
              // Report Buttons
              Expanded(
                child: ListView(
                  children: viewModel.reportManager.reports.map((report) {
                    final reportKey = report.reportId;
                    final isSelected = viewModel.id == reportKey;

                    return MouseRegion(
                      cursor: SystemMouseCursors.click,
                      child: Container(
                        decoration: BoxDecoration(
                          color: isSelected
                              ? _Body.selectedBackgroundColor
                              : Colors.transparent,
                        ),
                        child: ListTile(
                          leading: Icon(
                            Icons.insert_drive_file,
                            color: isSelected
                                ? _Body.selectedTextColor
                                : Colors.grey,
                          ),
                          title: Text(
                            report.reportName,
                            style: TextStyle(
                              color: isSelected
                                  ? _Body.selectedTextColor
                                  : Colors.black,
                              fontWeight: isSelected
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                            ),
                          ),
                          selected: isSelected,
                          hoverColor: _Body.hoverBackgroundColor,
                          onTap: () {
                            // Close drawer if open (on small screens)
                            if (Scaffold.of(context).isDrawerOpen) {
                              Navigator.of(context).pop();
                            }
                            viewModel.selectReport(reportKey);
                          },
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
      );
}
