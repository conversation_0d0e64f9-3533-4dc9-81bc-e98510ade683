import 'dart:async';
import 'dart:io' as io;
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'api_model.dart';
import 'requests/system.dart';
import '../misc/my_platform.dart';
import '../state/app_state.dart';

class AssetsModel {
  String get baseAddress => ApiModel().baseAddress;
  final _basePath = 'api/assets/';

  ValueNotifier<String?> get logoutErrorCode => ApiModel().logoutErrorCode;
  ValueNotifier<DateTime?> get serverTime => ApiModel().serverTime;

  static AssetsModel? _singleton;

  factory AssetsModel() {
    _singleton ??= AssetsModel._();
    return _singleton!;
  }

  AssetsModel._();

  Future<String?> ping() async {
    const endPoint = 'ping';
    final uri = ApiModel.production ? Uri.https(baseAddress, _basePath + endPoint) : Uri.http(baseAddress, _basePath + endPoint);
    return http.get(uri).then((response) {
      if (response.statusCode == 200) return response.body;
      return Future.error('unknown response ${response.statusCode}');
    });
  }

  Future<Uint8List> getBytes(Uri uri) async {
    final response = await http.get(uri);
    if (response.statusCode != 200) throw Exception('unknown response ${response.statusCode}');
    return response.bodyBytes;
  }

  Future<Map<String, dynamic>?> post(String endPoint, request) async {
    final body = jsonEncode(request);
    final headers = {
      'content-type': 'application/json',
    };
    final uri = ApiModel.production ? Uri.https(baseAddress, _basePath + endPoint) : Uri.http(baseAddress, _basePath + endPoint);
    final response = await http.post(uri, body: body, headers: headers);
    if (response.statusCode != 200) throw Exception('unknown response ${response.statusCode}');
    return jsonDecode(response.body) as Map<String, dynamic>;
  }

  Future<Map<String, dynamic>?> postMultipart(String endPoint, request, Iterable<http.MultipartFile> files) async {
    final body = jsonEncode(request);
    final uri = ApiModel.production ? Uri.https(baseAddress, _basePath + endPoint) : Uri.http(baseAddress, _basePath + endPoint);
    final multipartRequest = http.MultipartRequest('POST', uri);
    multipartRequest.files.add(http.MultipartFile.fromString('SystemRequest', body, contentType: MediaType('application', 'json')));
    multipartRequest.files.addAll(files);
    final response = await multipartRequest.send();
    if (response.statusCode != 200) throw Exception('unknown response ${response.statusCode}');
    return jsonDecode(await response.stream.bytesToString()) as Map<String, dynamic>;
  }

  Future<SystemResponse> sendImages(Map<String, String> imageFileMap) async {
    try {
      final systemRequest = await SystemRequest.create();
      final contentType = MediaType('image', 'jpeg');
      final files = await Future.wait(imageFileMap.entries.map((e) => http.MultipartFile.fromPath(e.key, e.value, contentType: contentType)));
      final map = await postMultipart('PostAssets', systemRequest, files);
      if (map == null) throw Exception('bad server response');
      final response = SystemResponse.fromJson(map);
      if (response.isError) {
        if (response.isLogoutError) logoutErrorCode.value = response.errorCode;
        throw Exception(response.errorCode.toString());
      }
      return response;
    } catch (e) {
      // print('$e\n${StackTrace.current}');
      rethrow;
    }
  }

  Future<Image?> getImage(String uuid) async {
    try {
      final systemRequest = await SystemRequest.create();
      final parameters = {
        'serverIP': systemRequest.serverIP,
        'databaseName': systemRequest.databaseName,
        'sessionId': systemRequest.sessionId,
        'uuid': uuid,
      };
      final uri = ApiModel.production ? Uri.https(baseAddress, '${_basePath}GetAsset', parameters) : Uri.http(baseAddress, '${_basePath}GetAsset', parameters);

      if (!MyPlatform.isWeb) {
        final fileName = '${AppState().applicationSupportDirectory.path}/$uuid.jpg';
        final file = io.File(fileName);
        if (await file.exists() && await file.length() > 128) return Image.file(file);

        final bytes = await getBytes(uri);

        if (await file.exists()) {
          await file.delete();
        }
        await file.create();
        await file.writeAsBytes(bytes);
        // print(file.length());
        return Image.file(file);
      } else {
        return Image.network(uri.toString());
      }
    } catch (e) {
      // print('$e\n${StackTrace.current}');
      return null;
    }
  }
}
