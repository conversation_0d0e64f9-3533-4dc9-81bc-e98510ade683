// import 'package:flutter/material.dart';
// import 'package:provider/provider.dart';
// import 'package:the_punch_flutter/api/reportFlutterModel.dart';
// import 'package:the_punch_flutter/api/requests/reportLocationHours.dart';
// import 'package:the_punch_flutter/menus/myAppBar.dart';
// import 'package:the_punch_flutter/misc/appLocalization.dart';
// import 'package:the_punch_flutter/pages/transitionMixin.dart';
// import 'package:the_punch_flutter/pdf/reportLocationHoursGenerator.dart';
// import 'package:the_punch_flutter/pdf/pdfWidget.dart';
// import 'package:the_punch_flutter/state/appState.dart';
// import 'package:the_punch_flutter/widgets/decoratedTextField.dart';
// import 'package:the_punch_flutter/misc/extensions.dart';

// class ReportLocationHoursPage extends MaterialPage with NoTransitionMixin {
//   ReportLocationHoursPage() : super(key: ValueKey('ReportLocationHoursPage'), child: _Scaffold());
// }

// class _Scaffold extends StatelessWidget {
//   const _Scaffold();

//   @override
//   Widget build(BuildContext context) {
//     return ChangeNotifierProvider<_ViewModel>(
//         create: (context) => _ViewModel(context),
//         child: MyScaffold(
//           appBar: MyAppBar(
//             AppLocalization.of(context).reportLocationsHoursWorked,
//             breadcrumbs: {
//               AppLocalization.of(context).reports: () => AppState().replaceStates([PageState.reports()]),
//               AppLocalization.of(context).reportLocationsHoursWorked: null,
//             },
//           ).appBar,
//           body: _Body(),
//         ));
//   }
// }

// class _Body extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return Center(
//         child: Column(children: [
//       _Row1(),
//       Expanded(child: _Row2()),
//     ]));
//   }
// }

// class _Row1 extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return Consumer<_ViewModel>(builder: (context, viewModel, _) {
//       return ConstrainedBox(
//           constraints: BoxConstraints(maxWidth: 200),
//           child: Padding(
//               padding: const EdgeInsets.all(8.0),
//               child: InkWell(
//                   onTap: () async {
//                     final pickedDate = await showDatePicker(
//                       context: context,
//                       initialDate: viewModel.weekDate,
//                       firstDate: DateTime.utc(DateTime.now().year - 20),
//                       lastDate: DateTime.utc(DateTime.now().year + 20),
//                       selectableDayPredicate: (day) => day.weekday == 1,
//                     );
//                     if (pickedDate != null) await viewModel.setWeekDate(context, pickedDate);
//                   },
//                   child: DecoratedText(
//                     padding: const EdgeInsets.all(8.0),
//                     text: viewModel.weekDate.toFormattedDateWithYear(context),
//                     labelText: AppLocalization.of(context).week,
//                     filled: true,
//                     centered: true,
//                   ))));
//     });
//   }
// }

// class _Row2 extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return Consumer<_ViewModel>(builder: (context, viewModel, _) {
//       if (viewModel.noResultsFound) return Center(child: Text(AppLocalization.of(context).noResultsFound));
//       if (viewModel.entities.isEmpty) return Container();
//       return PdfWidget(
//         pdfGenerator: ReportLocationHoursGenerator(context: context, dateRange: viewModel.dateRange, entities: viewModel.entities),
//         filename: viewModel.filename,
//       );
//     });
//   }
// }

// class _ViewModel extends ChangeNotifier {
//   var _weekDate = DateTime.now().startOfWeek;

//   var dateRange = '';
//   var filename = '';
//   var noResultsFound = false;
//   var entities = <ReportLocationHoursEntity>[];

//   _ViewModel(BuildContext context) {
//     setWeekDate(context, _weekDate);
//   }

//   DateTime get weekDate => _weekDate;

//   Future<void> setWeekDate(BuildContext context, DateTime value) async {
//     _weekDate = value;
//     final response = await ReportModel().reportLocationHours(weekDate);
//     entities = response.entities ?? [];
//     dateRange = weekDate.toFormattedDate(context) + ' - ' + weekDate.addDays(6).toFormattedDateWithYear(context);
//     filename = AppLocalization.of(context).reportLocationsHoursWorked + '.pdf';
//     noResultsFound = response.entities == null;
//     notifyListeners();
//   }
// }
