import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../helpers/color_helper.dart';
import '../../misc/extensions.dart';
import '../menu/aligned_popup_menu_button.dart';
import 'calendar_widget.dart';

class CalendarMonthWidget extends StatelessWidget {
  final Future<Iterable<CalendarEvent>> Function(
      BuildContext context, DateTime start, DateTime end) builder;
  final bool scrollable;
  final Widget suffix;
  final double height;

  const CalendarMonthWidget({
    super.key,
    required this.builder,
    required this.suffix,
    this.scrollable = true,
    required this.height,
  });

  @override
  Widget build(BuildContext context) => Column(
        children: [
         // _Header(suffix: suffix),
          Expanded(child: _Body(builder: builder, scrollable: scrollable)),
        ],
      );
}

class _Header extends StatelessWidget {
  final Widget suffix;

  const _Header({required this.suffix});

  @override
  Widget build(BuildContext context) => Consumer<CalendarViewModel>(
        builder: (context, viewModel, child) => Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CalendarHeaderPrefix(
              previousPage: () => viewModel.goToPreviousMonth(context),
              nextPage: () => viewModel.goToNextMonth(context),
              today: () => viewModel.setDate(DateTime.now().dateOnly),
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                DropdownButton<int>(
                  onChanged: (value) {
                    if (value != null && value != viewModel.date.month) {
                      viewModel.setDate(DateTime(viewModel.date.year, value));
                    }
                  },
                  value: viewModel.date.month,
                  items: List.generate(12, (i) => i + 1)
                      .map((e) => DropdownMenuItem<int>(
                            value: e,
                            child: Text(
                              DateFormat(DateFormat.MONTH).format(
                                DateTime(2021, e),
                              ),
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ))
                      .toList(),
                ),
                DropdownButton<int>(
                  onChanged: (value) {
                    if (value != null && value != viewModel.date.year) {
                      viewModel.setDate(DateTime(value, viewModel.date.month));
                    }
                  },
                  value: viewModel.date.year,
                  items: List.generate(40, (i) => i + 1)
                      .map(
                        (e) => DropdownMenuItem<int>(
                          value: e + 2000,
                          child: Text(
                            (e + 2000).toString(),
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ),
                      )
                      .toList(),
                ),
              ],
            ),
            suffix,
          ],
        ),
      );
}

class _Body extends StatefulWidget {
  final Future<Iterable<CalendarEvent>> Function(
      BuildContext context, DateTime start, DateTime end) builder;
  final bool scrollable;

  const _Body({required this.builder, this.scrollable = true});

  @override
  _BodyState createState() => _BodyState();
}

class _BodyState extends State<_Body> {
  static const _initialPage = 1073741824;
  final _controller = PageController(initialPage: _initialPage);
  final DateTime _initialMonth = DateTime.now().startOfMonth;

  @override
  void initState() {
    super.initState();
    _controller.addListener(() {
      if (_controller.page != null &&
          _controller.page!.toInt() == _controller.page) {
        final offset = _controller.page!.toInt() - _initialPage;
        final month = _initialMonth.addMonths(offset);
        final viewModel = context.read<CalendarViewModel>();
        if (viewModel.date.startOfMonth != month) viewModel.setDate(month);
      }
    });
  }

  @override
  Widget build(BuildContext context) =>
      Consumer<CalendarViewModel>(builder: (context, viewModel, _) {
        if (_controller.hasClients) {
          final offset =
              DateUtils.monthDelta(_initialMonth, viewModel.date.startOfMonth);
          final page = _initialPage + offset;
          if (page != _controller.page) {
            if ((page - _controller.page!).abs() > 2) {
              _controller.jumpToPage(page);
            } else {
              unawaited(_controller.animateToPage(page,
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.ease));
            }
          }
        }
        return PageView.builder(
            physics:
                widget.scrollable ? null : const NeverScrollableScrollPhysics(),
            controller: _controller,
            itemBuilder: (context, index) {
              final offset = index - _initialPage;
              final month = _initialMonth.addMonths(offset);
              return _Page(month: month, builder: widget.builder);
            });
      });
}

class _Page extends StatelessWidget {
  final DateTime month;
  final Future<Iterable<CalendarEvent>> Function(
      BuildContext context, DateTime start, DateTime end) builder;

  const _Page({required this.month, required this.builder});

  @override
  Widget build(BuildContext context) {
    final weekStartOffset = month.weekday != 0 ? month.weekday - 1 : 7;
    final weekStart = DateUtils.addDaysToDate(month, -(weekStartOffset));
    final rows = (month.difference(weekStart).inDays +
            DateUtils.getDaysInMonth(month.year, month.month)) /
        7;
    final controller = ScrollController();

    return Flex(direction: Axis.vertical, children: [
      _WeekRow(weekStart: weekStart),
      Expanded(
        child: SingleChildScrollView(
          controller: controller,
          child: ConstrainedBox(
            constraints: const BoxConstraints(
                maxHeight: CalendarViewModel.monthPageHeight),
            child: Flex(
              direction: Axis.vertical,
              children: [
                for (int row = 0; row < rows; row++)
                  _Row(
                    month: month,
                    weekStart: weekStart,
                    row: row,
                    builder: builder,
                  )
              ],
            ),
          ),
        ),
      )
    ]);
  }
}

class _Row extends StatelessWidget {
  final DateTime month;
  final DateTime weekStart;
  final int row;
  final Future<Iterable<CalendarEvent>> Function(
      BuildContext context, DateTime start, DateTime end) builder;

  const _Row(
      {required this.month,
      required this.weekStart,
      required this.row,
      required this.builder});

  @override
  Widget build(BuildContext context) => Flexible(
          child: Flex(direction: Axis.horizontal, children: [
        for (int col = 0; col < 7; col++)
          Expanded(
              child: Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!, width: 0.5),
          ),
          child: _Tile(
            month,
            DateUtils.addDaysToDate(weekStart, row * 7 + col),
            builder,
          ),
              ))
      ]));
}

class _WeekRow extends StatelessWidget {
  const _WeekRow({required this.weekStart});

  final DateTime weekStart;

  @override
  Widget build(BuildContext context) =>
      Flex(direction: Axis.horizontal, children: [
        for (int col = 0; col < 7; col++)
          Expanded(child: _WeekTile(DateUtils.addDaysToDate(weekStart, col))),
      ]);
}

class _WeekTile extends StatelessWidget {
  final DateTime date;

  const _WeekTile(this.date);

  @override
  Widget build(BuildContext context) => Center(
        child: Text(
          DateFormat(DateFormat.ABBR_WEEKDAY).format(date),
          style: Theme.of(context).textTheme.bodySmall,
        ),
      );
}

class _Tile extends StatelessWidget {
  final DateTime month;
  final DateTime date;
  final Future<Iterable<CalendarEvent>> Function(
      BuildContext context, DateTime start, DateTime end) builder;

  const _Tile(this.month, this.date, this.builder);

  @override
  Widget build(BuildContext context) {
    final isInMonth = month.month == date.month;
    final theme = Theme.of(context);
    final onSurface = theme.colorScheme.onSurface;
    final caption = theme.textTheme.bodySmall;
    final style = caption?.copyWith(
        color: isInMonth ? onSurface : onSurface.withAlpha(64));

    return FutureBuilder<Iterable<CalendarEvent>>(
        future:
            Future(() async => await builder(context, date, date.addDays(1))),
        builder: (context, snapshot) {
          final events = snapshot.data ?? [];
            return Card(
              elevation: 0,
            color: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.all(2),
                child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                    Center(
                    child: Text(DateFormat(DateFormat.DAY).format(date),style: style)
                    ),
                  if (events.isNotEmpty)
                  Expanded(
                    child: SingleChildScrollView(
                    child: ClipRect(
                      child: Wrap(
                      direction: Axis.vertical,
                        children: [
                        ...events.take(4).map((event) => _EventTile(event)),
                        if (events.length > 4)
                            Padding(
                            padding: const EdgeInsets.all(2),
                            child: Text('${events.length - 4} more',
                              style: Theme.of(context).textTheme.bodySmall),
                            ),
                        ],
                      ),
                    ),
                    ),
                  ),
                ],
                ),
              ),
            );
        });
  }
}

class _EventTile extends StatelessWidget {
  final CalendarEvent event;

  const _EventTile(this.event);

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);
    final start = event.start.toFormattedTime(locale);


    Widget child = Padding(
      padding: const EdgeInsets.fromLTRB(2, 2, 2, 2),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(8), // Set the desired radius
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            '$start ${event.text}',
            style: Theme.of(context)
                .textTheme
                .bodySmall
                ?.copyWith(color: Colors.black),
          ),
        ),
      ),
    );

    if (event.onTap != null) {
      child = InkWell(
        onTap: event.onTap,
        child: child,
      );
    }

    if (event.popupItemBuilder != null) {
      child = AlignedPopupMenuButton<int>(
        itemBuilder: event.popupItemBuilder!,
        onSelected: event.popupOnSelected,
        child: child,
      );
    }

    if (event.tooltip != null) {
      child = Tooltip(
        message: event.tooltip ?? '',
        textStyle: event.tooltipTextStyle,
        child: child,
      );
    }

    return child;
  }
}
