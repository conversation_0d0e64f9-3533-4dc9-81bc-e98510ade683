import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../dataModel/data/note.dart';
import '../dataModel/data/user.dart';
import '../dataModel/data_model.dart';
import '../dialogs/edit_note_dialog.dart';
import '../dialogs/remove_dialog.dart';
import '../dialogs/view_note_dialog.dart';
import '../helpers/color_helper.dart';
import '../misc/app_localization.dart';
import '../misc/extensions.dart';
import '../pages/view_model_mixin.dart';

class NotesWidget extends StatelessWidget {
  final String? userId;
  final String? scheduleId;
  final String? locationId;

  NotesWidget({
    Key? key,
    this.userId,
    this.scheduleId,
    this.locationId,
  }) : super(key: key) {
    // Ensure that at least one ID is provided
    if (userId == null && scheduleId == null && locationId == null) {
      throw UnimplementedError('At least one ID must be provided.');
    }
  }

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
        create: (context) => _ViewModel(
          userId: userId,
          scheduleId: scheduleId,
          locationId: locationId,
        ),
        child: _Body(),
      );
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;

    return Consumer<_ViewModel>(
      builder: (context, viewModel, _) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
        children: [

                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
                Row(
                children: [
                  Icon(
                  Icons.sticky_note_2_outlined,
                  size: 24,
                  color: ColorHelper.thePunchDarkBlue(),
                  ),
                  const SizedBox(width: 8),
                  Text(
                  'Manager Notes',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 20,
                    color: ColorHelper.thePunchDarkBlue(),
                  ),
                  ),
                 
                ],
                ),
                 IconButton(
              icon: Icon(Icons.add),
              onPressed: () => _showEditNoteDialog(context, viewModel, isNew: true),
            )

            ],
          ),
             Container(
             // padding: const EdgeInsets.all(16),
              child: Container(
                decoration: BoxDecoration(
                border: Border.all(color: ColorHelper.thePunchAdminButtonBlue().withOpacity(0.2), width: 1),
                borderRadius: BorderRadius.circular(8),
                ),
             //padding: const EdgeInsets.all(16),
  
             child: viewModel.isLoading
              ? Center(child: CircularProgressIndicator())
              : viewModel.notes.isEmpty
                ? Center(
                  child: Text(
                  AppLocalization.of(context).noteNotFound,
                  style: TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                  ),
                )
                : isMobile
                  ? _buildMobileList(context, viewModel)
                  : _buildDataTable(context, viewModel),
             ))
        ]
      ),
      ),
    );
  }

  Widget _buildDataTable(BuildContext context, _ViewModel viewModel) {
    final locale = Localizations.localeOf(context);

    return SingleChildScrollView(
      padding: EdgeInsets.all(8.0),
      child: DataTable(
        headingRowColor: MaterialStateProperty.resolveWith((states) => Colors.black!),
        dataRowHeight: 70,
        headingRowHeight: 56,
        horizontalMargin: 12,
        columnSpacing: 24,
        showCheckboxColumn: false,
        columns: _getColumns(context),
        rows: _getRows(context, viewModel, locale),
      ),
    );
  }

  Widget _buildMobileList(BuildContext context, _ViewModel viewModel) {
    final locale = Localizations.localeOf(context);

    return ListView.builder(
      padding: EdgeInsets.all(8.0),
      itemCount: viewModel.notes.length,
      itemBuilder: (context, index) {
        final note = viewModel.notes[index];
        final createdBy = viewModel.employeeMap[note.createdByUserId]?.name ?? '';

        return Card(
          margin: EdgeInsets.symmetric(vertical: 6),
          child: ListTile(
            title: Text(
              note.text,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Text(
              '${note.createdOn.toFormattedDateTime(locale)} • $createdBy',
              style: TextStyle(color: Colors.grey[600]),
            ),
            onTap: () => _showViewNoteDialog(context, note),
            trailing: PopupMenuButton<String>(
              onSelected: (value) => _handleMenuAction(context, value, note),
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'view',
                  child: ListTile(
                    leading: Icon(Icons.visibility),
                    title: Text(AppLocalization.of(context).view),
                  ),
                ),
                PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text(AppLocalization.of(context).edit),
                  ),
                ),
                PopupMenuItem(
                  value: 'remove',
                  child: ListTile(
                    leading: Icon(Icons.delete),
                    title: Text(AppLocalization.of(context).remove),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  List<DataColumn> _getColumns(BuildContext context) => [
        DataColumn(
          label: Text(
            AppLocalization.of(context).on,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        DataColumn(
          label: Text(
            AppLocalization.of(context).createdBy,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        DataColumn(
          label: Text(
            AppLocalization.of(context).note,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
        DataColumn(
          label: Text(
            AppLocalization.of(context).actions,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
      ];

  List<DataRow> _getRows(BuildContext context, _ViewModel viewModel, Locale locale) => viewModel.notes.map((note) {
      final createdBy = viewModel.employeeMap[note.createdByUserId]?.name ?? '';
      return DataRow(
        cells: [
          DataCell(Text(note.createdOn.toFormattedDateTime(locale))),
          DataCell(Text(createdBy)),
          DataCell(
            Tooltip(
              message: note.text,
              child: Text(
                note.text,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          DataCell(
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                IconButton(
                  icon: Icon(Icons.visibility),
                  tooltip: AppLocalization.of(context).view,
                  onPressed: () => _showViewNoteDialog(context, note),
                ),
                IconButton(
                  icon: Icon(Icons.edit),
                  tooltip: AppLocalization.of(context).edit,
                  onPressed: () => _showEditNoteDialog(context, viewModel, note: note),
                ),
                IconButton(
                  icon: Icon(Icons.delete),
                  tooltip: AppLocalization.of(context).remove,
                  onPressed: () => _showRemoveDialog(context, note),
                ),
              ],
            ),
          ),
        ],
      );
    }).toList();

  void _handleMenuAction(BuildContext context, String value, Note note) {
    switch (value) {
      case 'view':
        _showViewNoteDialog(context, note);
        break;
      case 'edit':
        _showEditNoteDialog(context, Provider.of<_ViewModel>(context, listen: false), note: note);
        break;
      case 'remove':
        _showRemoveDialog(context, note);
        break;
    }
  }

  void _showViewNoteDialog(BuildContext context, Note note) {
    showDialog(
      context: context,
      builder: (context) => ViewNoteDialog(note: note),
    );
  }

  void _showEditNoteDialog(BuildContext context, _ViewModel viewModel, {Note? note, bool isNew = false}) {
    showDialog(
      context: context,
      builder: (context) => EditNoteDialog(note: note ?? viewModel.createNote(), isNew: isNew),
    );
  }

  void _showRemoveDialog(BuildContext context, Note note) {
    showDialog(
      context: context,
      builder: (context) => RemoveDialog(
        remove: () => DataModel().noteModel.saveDirty([note]),
      ),
    );
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String? userId;
  final String? scheduleId;
  final String? locationId;

  List<Note> notes = [];
  Map<String, User> employeeMap = {};
  bool isLoading = true;

  _ViewModel({this.userId, this.scheduleId, this.locationId}) {
    addListenables([
      DataModel().noteModel,
      DataModel().userModel,
    ]);
    _init();
  }

  Future<void> _init() async {
    await refresh();
    isLoading = false;
    notifyListeners();
  }

  @override
  Future<void> refresh() async {
    var fetchedNotes = <Note>[];

    if (userId != null) {
      fetchedNotes = (await DataModel().noteModel.getByUserId(userId!)).toList();
    } else if (scheduleId != null) {
      fetchedNotes = (await DataModel().noteModel.getByScheduleId(scheduleId!)).toList();
    } else if (locationId != null) {
      fetchedNotes = (await DataModel().noteModel.getByLocationId(locationId!)).toList();
    }

    notes = fetchedNotes;
    notes.sort((a, b) => (b.lastChangedOn ?? b.createdOn).compareTo(a.lastChangedOn ?? a.createdOn));

    final employeeIds = notes
        .where((e) => e.createdByUserId != null)
        .map((e) => e.createdByUserId!)
        .toSet();

    final employees = await DataModel().userModel.getByIds(employeeIds);
    employeeMap = {for (final e in employees) e.id: e};

    notifyListeners();
  }

  Note createNote() {
    if (userId != null) return Note.createUser(userId!);
    if (scheduleId != null) return Note.createSchedule(scheduleId!);
    if (locationId != null) return Note.createLocation(locationId!);
    throw UnimplementedError('Cannot create note without a valid ID.');
  }
}
