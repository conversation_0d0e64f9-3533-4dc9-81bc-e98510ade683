import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../base_data.dart';
import '../../misc/json_conversion.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import 'package:uuid/uuid.dart';

part 'job_type.g.dart';

@HiveType(typeId: 12)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class JobType extends BaseData {

  static const String scheduledId = '95E597BF-075A-4048-9ADF-C95E34163C97';
  static const String unscheduledId = '4D024B8E-03F3-4B2D-9CD9-0D6809C786AD';
  static const String administrativeId = '87D05223-4F8E-4B7A-9B1A-1AB9E644590D';
  static const String travelTimeId = '2EBCDAA1-7DE7-4106-BC26-D99CA120C820';

  static const String specialEventId = '5A6B78CF-1234-5678-9ABC-DEF012345678';
  static const String inspectionId = '6B7C89DF-2345-6789-0ABC-EF0123456789';
  static const String trainingId = '7C8D90EF-3456-7890-1ABC-F01234567890';
  static const String projectId = '8D9E01F0-4567-8901-2ABC-012345678901';
  static const String customerVisitId = '9E0123F1-5678-9012-3ABC-123456789012';
  static const String employeeVisitId = '0F1234E2-6789-0123-4ABC-************';

  static const String trackingId = 'F13A4567-1234-5678-ABCD-9876543210FE';


  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  String name;

  @HiveField(102)
  String? description;

  @HiveField(103)
  int order;

  JobType({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.name,
    this.description,
    this.order = 0,
  });

  factory JobType.fromJson(Map<String, dynamic> json) {
    try {
      return _$JobTypeFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }
  Map<String, dynamic> toJson() => _$JobTypeToJson(this);

  factory JobType.create() => JobType(
        id: const Uuid().v4(),
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
        name: '',
      );
}
