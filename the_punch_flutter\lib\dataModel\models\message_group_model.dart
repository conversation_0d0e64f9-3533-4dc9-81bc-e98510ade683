import '../../api/sync_model.dart';
import '../../api/api_model.dart'; // <-- Make sure you import ApiModel so you can call deleteChatGroup()
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import '../base_data.dart';
import '../data/message_group.dart';
import '../hive_db.dart';

class MessageGroupModel extends BaseDataModel<MessageGroup> {
  @override
  Future<Iterable<MessageGroup>> get all async =>
      (await HiveDb.database).messageGroups.values;

  @override
  Future<void> save(Iterable<MessageGroup> groups) async {
    final box = (await HiveDb.database).messageGroups;
    await box.putAll({for (final g in groups) g.id: g});
    notifyListeners();
  }

  /// Clear everything from local storage
  Future<void> clearAll() async {
    final box = (await HiveDb.database).messageGroups;
    await box.clear();
    notifyListeners();
  }

  Future<void> updateMessageGroups(Iterable<MessageGroup> fetchedGroups) async {
    final db = await HiveDb.database;
    final localBox = db.messageGroups;

    final localMap = {for (final grp in localBox.values) grp.id: grp};
    final toSave = <String, MessageGroup>{};

    for (final fetched in fetchedGroups) {
      final localGrp = localMap[fetched.id];
      if (localGrp == null) {
        // Brand new
        toSave[fetched.id] = fetched;
      } else {
        // Merge
        final updated = MessageGroup(
          id: fetched.id,
          // choose whichever createdByUserId you prefer:
          createdByUserId: LoginState.userId,
          name: localGrp.isDirty && localGrp.name != null
              ? localGrp.name
              : fetched.name,
          createdOn: _mostRecentDateTime(localGrp.createdOn, fetched.createdOn),
          lastChangedOn:
              _mostRecentDateTime(localGrp.lastChangedOn, fetched.lastChangedOn),
          isDirty: localGrp.isDirty, // keep local isDirty
        );

        toSave[updated.id] = updated;
      }
    }

    if (toSave.isNotEmpty) {
      await localBox.putAll(toSave);
      notifyListeners();
    }
  }

  DateTime _mostRecentDateTime(DateTime? dt1, DateTime? dt2) {
    if (dt1 == null && dt2 == null) {
      return DateTime.fromMillisecondsSinceEpoch(0);
    } else if (dt1 == null) {
      return dt2!;
    } else if (dt2 == null) {
      return dt1;
    } else {
      return dt1.isAfter(dt2) ? dt1 : dt2;
    }
  }

  /// **NEW**: Deletes a specific group from the server, then from local Hive.
  Future<void> deleteGroup(String groupId) async {
    // 1) Ask the server to delete
    final success = await ApiModel().deleteChatGroup(groupId);
    if (!success) {
      // handle a false return if you prefer
      return;
    }
    // 2) Remove from local hive
    final box = (await HiveDb.database).messageGroups;
    await box.delete(groupId);
    notifyListeners();
  }

}
