import 'dart:async';
import 'dart:math'; // for Random
import 'package:flutter/material.dart';
import 'package:hive/hive.dart';

import '../../api/api_model.dart';
import '../../dataModel/data/group_member.dart';
import '../../dataModel/data/message_group.dart';
import '../../dataModel/data/user.dart';
import '../../dataModel/data/user_type.dart';
import '../../dataModel/data_model.dart';
import '../../helpers/color_helper.dart';
import '../../state/login_state.dart';
import '../constrained_search_dialog.dart';
import '../material_dialog.dart';
import '../../misc/app_localization.dart';
import '../../misc/change_notification_builder.dart';
import '../../pages/view_model_mixin.dart';
import '../../widgets/padded_card.dart';

class CreateChatDialog extends StatefulWidget {
  final List<String> ignoreEmployees;
  final void Function(List<String> selectedEmployeeIds) onMultiSelection;

  const CreateChatDialog({
    required this.ignoreEmployees,
    required this.onMultiSelection,
    super.key,
  });

  @override
  State<CreateChatDialog> createState() => _CreateChatDialogState();
}

class _CreateChatDialogState extends State<CreateChatDialog> {
  /// All users fetched from DB (minus those we must ignore).
  late List<User> _allUsers = [];

  /// The list of users currently displayed based on role + search filters.
  late List<User> _displayedUsers = [];

  /// Keep track of selected users by their ID.
  final _selectedEmployeeIds = <String>{};

  /// The search text typed by the user.
  String _searchTerm = '';

  /// Whether the current user is manager/admin.
  bool _currentUserIsManagerOrAdmin = false;

  @override
  void initState() {
    super.initState();
    unawaited(_loadUsers());
  }

  /// Utility to check if a userTypeId belongs to manager/admin.
  bool _isManagerOrAdminUserType(String userTypeId) =>
      userTypeId.toUpperCase() == UserType.managerId.toUpperCase() ||
      userTypeId.toUpperCase() == UserType.administratorId.toUpperCase();

  /// Loads all users from DB, ignoring those in [ignoreEmployees].
  Future<void> _loadUsers() async {
    // 1) Check the current user's userType
    final currentUser =
        await DataModel().userModel.getCurrentUser(LoginState.userId);

    _currentUserIsManagerOrAdmin = currentUser != null
        ? _isManagerOrAdminUserType(currentUser.userTypeId)
        : false;

    // 2) Fetch ALL users (not just employees)
    final allUsers = await DataModel().userModel.getAllUsers();

    // 3) Remove any users we must ignore
    _allUsers = allUsers.where((u) => !widget.ignoreEmployees.contains(u.id)).toList();

    // 4) Apply the initial filters
    _applyFilters();
  }

  /// Applies role-based and search-based filters to _allUsers.
  /// 
  /// - If _searchTerm is empty:
  ///   - manager/admin sees all,
  ///   - employees see only manager/admin.
  /// - If _searchTerm is non-empty:
  ///   - see *all* who match the search text (regardless of role).
  void _applyFilters() {
    final searchLower = _searchTerm.trim().toLowerCase();

    if (searchLower.isEmpty) {
      // No search text => restrict if employee
      if (_currentUserIsManagerOrAdmin) {
        _displayedUsers = _allUsers;
      } else {
        _displayedUsers = _allUsers.where((u) => _isManagerOrAdminUserType(u.userTypeId)).toList();
      }
    } else {
      // Some text => show ANYONE who matches
      _displayedUsers = _allUsers.where((u) => u.name.toLowerCase().contains(searchLower)).toList();
    }

    setState(() {});
  }

  /// A getter to check if all *displayed* users are currently selected.
  bool get _allSelected =>
      _displayedUsers.isNotEmpty &&
      _selectedEmployeeIds.length == _displayedUsers.length;

  /// Toggles between select all and deselect all for the *displayed* users.
  void _toggleSelectAll() {
    setState(() {
      if (_allSelected) {
        // Deselect only those in the displayed list
        for (final user in _displayedUsers) {
          _selectedEmployeeIds.remove(user.id);
        }
      } else {
        // Select all displayed
        for (final user in _displayedUsers) {
          _selectedEmployeeIds.add(user.id);
        }
      }
    });
  }

  /// Helper to create the group in Hive and on the server
  Future<void> _createGroup(String? groupName) async {
    // If groupName is null or empty, default to "group-<randomNumber>"
    if (groupName == null || groupName.trim().isEmpty) {
      final randomNumber = Random().nextInt(100000);
      groupName = 'group-$randomNumber';
    }

    // 1) Create the group locally
    final group = MessageGroup.create(groupName);
    final groupBox = Hive.box<MessageGroup>('messageGroup');
    await groupBox.put(group.id, group);

    // 2) _selectedEmployeeIds should already include the current user
    final groupMemberBox = Hive.box<GroupMember>('groupMember');
    for (final userId in _selectedEmployeeIds) {
      final gm = GroupMember.create(groupId: group.id, userId: userId);
      await groupMemberBox.put(gm.id, gm);
    }

    // 3) Call the API to store group + members on the backend
    try {
      await ApiModel().createChatGroup(group, _selectedEmployeeIds.toList());
    } catch (e) {
      print('Failed to create group on server: $e');
      // TODO: handle or show error to the user
    }
  }

  /// Prompts for a group name, returns the typed name or null if user cancels
  Future<String?> _promptForGroupName(BuildContext context) async {
    final controller = TextEditingController();
    return showDialog<String>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text(
          'Name this Group:',
          style: TextStyle(
            color: Colors.grey,
          ),
        ),
        content: TextField(
          controller: controller,
          autofocus: true,
          style: const TextStyle(color: Colors.black),
          decoration: const InputDecoration(
            hintText: 'Enter a custom group name or leave blank',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              // If you want to close both dialogs when user cancels:
              Navigator.pop(ctx, null); // closes naming prompt
              Navigator.of(context).pop(); // closes main selection dialog
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(ctx, controller.text.trim());
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Builds the search bar at the top of the dialog’s content with grey focus color.
  Widget _buildSearchBar() => TextField(
      style: const TextStyle(color: Colors.grey),
      cursorColor: Colors.grey,
      decoration: const InputDecoration(
        labelText: 'Search by Name',
        labelStyle: TextStyle(color: Colors.grey),
        prefixIcon: Icon(Icons.search, color: Colors.grey),
        hintText: 'Type a name...',
        // For a consistent grey underline/border when focused:
        focusedBorder: UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.grey),
        ),
        enabledBorder: UnderlineInputBorder(
          borderSide: BorderSide(color: Colors.grey),
        ),
      ),
      onChanged: (value) {
        _searchTerm = value;
        _applyFilters();
      },
    );

  @override
  Widget build(BuildContext context) => AlertDialog(
      // Title includes a Select All / Deselect All button
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Flexible(
            child: Text(
              'Select users to create a chat with:',
              style: TextStyle(
                color: Colors.grey,
              ),
            ),
          ),
          TextButton(
            onPressed: _toggleSelectAll,
            // Slightly smaller font to prevent overflow for “Deselect All”
            child: Text(
              _allSelected ? 'Deselect All' : 'Select All',
              style: const TextStyle(fontSize: 14.0),
            ),
          ),
        ],
      ),
      content: ConstrainedBox(
        constraints: const BoxConstraints(maxHeight: 400, minWidth: 300),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildSearchBar(),
            const SizedBox(height: 8),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    for (final user in _displayedUsers)
                      CheckboxListTile(
                        title: Text(user.name),
                        value: _selectedEmployeeIds.contains(user.id),
                        onChanged: (checked) {
                          setState(() {
                            if (checked == true) {
                              _selectedEmployeeIds.add(user.id);
                            } else {
                              _selectedEmployeeIds.remove(user.id);
                            }
                          });
                        },
                        activeColor: ColorHelper.thePunchAccentRed(),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(), // Cancel selection
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () async {
            if (_selectedEmployeeIds.isEmpty) {
              Navigator.of(context).pop();
              return;
            }

            // Always add the current user to the chat
            _selectedEmployeeIds.add(LoginState.userId);

            // If set length is 2 => single chat
            if (_selectedEmployeeIds.length == 2) {
              // Single user chat with default name
              await _createGroup(null);
            } else {
              // Multi-user => prompt for group name
              final typedName = await _promptForGroupName(context);
              if (typedName == null) {
                // user canceled naming prompt
                return;
              }
              await _createGroup(typedName);
            }

            // Trigger the callback to refresh UI, etc.
            widget.onMultiSelection(_selectedEmployeeIds.toList());
            Navigator.of(context).pop();
          },
          child: const Text('Done'),
        ),
      ],
    );
}
