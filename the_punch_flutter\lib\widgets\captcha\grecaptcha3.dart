// @JS()
// library grecaptcha3;

// import 'package:js/js.dart';
// import 'package:js/js_util.dart';

// @JS()
// @anonymous
// class Options {
//   external String get action;
//   external factory Options({String action});
// }

// @JS('grecaptcha.ready')
// external Object captchaReady();

// @JS('grecaptcha.execute')
// external Object captchaExecute(String key, Options opts);

// dynamic doValidation(String key) async {
//   await promiseToFuture(captchaReady());
//   return await promiseToFuture(captchaExecute(key, Options(action: 'submit')));
// }
