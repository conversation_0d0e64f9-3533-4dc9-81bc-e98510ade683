// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginRequest _$LoginRequestFromJson(Map<String, dynamic> json) => LoginRequest(
      organizationId: json['OrganizationID'] as String? ?? '',
      username: json['Username'] as String? ?? '',
      password: json['Password'] as String? ?? '',
      deviceId: json['deviceID'] as String? ?? '',
    );

Map<String, dynamic> _$LoginRequestToJson(LoginRequest instance) =>
    <String, dynamic>{
      'OrganizationID': instance.organizationId,
      'Username': instance.username,
      'Password': instance.password,
      'deviceID': instance.deviceId,
    };

LoginResponse _$LoginResponseFromJson(Map<String, dynamic> json) =>
    LoginResponse(
      databaseInfo:
          SystemRequest.fromJson(json['DatabaseInfo'] as Map<String, dynamic>),
      user: User.fromJson(json['User'] as Map<String, dynamic>),
      userSessionExpirationMinsWeb:
          (json['UserSessionExpirationMinsWeb'] as num).toInt(),
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$LoginResponseToJson(LoginResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['DatabaseInfo'] = instance.databaseInfo;
  val['User'] = instance.user;
  val['UserSessionExpirationMinsWeb'] = instance.userSessionExpirationMinsWeb;
  return val;
}
