import '../data/user_type.dart';
import '../base_data.dart';
import '../hive_db.dart';

class UserTypeModel extends BaseDataModel<UserType> {
  @override
  Future<Iterable<UserType>> get all async => (await HiveDb.database).userTypes.values;

  @override
  Future<void> save(Iterable<UserType> t) async {
    await (await HiveDb.database).userTypes.putAll({for (final e in t) e.id: e});
    notifyListeners();
  }

  Future<void> deleteUserType(String id) async {
    try {
      var db = await HiveDb.database;
      if (db.userTypes.containsKey(id)) {
        await db.userTypes.delete(id);
        notifyListeners();
        print('UserType with id $id deleted successfully.');
      } else {
        print('UserType with id $id not found in the database.');
      }
    } catch (e) {
      print('Error when trying to delete UserType: $e');
    }
  }

  Future<Iterable<UserType>> get allEmployees async => (await all).where((e) => e.id != UserType.sysopId).where((e) => !e.isContact);
  Future<Iterable<UserType>> get activeEmployees async => (await active).where((e) => e.id != UserType.sysopId).where((e) => !e.isContact);

  Future<Iterable<UserType>> get allContacts async => (await all).where((e) => e.isContact);
  Future<Iterable<UserType>> get activeContacts async => (await active).where((e) => e.isContact);

  Future<Iterable<UserType>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }

  Future<UserType?> getUserTypeById(String? id) async {
    var db = await HiveDb.database;
    return db.userTypes.get(id);
  }
}