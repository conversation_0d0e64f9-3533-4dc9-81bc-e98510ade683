import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import '../api/sync_model.dart';
import '../misc/my_platform.dart';

class FCMModel {
  static String? token;

  static Future<void> initialize() async {
    try {
      // FCM works on web pages, but pops up that annoying message
      if (!MyPlatform.isWeb && !MyPlatform.isAndroid && !MyPlatform.isIOS) return;
      // if (!MyPlatform.isAndroid && !MyPlatform.isIOS) return;

      final messaging = FirebaseMessaging.instance;

      final settings = await messaging.requestPermission(
        announcement: true,
        criticalAlert: true,
        provisional: true,
        //sound: true,
      );

      await messaging.setForegroundNotificationPresentationOptions(
        alert: true, // Required to display a heads up notification
        badge: true,
        sound: true,
      );

      // get FCM token
      if (MyPlatform.isWeb) {
        token = await messaging.getToken(vapidKey: 'BJwqoeZpfRqDUkhMnXwhKMK40wsdmIruNOE3sgZRn6GzmivdxcGafK97OsKaKqqJf896iMKQjkjDT1W5paDf-d4');
      } else {
        token = await messaging.getToken();
      }
      print('FCM token: $token');
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
    }
  }

  static void startListener(SyncModel syncModel) {
    FirebaseMessaging.onMessage.listen((message) async {
      // if (kDebugMode) print('FCMModel onMessage data ${message.data}');
      final type = message.data['type'];
      if (type == null) return;
      await Future.delayed(const Duration(seconds: 1));
      if (type == 'refresh') await syncModel.sync();
      if (type == 'message') await syncModel.sync();
    });
  }
}
