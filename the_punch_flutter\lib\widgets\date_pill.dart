import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../misc/extensions.dart';

class DatePill extends StatelessWidget {
  final DateTime? date;

  const DatePill({super.key, required this.date});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final surface = theme.colorScheme.surface;
    final onSurface = theme.colorScheme.onSurface;
    final date = this.date?.dateOnly;

    final languageCode = Localizations.localeOf(context).toLanguageTag();
    final dow = (date != null)
        ? DateFormat(DateFormat.ABBR_WEEKDAY, languageCode).format(date)
        : '';
    final day = (date != null)
        ? DateFormat(DateFormat.DAY, languageCode).format(date)
        : '';
    final month = (date != null)
        ? DateFormat(DateFormat.ABBR_MONTH, languageCode).format(date)
        : '';

    const padding = EdgeInsets.symmetric(horizontal: 4);

    return ConstrainedBox(
      constraints: const BoxConstraints(minWidth: 25),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(25),
        child: IntrinsicWidth(
          child: Flex(
            direction: Axis.vertical,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: [
              ColoredBox(
                color: surface,
                child: Padding(
                  padding: padding.copyWith(top: 4),
                  child: Center(
                      child: Text(dow, style: TextStyle(color: onSurface))),
                ),
              ),
              ColoredBox(
                color: surface,
                child: Padding(
                  padding: padding,
                  child: Center(
                      child: Text(day, style: TextStyle(color: onSurface))),
                ),
              ),
              ColoredBox(
                color: onSurface,
                child: Padding(
                  padding: padding.copyWith(bottom: 4),
                  child: Center(
                      child: Text(month, style: TextStyle(color: surface))),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class DateExt extends StatelessWidget {
  final DateTime? date;

  const DateExt({super.key, required this.date});

  @override
  Widget build(BuildContext context) {
    final date = this.date?.dateOnly;

    final languageCode = Localizations.localeOf(context).toLanguageTag();
    final dow = (date != null)
        ? DateFormat(DateFormat.ABBR_WEEKDAY, languageCode).format(date)
        : '';
    final day = (date != null)
        ? DateFormat(DateFormat.DAY, languageCode).format(date)
        : '';
    final month = (date != null)
        ? DateFormat(DateFormat.ABBR_MONTH, languageCode).format(date)
        : '';
    final year = (date != null)
        ? DateFormat(DateFormat.YEAR, languageCode).format(date)
        : '';

    return Text(
      '$dow, $month $day, $year'.toUpperCase(),
      textAlign: TextAlign.center,
      style: Theme.of(context).textTheme.titleMedium,
    );
  }
}
