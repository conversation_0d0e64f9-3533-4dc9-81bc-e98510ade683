// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fetch_notifications_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FetchNotificationsRequest _$FetchNotificationsRequestFromJson(
        Map<String, dynamic> json) =>
    FetchNotificationsRequest(
      targetUserId: json['TargetUserId'] as String,
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$FetchNotificationsRequestToJson(
        FetchNotificationsRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'TargetUserId': instance.targetUserId,
    };

FetchNotificationsResponse _$FetchNotificationsResponseFromJson(
        Map<String, dynamic> json) =>
    FetchNotificationsResponse(
      notifications: (json['Notifications'] as List<dynamic>)
          .map((e) => ThePunchNotification.fromJson(e as Map<String, dynamic>))
          .toList(),
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$FetchNotificationsResponseToJson(
    FetchNotificationsResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['Notifications'] = instance.notifications;
  return val;
}
