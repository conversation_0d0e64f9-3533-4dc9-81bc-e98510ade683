import 'package:flutter/cupertino.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_localized_locales/flutter_localized_locales.dart';

abstract class AppLocalization {
  static AppLocalizations of(BuildContext context) => AppLocalizations.of(context)!;

  static String translate(BuildContext context, String code) {
    switch (code) {
      case 'INTERNAL_ERROR':
        return AppLocalization.of(context).internalError;
      case 'NO_PERMISSION':
        return AppLocalization.of(context).noPermission;
      case 'INVALID_REQUEST':
        return AppLocalization.of(context).invalidRequest;
      case 'USER_NOT_FOUND_OR_INACTIVE':
        return AppLocalization.of(context).userNotFoundOrInactive;
      case 'DEVICE_ID_REQUIRED':
        return AppLocalization.of(context).deviceIdRequired;
      case 'ORGANIZATION_ID_NOT_FOUND':
        return AppLocalization.of(context).organizationIdNotFound;
      case 'SESSION_NOT_FOUND':
        return AppLocalization.of(context).sessionNotFound;
      case 'SESSION_LOGGED_OUT':
        return AppLocalization.of(context).sessionLoggedOut;
      case 'SESSION_TIMED_OUT':
        return AppLocalization.of(context).sessionTimedOut;
      case 'SESSION_FORCED_OUT':
        return AppLocalization.of(context).sessionForcedOut;
      case 'SESSION_ROTATED_OUT':
        return AppLocalization.of(context).sessionRotatedOut;
      case 'CONTACT_TYPE_NOT_FOUND':
        return AppLocalization.of(context).contactTypeNotFound;
      case 'PERMISSION_NOT_FOUND':
        return AppLocalization.of(context).permissionNotFound;
      case 'EMPLOYEE_TYPE_NOT_FOUND':
        return AppLocalization.of(context).employeeTypeNotFound;
      case 'LOCATION_NOT_FOUND':
        return AppLocalization.of(context).locationNotFound;
      case 'CONTACT_NOT_FOUND':
        return AppLocalization.of(context).contactNotFound;
      case 'REPORT_NOT_FOUND':
        return AppLocalization.of(context).reportNotFound;
      case 'EMPLOYEE_OR_LOCATION_REQUIRED':
        return AppLocalization.of(context).employeeOrLocationRequired;
      case 'REGISTRATION_KEY_REQUIRED':
        return AppLocalization.of(context).registrationKeyRequired;
      case 'NOTE_NOT_FOUND':
        return AppLocalization.of(context).noteNotFound;
      case 'PERMISSION_ALREADY_IN_CONTACT_TYPE':
        return AppLocalization.of(context).permissionAlreadyInContactType;
      case 'NOT_EDITABLE':
        return AppLocalization.of(context).notEditable;
      case 'PERMISSION_NOT_FOUND_IN_CONTACT_TYPE':
        return AppLocalization.of(context).permissionNotFoundInContactType;
      case 'EMPLOYEE_NOT_FOUND':
        return AppLocalization.of(context).employeeNotFound;
      case 'PERMISSION_ALREADY_IN_EMPLOYEE_TYPE':
        return AppLocalization.of(context).permissionAlreadyInEmployeeType;
      case 'PERMISSION_NOT_FOUND_IN_EMPLOYEE_TYPE':
        return AppLocalization.of(context).permissionNotFoundInEmployeeType;
      case 'JOB_TYPE_NOT_FOUND':
        return AppLocalization.of(context).jobTypeNotFound;
      case 'CONTACT_ALREADY_IN_LOCATION':
        return AppLocalization.of(context).contactAlreadyInLocation;
      case 'CONTACT_NOT_FOUND_IN_LOCATION':
        return AppLocalization.of(context).contactNotFoundInLocation;
      case 'PUNCH_CARD_NOT_FOUND':
        return AppLocalization.of(context).punchCardNotFound;
      case 'SCHEDULE_NOT_FOUND':
        return AppLocalization.of(context).scheduleNotFound;
      case 'SCHEDULETEMPLATE_COULD_NOT_BE_CREATED':
        return AppLocalization.of(context).scheduletemplateCouldNotBeCreated;
      case 'SCHEDULETEMPLATE_INVALID_RECURRING_FREQUENCY_ID':
        return AppLocalization.of(context).scheduletemplateInvalidRecurringFrequencyId;
      case 'USERNAME_OR_PASSWORD_INCORRECT':
        return AppLocalization.of(context).usernameOrPasswordIncorrect;
      case 'USER_MARKED_INACTIVE':
        return AppLocalization.of(context).userMarkedInactive;
      case 'OLD_PASSWORD_INCORRECT':
        return AppLocalization.of(context).oldPasswordIncorrect;
      case 'REGISTRATION_REQUEST_EXPIRED':
        return AppLocalization.of(context).registrationRequestExpired;
      case 'REGISTRATION_REQUEST_NOT_FOUND':
        return AppLocalization.of(context).registrationRequestNotFound;
      case 'FILL_IN_OPTIONS_CLICK_RUN_REPORT':
        return AppLocalization.of(context).fillInOptionsClickRunReport;
      case 'CHRISTMAS_HOLIDAY':
        return AppLocalization.of(context).christmasHoliday;
      case 'THANKSGIVING_HOLIDAY':
        return AppLocalization.of(context).thanksgivingHoliday;
      case 'NEW_YEARS_HOLIDAY':
        return AppLocalization.of(context).newYearsHoliday;
      case 'FOURTH_OF_JULY_HOLIDAY':
        return AppLocalization.of(context).fourthOfJulyHoliday;
      default:
        return code;
    }
  }

  static String translatePayRateFrequency(BuildContext context, int payRateFrequency) {
    switch (payRateFrequency) {
      case 0:
        return AppLocalization.of(context).hourly;
      case 1:
        return AppLocalization.of(context).weekly;
      case 2:
        return AppLocalization.of(context).yearly;
      default:
        return '';
    }
  }

  static String translateLanguageKey(BuildContext context, String languageKey) {
    switch (languageKey) {
      case 'en-US':
        return AppLocalization.of(context).languageEnUs;
      case 'es-US':
        return AppLocalization.of(context).languageEsUs;
      default:
        return '';
    }
  }

  static String localeName(BuildContext context, String key) => LocaleNames.of(context)?.nameOf(key.replaceAll('-', '_')) ?? key;
}
