// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'location_contact.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LocationContactAdapter extends TypeAdapter<LocationContact> {
  @override
  final int typeId = 19;

  @override
  LocationContact read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocationContact(
      isDirty: fields[0] as bool,
      isActive: fields[1] as bool,
      createdOn: fields[2] as DateTime,
      createdByUserId: fields[3] as String?,
      lastChangedOn: fields[4] as DateTime?,
      lastChangedByUserId: fields[5] as String?,
      id: fields[100] as String,
      locationId: fields[101] as String,
      userId: fields[102] as String,
      order: fields[103] as int,
    );
  }

  @override
  void write(BinaryWriter writer, LocationContact obj) {
    writer
      ..writeByte(10)
      ..writeByte(100)
      ..write(obj.id)
      ..writeByte(101)
      ..write(obj.locationId)
      ..writeByte(102)
      ..write(obj.userId)
      ..writeByte(103)
      ..write(obj.order)
      ..writeByte(0)
      ..write(obj.isDirty)
      ..writeByte(1)
      ..write(obj.isActive)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdByUserId)
      ..writeByte(4)
      ..write(obj.lastChangedOn)
      ..writeByte(5)
      ..write(obj.lastChangedByUserId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocationContactAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LocationContact _$LocationContactFromJson(Map<String, dynamic> json) =>
    LocationContact(
      isActive: json['IsActive'] as bool? ?? true,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
      id: idFromJson(json['Id']),
      locationId: idFromJson(json['LocationId']),
      userId: idFromJson(json['UserId']),
      order: (json['ContactLocationRank'] as num).toInt(),
    );

Map<String, dynamic> _$LocationContactToJson(LocationContact instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['LocationId'] = instance.locationId;
  val['UserId'] = instance.userId;
  val['ContactLocationRank'] = instance.order;
  return val;
}
