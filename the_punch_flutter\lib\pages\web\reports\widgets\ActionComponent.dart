import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../dataModel/data/punch_card.dart';

class ActionComponent extends StatelessWidget {
  final double width;
  final double height;
  
  final PunchCard punchCard;

   const ActionComponent({
    super.key,
    this.width = 136.0,
    this.height = 336.0,
    required this.punchCard,
  });

  @override
  Widget build(BuildContext context) => Column(
    children: [
      
      DecoratedBox(
      
        decoration: BoxDecoration(
          color: const Color(0xFF4BA2E7),
          borderRadius: BorderRadius.circular(100),
          border: Border.all(
            color: const Color(0x578A959D),
            width: 1,
          ),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(100),
            onTap: () {
               context.pushNamed(
                '/punchCards/view',
                queryParameters: {
                  'id': punchCard.punchCardLinkId ?? punchCard.id,
                },
              );
             // Handle button tap
            },
            child: const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              
              child: Center(
                child: Text(
                  'View Punch Card',
                  style: TextStyle(
                    fontFamily: 'Poppins',
                    fontSize: 12,
                    fontWeight: FontWeight.w700,
                    letterSpacing: -0.12,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    ],
  );
}

