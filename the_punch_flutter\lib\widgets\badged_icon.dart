import 'package:flutter/material.dart';

import '../helpers/color_helper.dart';

class BadgedIcon extends StatelessWidget {
  final IconData? icon;
  final bool showBadge;
  final int? count;
  final Color? color;
  final bool isSelected;

  const BadgedIcon(this.icon,
      {super.key,
      this.showBadge = false,
      this.count,
      this.color,
      this.isSelected = false});

  @override
  Widget build(BuildContext context) => IntrinsicWidth(
        child: Stack(
          children: [
            Padding(
              padding: (count == null)
                  ? const EdgeInsets.only(left: 6, right: 6)
                  : const EdgeInsets.only(left: 6, right: 6),
              child: Icon(
                icon,
                color: isSelected
                    ? ColorHelper.thePunchDesktopLightGray()
                    : ColorHelper.thePunchLightGray(),
              ),
            ),
            if (showBadge && count == null)
              Align(
                alignment: Alignment.topRight,
                child: Icon(
                  Icons.brightness_1,
                  size: 14,
                  color: color ?? ColorHelper.thePunchRed(),
                ),
              ),
            if (count != null)
              Align(
                alignment: Alignment.topRight,
                child: ConstrainedBox(
                  constraints:
                      const BoxConstraints(minHeight: 14, minWidth: 14),
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: color ?? ColorHelper.thePunchRed(),
                    ),
                    padding: const EdgeInsets.all(3),
                    child: Text(
                      count!.toString(),
                      style: const TextStyle(color: Colors.white, fontSize: 8),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
}
