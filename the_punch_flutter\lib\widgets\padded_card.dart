import 'package:flutter/material.dart';

class PaddedCard extends Card {
  const PaddedCard({
    super.key,
    super.color,
    super.shadowColor,
    super.elevation = 5.0,
    super.shape,
    super.borderOnForeground,
    super.margin = const EdgeInsets.all(8),
    super.clipBehavior,
    super.child,
    super.semanticContainer,
  });

  @override
  Widget? get child =>
      Padding(padding: const EdgeInsets.all(4), child: super.child);
}
