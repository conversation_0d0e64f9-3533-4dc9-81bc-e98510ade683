import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
// import 'package:pdf/pdf.dart';
// import 'package:printing/printing.dart';

class PdfWidget extends StatelessWidget {
  final String? filename;

  const PdfWidget({super.key, this.filename});

  // static const PdfPageFormat portrait = PdfPageFormat(
  //   8.5 * PdfPageFormat.inch,
  //   11.0 * PdfPageFormat.inch,
  //   marginAll: PdfPageFormat.inch * 0.5,
  // );

  // static const PdfPageFormat landscape = PdfPageFormat(
  //   11.0 * PdfPageFormat.inch,
  //   8.5 * PdfPageFormat.inch,
  //   marginAll: PdfPageFormat.inch * 0.5,
  // );

  @override
  Widget build(BuildContext context) => const Placeholder();
  // return PdfPreview(
  //   maxPageWidth: 1000,
  //   build: (format) => pdfGenerator.buildPdf(format),
  //   pdfFileName: filename,
  //   initialPageFormat: landscape,
  //   pageFormats: {'Landscape': landscape, 'Portrait': portrait},
  //   canChangeOrientation: false,
  // );
}
