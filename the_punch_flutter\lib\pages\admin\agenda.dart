import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'dart:async';
import 'package:flutter/material.dart';
import '../../dataModel/data/user.dart';
import '../../dataModel/data_model.dart';
import '../../dataModel/data/alert.dart';
import '../../dataModel/data/job_type.dart';
import '../../dataModel/data/location.dart';
import '../../dataModel/data/punch_card.dart';
import '../../dataModel/data/schedule.dart';
import '../../misc/app_localization.dart';
import '../view_model_mixin.dart';
import '../../widgets/date_bar.dart';
import '../../widgets/icon_tile.dart';
import '../../widgets/my_body.dart';
import '../../misc/extensions.dart';
import 'package:dartx/dartx.dart';

import '../web/my_scaffold.dart';

class AgendaPage extends StatefulWidget {
  const AgendaPage({super.key});
  @override
  State<AgendaPage> createState() => _ScaffoldState();
}

class _ScaffoldState extends State<AgendaPage> {
  final agendaData = _ViewModel();
  final selectedDate = ValueNotifier<DateTime>(DateTime.now().dateOnly);

  @override
  Widget build(BuildContext context) => MyScaffold(
      title: AppLocalizations.of(context)!.agenda,
      body: MyRoundedBody(
        header: _Header(agendaData, selectedDate),
        body: _Body(agendaData, selectedDate),
      ));
}

class _Header extends StatelessWidget {
  final _ViewModel agendaData;
  final ValueNotifier<DateTime> selectedDate;

  const _Header(this.agendaData, this.selectedDate);

  @override
  Widget build(BuildContext context) => StreamBuilder<Map<DateTime, int>>(
      stream: agendaData.countByDatesStream,
      builder: (context, snapshot) =>
          DateBar(pipsByDate: snapshot.data, initialDate: DateTime.now().startOfWeek, selectedDate: selectedDate));
}

class _Body extends StatefulWidget {
  final _ViewModel agendaData;
  final ValueNotifier<DateTime> selectedDate;

  const _Body(this.agendaData, this.selectedDate);

  @override
  _BodyState createState() => _BodyState();
}

class _BodyState extends State<_Body> {
  PageController? pageController;

  PageController _createPageController() {
    final agendaDatesLocal = widget.agendaData.dates.map((e) => e.dateOnly.forceLocal).toList();
    final pageController = PageController(initialPage: agendaDatesLocal.indexOf(widget.selectedDate.value));
    pageController.addListener(_pageControllerListener);
    widget.selectedDate.addListener(_selectedDateListener);
    return pageController;
  }

  void _pageControllerListener() {
    if (widget.agendaData.dates.isEmpty) return;
    final pageController = this.pageController!;
    if (!pageController.hasClients) return;
    if (pageController.page == null) return;
    if (pageController.page!.toInt() != pageController.page) return;
    final date = widget.agendaData.dates[pageController.page!.toInt()];
    widget.selectedDate.value = date.dateOnly.forceLocal;
  }

  void _selectedDateListener() {
    if (widget.agendaData.dates.isEmpty) return;
    final pageController = this.pageController!;
    if (!pageController.hasClients) return;
    final agendaDatesLocal = widget.agendaData.dates.map((e) => e.dateOnly.forceLocal).toList();
    final page = agendaDatesLocal.indexOf(widget.selectedDate.value);
    if (page == -1) return;
    unawaited(pageController.animateToPage(page, duration: const Duration(milliseconds: 500), curve: Curves.ease));
  }

  @override
  void dispose() {
    pageController?.addListener(_pageControllerListener);
    widget.selectedDate.removeListener(_selectedDateListener);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => StreamBuilder<List<DateTime>>(
      stream: widget.agendaData.datesStream,
      builder: (context, snapshot) {
        if (!snapshot.hasData || snapshot.data!.isEmpty) return Container();
        final dates = snapshot.data!;
        pageController ??= _createPageController();
        if (pageController!.hasClients && pageController!.page != dates.indexOf(widget.selectedDate.value)) {
          pageController!.jumpToPage(dates.indexOf(widget.selectedDate.value));
        }
        return PageView.builder(
          controller: pageController,
          itemCount: dates.length,
          itemBuilder: (context, index) => _BodyPage(dates[index]),
        );
      });
}

class _BodyPage extends StatefulWidget {
  final DateTime date;

  const _BodyPage(this.date);

  @override
  _BodyPageState createState() => _BodyPageState();
}

class _BodyPageState extends State<_BodyPage> {
  late final _PageViewModel _pageData;

  @override
  void initState() {
    super.initState();
    _pageData = _PageViewModel(widget.date);
  }

  @override
  Widget build(BuildContext context) => StreamBuilder<List<dynamic>>(
        stream: _pageData.schedulesOrPunchCards,
        builder: (context, snapshot) {
          if (!snapshot.hasData) return Container();
          final schedulesOrPunchCards = snapshot.data!;
          return Center(
              child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 500),
                  child: ListView(
                    children: [
                      Container(height: 4),
                      for (final scheduleOrPunchCard in schedulesOrPunchCards)
                        Column(children: [
                          if (scheduleOrPunchCard.runtimeType == Schedule)
                            _ScheduleTile(
                              scheduleOrPunchCard as Schedule,
                              _pageData.getEmployee(scheduleOrPunchCard),
                              _pageData.getLocation(scheduleOrPunchCard)!,
                              _pageData.getAlerts(scheduleOrPunchCard),
                            ),
                          if (scheduleOrPunchCard.runtimeType == PunchCard)
                            _PunchCardTile(
                              scheduleOrPunchCard as PunchCard,
                              _pageData.getEmployee(scheduleOrPunchCard),
                              _pageData.jobTypeMap[scheduleOrPunchCard.jobTypeId],
                              _pageData.getLocation(scheduleOrPunchCard),
                              _pageData.getAlerts(scheduleOrPunchCard),
                            )
                        ])
                    ],
                  )));
        },
      );
}

class _ScheduleTile extends StatelessWidget {
  final Schedule schedule;
  final User employee;
  final Location location;
  final Iterable<Alert> alerts;

  const _ScheduleTile(this.schedule, this.employee, this.location, this.alerts);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final secondaryBodyText1 = theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.secondary);

    return IconCardTile(
        leading: const Icon(Icons.dashboard),
        title: Text(AppLocalization.of(context).schedule, style: secondaryBodyText1),
        body: Column(children: [
          Flex(direction: Axis.horizontal, children: [
            _EmployeeName(employeeName: employee.name),
            _WorkName(workName: location.name),
            _StartEndTime(start: schedule.startDateUtc, end: schedule.endDateUtc),
          ]),
          if (alerts.isNotEmpty) _Alerts(alerts: alerts),
        ]));
  }
}

class _PunchCardTile extends StatelessWidget {
  final PunchCard punchCard;
  final User employee;
  final JobType? jobType;
  final Location? location;
  final Iterable<Alert> alerts;

  const _PunchCardTile(this.punchCard, this.employee, this.jobType, this.location, this.alerts);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final secondaryBodyText1 = theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.secondary);

    final workName = location?.name ?? jobType?.name ?? '';

    return GestureDetector(
        child: IconCardTile(
            leading: const Icon(Icons.timelapse),
            title: Text(AppLocalization.of(context).punchCard, style: secondaryBodyText1),
            body: Column(children: [
              Flex(direction: Axis.horizontal, children: [
                _EmployeeName(employeeName: employee.name),
                _WorkName(workName: workName),
                _StartEndTime(start: punchCard.clockedIn, end: punchCard.clockedOut),
              ]),
              if (alerts.isNotEmpty) _Alerts(alerts: alerts),
            ])));
  }
}

class _EmployeeName extends StatelessWidget {
  const _EmployeeName({required this.employeeName});

  final String employeeName;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final caption = theme.textTheme.bodySmall;
    return Flexible(child: Align(alignment: Alignment.centerLeft, child: Text(employeeName, style: caption)));
  }
}

class _WorkName extends StatelessWidget {
  const _WorkName({required this.workName});

  final String workName;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final caption = theme.textTheme.bodySmall;
    return Flexible(child: Align(alignment: Alignment.centerLeft, child: Text(workName, style: caption)));
  }
}

class _StartEndTime extends StatelessWidget {
  const _StartEndTime({required this.start, required this.end});

  final DateTime start;
  final DateTime? end;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final locale = Localizations.localeOf(context);
    final caption = theme.textTheme.bodySmall;
    return Flexible(
        flex: 0,
        child: Align(
            alignment: Alignment.centerRight,
            child: Row(mainAxisSize: MainAxisSize.min, children: [
              ConstrainedBox(
                constraints: const BoxConstraints(minWidth: 50),
                child: Text(start.toFormattedTime(locale), style: caption),
              ),
              if (end == null)
                Container(width: 50)
              else
                ConstrainedBox(
                  constraints: const BoxConstraints(minWidth: 50),
                  child: Text(end!.toFormattedTime(locale), style: caption),
                )
            ])));
  }
}

class _Alerts extends StatelessWidget {
  const _Alerts({required this.alerts});

  final Iterable<Alert> alerts;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final caption = theme.textTheme.bodySmall;

    final alertString = alerts.map((e) => e.name(context)).join(' ');
    return Align(alignment: Alignment.centerLeft, child: Text(alertString, style: caption));
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final _countByDatesController = StreamController<Map<DateTime, int>>();
  final _datesController = StreamController<List<DateTime>>();
  Stream<Map<DateTime, int>> get countByDatesStream => _countByDatesController.stream;
  Stream<List<DateTime>> get datesStream => _datesController.stream;
  List<DateTime> dates = [];

  _ViewModel() {
    addListenables([DataModel().scheduleModel, DataModel().punchCardModel]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    final scheduleDates = (await DataModel().scheduleModel.getAllStartsWithoutPunchCards()).map((e) => e.dateOnly);
    final punchCardDates = (await DataModel().punchCardModel.getAllStarts()).map((e) => e.dateOnly);

    dates = scheduleDates.followedBy(punchCardDates).toList();

    final countByDates = <DateTime, int>{};
    for (final element in dates) {
      if (!countByDates.containsKey(element)) {
        countByDates[element] = 1;
      } else {
        countByDates[element] = countByDates[element]! + 1;
      }
    }
    _countByDatesController.add(countByDates);

    dates = dates.distinct().toList();
    dates.sort((a, b) => a.compareTo(b));
    _datesController.add(dates);
    notifyListeners();
  }
}

class _PageViewModel extends ChangeNotifier with ViewModelMixin {
  final DateTime date;
  var scheduleMap = <String, Schedule>{};
  var employeeMap = <String, User>{};
  var jobTypeMap = <String, JobType>{};
  var locationMap = <String, Location>{};
  var scheduleAlertsMap = <String, Iterable<Alert>>{};
  var punchCardAlertsMap = <String, Iterable<Alert>>{};

  final _streamController = StreamController<List<dynamic>>();
  Stream<List<dynamic>> get schedulesOrPunchCards => _streamController.stream;

  _PageViewModel(this.date) {
    addListenables([
      DataModel().scheduleModel,
      DataModel().punchCardModel,
      DataModel().userModel,
      DataModel().jobTypeModel,
      DataModel().locationModel,
      DataModel().alertModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    final dayStart = date.dateOnly;
    final dayEnd = date.dateOnly.addDays(1);
    final punchCards = await DataModel().punchCardModel.getBetween(dayStart.toUtc(), dayEnd.toUtc());

    final scheduleIdsWithPunchCards = punchCards.map((e) => e.scheduleId).where((e) => e != null).map((e) => e!);
    final schedules = (await DataModel().scheduleModel.getBetween(dayStart.toUtc(), dayEnd.toUtc()))
        .followedBy(await DataModel().scheduleModel.getByIds(scheduleIdsWithPunchCards));
    scheduleMap = {for (final e in schedules) e.id: e};

    final jobTypeIds = punchCards.map((e) => e.jobTypeId).toSet();
    final jobTypes = await DataModel().jobTypeModel.getByIds(jobTypeIds);
    jobTypeMap.addAll({for (final e in jobTypes) e.id: e});

    final locationIds = schedules
        .map((e) => e.locationId)
        .followedBy(punchCards.map((e) => e.locationId).where((e) => e != null).map((e) => e!))
        .toSet();
    final locations = await DataModel().locationModel.getByIds(locationIds);
    locationMap.addAll({for (final e in locations) e.id: e});

    final employeeIds = schedules.map((e) => e.userId).followedBy(punchCards.map((e) => e.userId)).toSet();
    final employees = await DataModel().userModel.getByIds(employeeIds);
    employeeMap.addAll({for (final e in employees) e.id: e});

    final punchCardIds = punchCards.map((e) => e.id);
    final punchCardAlerts = await DataModel().alertModel.getActiveByPunchCardIds(punchCardIds);
    punchCardAlertsMap = punchCardAlerts.groupBy((e) => e.punchCardId!);
    punchCardAlertsMap = {for (final e in punchCardAlertsMap.entries) e.key: e.value.distinctBy((e) => e.alertTypeId)};

    final scheduleIds = schedules.map((e) => e.id);
    final scheduleAlerts = await DataModel().alertModel.getByScheduleIds(scheduleIds);
    scheduleAlertsMap = scheduleAlerts.groupBy((e) => e.scheduleId!);
    scheduleAlertsMap = {for (final e in scheduleAlertsMap.entries) e.key: e.value.distinctBy((e) => e.alertTypeId)};

    final schedulesOrPunchCards = [];
    schedulesOrPunchCards.addAll(schedules.where((e) => !scheduleIdsWithPunchCards.contains(e.id)));
    schedulesOrPunchCards.addAll(punchCards);
    schedulesOrPunchCards.sort((a, b) {
      final aStart = (a.runtimeType == Schedule) ? (a as Schedule).startDateUtc : (a as PunchCard).clockedIn;
      final bStart = (b.runtimeType == Schedule) ? (b as Schedule).startDateUtc : (b as PunchCard).clockedIn;
      return aStart.compareTo(bStart);
    });

    _streamController.add(schedulesOrPunchCards);
    notifyListeners();
  }

  User getEmployee(scheduleOrPunchCard) {
    if (scheduleOrPunchCard.runtimeType == PunchCard) {
      final punchCard = scheduleOrPunchCard as PunchCard;
      return employeeMap[punchCard.userId]!;
    }
    if (scheduleOrPunchCard.runtimeType == Schedule) {
      final schedule = scheduleOrPunchCard as Schedule;
      return employeeMap[schedule.userId]!;
    }
    throw (Exception('missing employee from employeeMap'));
  }

  Location? getLocation(scheduleOrPunchCard) {
    if (scheduleOrPunchCard.runtimeType == PunchCard) {
      final punchCard = scheduleOrPunchCard as PunchCard;
      if (punchCard.locationId != null) return locationMap[punchCard.locationId!];
      if (punchCard.scheduleId != null) return locationMap[scheduleMap[punchCard.scheduleId]!.locationId];
      return null;
    }
    if (scheduleOrPunchCard.runtimeType == Schedule) {
      final schedule = scheduleOrPunchCard as Schedule;
      return locationMap[schedule.locationId];
    }
    return null;
  }

  Iterable<Alert> getAlerts(scheduleOrPunchCard) {
    if (scheduleOrPunchCard.runtimeType == PunchCard) {
      final punchCard = scheduleOrPunchCard as PunchCard;
      return punchCardAlertsMap[punchCard.id] ?? [];
    }
    if (scheduleOrPunchCard.runtimeType == Schedule) {
      final schedule = scheduleOrPunchCard as Schedule;
      return scheduleAlertsMap[schedule.id] ?? [];
    }
    return [];
  }
}
