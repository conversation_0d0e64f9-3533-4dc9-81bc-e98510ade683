// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'inspection_template.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class InspectionTemplateAdapter extends TypeAdapter<InspectionTemplate> {
  @override
  final int typeId = 9;

  @override
  InspectionTemplate read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return InspectionTemplate(
      isDirty: fields[0] as bool,
      isActive: fields[1] as bool,
      createdOn: fields[2] as DateTime,
      createdByUserId: fields[3] as String?,
      lastChangedOn: fields[4] as DateTime?,
      lastChangedByUserId: fields[5] as String?,
      id: fields[100] as String,
      name: fields[101] as String,
      locationId: fields[102] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, InspectionTemplate obj) {
    writer
      ..writeByte(9)
      ..writeByte(100)
      ..write(obj.id)
      ..writeByte(101)
      ..write(obj.name)
      ..writeByte(102)
      ..write(obj.locationId)
      ..writeByte(0)
      ..write(obj.isDirty)
      ..writeByte(1)
      ..write(obj.isActive)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdByUserId)
      ..writeByte(4)
      ..write(obj.lastChangedOn)
      ..writeByte(5)
      ..write(obj.lastChangedByUserId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is InspectionTemplateAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class InspectionTemplateAreaAdapter
    extends TypeAdapter<InspectionTemplateArea> {
  @override
  final int typeId = 10;

  @override
  InspectionTemplateArea read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return InspectionTemplateArea(
      isDirty: fields[0] as bool,
      isActive: fields[1] as bool,
      createdOn: fields[2] as DateTime,
      createdByUserId: fields[3] as String?,
      lastChangedOn: fields[4] as DateTime?,
      lastChangedByUserId: fields[5] as String?,
      id: fields[100] as String,
      name: fields[101] as String,
      inspectionTemplateId: fields[102] as String,
      order: fields[103] as int,
    );
  }

  @override
  void write(BinaryWriter writer, InspectionTemplateArea obj) {
    writer
      ..writeByte(10)
      ..writeByte(100)
      ..write(obj.id)
      ..writeByte(101)
      ..write(obj.name)
      ..writeByte(102)
      ..write(obj.inspectionTemplateId)
      ..writeByte(103)
      ..write(obj.order)
      ..writeByte(0)
      ..write(obj.isDirty)
      ..writeByte(1)
      ..write(obj.isActive)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdByUserId)
      ..writeByte(4)
      ..write(obj.lastChangedOn)
      ..writeByte(5)
      ..write(obj.lastChangedByUserId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is InspectionTemplateAreaAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class InspectionTemplateItemAdapter
    extends TypeAdapter<InspectionTemplateItem> {
  @override
  final int typeId = 11;

  @override
  InspectionTemplateItem read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return InspectionTemplateItem(
      isDirty: fields[0] as bool,
      isActive: fields[1] as bool,
      createdOn: fields[2] as DateTime,
      createdByUserId: fields[3] as String?,
      lastChangedOn: fields[4] as DateTime?,
      lastChangedByUserId: fields[5] as String?,
      id: fields[100] as String,
      name: fields[101] as String,
      inspectionTemplateId: fields[102] as String,
      inspectionTemplateAreaId: fields[103] as String,
      order: fields[104] as int,
    );
  }

  @override
  void write(BinaryWriter writer, InspectionTemplateItem obj) {
    writer
      ..writeByte(11)
      ..writeByte(100)
      ..write(obj.id)
      ..writeByte(101)
      ..write(obj.name)
      ..writeByte(102)
      ..write(obj.inspectionTemplateId)
      ..writeByte(103)
      ..write(obj.inspectionTemplateAreaId)
      ..writeByte(104)
      ..write(obj.order)
      ..writeByte(0)
      ..write(obj.isDirty)
      ..writeByte(1)
      ..write(obj.isActive)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdByUserId)
      ..writeByte(4)
      ..write(obj.lastChangedOn)
      ..writeByte(5)
      ..write(obj.lastChangedByUserId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is InspectionTemplateItemAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

InspectionTemplate _$InspectionTemplateFromJson(Map<String, dynamic> json) =>
    InspectionTemplate(
      isActive: json['IsActive'] as bool? ?? true,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
      id: idFromJson(json['Id']),
      name: json['Name'] as String,
      locationId: nullableIdFromJson(json['LocationId']),
    );

Map<String, dynamic> _$InspectionTemplateToJson(InspectionTemplate instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['Name'] = instance.name;
  writeNotNull('LocationId', instance.locationId);
  return val;
}

InspectionTemplateArea _$InspectionTemplateAreaFromJson(
        Map<String, dynamic> json) =>
    InspectionTemplateArea(
      isActive: json['IsActive'] as bool? ?? true,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
      id: idFromJson(json['Id']),
      name: json['Name'] as String,
      inspectionTemplateId: idFromJson(json['InspectionTemplateId']),
      order: (json['Order'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$InspectionTemplateAreaToJson(
    InspectionTemplateArea instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['Name'] = instance.name;
  val['InspectionTemplateId'] = instance.inspectionTemplateId;
  val['Order'] = instance.order;
  return val;
}

InspectionTemplateItem _$InspectionTemplateItemFromJson(
        Map<String, dynamic> json) =>
    InspectionTemplateItem(
      isActive: json['IsActive'] as bool? ?? true,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
      id: idFromJson(json['Id']),
      name: json['Name'] as String,
      inspectionTemplateId: idFromJson(json['InspectionTemplateId']),
      inspectionTemplateAreaId: idFromJson(json['InspectionTemplateAreaId']),
      order: (json['Order'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$InspectionTemplateItemToJson(
    InspectionTemplateItem instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['Name'] = instance.name;
  val['InspectionTemplateId'] = instance.inspectionTemplateId;
  val['InspectionTemplateAreaId'] = instance.inspectionTemplateAreaId;
  val['Order'] = instance.order;
  return val;
}
