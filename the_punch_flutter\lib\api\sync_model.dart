import 'dart:async';
import 'dart:io';
import 'package:async/async.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'api_model.dart';
import 'assets_model.dart';
import '../dataModel/data_model.dart';
import '../misc/my_platform.dart';
import '../state/app_state.dart';
import '../state/login_state.dart';
import '../state/server_time_state.dart';
import '../misc/extensions.dart';

class SyncModel extends ChangeNotifier {
  StreamSubscription<ConnectivityResult>? subscription;
  ConnectivityResult? connectivityResult;

  static SyncModel? _singleton;
  factory SyncModel() {
    _singleton ??= SyncModel._();
    return _singleton!;
  }
  SyncModel._() {
    unawaited(_initialize());
  }

  Future<void> _initialize() async {
    try {
      subscription = Connectivity().onConnectivityChanged.listen(connectionChanged as void Function(ConnectivityResult event)?) as StreamSubscription<ConnectivityResult>?;
      final connectivityResult = await Connectivity().checkConnectivity();
      await connectionChanged(connectivityResult as ConnectivityResult);
    } catch (e) {
      //if (kDebugMode) print('$e\n${StackTrace.current}');
    }
  }

  Future<void> connectionChanged(ConnectivityResult newConnectivityResult) async {
    final lastConnectivityResult = connectivityResult;
    if (lastConnectivityResult == newConnectivityResult) return;
    connectivityResult = newConnectivityResult;
    // changing from no network to mobile, so sync
    if (connectivityResult == ConnectivityResult.mobile && lastConnectivityResult == ConnectivityResult.none) {
      if (kDebugMode) print('I am connected to a mobile network.');
      await sync();
      // changing to wifi, so sync
    } else if (connectivityResult == ConnectivityResult.wifi) {
      if (kDebugMode) print('I am connected to a wifi network.');
      await sync();
    } else if (connectivityResult == ConnectivityResult.none) {
      if (kDebugMode) print('I am not connected.');
    }
  }

  var _isSyncing = false;
  var _isSyncingAgain = false;
  RestartableTimer? _timer;

  Future<void> sync({DateTime? lastSynced}) async {
    if (!LoginState.isLoggedIn) return;
    if (connectivityResult == ConnectivityResult.none) return;

    if (_isSyncingAgain) return;
    if (_isSyncing) {
      _isSyncingAgain = true;
      return;
    }
    _isSyncing = true;
    if (_timer != null) {
      _timer?.reset();
      return;
    }
    _timer = RestartableTimer(const Duration(milliseconds: 500), () async {
      _timer = null;
      _isSyncingAgain = true;
      while (_isSyncingAgain) {
        _isSyncingAgain = false;
        await _sync(lastSynced: lastSynced);
      }
      _isSyncing = false;
    });
  }

  Future syncSynchronous({DateTime? lastSynced}) async {
    if (!LoginState.isLoggedIn) return;
    if (connectivityResult == ConnectivityResult.none) return;

    await _sync(lastSynced: lastSynced);
  }

  Future<void> _sync({DateTime? lastSynced}) async {
    try {
      if (lastSynced != null) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('lastSynced', lastSynced.toIso8601String());
      }
      // print(
      //     '_sync started employees: ${(await DataModel().userModel.allEmployees).length} inspections: ${(await DataModel().inspectionModel.active).length} locations: ${(await DataModel().locationModel.active).length}');
      await _sendDirty();
      await _getConditionalChanges();
      // print(
      //     '_sync finished employees: ${(await DataModel().userModel.allEmployees).length} inspections: ${(await DataModel().inspectionModel.active).length} locations: ${(await DataModel().locationModel.active).length}');
    } catch (e) {
      //if (kDebugMode) print('$e\n${StackTrace.current}');
    }
  }

  Future<void> _sendDirty() async {
    try {
      var allMarked = false;
      while (!allMarked) {
        allMarked = true;

        final sendDirtyStarted = ServerTimeState().utcTime;

      // Log the data before sending
      final alerts = (await DataModel().alertModel.dirty).nullIfEmpty;
      
      final geoLocations = (await DataModel().geoLocationModel.dirty).nullIfEmpty;
      
      final inspections = (await DataModel().inspectionModel.dirty).nullIfEmpty;
      
      final inspectionAreas = (await DataModel().inspectionAreaModel.dirty).nullIfEmpty;
      
      final inspectionItems = (await DataModel().inspectionItemModel.dirty).nullIfEmpty;
      
      final inspectionImages = (await DataModel().inspectionImageModel.dirty).nullIfEmpty;
      
      final inspectionTemplates = (await DataModel().inspectionTemplateModel.dirty).nullIfEmpty;
      
      final inspectionTemplateAreas = (await DataModel().inspectionTemplateAreaModel.dirty).nullIfEmpty;
      
      final inspectionTemplateItems = (await DataModel().inspectionTemplateItemModel.dirty).nullIfEmpty;
      
      final jobTypes = (await DataModel().jobTypeModel.dirty).nullIfEmpty;
      
      final locations = (await DataModel().locationModel.dirty).nullIfEmpty;

      final locationContacts = (await DataModel().locationContactModel.dirty).nullIfEmpty;

      final locationNotes = (await DataModel().locationNoteModel.dirty).nullIfEmpty;
      
      final messages = (await DataModel().messageModel.dirty).nullIfEmpty;
      
      final notes = (await DataModel().noteModel.dirty).nullIfEmpty;
      
      final punchCards = (await DataModel().punchCardModel.dirty).nullIfEmpty;
      
      final schedules = (await DataModel().scheduleModel.dirty).nullIfEmpty;
      
      final users = (await DataModel().userModel.dirty).nullIfEmpty;
      
      final userTypes = (await DataModel().userTypeModel.dirty).nullIfEmpty;
      
      final userTypePermissions = (await DataModel().userTypePermissionModel.dirty).nullIfEmpty;

        if (alerts == null &&
            geoLocations == null &&
            inspections == null &&
            inspectionAreas == null &&
            inspectionItems == null &&
            inspectionImages == null &&
            inspectionTemplates == null &&
            inspectionTemplateAreas == null &&
            inspectionTemplateItems == null &&
            jobTypes == null &&
            locations == null &&
            locationContacts == null &&
            locationNotes == null &&
            messages == null &&
            notes == null &&
            punchCards == null &&
            schedules == null &&
            users == null &&
            userTypes == null &&
            userTypePermissions == null) return;

        final response = await ApiModel().postChanges(
          alerts: alerts,
          locationContacts: locationContacts,
          locationNotes: locationNotes,
          geoLocations: geoLocations,
          inspections: inspections,
          inspectionAreas: inspectionAreas,
          inspectionItems: inspectionItems,
          inspectionImages: inspectionImages,
          inspectionTemplates: inspectionTemplates,
          inspectionTemplateAreas: inspectionTemplateAreas,
          inspectionTemplateItems: inspectionTemplateItems,
          jobTypes: jobTypes,
          locations: locations,
          messages: messages,
          notes: notes,
          punchCards: punchCards,
          schedules: schedules,
          users: users,
          userTypes: userTypes,
          userTypePermissions: userTypePermissions,
        );

        if (response.isError) throw ('Error sending dirty changes ${response.errorCode}');

        allMarked &= await DataModel().alertModel.saveClean(alerts, sendStarted: sendDirtyStarted);
        allMarked &= await DataModel().locationContactModel.saveClean(locationContacts, sendStarted: sendDirtyStarted);
        allMarked &= await DataModel().locationNoteModel.saveClean(locationNotes, sendStarted: sendDirtyStarted);
        allMarked &= await DataModel().geoLocationModel.saveClean(geoLocations, sendStarted: sendDirtyStarted);
        allMarked &= await DataModel().inspectionModel.saveClean(inspections, sendStarted: sendDirtyStarted);
        allMarked &= await DataModel().inspectionAreaModel.saveClean(inspectionAreas, sendStarted: sendDirtyStarted);
        allMarked &= await DataModel().inspectionItemModel.saveClean(inspectionItems, sendStarted: sendDirtyStarted);
        allMarked &= await DataModel().inspectionTemplateModel.saveClean(inspectionTemplates, sendStarted: sendDirtyStarted);
        allMarked &= await DataModel().inspectionTemplateAreaModel.saveClean(inspectionTemplateAreas, sendStarted: sendDirtyStarted);
        allMarked &= await DataModel().inspectionTemplateItemModel.saveClean(inspectionTemplateItems, sendStarted: sendDirtyStarted);
        allMarked &= await DataModel().jobTypeModel.saveClean(jobTypes, sendStarted: sendDirtyStarted);
        allMarked &= await DataModel().locationModel.saveClean(locations, sendStarted: sendDirtyStarted);
        allMarked &= await DataModel().messageModel.saveClean(messages, sendStarted: sendDirtyStarted);
        allMarked &= await DataModel().noteModel.saveClean(notes, sendStarted: sendDirtyStarted);
        allMarked &= await DataModel().punchCardModel.saveClean(punchCards, sendStarted: sendDirtyStarted);
        allMarked &= await DataModel().scheduleModel.saveClean(schedules, sendStarted: sendDirtyStarted);
        allMarked &= await DataModel().userModel.saveClean(users, sendStarted: sendDirtyStarted);
        allMarked &= await DataModel().userTypeModel.saveClean(userTypes, sendStarted: sendDirtyStarted);
        allMarked &= await DataModel().userTypePermissionModel.saveClean(userTypePermissions, sendStarted: sendDirtyStarted);

        // before marking dirtyInspectionImages as clean, send active images
        final activeInspectionImages = inspectionImages?.where((e) => e.isActive);
        if (!MyPlatform.isWeb && activeInspectionImages != null && activeInspectionImages.isNotEmpty) {
          final imageMap = {for (final e in activeInspectionImages) e.id: '${AppState().applicationSupportDirectory.path}/${e.id}.jpg'};
          await AssetsModel().sendImages(imageMap);
        }
        if (response.isError) throw ('Error sending dirty images ${response.errorCode}');
        allMarked &= await DataModel().inspectionImageModel.saveClean(inspectionImages, sendStarted: sendDirtyStarted);
      }
    } catch (e) {
      //if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }

  Future<void> _getConditionalChanges() async {
    final prefs = await SharedPreferences.getInstance();
    final lastSyncedString = prefs.getString('lastSynced');
    final DateTime lastSynced;
    if (lastSyncedString != null) {
      lastSynced = DateTime.parse(lastSyncedString);
    } else {
      lastSynced = DateTime.now().subtract(const Duration(days: 3000));
    }

    try {
      //final changes = await ApiModel().getConditionalChanges(DateTime.now().month, DateTime.now().year, lastSynced);

final changes = await ApiModel().getConditionalChanges(DateTime.now().month, DateTime.now().year, lastSynced);

print('saving..');

DataModel().alertModel.saveClean(changes.alerts);
DataModel().geoLocationModel.saveClean(changes.geoLocations);
DataModel().inspectionModel.saveClean(changes.inspections);
DataModel().inspectionAreaModel.saveClean(changes.inspectionAreas);
DataModel().inspectionItemModel.saveClean(changes.inspectionItems);
DataModel().inspectionImageModel.saveClean(changes.inspectionImages);
DataModel().inspectionTemplateModel.saveClean(changes.inspectionTemplates);
DataModel().inspectionTemplateAreaModel.saveClean(changes.inspectionTemplateAreas);
DataModel().inspectionTemplateItemModel.saveClean(changes.inspectionTemplateItems);
DataModel().jobTypeModel.saveClean(changes.jobTypes);
DataModel().locationModel.saveClean(changes.locations);
DataModel().locationContactModel.saveClean(changes.locationContacts);
DataModel().locationNoteModel.saveClean(changes.locationNotes);
DataModel().messageModel.saveClean(changes.messages);
DataModel().noteModel.saveClean(changes.notes);
DataModel().permissionModel.saveClean(changes.permissions);
DataModel().punchCardModel.saveClean(changes.punchCards);
DataModel().scheduleModel.saveClean(changes.schedules);
DataModel().scheduleTemplateModel.saveClean(changes.scheduleTemplates);
DataModel().userModel.saveClean(changes.users);
DataModel().userPermissionModel.saveClean(changes.userPermissions);
DataModel().userTypeModel.saveClean(changes.userTypes);
DataModel().userTypePermissionModel.saveClean(changes.userTypePermissions);
DataModel().languageModel.saveClean(changes.languages);
DataModel().travelPingModel.saveClean(changes.travelPings); // Save travelPings data

print('saved!');

if (!MyPlatform.isWeb) {
    final deactivatedInspectionImages = changes.inspectionImages.where((e) => !e.isActive);
    if (deactivatedInspectionImages.isNotEmpty) {
        await Future.wait(deactivatedInspectionImages.map((e) async {
            // Add your handling code for deactivated inspection images
        }));
    }
}

      await prefs.setString('lastSynced', (changes.serverTime ?? DateTime.now()).toIso8601String());
    } catch (e) {
      print('Error fetching conditional changes: $e\n${StackTrace.current}');
      rethrow;
    }
  }

  Future<void> _getChanges() async {
    final prefs = await SharedPreferences.getInstance();
    final lastSyncedString = prefs.getString('lastSynced');
    final DateTime lastSynced;
    if (lastSyncedString != null) {
      lastSynced = DateTime.parse(lastSyncedString);
    } else {
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 3000));
      lastSynced = thirtyDaysAgo;
    }

    try {

      print('hello1');

      final changes = await ApiModel().getChanges(lastSynced);


      print('hello2');

      await DataModel().alertModel.saveClean(changes.alerts);
      await DataModel().geoLocationModel.saveClean(changes.geoLocations);
      await DataModel().inspectionModel.saveClean(changes.inspections);
      await DataModel().inspectionAreaModel.saveClean(changes.inspectionAreas);
      await DataModel().inspectionItemModel.saveClean(changes.inspectionItems);
      await DataModel().inspectionImageModel.saveClean(changes.inspectionImages);
      await DataModel().inspectionTemplateModel.saveClean(changes.inspectionTemplates);
      await DataModel().inspectionTemplateAreaModel.saveClean(changes.inspectionTemplateAreas);
      await DataModel().inspectionTemplateItemModel.saveClean(changes.inspectionTemplateItems);
      await DataModel().jobTypeModel.saveClean(changes.jobTypes);
      await DataModel().locationModel.saveClean(changes.locations);
      await DataModel().locationContactModel.saveClean(changes.locationContacts);
        try {
          await DataModel().locationNoteModel.saveClean(changes.locationNotes);
        } catch (e) {
          print('Error saving Location Notes: $e');
        }
      await DataModel().messageModel.saveClean(changes.messages);
      await DataModel().noteModel.saveClean(changes.notes);
      await DataModel().permissionModel.saveClean(changes.permissions);
      await DataModel().punchCardModel.saveClean(changes.punchCards);

      // print('before scheduleModel ${DateTime.now().millisecondsSinceEpoch - start}');
      await DataModel().scheduleModel.saveClean(changes.schedules);

      // print('after scheduleModel ${DateTime.now().millisecondsSinceEpoch - start}');
      await DataModel().scheduleTemplateModel.saveClean(changes.scheduleTemplates);
      await DataModel().userModel.saveClean(changes.users);
      await DataModel().userPermissionModel.saveClean(changes.userPermissions);
      await DataModel().userTypeModel.saveClean(changes.userTypes);
      await DataModel().userTypePermissionModel.saveClean(changes.userTypePermissions);
      await DataModel().languageModel.saveClean(changes.languages);

      // print('after all save  ${DateTime.now().millisecondsSinceEpoch - start}');

      if (!MyPlatform.isWeb) {
        final deactivatedInspectionImages = changes.inspectionImages.where((e) => !e.isActive);
        if (deactivatedInspectionImages.isNotEmpty) {
          await Future.wait(deactivatedInspectionImages.map((e) async {
            final filePath = '${AppState().applicationSupportDirectory.path}\\${e.id}.jpg';
            final file = File(filePath);
            if (await file.exists()) await file.delete();
          }));
        }
      }
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 3000));
      await prefs.setString('lastSynced', (changes.serverTime ?? thirtyDaysAgo).toIso8601String());
    } catch (e) {
      print('Error fetching changes: $e\n${StackTrace.current}');
      rethrow;
    }
  }


// Future<void> _getChanges() async {
//     final prefs = await SharedPreferences.getInstance();
//     final lastSyncedString = prefs.getString('lastSynced');
//     final DateTime lastSynced = lastSyncedString != null ? DateTime.parse(lastSyncedString) : DateTime.now().subtract(const Duration(days: 3000));

//     try {
//       // Fetch all data
//       final List<String> allTables = [
//         'alerts', 'locationcontacts', 'locationnotes', 'geolocations', 
//         'inspections', 'inspectionareas', 'inspectionitems', 'inspectionimages',
//         'inspectiontemplates', 'inspectiontemplateareas', 'inspectiontemplateitems', 
//         'jobtypes', 'locations', 'notes', 'messages', 'punchcards', 
//         'users', 'usertypes', 'usertypepermissions', 'permissions', 'languages'
//       ];
//       final allDataChanges = await ApiModel().getAllData(allTables);

//       // Process each type of data with appropriate sync time
//       await DataModel().alertModel.saveClean(allDataChanges.alerts);
//       await DataModel().geoLocationModel.saveClean(allDataChanges.geoLocations);
//       await DataModel().inspectionModel.saveClean(allDataChanges.inspections);
//       await DataModel().inspectionAreaModel.saveClean(allDataChanges.inspectionAreas);
//       await DataModel().inspectionItemModel.saveClean(allDataChanges.inspectionItems);
//       await DataModel().inspectionImageModel.saveClean(allDataChanges.inspectionImages);
//       await DataModel().inspectionTemplateModel.saveClean(allDataChanges.inspectionTemplates);
//       await DataModel().inspectionTemplateAreaModel.saveClean(allDataChanges.inspectionTemplateAreas);
//       await DataModel().inspectionTemplateItemModel.saveClean(allDataChanges.inspectionTemplateItems);
//       await DataModel().jobTypeModel.saveClean(allDataChanges.jobTypes);
//       await DataModel().locationModel.saveClean(allDataChanges.locations);
//       await DataModel().locationContactModel.saveClean(allDataChanges.locationContacts);
//       await DataModel().locationNoteModel.saveClean(allDataChanges.locationNotes);
//       await DataModel().messageModel.saveClean(allDataChanges.messages);
//       await DataModel().noteModel.saveClean(allDataChanges.notes);
//       await DataModel().permissionModel.saveClean(allDataChanges.permissions);
//       await DataModel().punchCardModel.saveClean(allDataChanges.punchCards);
//       await DataModel().userModel.saveClean(allDataChanges.users);
//       await DataModel().userTypeModel.saveClean(allDataChanges.userTypes);
//       await DataModel().userTypePermissionModel.saveClean(allDataChanges.userTypePermissions);
//       await DataModel().languageModel.saveClean(allDataChanges.languages);

//       // Fetch recent data for schedules
//       final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
//       final List<String> scheduleTables = ['schedules', 'scheduletemplates'];
//       final recentDataChanges = await ApiModel().getRecentData(scheduleTables, thirtyDaysAgo);
//       await DataModel().scheduleModel.saveClean(recentDataChanges.schedules);
//       await DataModel().scheduleTemplateModel.saveClean(recentDataChanges.scheduleTemplates);

//       if (!MyPlatform.isWeb) {
//         final deactivatedInspectionImages = allDataChanges.inspectionImages.where((e) => !e.isActive);
//         if (deactivatedInspectionImages.isNotEmpty) {
//           await Future.wait(deactivatedInspectionImages.map((e) async {
//             final filePath = '${AppState().applicationSupportDirectory.path}\\${e.id}.jpg';
//             final file = File(filePath);
//             if (await file.exists()) await file.delete();
//           }));
//         }
//       }
//       await prefs.setString('lastSynced', (allDataChanges.serverTime ?? thirtyDaysAgo).toIso8601String());
//     } catch (e) {
//       print('Error fetching changes: $e\n${StackTrace.current}');
//       rethrow;
//     }
//   }

  @override
  void dispose() {
    unawaited(subscription?.cancel());
    super.dispose();
  }

  Future<void> pushAll() async {
    try {
      final dirtyAlerts = await DataModel().alertModel.all;
      final dirtyLocationContacts = await DataModel().locationContactModel.all;
      final dirtyLocationNotes = await DataModel().locationNoteModel.all;
      final dirtyGeoLocations = await DataModel().geoLocationModel.all;
      final dirtyInspections = await DataModel().inspectionModel.all;
      final dirtyInspectionAreas = await DataModel().inspectionAreaModel.all;
      final dirtyInspectionItems = await DataModel().inspectionItemModel.all;
      final dirtyInspectionImages = await DataModel().inspectionImageModel.all;
      final dirtyInspectionTemplates = await DataModel().inspectionTemplateModel.all;
      final dirtyInspectionTemplateAreas = await DataModel().inspectionTemplateAreaModel.all;
      final dirtyInspectionTemplateItems = await DataModel().inspectionTemplateItemModel.all;
      final dirtyJobTypes = await DataModel().jobTypeModel.all;
      final dirtyLocations = await DataModel().locationModel.all;
      final dirtyMessages = await DataModel().messageModel.all;
      final dirtyNotes = await DataModel().noteModel.all;
      final dirtyPunchCards = await DataModel().punchCardModel.all;
      final dirtySchedules = await DataModel().scheduleModel.all;
      final dirtyUsers = await DataModel().userModel.all;
      final dirtyUserTypes = await DataModel().userTypeModel.all;

      final response = await ApiModel().postChanges(
        alerts: dirtyAlerts,
        locationContacts: dirtyLocationContacts,
        locationNotes: dirtyLocationNotes,
        geoLocations: dirtyGeoLocations,
        inspections: dirtyInspections,
        inspectionAreas: dirtyInspectionAreas,
        inspectionItems: dirtyInspectionItems,
        inspectionImages: dirtyInspectionImages,
        inspectionTemplates: dirtyInspectionTemplates,
        inspectionTemplateAreas: dirtyInspectionTemplateAreas,
        inspectionTemplateItems: dirtyInspectionTemplateItems,
        jobTypes: dirtyJobTypes,
        locations: dirtyLocations,
        messages: dirtyMessages,
        notes: dirtyNotes,
        punchCards: dirtyPunchCards,
        schedules: dirtySchedules,
        users: dirtyUsers,
        userTypes: dirtyUserTypes,
      );

      if (response.isError) throw ('Error pushing all ${response.errorCode}');
    } catch (e) {
      //if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }
}
