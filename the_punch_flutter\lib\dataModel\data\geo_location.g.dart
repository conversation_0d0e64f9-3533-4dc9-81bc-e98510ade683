// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'geo_location.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class GeoLocationAdapter extends TypeAdapter<GeoLocation> {
  @override
  final int typeId = 4;

  @override
  GeoLocation read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return GeoLocation(
      isDirty: fields[0] as bool,
      isActive: fields[1] as bool,
      createdOn: fields[2] as DateTime,
      createdByUserId: fields[3] as String?,
      lastChangedOn: fields[4] as DateTime?,
      lastChangedByUserId: fields[5] as String?,
      id: fields[100] as String,
      punchCardId: fields[101] as String,
      recordedOn: fields[102] as DateTime,
      latitude: fields[103] as double?,
      longitude: fields[104] as double?,
      geoLocationTypeId: fields[105] as String,
      distanceFromLocation: fields[106] as double?,
    );
  }

  @override
  void write(BinaryWriter writer, GeoLocation obj) {
    writer
      ..writeByte(13)
      ..writeByte(100)
      ..write(obj.id)
      ..writeByte(101)
      ..write(obj.punchCardId)
      ..writeByte(102)
      ..write(obj.recordedOn)
      ..writeByte(103)
      ..write(obj.latitude)
      ..writeByte(104)
      ..write(obj.longitude)
      ..writeByte(105)
      ..write(obj.geoLocationTypeId)
      ..writeByte(106)
      ..write(obj.distanceFromLocation)
      ..writeByte(0)
      ..write(obj.isDirty)
      ..writeByte(1)
      ..write(obj.isActive)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdByUserId)
      ..writeByte(4)
      ..write(obj.lastChangedOn)
      ..writeByte(5)
      ..write(obj.lastChangedByUserId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GeoLocationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GeoLocation _$GeoLocationFromJson(Map<String, dynamic> json) => GeoLocation(
      isActive: json['IsActive'] as bool? ?? true,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
      id: idFromJson(json['Id']),
      punchCardId: idFromJson(json['PunchCardId']),
      recordedOn: dateTimeFromJson(json['RecordedOn']),
      latitude: (json['Latitude'] as num?)?.toDouble(),
      longitude: (json['Longitude'] as num?)?.toDouble(),
      geoLocationTypeId: idFromJson(json['GeoLocationTypeId']),
      distanceFromLocation: (json['DistanceFromLocation'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$GeoLocationToJson(GeoLocation instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['PunchCardId'] = instance.punchCardId;
  val['RecordedOn'] = dateTimeToJson(instance.recordedOn);
  writeNotNull('Latitude', instance.latitude);
  writeNotNull('Longitude', instance.longitude);
  val['GeoLocationTypeId'] = instance.geoLocationTypeId;
  writeNotNull('DistanceFromLocation', instance.distanceFromLocation);
  return val;
}
