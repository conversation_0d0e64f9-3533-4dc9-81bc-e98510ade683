import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../base_data.dart';
import '../../misc/json_conversion.dart';

part 'schedule.g.dart';

@HiveType(typeId: 18)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class Schedule extends BaseData {
  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  @Json<PERSON>ey(name: 'StartDateUTC', fromJson: dateTimeFromJson, toJson: dateTimeToJson)
  DateTime startDateUtc;

  @HiveField(102)
  @Json<PERSON><PERSON>(name: 'EndDateUTC', fromJson: dateTimeFromJson, toJson: dateTimeToJson)
  DateTime endDateUtc;

  @HiveField(103)
  @JsonKey(fromJson: idFromJson)
  String locationId;

  @HiveField(104)
  @JsonKey(fromJson: idFromJson)
  String userId;

  @HiveField(105)
  @Json<PERSON>ey(fromJson: idFromJson)
  String scheduleTemplateId;

  @HiveField(106)
  @JsonKey(fromJson: nullableDurationFromJson, toJson: nullableDurationToJson)
  Duration? travelTime;

  @HiveField(107)
  @JsonKey(fromJson: dateTimeFromJson, toJson: dateTimeToJson)
  DateTime startDateLocal;

  @HiveField(108)
  @JsonKey(fromJson: dateTimeFromJson, toJson: dateTimeToJson)
  DateTime endDateLocal;

  @HiveField(109)
  @JsonKey(fromJson: dateTimeFromJson, toJson: dateTimeToJson)
  DateTime originalStartDateLocal;

  @HiveField(110)
  @JsonKey(fromJson: nullableIdFromJson)
  String? nextLocationId;

  @HiveField(111)
  @JsonKey(name: 'notification1sent', defaultValue: false)
  bool notification1sent;

  @HiveField(112)
  @JsonKey(name: 'notification2sent', defaultValue: false)
  bool notification2sent;

  @HiveField(113)
  @JsonKey(name: 'notification3sent', defaultValue: false)
  bool notification3sent;

  @HiveField(114)
  @JsonKey(name: 'notification4sent', defaultValue: false)
  bool notification4sent;

  Schedule({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.locationId,
    required this.startDateUtc,
    required this.endDateUtc,
    required this.userId,
    required this.scheduleTemplateId,
    this.travelTime,
    required this.startDateLocal,
    required this.endDateLocal,
    required this.originalStartDateLocal,
    this.nextLocationId,
    this.notification1sent = false,
    this.notification2sent = false,
    this.notification3sent = false,
    this.notification4sent = false,
  }) {
    originalStartDateLocal = originalStartDateLocal.toUtc();
    startDateLocal = startDateLocal.toUtc();
    endDateLocal = endDateLocal.toUtc();
  }

  factory Schedule.fromJson(Map<String, dynamic> json) {
    try {
      return _$ScheduleFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() => _$ScheduleToJson(this);

  factory Schedule.from(Schedule o) => Schedule(
        isDirty: o.isDirty,
        isActive: o.isActive,
        createdOn: o.createdOn,
        createdByUserId: o.createdByUserId,
        lastChangedOn: o.lastChangedOn,
        lastChangedByUserId: o.lastChangedByUserId,
        id: o.id,
        locationId: o.locationId,
        startDateUtc: o.startDateUtc,
        endDateUtc: o.endDateUtc,
        userId: o.userId,
        scheduleTemplateId: o.scheduleTemplateId,
        travelTime: o.travelTime,
        startDateLocal: o.startDateLocal,
        endDateLocal: o.endDateLocal,
        originalStartDateLocal: o.originalStartDateLocal,
        nextLocationId: o.nextLocationId,
        notification1sent: o.notification1sent,
        notification2sent: o.notification2sent,
        notification3sent: o.notification3sent,
        notification4sent: o.notification4sent,
      );
}


@HiveType(typeId: 24)
class ScheduleIdsByMonth {
  @HiveField(100)
  List<String> ids = [];
}
