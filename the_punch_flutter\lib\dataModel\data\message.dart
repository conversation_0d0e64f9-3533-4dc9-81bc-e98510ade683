import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../base_data.dart';
import '../../misc/json_conversion.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import 'package:uuid/uuid.dart';

part 'message.g.dart';

@HiveType(typeId: 14)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class Message extends BaseData {
  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  String body;

  @HiveField(102)
  @JsonKey(fromJson: idFromJson)
  String fromUserId;

  @HiveField(103)
  @JsonKey(fromJson: idFromJson)
  String toGroupId;

  @HiveField(104)
  @Json<PERSON>ey(toJson: dateTimeToJson, fromJson: dateTimeFromJson)
  DateTime sentOn;

  @HiveField(105)
  @JsonKey(toJson: nullableDateTimeToJson, fromJson: nullableDateTimeFromJson)
  DateTime? openedOn;

  Message({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.body,
    required this.fromUserId,
    required this.toGroupId,
    required this.sentOn,
    this.openedOn,
  });

  factory Message.fromJson(Map<String, dynamic> json) {
    try {
      return _$MessageFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }
  Map<String, dynamic> toJson() => _$MessageToJson(this);

  factory Message.create() => Message(
        id: const Uuid().v4(),
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
        sentOn: ServerTimeState().utcTime,
        toGroupId: '',
        fromUserId: LoginState.userId,
        body: '',
      );
}
