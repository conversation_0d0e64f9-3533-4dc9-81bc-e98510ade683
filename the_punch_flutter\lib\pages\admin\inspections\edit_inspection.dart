import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../../dataModel/data_model.dart';
import '../../../dataModel/data/inspection.dart';
import '../../../dataModel/data/location.dart';
import '../../../helpers/color_helper.dart';
import '../../../helpers/screen_helper.dart';
import '../../view_model_mixin.dart';
import '../../../state/server_time_state.dart';
import '../../../widgets/date_pill.dart';
import '../../../widgets/padded_card.dart';
import '../../../misc/extensions.dart';
import '../../../misc/collection_extensions.dart';
import '../../web/home/<USER>';
import '../../web/my_scaffold.dart';

class EditInspectionPage extends StatelessWidget {
  final String id;
  final String newInspection;

  EditInspectionPage(Map<String, String> queryParms, {super.key})
      : id = queryParms['id'] ?? '',
        newInspection = queryParms['1'] ?? '';

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider(
        create: (context) => _ViewModel(id),
        builder: (context, child) => MyScaffold(
          title: newInspection == '1'
              ? AppLocalizations.of(context)!.createNewInspection
              : AppLocalizations.of(context)!.editInspection,
          // body: MyBody(header: _Header(), body: _Body()),
          body: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _Header(),
              Expanded(
                child: _Body(),
              )
            ],
          ),
        ),
      );
}

class _Header extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline5 = theme.textTheme.titleLarge?.copyWith(
      color: Colors.white,
    );
    final headline6 = theme.textTheme.titleMedium?.copyWith(
      color: Colors.white,
    );
    final caption = theme.textTheme.bodyMedium?.copyWith(color: Colors.white);

    return Padding(
      padding: EdgeInsets.only(
        top: ScreenHelper.screenHeightPercentage(context, 2),
      ),
      child: IntrinsicHeight(
        child: ColoredBox(
          color: ColorHelper.thePunchRed(),
          child: Consumer<_ViewModel>(
              builder: (context, data, child) => Center(
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        maxWidth: ScreenHelper.screenWidth(context),
                      ),
                      child: Stack(
                        children: [
                          Align(
                            alignment: Alignment.centerLeft,
                            child: Padding(
                              padding: const EdgeInsets.all(8),
                              child: DatePill(
                                  date: data.inspection?.dateTime
                                      .toLocal()
                                      .dateOnly),
                            ),
                          ),
                          Align(
                            child: Padding(
                              padding: EdgeInsets.only(
                                top: ScreenHelper.screenHeightPercentage(
                                    context, 2),
                              ),
                              child: Column(
                                children: [
                                  Text(data.location?.name ?? '',
                                      style: headline5),
                                  Text(data.inspection?.name ?? '',
                                      style: headline6),
                                ],
                              ),
                            ),
                          ),
                          Align(
                              alignment: Alignment.bottomRight,
                              child: Padding(
                                padding: const EdgeInsets.all(8),
                                child: Text(data.completed?.toPercent ?? '',
                                    style: caption),
                              )),
                        ],
                      ),
                    ),
                  )),
        ),
      ),
    );
  }
}

class _Body extends StatefulWidget {
  @override
  _BodyState createState() => _BodyState();
}

class _BodyState extends State<_Body> {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
        builder: (context, data, child) {
          if (data.areas == null || data.areas!.isEmpty) return Container();
          final areas = data.areas!.toList();
          final completedByArea = data.completedByArea!;
          final itemsByArea = data.itemsByArea!;
          final itemsWithNotes = data.itemsWithNotes!;
          final itemsWithImages = data.itemsWithImages!;

          return Padding(
            padding: EdgeInsets.only(
              top: ScreenHelper.screenHeightPercentage(context, 2),
            ),
            child: Center(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 500),
                child: ListView(
                  children: [
                    for (int i = 0; i < areas.length; i++) ...[
                      _AreaTile(
                        area: areas[i],
                        completed: completedByArea[areas[i].id] ?? 1.0,
                      ),
                      ...(itemsByArea[areas[i].id] ?? []).map(
                        (item) => _ItemTile(
                          item: item,
                          hasNote: itemsWithNotes.contains(item.id),
                          hasImage: itemsWithImages.contains(item.id),
                        ),
                      ),
                    ]
                  ],
                ),
              ),
            ),
          );
        },
      );
}

class _AreaTile extends StatelessWidget {
  const _AreaTile({required this.area, required this.completed});

  final InspectionArea area;
  final double completed;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline6 = theme.textTheme.titleLarge;
    final caption = theme.textTheme.bodySmall;

    return IntrinsicHeight(
      child: Stack(
        children: [
          Align(
            child: Text(area.name, style: headline6),
          ),
          Align(
            alignment: Alignment.bottomRight,
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Text(completed.toPercent, style: caption),
            ),
          ),
        ],
      ),
    );
  }
}

class _ItemTile extends StatefulWidget {
  const _ItemTile(
      {required this.item, required this.hasNote, required this.hasImage});

  final InspectionItem item;
  final bool hasNote;
  final bool hasImage;

  @override
  _ItemTileState createState() => _ItemTileState();
}

class _ItemTileState extends State<_ItemTile> {
  @override
  Widget build(BuildContext context) => GestureDetector(
        onTap: () async {
          await context.pushNamed('/inspections/edit/editItem',
              queryParameters: {'id': widget.item.id});
        },
        child: PaddedCard(
          elevation: 0,
          child: Row(
            children: [
              _createLabel(),
              _createIcons(),
              _createToggleButtons(),
            ],
          ),
        ),
      );

  Widget _createLabel() {
    final theme = Theme.of(context);
    final body1 = theme.textTheme.bodyLarge?.copyWith(
      color: ColorHelper.thePunchGray(),
    );
    return Expanded(
      child: Text(widget.item.name, style: body1),
    );
  }

  Widget _createIcons() => Padding(
        padding: EdgeInsets.only(
          right: ScreenHelper.screenWidthPercentage(context, 5),
        ),
        child: Column(mainAxisSize: MainAxisSize.min, children: [
          Visibility(
            visible: widget.hasNote,
            child: const Icon(Icons.edit, size: 15),
          ),
          SizedBox(
            height: ScreenHelper.screenHeightPercentage(context, 1),
          ),
          Visibility(
            visible: widget.hasImage,
            child: const Icon(Icons.camera_alt, size: 15),
          ),
        ]),
      );

  Widget _createToggleButtons() {
    final isSelected = [1, 2].map((i) => i == widget.item.grade).toList();
    return ToggleButtons(
      borderRadius: BorderRadius.circular(20),
      hoverColor: ColorHelper.thePunchGray(),
      color: ColorHelper.thePunchLightGray(),
      selectedColor: ColorHelper.thePunchRed(),
      onPressed: (int index) async {
        for (var buttonIndex = 0;
            buttonIndex < isSelected.length;
            buttonIndex++) {
          if (buttonIndex == index) {
            isSelected[buttonIndex] = !isSelected[buttonIndex];
          } else {
            isSelected[buttonIndex] = false;
          }
        }
        final selectedButton =
            [isSelected.indexOf(true)].map((e) => e == -1 ? null : e + 1).first;
        await Provider.of<_ViewModel>(context, listen: false)
            .setGrade(widget.item.id, selectedButton);
      },
      isSelected: isSelected,
      children: const [
        Icon(Icons.thumb_down),
        Icon(Icons.thumb_up),
      ],
    );
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String id;

  Inspection? inspection;
  Location? location;
  List<InspectionArea>? areas;
  Map<String, InspectionItem>? itemMap;
  Map<String, List<InspectionItem>>? itemsByArea;
  Set<String>? itemsWithNotes;
  Set<String>? itemsWithImages;
  double? completed;
  Map<String, double>? completedByArea;

  _ViewModel(this.id) {
    addListenables([
      DataModel().inspectionModel,
      DataModel().locationModel,
      DataModel().inspectionAreaModel,
      DataModel().inspectionItemModel,
      DataModel().inspectionImageModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    inspection = await DataModel().inspectionModel.getById(id);
    location = await DataModel().locationModel.getById(inspection!.locationId);
    areas =
        (await DataModel().inspectionAreaModel.getByInspectionId(id)).toList();
    areas!.sort((a, b) => a.order.compareTo(b.order));
    final items =
        (await DataModel().inspectionItemModel.getByInspectionId(id)).toList();
    itemMap = {for (final item in items) item.id: item};
    itemsByArea = items.groupBy((e) => e.inspectionAreaId);
    for (final items in itemsByArea!.values) {
      items.sort((a, b) => a.order.compareTo(b.order));
    }

    itemsWithNotes = items
        .where((e) => e.note != null && e.note!.isNotEmpty)
        .map((e) => e.id)
        .toSet();
    final itemIds = items.map((e) => e.id);
    itemsWithImages = (await DataModel()
            .inspectionImageModel
            .getInspectionItemIdsWithImages(itemIds))
        .toSet();

    if (items.isEmpty) {
      completed = 1.0;
      completedByArea = {};
    } else {
      completed = items.where((e) => e.grade != null).length / items.length;
      completedByArea = {
        for (final e in itemsByArea!.entries)
          e.key: e.value.where((item) => item.grade != null).length /
              e.value.length
      };
    }
    notifyListeners();
  }

  Future<void> setGrade(String id, int? grade) async {
    final inspectionItem = itemMap![id]!;
    inspectionItem.grade = grade;
    inspectionItem.lastChangedOn = ServerTimeState().utcTime;
    inspection!.lastChangedOn = ServerTimeState().utcTime;
    await DataModel().inspectionModel.saveDirty([inspection!]);
    await DataModel().inspectionItemModel.saveDirty([inspectionItem]);
  }
}
