// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'language.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LanguageAdapter extends TypeAdapter<Language> {
  @override
  final int typeId = 25;

  @override
  Language read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Language(
      isDirty: fields[0] as bool,
      isActive: fields[1] as bool,
      createdOn: fields[2] as DateTime,
      createdByUserId: fields[3] as String?,
      lastChangedOn: fields[4] as DateTime?,
      lastChangedByUserId: fields[5] as String?,
      id: fields[100] as String,
      key: fields[101] as String,
      isDefault: fields[102] as bool,
      name: fields[103] as String,
    );
  }

  @override
  void write(BinaryWriter writer, Language obj) {
    writer
      ..writeByte(10)
      ..writeByte(100)
      ..write(obj.id)
      ..writeByte(101)
      ..write(obj.key)
      ..writeByte(102)
      ..write(obj.isDefault)
      ..writeByte(103)
      ..write(obj.name)
      ..writeByte(0)
      ..write(obj.isDirty)
      ..writeByte(1)
      ..write(obj.isActive)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdByUserId)
      ..writeByte(4)
      ..write(obj.lastChangedOn)
      ..writeByte(5)
      ..write(obj.lastChangedByUserId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LanguageAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Language _$LanguageFromJson(Map<String, dynamic> json) => Language(
      isActive: json['IsActive'] as bool? ?? true,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
      id: idFromJson(json['Id']),
      key: json['Key'] as String,
      isDefault: json['IsDefault'] as bool,
      name: '',
    );

Map<String, dynamic> _$LanguageToJson(Language instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['Key'] = instance.key;
  val['IsDefault'] = instance.isDefault;
  val['Name'] = instance.name;
  return val;
}
