// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_schedules_for_month.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetSchedulesForMonthRequest _$GetSchedulesForMonthRequestFromJson(
        Map<String, dynamic> json) =>
    GetSchedulesForMonthRequest(
      month: (json['Month'] as num).toInt(),
      year: (json['Year'] as num).toInt(),
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$GetSchedulesForMonthRequestToJson(
        GetSchedulesForMonthRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'Month': instance.month,
      'Year': instance.year,
    };

GetSchedulesForMonthResponse _$GetSchedulesForMonthResponseFromJson(
        Map<String, dynamic> json) =>
    GetSchedulesForMonthResponse(
      schedules: (json['Schedules'] as List<dynamic>?)
              ?.map((e) => Schedule.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$GetSchedulesForMonthResponseToJson(
    GetSchedulesForMonthResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['Schedules'] = instance.schedules;
  return val;
}
