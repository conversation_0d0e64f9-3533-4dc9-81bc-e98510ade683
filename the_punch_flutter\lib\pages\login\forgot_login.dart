// ignore_for_file: use_build_context_synchronously

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../api/api_model.dart';
import '../../dialogs/busy_dialog.dart';
import '../../dialogs/error_dialog.dart';
import '../../dialogs/message_dialog.dart';
import '../../dialogs/organizations_dialog.dart';
import '../../helpers/screen_helper.dart';
import '../../misc/app_localization.dart';
import '../../misc/change_notification_builder.dart';
import '../../misc/logging.dart';
import '../../misc/my_platform.dart';
import '../../widgets/captcha/grecaptcha2_stub.dart'
    if (dart.library.html) 'package:the_punch_flutter/widgets/captcha/grecaptcha2.dart'
    as grecaptcha2;
import '../../widgets/decorated_text_field.dart';
import '../../widgets/padded_card.dart';
import '../../misc/extensions.dart';
import '../web/my_scaffold.dart';

class ForgotLoginPage extends StatelessWidget {
  const ForgotLoginPage({super.key});
  @override
  Widget build(BuildContext context) => MyScaffold(
        enableBottomBar: false,
        showLoggedOutDrawer: true,
        showDesktopHeader: false,
        title: AppLocalization.of(context).forgotLogin,
        body: const Center(
          child: Padding(
            padding: EdgeInsets.all(8),
            child: _Body(),
          ),
        ),
      );
}

class _Body extends StatelessWidget {
  const _Body();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline6 = theme.textTheme.titleLarge;

    return ChangeNotifierBuilder<_ViewModel>(
      create: (context) => _ViewModel(),
      builder: (context, viewModel, _) => ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: ScreenHelper.screenWidth(context),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            PaddedCard(
              elevation: 1,
              color: Colors.white,
              child: Flex(
                mainAxisSize: MainAxisSize.min,
                direction: Axis.vertical,
                children: [
                  Text(AppLocalization.of(context).forgotLogin,
                      style: headline6),
                  SizedBox(
                    height: ScreenHelper.screenHeightPercentage(context, 2),
                  ),
                  const _Row1(),
                  const _Row2(),
                ],
              ),
            ),
            if (MyPlatform.isWeb) _Captcha(),
            SizedBox(
              height: ScreenHelper.screenHeightPercentage(context, 2),
            ),
            ElevatedButton(
              onPressed: !viewModel.validateRequest
                  ? null
                  : () => unawaited(
                        _completeRequest(context, viewModel),
                      ),
              child: Text(AppLocalization.of(context).send),
            ),
          ],
        ),
      ),
    );
  }
}

Future<void> _completeRequest(
    BuildContext context, _ViewModel viewModel) async {
  await showDialog(
      context: context,
      builder: (context) {
        final router = GoRouter.of(context);
        return BusyDialog(future: () async {
          try {
            await ApiModel().forgotLogin(
                organizationId: viewModel.organizationId,
                email: viewModel.email,
                captchaResponse: viewModel.captchaResponse);
            if (!context.mounted) return;
            await showDialog(
              context: context,
              builder: (context) => MessageDialog(
                title: AppLocalization.of(context).forgotLogin,
                message: 'Email sent to ${viewModel.email}.',
              ),
            );
            router.go('/login');
          } on ApiException catch (e, stack) {
            await logApiException(e, stack);
            if (!context.mounted) return;
            await showDialog(
                context: context,
                builder: (context) => ErrorDialog(errorCode: e.errorCode));
          } on Exception catch (e, stack) {
            await logException(e, stack);
            if (!context.mounted) return;
            await showDialog(
                context: context, builder: (context) => const ErrorDialog());
          }
        });
      });
}

class _Row1 extends StatefulWidget {
  const _Row1();
  @override
  State<_Row1> createState() => _Row1State();
}

class _Row1State extends State<_Row1> {
  final controller = TextEditingController();
  late _ViewModel viewModel;

  @override
  void initState() {
    viewModel = context.read<_ViewModel>();
    viewModel.addListener(viewModelListener);
    controller.text = viewModel.organizationId;

    super.initState();
  }

  @override
  void dispose() {
    viewModel.removeListener(viewModelListener);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => DecoratedTextField(
        padding: const EdgeInsets.all(8),
        controller: controller,
        labelText: AppLocalization.of(context).organizationId,
        onChanged: (value) => viewModel.setOrganizationId(value),
        validator: (value) => viewModel.organizationId.isEmpty
            ? AppLocalization.of(context).organizationIdRequired
            : null,
        suffixIcons: [
          IconButton(
              icon: const Icon(Icons.search),
              onPressed: () => searchOrganizationId(context, viewModel)),
        ],
      );

  void viewModelListener() {
    if (controller.text != viewModel.organizationId) {
      controller.text = viewModel.organizationId;
    }
  }

  void searchOrganizationId(BuildContext context, _ViewModel viewModel) {
    if (!context.mounted) return;
    unawaited(showDialog(
        context: context,
        builder: (BuildContext context) => OrganizationsDialog(
            organizationSelected: (value) =>
                viewModel.setOrganizationId(value))));
  }
}

class _Row2 extends StatelessWidget {
  const _Row2();

  @override
  Widget build(BuildContext context) {
    final viewModel = context.read<_ViewModel>();

    return DecoratedTextField(
      padding: const EdgeInsets.all(8),
      initialValue: viewModel.email,
      labelText: AppLocalization.of(context).emailAddress,
      keyboardType: TextInputType.emailAddress,
      onChanged: viewModel.setEmail,
      validator: (value) => viewModel.email.isEmpty
          ? AppLocalization.of(context).emailRequired
          : !viewModel.validateEmail
              ? AppLocalization.of(context).invalidEmail
              : null,
    );
  }
}

class _Captcha extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final viewModel = context.watch<_ViewModel>();
    return grecaptcha2.GRecaptcha2(viewModel.setCaptchaResponse);
    // return ElevatedButton(
    //   child: const Text('Get new token'),
    //   onPressed: () async {
    //     String token = await GRecaptchaV3.execute('submit') ?? '';
    //     viewModel.setCaptchaResponse(token);
    //   },
    // );
  }
}

class _ViewModel extends ChangeNotifier {
  var organizationId = '';
  var email = '';
  var captchaResponse =
      MyPlatform.isWeb ? '' : 'no captcha available on platform';

  _ViewModel();

  bool get validateOrganizationId => organizationId.isNotEmpty;
  bool get validateEmail => email.validateEmail;
  bool get validateCaptchaResponse => captchaResponse.isNotEmpty;
  bool get validateRequest => validateEmail && validateCaptchaResponse;

  void setOrganizationId(String value) {
    if (organizationId == value) return;
    organizationId = value;
    notifyListeners();
  }

  void setEmail(String value) {
    if (email == value) return;
    email = value;
    notifyListeners();
  }

  void setCaptchaResponse(String value) {
    captchaResponse = value;
    notifyListeners();
  }
}
