import 'package:json_annotation/json_annotation.dart';
import '../../misc/json_conversion.dart';

import 'system.dart';

part 'reports.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class ReportsRequest extends SystemRequest {
  bool? isActive;

  ReportsRequest({
    required this.isActive,
    required super.serverIP,
    required super.databaseName,
    required super.sessionId,
  });

  static Future<ReportsRequest> create(bool? isActive) async {
    final systemRequest = await SystemRequest.create();

    return ReportsRequest(
      isActive: isActive,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory ReportsRequest.fromJson(Map<String, dynamic> json) => _$ReportsRequestFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$ReportsRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class ReportsResponse extends SystemResponse {
  List<Report> reports;

  ReportsResponse({
    required this.reports,
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory ReportsResponse.fromJson(Map<String, dynamic> json) => _$ReportsResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$ReportsResponseToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class Report {
  String id;
  String name;
  String description;
  @JsonKey(name: 'SSRSName')
  String ssrsName;

  Report({
    required this.id,
    required this.name,
    required this.description,
    required this.ssrsName,
  });

  factory Report.fromJson(Map<String, dynamic> json) => _$ReportFromJson(json);
  Map<String, dynamic> toJson() => _$ReportToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class GetReportRequest extends SystemRequest {
  String id;

  GetReportRequest({
    required this.id,
    required super.serverIP,
    required super.databaseName,
    required super.sessionId,
  });

  static Future<GetReportRequest> create(String id) async {
    final systemRequest = await SystemRequest.create();

    return GetReportRequest(
      id: id,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory GetReportRequest.fromJson(Map<String, dynamic> json) => _$GetReportRequestFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetReportRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class GetReportResponse extends SystemResponse {
  List<Parameter> parameters;

  GetReportResponse({
    required this.parameters,
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory GetReportResponse.fromJson(Map<String, dynamic> json) => _$GetReportResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$GetReportResponseToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class Parameter {
  String name;
  String prompt;
  @JsonKey(name: 'UIControl')
  String uiControl;
  @JsonKey(defaultValue: '')
  String value;
  @JsonKey(defaultValue: {})
  Map<String, String> validValues;
  @JsonKey(ignore: true)
  DateTime? dateTime;

  Parameter({
    required this.name,
    required this.prompt,
    required this.uiControl,
    required this.value,
    required this.validValues,
  });

  factory Parameter.fromJson(Map<String, dynamic> json) => _$ParameterFromJson(json);
  Map<String, dynamic> toJson() => _$ParameterToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class RunReportRequest extends SystemRequest {
  String id;
  Map<String, String> parameters;
  String reportType;

  RunReportRequest({
    required this.id,
    required this.parameters,
    required this.reportType,
    required super.serverIP,
    required super.databaseName,
    required super.sessionId,
  });

  static Future<RunReportRequest> create(String id, Map<String, String> parameters, String reportType) async {
    final systemRequest = await SystemRequest.create();

    return RunReportRequest(
      id: id,
      parameters: parameters,
      reportType: reportType,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory RunReportRequest.fromJson(Map<String, dynamic> json) => _$RunReportRequestFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$RunReportRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class RunReportResponse extends SystemResponse {
  String reportData;
  String fileName;

  RunReportResponse({
    required this.reportData,
    required this.fileName,
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory RunReportResponse.fromJson(Map<String, dynamic> json) => _$RunReportResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$RunReportResponseToJson(this);
}
