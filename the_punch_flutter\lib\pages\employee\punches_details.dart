// File: punches_details_page.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../dataModel/data/punch_card.dart';
import '../../dataModel/data_model.dart';
import '../../helpers/color_helper.dart';
import '../view_model_mixin.dart';
import '../web/my_scaffold.dart';

class PunchesDetailsPage extends StatelessWidget {
  final String punchCardLinkId;

  PunchesDetailsPage(Map<String, String> queryParams, {super.key})
      : punchCardLinkId = queryParams['linkId'] ?? '';

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider(
        create: (context) => _ViewModel(linkId: punchCardLinkId),
        child: MyScaffold(
          showBackButton: true,
          title: 'Punch Card',
          body: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _Header(),
              Expanded(
                child: _Body(),
              ),
            ],
          ),
        ),
      );
}

class _Header extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryTextStyle = theme.textTheme.titleLarge?.copyWith(
      color: ColorHelper.thePunchGray(),
    );

    return Consumer<_ViewModel>(builder: (context, viewModel, _) {
      if (viewModel.punchCards.isEmpty) {
        return Container();
      }

      return IntrinsicHeight(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(15),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Punch Card',
                    style: primaryTextStyle,
                  ),
                  if (viewModel.hasLivePunchCard)
                    Text(
                      'LIVE',
                      style: primaryTextStyle!.copyWith(color: Colors.green),
                    ),
                ],
              ),
            ),
            const Divider(),
          ],
        ),
      );
    });
  }
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(builder: (context, viewModel, _) {
      if (viewModel.punchCards.isEmpty) {
        return const Center(
          child: Text('No Punch Cards.'),
        );
      }

      return ListView.builder(
        itemCount: viewModel.punchCards.length,
        itemBuilder: (context, index) {
          final punchCard = viewModel.punchCards[index];
          final punchInOutString =
              '${punchCard.clockedIn} - ${punchCard.clockedOut ?? 'Ongoing'}';

          return ListTile(
            title: Text('Task ${index + 1}'),
            subtitle: Text(punchInOutString),
          );
        },
      );
    });
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String linkId;
  List<PunchCard> punchCards = [];
  bool get hasLivePunchCard =>
      punchCards.any((punchCard) => punchCard.clockedOut == null);

  _ViewModel({required this.linkId}) {
    refresh();
  }

  Future<void> refresh() async {
    try {
      punchCards = (await DataModel().punchCardModel.getAllByLinkId(linkId)).toList();
      notifyListeners();
    } catch (e) {
      // Error handling can be added here if needed
    }
  }
}
