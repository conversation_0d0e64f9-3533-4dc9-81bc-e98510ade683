import '../base_data.dart';
import '../data/location_contact.dart';
import '../hive_db.dart';

class LocationContactModel extends BaseDataModel<LocationContact> {
  @override
  Future<Iterable<LocationContact>> get all async => (await HiveDb.database).locationContacts.values;

  @override
  Future<void> save(Iterable<LocationContact> t) async {
    await (await HiveDb.database).locationContacts.putAll({for (final e in t) e.id: e});
    notifyListeners();
  }

  Future<Iterable<LocationContact>> getByLocationIds(Iterable<String> ids) async => ids.isEmpty ? [] : (await active).where((e) => ids.contains(e.locationId));

  Future<Iterable<LocationContact>> getByUserIds(Iterable<String> ids) async => ids.isEmpty ? [] : (await active).where((e) => ids.contains(e.userId));

  Future<Iterable<LocationContact>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }
}
