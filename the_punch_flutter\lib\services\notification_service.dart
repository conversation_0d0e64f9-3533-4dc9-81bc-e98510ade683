import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../navigator_keys.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  static Future<void> initialize() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('logo512');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings();

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) {
        // Handle the notification tap
        handleNotificationClick(response.payload);
      },
    );
  }

  static Future<void> showNotification({
    required String title,
    required String body,
    String? payload, // Added payload parameter
  }) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'the_punch_channel_id',
      'The Punch Alerts',
      channelDescription: 'Notifications for The Punch',
      importance: Importance.max,
      priority: Priority.high,
    );

    const DarwinNotificationDetails iosPlatformChannelSpecifics =
        DarwinNotificationDetails();

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iosPlatformChannelSpecifics,
    );

    await _notificationsPlugin.show(
      0,
      title,
      body,
      platformChannelSpecifics,
      payload: payload, // Pass the payload here
    );
  }

  // Add the notification handler here
  static void handleNotificationClick(String? targetPage) {
    if (targetPage != null) {
      final context = rootNavigatorKey.currentContext;
      print('Notification clicked with targetPage: $targetPage');
      if (context != null) {
        GoRouter.of(context).go(targetPage);
      } else {
        print('Error: rootNavigatorKey.currentContext is null');
      }
    }
  }
}
