import 'package:flutter/widgets.dart';

mixin ViewModelMixin on ChangeNotifier {
  final _listenables = <Listenable>[];
  var _mounted = true;
  bool get mounted => _mounted;
  bool isRefreshed = false;

  void addListenables(Iterable<Listenable> listenables) {
    for (final e in listenables) {
      e.addListener(refresh);
    }
    _listenables.addAll(listenables);
  }

  void removeListenables(Iterable<Listenable> listenables) {
    for (final e in listenables) {
      e.removeListener(refresh);
      _listenables.remove(e);
    }
  }

  void enableListenables() {
    for (final e in _listenables) {
      e.addListener(refresh);
    }
  }

  void disableListenables() {
    for (final e in _listenables) {
      e.removeListener(refresh);
    }
  }

  Future<void> refresh();

  @override
  void notifyListeners() {
    isRefreshed = true;
    super.notifyListeners();
  }

  @override
  void dispose() {
    _mounted = false;
    for (final e in _listenables) {
      e.removeListener(refresh);
    }
    super.dispose();
  }
}
