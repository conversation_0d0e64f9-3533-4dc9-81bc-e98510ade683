import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:uuid/uuid.dart';
import '../../misc/json_conversion.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import '../base_data.dart';

part 'location_notes.g.dart';

@HiveType(typeId: 26)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class LocationNote extends BaseData {
  
  @HiveField(6)
  @Json<PERSON>ey(name: 'Id', fromJson: idFromJson, toJson: idToJson)
  String _id;
  @override
  String get id => _id;
  @override
  set id(String value) => _id = value;

  @HiveField(7)
  @Json<PERSON>ey(name: 'LocationId', fromJson: idFromJson, toJson: idToJson)
  String locationId;

  @HiveField(8)
  @<PERSON>son<PERSON><PERSON>(name: 'Note')
  final String note;

  LocationNote({
    String? id,  // Optional ID
    bool isDirty = false,
    bool isActive = true,
    required DateTime createdOn,
    String? createdByUserId,
    DateTime? lastChangedOn,
    String? lastChangedByUserId,
    required this.locationId,
    required this.note,
  }) : _id = id ?? Uuid().v4(), // Generate a new UUID if not provided
       super(
          isDirty: isDirty,
          isActive: isActive,
          createdOn: createdOn,
          createdByUserId: createdByUserId,
          lastChangedOn: lastChangedOn,
          lastChangedByUserId: lastChangedByUserId,
        );

  // From JSON
  factory LocationNote.fromJson(Map<String, dynamic> json) => _$LocationNoteFromJson(json);

  // To JSON
  Map<String, dynamic> toJson() => _$LocationNoteToJson(this);

  // Create a new instance with or without an initial ID
  factory LocationNote.create({String? initialId}) => LocationNote(
        id: initialId ?? Uuid().v4(),
        locationId: '',  // This should be set explicitly or passed as a parameter
        note: '',
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
        lastChangedOn: ServerTimeState().utcTime,
        lastChangedByUserId: LoginState.userId,
        isActive: true,
    );
}

String idToJson(String id) => id;

String idFromJson(String id) => id;