// ignore_for_file: use_build_context_synchronously

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../api/api_model.dart';
import '../../dialogs/busy_dialog.dart';
import '../../dialogs/error_dialog.dart';
import '../../misc/app_localization.dart';
import '../../misc/logging.dart';
import '../../misc/my_platform.dart';
import '../../widgets/decorated_text_field.dart';
import '../../widgets/padded_card.dart';
import '../web/my_scaffold.dart';

class CompleteRegistrationPage extends StatelessWidget {
  final String registrationKey;

  CompleteRegistrationPage(Map<String, String> queryParms, {super.key})
      : registrationKey = queryParms['id'] ?? '';

  @override
  Widget build(BuildContext context) => MyScaffold(
        enableBottomBar: false,
        showLoggedOutDrawer: true,
        showDesktopHeader: false,
        title: AppLocalization.of(context).register,
        body: Center(
            child: Padding(
                padding: const EdgeInsets.all(8),
                child: _Body(registrationKey))),
      );
}

class _Body extends StatelessWidget {
  final String registrationKey;

  const _Body(this.registrationKey);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline6 = theme.textTheme.titleLarge;
    return ChangeNotifierProvider<_ViewModel>(
      create: (context) => _ViewModel(registrationKey),
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 500),
        child: ListView(
          shrinkWrap: true,
          children: [
            PaddedCard(
              elevation: 1,
              color: Colors.white,
              child: Flex(
                mainAxisSize: MainAxisSize.min,
                direction: Axis.vertical,
                children: [
                  Text(AppLocalization.of(context).registrationConfirmation,
                      style: headline6),
                  _Row1(),
                ],
              ),
            ),
            // _Captcha(),
            _RegisterButton(),
          ],
        ),
      ),
    );
  }
}

class _Row1 extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
        builder: (context, viewModel, _) => IntrinsicHeight(
          child: Flex(
            mainAxisSize: MainAxisSize.min,
            direction: Axis.horizontal,
            children: [
              Flexible(
                child: DecoratedTextField(
                  padding: const EdgeInsets.all(8),
                  initialValue: viewModel.registrationKey,
                  labelText: AppLocalization.of(context).registrationKey,
                  onChanged: (value) => viewModel.setRegistrationKey(value),
                  validator: (value) => viewModel.registrationKey.isEmpty
                      ? AppLocalization.of(context).registrationKeyRequired
                      : null,
                ),
              ),
            ],
          ),
        ),
      );
}

// class _Captcha extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return Consumer<_ViewModel>(builder: (context, viewModel, _) {
//       return grecaptcha2.GRecaptcha2((value) => viewModel.setCaptchaResponse(value));
//     });
//   }
// }

class _RegisterButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
      builder: (context, viewModel, _) => FittedBox(
          fit: BoxFit.scaleDown,
          child: Padding(
              padding: const EdgeInsets.all(8),
              child: ElevatedButton(
                onPressed: !viewModel.canConfirm
                    ? null
                    : () => unawaited(completeRegistration(context, viewModel)),
                child: Text(AppLocalization.of(context).register),
              ))));

  Future<void> completeRegistration(
      BuildContext context, _ViewModel viewModel) async {
    await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          final router = GoRouter.of(context);
          return BusyDialog(
              subTitle: AppLocalization.of(context).registrationConfirmation,
              future: () async {
                try {
                  final response = await ApiModel().completeRegistration(
                      registrationKey: viewModel.registrationKey,
                      captchaResponse: viewModel.captchaResponse,
                      timeZone: MyPlatform.deviceIana);
                  if (!response.isError) {
                    await SharedPreferences.getInstance().then((value) async {
                      await value.setString(
                          'organizationId', response.organizationId);
                      await value.setString('username', response.username);
                    });
                    router.go('/login');
                  }
                } on ApiException catch (e, stack) {
                  await logApiException(e, stack);
                  if (!context.mounted) return;
                  await showDialog(
                      context: context,
                      builder: (context) =>
                          ErrorDialog(errorCode: e.errorCode));
                } on Exception catch (e, stack) {
                  await logException(e, stack);
                  if (!context.mounted) return;
                  await showDialog(
                      context: context,
                      builder: (context) => const ErrorDialog());
                }
              });
        });
  }
}

class _ViewModel extends ChangeNotifier {
  String registrationKey;
  String captchaResponse = '';

  _ViewModel(this.registrationKey);

  bool get validateRegistrationKey => registrationKey.isNotEmpty;
  // bool get validateCaptchaResponse => captchaResponse.isNotEmpty;
  bool get validateCaptchaResponse => true;

  bool get canConfirm => validateRegistrationKey && validateCaptchaResponse;

  void setRegistrationKey(String value) {
    registrationKey = value;
    notifyListeners();
  }

  void setCaptchaResponse(String value) {
    captchaResponse = value;
    notifyListeners();
  }
}
