// lib/api/requests/build_asset_image_request.dart
import 'package:json_annotation/json_annotation.dart';
import '../../misc/json_conversion.dart';
import 'system.dart';

part 'build_asset_image_request.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class BuildAssetImageRequest extends SystemRequest {
  final String guid;

  BuildAssetImageRequest({
    required this.guid,
    required super.serverIP,
    required super.databaseName,
    required super.sessionId,
  });

  // Convenience constructor to fill from current session
  static Future<BuildAssetImageRequest> create(String guid) async {
    final sysReq = await SystemRequest.create();
    return BuildAssetImageRequest(
      guid: guid,
      serverIP: sysReq.serverIP,
      databaseName: sysReq.databaseName,
      sessionId: sysReq.sessionId,
    );
  }

  factory BuildAssetImageRequest.fromJson(Map<String, dynamic> json)
    => _$BuildAssetImageRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() {
    final data = _$BuildAssetImageRequestToJson(this);
    data.removeWhere((key, value) => value == null);
    return data;
  }
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class BuildAssetImageResponse extends SystemResponse {
  final String? imageUrl;

  BuildAssetImageResponse({
    this.imageUrl,
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory BuildAssetImageResponse.fromJson(Map<String, dynamic> json)
    => _$BuildAssetImageResponseFromJson(json);

  @override
  Map<String, dynamic> toJson() {
    final data = _$BuildAssetImageResponseToJson(this);
    data.removeWhere((key, value) => value == null);
    return data;
  }
}
