// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reports.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ReportsRequest _$ReportsRequestFromJson(Map<String, dynamic> json) =>
    ReportsRequest(
      isActive: json['IsActive'] as bool?,
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$ReportsRequestToJson(ReportsRequest instance) {
  final val = <String, dynamic>{
    'Request_ServerIP': instance.serverIP,
    'Request_DatabaseName': instance.databaseName,
    'Request_SessionID': instance.sessionId,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('IsActive', instance.isActive);
  return val;
}

ReportsResponse _$ReportsResponseFromJson(Map<String, dynamic> json) =>
    ReportsResponse(
      reports: (json['Reports'] as List<dynamic>)
          .map((e) => Report.fromJson(e as Map<String, dynamic>))
          .toList(),
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$ReportsResponseToJson(ReportsResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['Reports'] = instance.reports;
  return val;
}

Report _$ReportFromJson(Map<String, dynamic> json) => Report(
      id: json['Id'] as String,
      name: json['Name'] as String,
      description: json['Description'] as String,
      ssrsName: json['SSRSName'] as String,
    );

Map<String, dynamic> _$ReportToJson(Report instance) => <String, dynamic>{
      'Id': instance.id,
      'Name': instance.name,
      'Description': instance.description,
      'SSRSName': instance.ssrsName,
    };

GetReportRequest _$GetReportRequestFromJson(Map<String, dynamic> json) =>
    GetReportRequest(
      id: json['Id'] as String,
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$GetReportRequestToJson(GetReportRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'Id': instance.id,
    };

GetReportResponse _$GetReportResponseFromJson(Map<String, dynamic> json) =>
    GetReportResponse(
      parameters: (json['Parameters'] as List<dynamic>)
          .map((e) => Parameter.fromJson(e as Map<String, dynamic>))
          .toList(),
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$GetReportResponseToJson(GetReportResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['Parameters'] = instance.parameters;
  return val;
}

Parameter _$ParameterFromJson(Map<String, dynamic> json) => Parameter(
      name: json['Name'] as String,
      prompt: json['Prompt'] as String,
      uiControl: json['UIControl'] as String,
      value: json['Value'] as String? ?? '',
      validValues: (json['ValidValues'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as String),
          ) ??
          {},
    );

Map<String, dynamic> _$ParameterToJson(Parameter instance) => <String, dynamic>{
      'Name': instance.name,
      'Prompt': instance.prompt,
      'UIControl': instance.uiControl,
      'Value': instance.value,
      'ValidValues': instance.validValues,
    };

RunReportRequest _$RunReportRequestFromJson(Map<String, dynamic> json) =>
    RunReportRequest(
      id: json['Id'] as String,
      parameters: Map<String, String>.from(json['Parameters'] as Map),
      reportType: json['ReportType'] as String,
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$RunReportRequestToJson(RunReportRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'Id': instance.id,
      'Parameters': instance.parameters,
      'ReportType': instance.reportType,
    };

RunReportResponse _$RunReportResponseFromJson(Map<String, dynamic> json) =>
    RunReportResponse(
      reportData: json['ReportData'] as String,
      fileName: json['FileName'] as String,
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$RunReportResponseToJson(RunReportResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['ReportData'] = instance.reportData;
  val['FileName'] = instance.fileName;
  return val;
}
