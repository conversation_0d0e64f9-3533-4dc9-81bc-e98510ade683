import 'package:flutter/material.dart';

class Report extends StatefulWidget {
  final String initialMonth;
  final String initialYear;
  final int total;
  final String time;

  const Report({
    Key? key,
    this.initialMonth = 'Dec',
    this.initialYear = '2024',
    this.total = 229,
    this.time = '603h 36m',
  }) : super(key: key);

  @override
  _ReportState createState() => _ReportState();
}

class _ReportState extends State<Report> {
  late String selectedMonth;
  late String selectedYear;

  @override
  void initState() {
    super.initState();
    selectedMonth = widget.initialMonth;
    selectedYear = widget.initialYear;
  }

  @override
  Widget build(BuildContext context) => Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: const Color(0xFFEBF6FF),
        borderRadius: BorderRadius.circular(32),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Total Counts',
            style: TextStyle(
              fontFamily: 'Poppins',
              fontSize: 14,
              fontWeight: FontWeight.w700,
              letterSpacing: -0.14,
              color: Color(0xFF091F30),
            ),
          ),
          const SizedBox(height: 14),
          Row(
            children: [
              Row(
                children: [
                  _buildDropdown(
                    selectedMonth,
                    ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                    (value) => setState(() => selectedMonth = value!),
                    'arrow-dr.png',
                  ),
                  const SizedBox(width: 7),
                  _buildDropdown(
                    selectedYear,
                    ['2022', '2023', '2024', '2025'],
                    (value) => setState(() => selectedYear = value!),
                    'arrow-dr-2.png',
                  ),
                ],
              ),
              const Spacer(),
              _buildCountColumn('Total', widget.total.toString()),
              const SizedBox(width: 16),
              _buildCountColumn('Time', widget.time),
            ],
          ),
        ],
      ),
    );

  Widget _buildDropdown(String value, List<String> items, ValueChanged<String?> onChanged, String arrowImage) => Container(
      padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 11),
      decoration: BoxDecoration(
        color: const Color(0xFF091F30),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          DropdownButton<String>(
            value: value,
            dropdownColor: const Color(0xFF091F30),
            icon: Image.network(
              'https://dashboard.codeparrot.ai/api/image/Z_ZpDIVxqHvUClGb/$arrowImage',
              width: 24,
              height: 24,
            ),
            iconSize: 24,
            underline: Container(),
            onChanged: onChanged,
            items: items.map<DropdownMenuItem<String>>((String item) => DropdownMenuItem<String>(
                value: item,
                child: Text(
                  item,
                  style: const TextStyle(
                    fontFamily: 'Poppins',
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    letterSpacing: -0.14,
                    color: Colors.white,
                  ),
                ),
              )).toList(),
          ),
        ],
      ),
    );

  Widget _buildCountColumn(String label, String value) => Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontFamily: 'Poppins',
            fontSize: 12,
            fontWeight: FontWeight.w500,
            letterSpacing: -0.12,
            color: Color(0xFF091F30),
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontFamily: 'Poppins',
            fontSize: 20,
            fontWeight: FontWeight.w700,
            letterSpacing: -0.2,
            color: Color(0xFF091F30),
          ),
        ),
      ],
    );
}

