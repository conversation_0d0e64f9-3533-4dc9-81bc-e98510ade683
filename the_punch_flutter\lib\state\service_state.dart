import 'package:shared_preferences/shared_preferences.dart';

class ServiceState {
  static const _serviceRunningKey = 'service_running';

  static Future<void> setServiceRunning(bool isRunning) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_serviceRunningKey, isRunning);
  }

  static Future<bool> isServiceRunning() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_serviceRunningKey) ?? false;
  }
}