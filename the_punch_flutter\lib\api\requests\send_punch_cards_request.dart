import 'package:json_annotation/json_annotation.dart';
import '../../dataModel/data/punch_card.dart';
import '../../misc/json_conversion.dart';
import 'system.dart';

part 'send_punch_cards_request.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class SendPunchCardsRequest extends SystemRequest {
  final List<PunchCard> punchCards;

  SendPunchCardsRequest({
    required this.punchCards,
    required String serverIP,
    required String databaseName,
    required String sessionId,
  }) : super(serverIP: serverIP, databaseName: databaseName, sessionId: sessionId);

  static Future<SendPunchCardsRequest> create(List<PunchCard> punchCards) async {
    final systemRequest = await SystemRequest.create();
    return SendPunchCardsRequest(
      punchCards: punchCards,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory SendPunchCardsRequest.fromJson(Map<String, dynamic> json) =>
      _$SendPunchCardsRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$SendPunchCardsRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class SendPunchCardsResponse extends SystemResponse {
  final bool success;

  SendPunchCardsResponse({
    required this.success,
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory SendPunchCardsResponse.fromJson(Map<String, dynamic> json) =>
      _$SendPunchCardsResponseFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$SendPunchCardsResponseToJson(this);
}
