// lib/services/cached_image_service.dart

import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import './storage_manager_service.dart'; // <-- Adjust import path to your StorageManager

/// A service that caches image URLs in memory *and* persists them to disk.
/// Keys: the "body" from a Message (either a GUID or a direct URL).
/// Values: the final resolved URL you need to display the image.
class CachedImageService {
  // Singleton instance
  static final CachedImageService _instance = CachedImageService._internal();
  factory CachedImageService() => _instance;
  CachedImageService._internal();

  final StorageManager _storageManager = StorageManager();

  /// In-memory cache for quick lookups
  final Map<String, String> _cache = {};

  /// Whether we've already loaded from disk
  bool _loadedFromDisk = false;

  /// Load from disk once (if we haven't already).
  Future<void> _loadFromDiskIfNeeded() async {
    if (_loadedFromDisk) return;

    try {
      final items = await _storageManager.readData('imageCache'); 
      // This returns a List<Map<String,dynamic>> from the JSON file.

      for (final item in items) {
        final body = item['body'] as String?;
        final finalUrl = item['finalUrl'] as String?;
        if (body != null && finalUrl != null) {
          _cache[body] = finalUrl;
        }
      }

      _loadedFromDisk = true;
    } catch (e) {
      debugPrint('Error loading image cache from disk: $e');
    }
  }

  /// Returns the final URL for [body] if it's in the cache,
  /// or null if it's not found.
  Future<String?> getCachedUrl(String body) async {
    await _loadFromDiskIfNeeded();
    return _cache[body];
  }

  /// Force-set the final URL for [body] in memory and disk.
  Future<void> setUrl(String body, String finalUrl) async {
    await _loadFromDiskIfNeeded();
    _cache[body] = finalUrl;
    await _saveToDisk();
  }

  /// If the final URL is not known, we call [buildUrlFn] to get it,
  /// then store it in memory + disk.
  Future<String> ensureUrl(String body, Future<String> Function() buildUrlFn) async {
    await _loadFromDiskIfNeeded();

    // If we already have it, just return
    if (_cache.containsKey(body)) {
      return _cache[body] ?? '';
    }

    try {
      final finalUrl = await buildUrlFn();
      _cache[body] = finalUrl;
    } catch (e) {
      debugPrint('Error building URL for $body: $e');
      _cache[body] = ''; // fallback
    }

    await _saveToDisk();
    return _cache[body] ?? '';
  }

  /// Persist the in-memory cache to disk.
  Future<void> _saveToDisk() async {
    // Convert _cache to a List of Maps for JSON
    final dataList = _cache.entries.map((e) => {
      'body': e.key,
      'finalUrl': e.value,
    }).toList();

    // We store under type="imageCache" so it saves to stashed_imageCache.json
    await _storageManager.writeData('imageCache', dataList);
  }
}
