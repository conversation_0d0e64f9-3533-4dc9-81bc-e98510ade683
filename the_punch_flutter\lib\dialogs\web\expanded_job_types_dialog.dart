import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import '../../dataModel/data/job_type.dart';
import '../../dataModel/data_model.dart';
import '../constrained_search_dialog.dart';
import '../../misc/change_notification_builder.dart';
import '../../pages/view_model_mixin.dart';
import '../../widgets/padded_card.dart';

class ExpandedJobTypesDialog extends StatelessWidget {
  final Function(String) onSelection;
  final search = ValueNotifier<String>('');

  ExpandedJobTypesDialog({super.key, required this.onSelection});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline6 = theme.textTheme.titleLarge;

    return ChangeNotifierBuilder<_ViewModel>(
      create: (context) => _ViewModel(),
      builder: (context, viewModel, child) {
        if (viewModel.jobTypes.isEmpty) return Container();
        return ConstrainedSearchDialog(
          autofocus: true,
          title: Text(AppLocalizations.of(context)!.employees, style: headline6),
          builder: (search) {
            final visibleJobTypes = viewModel.jobTypes.where((e) => _jobTypeMatches(e, search));
            return visibleJobTypes.map((e) => _Tile(jobType: e, onSelection: onSelection));
          },
        );
      },
    );
  }

  bool _jobTypeMatches(JobType jobType, String search) {
    if (jobType.name.toLowerCase().contains(search.toLowerCase())) return true;
    if (jobType.description != null && jobType.description!.contains(search)) return true;
    return false;
  }
}

class _Tile extends StatelessWidget {
  const _Tile({required this.onSelection, required this.jobType});

  final JobType jobType;
  final Function(String) onSelection;

  @override
  Widget build(BuildContext context) => GestureDetector(
        onTap: () {
          Navigator.of(context).pop();
          onSelection(jobType.id);
        },
        child: PaddedCard(
          child: Center(
              child: Column(
            children: [
              Text(jobType.name),
              if (jobType.description != null) Text(jobType.description!),
            ],
          )),
        ),
      );
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  List<JobType> jobTypes = [];
  _ViewModel() {
    addListenables([DataModel().jobTypeModel]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    jobTypes = (await DataModel().jobTypeModel.activeVisible).toList();
    jobTypes.sort((a, b) => a.order.compareTo(b.order));
    notifyListeners();
  }
}
