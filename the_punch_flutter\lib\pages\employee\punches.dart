import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../dataModel/data/job_type.dart';
import '../../dataModel/data/location.dart';
import '../../dataModel/data/punch_card.dart';
import '../../dataModel/data/schedule.dart';
import '../../dataModel/data_model.dart';
import '../../dataModel/models/location_model.dart';
import '../../helpers/color_helper.dart';
import '../../helpers/screen_helper.dart';
import '../../misc/app_localization.dart';
import '../../misc/month_scroll.dart';
import '../../misc/week_scroll.dart';
import '../view_model_mixin.dart';
import '../../state/login_state.dart';
import '../../widgets/padded_card.dart';
import '../../misc/extensions.dart';
import '../web/my_scaffold.dart';

import 'package:intl/intl.dart';

class PunchesPage extends StatelessWidget {
  const PunchesPage({super.key});

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider(
        create: (context) => _ViewModel(),
        child: MyScaffold(
          title: AppLocalizations.of(context)!.punches,
          body: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _Header(),
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    // gradient: LinearGradient(
                    //   colors: [
                    //     ColorHelper.thePunchDesktopGray(),
                    //     Colors.white
                    //   ], // Gradient colors
                    //   begin: Alignment.topCenter,
                    //   end: Alignment.bottomCenter,
                    // ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                   
                      Expanded(
                        child: _Body(),
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      );
}

class _Header extends StatefulWidget {
  @override
  _HeaderState createState() => _HeaderState();
}

class _HeaderState extends State<_Header> {
  static const initialPage = **********;
  DateTime selectedDate = DateTime.now().dateOnly;
  final pageController = PageController(initialPage: initialPage);

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<_ViewModel>(context);
    final theme = Theme.of(context);
    const onPrimary = Colors.white;
    final body1 = theme.textTheme.titleLarge?.copyWith(
        color: ColorHelper.thePunchDarkBlue(),
        fontSize: ScreenHelper.screenHeightPercentage(context, 1.5),
        fontWeight: FontWeight.w800);
    print('INDEX ${initialPage}  ');
    return Padding(
      padding: EdgeInsets.only(
        top: ScreenHelper.screenHeightPercentage(context, 2),
      ),
      child: Container(
        margin: const EdgeInsets.fromLTRB(0, 0, 0, 10),
        constraints: BoxConstraints(
          maxWidth: ScreenHelper.screenWidth(context),
        ),
        color: Colors.transparent,
        child: Column(
          children: [
            Consumer<_ViewModel>(
              builder: (context, viewModel, child) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Column(
                  children: [
                    if (viewModel.totalDuration > Duration.zero)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(viewModel.totalDuration.toFormatted,
                              style: body1?.copyWith(
                                  color: ColorHelper.thePunchGray())),
                        ],
                      ),
                    if (viewModel.totalDuration <= Duration.zero)
                      Text(AppLocalizations.of(context)!.noPunchCardsThisWeek,
                          style: body1),
                  ],
                ),
              ),
            ),
            WeekScroller(
              pageController: pageController,
            ),
            Flex(
              direction: Axis.horizontal,
              children: [
                Expanded(
                  child: SizedBox(
                    height: 0,
                    child: buildPageView(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget buildPageView() => Consumer<_ViewModel>(
        builder: (context, viewModel, child) => PageView.builder(
          controller: pageController,
          onPageChanged: (index) async {
            final offset = index - initialPage;
            viewModel.weekStart.value =
                viewModel.initialDate.addDays(7 * offset).dateOnly;
          },
          itemBuilder: (context, index) {
            final offset = index - initialPage;
            final weekStart =
                viewModel.initialDate.addDays(7 * offset).dateOnly;
            final weekEnd = weekStart.add(const Duration(days: 6)).dateOnly;

            final locale = Localizations.localeOf(context);

            final theme = Theme.of(context);
            final headline6 = theme.textTheme.titleLarge?.copyWith(
              color: ColorHelper.thePunchDarkBlue(),
            );

            final dateString =
                '${weekStart.toFormattedDate(locale)} - ${weekEnd.toFormattedDateWithYear(locale)}';

            return Stack(
              children: [
                // Align(
                //   child: Text(dateString, style: headline6),
                // ),
              ],
            );
          },
        ),
      );
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          if (viewModel.groupedPunchCards.isEmpty) {
            return Center(
              child: Text(
                AppLocalizations.of(context)!.noPunchCardsThisWeek,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: ColorHelper.thePunchGray(),
                      fontWeight: FontWeight.bold,
                    ),
              ),
            );
          }

          // Grouping punch cards by local date
          var groupedByDate = <DateTime, List<GroupedPunchCard>>{};
          for (var groupedCard in viewModel.groupedPunchCards) {
            final localDateOnly = groupedCard.firstClockIn.toLocal().dateOnly;
            groupedByDate.putIfAbsent(localDateOnly, () => []).add(groupedCard);
          }

          return Center(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: ScreenHelper.screenWidth(context),
              ),
              child: ListView(
                children: groupedByDate.entries.map((entry) {
                  DateTime date = entry.key;
                  List<GroupedPunchCard> groupedCards = entry.value;
                  final formattedDate =
                      DateFormat('MMMM d\',\' yyyy').format(date);

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Center(
                      //   child: Padding(
                      //     padding: EdgeInsets.symmetric(vertical: 8),
                      //     child: 
                      //     Text(
                      //       formattedDate,
                      //       style: Theme.of(context)
                      //           .textTheme
                      //           .titleLarge
                      //           ?.copyWith(
                      //             color: Colors.black,
                      //           ),
                      //     ),
                      //   ),
                      // ),
                      ...groupedCards.asMap().entries.map((entry) {
                        int index = entry.key + 1; // Start counting from 1
                        GroupedPunchCard groupedCard = entry.value;
                        return _Tile(
                          groupedPunchCard: groupedCard,
                          index: index,
                        );
                      }).toList(),
                    ],
                  );
                }).toList(),
              ),
            ),
          );
        },
      );
}

class _Tile extends StatelessWidget {
  final GroupedPunchCard groupedPunchCard;
  final int index; // Sequential numbering

  const _Tile({
    required this.groupedPunchCard,
    required this.index,
  });

  Future<String> getTravelLocationNames(
      String locationId, String previousLocationId) async {
    var loc1 = await LocationModel().getLocationById(locationId);
    var loc2 = await LocationModel().getLocationById(previousLocationId);

    if (loc1 != null && loc2 != null) {
      // Format: PreviousLocation --> CurrentLocation
      return '${loc2.name} --> ${loc1.name}';
    } else {
      return 'Location information not available';
    }
  }

  Future<Location?> getLocationForGroupedPunchCard(
      GroupedPunchCard groupedPunchCard) async {
    // Determine the location based on the first punch card's locationId or the schedule's locationId
    final firstCard = groupedPunchCard.punchCards.first;
    if (firstCard.locationId != null) {
      return await LocationModel().getLocationById(firstCard.locationId!);
    } else if (groupedPunchCard.schedule != null) {
      return await LocationModel()
          .getLocationById(groupedPunchCard.schedule!.locationId);
    } else {
      return null;
    }
  }

  String truncateWithEllipsis(int cutoff, String text) {
    return (text.length <= cutoff) ? text : '${text.substring(0, cutoff)}...';
  }

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);
    final theme = Theme.of(context);
    final caption = theme.textTheme.bodyMedium?.copyWith(color: Colors.black);
    final body1 = theme.textTheme.titleLarge?.copyWith(
        fontSize: ScreenHelper.screenHeightPercentage(context, 2),
        color: Colors.black);
    final body2 = theme.textTheme.bodyMedium?.copyWith(
        fontSize: ScreenHelper.screenHeightPercentage(context, 1.6),
        color: Colors.black);
    final headlineSmall = theme.textTheme.headlineSmall?.copyWith(
      color: ColorHelper.thePunchRed(),
    );

    final firstClockIn = groupedPunchCard.firstClockIn;
    final lastClockOut = groupedPunchCard.lastClockOut;
    final duration = groupedPunchCard.duration;
    final isLive = lastClockOut == null;
    final durationString =
        duration != null ? duration.ceilMinutes.toFormatted : 'LIVE';

    final endString = lastClockOut?.toFormattedTime(locale) ?? 'LIVE';

    // Conditional styling for durationString
    final durationStyle = durationString == 'LIVE'
        ? theme.textTheme.headlineSmall?.copyWith(color: Colors.green)
        : headlineSmall;

    // Conditional styling for endString
    final endStringStyle =
        endString == 'LIVE' ? caption?.copyWith(color: Colors.green) : caption;

    final startString = firstClockIn.toFormattedTime(locale);

    final String? scheduleString;
    if (groupedPunchCard.schedule != null) {
      scheduleString =
          '${AppLocalizations.of(context)!.scheduled} ${groupedPunchCard.schedule!.startDateUtc.toFormattedTime(locale)} - ${groupedPunchCard.schedule!.endDateUtc.toFormattedTime(locale)}';
    } else {
      scheduleString = null;
    }

    // Determine if this is a travel punch
    final isTravel = groupedPunchCard.punchCards.any((card) =>
        card.jobTypeId.toLowerCase() == JobType.travelTimeId.toLowerCase());

    return GestureDetector(
      onTap: () async => context.pushNamed('/punches/details',
          queryParameters: {'linkId': groupedPunchCard.linkId}),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // Container(
          //   constraints: const BoxConstraints(minWidth: 65),
          //   child: Padding(
          //     padding: const EdgeInsets.only(left: 20),
          //     child: Column(
          //       crossAxisAlignment: CrossAxisAlignment.end,
          //       children: [
          //         Text(
          //           isTravel
          //               ? 'Travel'
          //               : 'Punch #$index', // Sequential numbering
          //           style: TextStyle(
          //               color: ColorHelper.thePunchGray(),
          //               fontWeight: FontWeight.bold),
          //         ),
          //         Text(startString, style: caption),
          //         Text(endString, style: endStringStyle),
          //       ],
          //     ),
          //   ),
          // ),
          // Padding(
          //   padding: const EdgeInsets.only(right:0),
          //   child:
          Container(
              // margin: const EdgeInsets.only(right: 16),
              child: Flexible(
            child: FractionallySizedBox(
              widthFactor: 0.80, // 75% of the parent width
              alignment: Alignment.centerRight, // Aligns to the right
              child: IntrinsicHeight(
                child: PaddedCard(
                  elevation: 1,
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    child: Stack(
                      children: [
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (isTravel &&
                                  groupedPunchCard.punchCards.isNotEmpty &&
                                  groupedPunchCard
                                          .punchCards.last.previousLocationId !=
                                      null &&
                                  groupedPunchCard.punchCards.last.locationId !=
                                      null)
                                FutureBuilder<String>(
                                  future: getTravelLocationNames(
                                      groupedPunchCard
                                          .punchCards.last.locationId!,
                                      groupedPunchCard
                                          .punchCards.last.previousLocationId!),
                                  builder: (context, snapshot) {
                                    if (snapshot.connectionState ==
                                        ConnectionState.waiting) {
                                      return Text('Travel Time', style: body1);
                                    } else if (snapshot.hasError) {
                                      return Text('Error loading locations',
                                          style: body1);
                                    } else {
                                      return Row(
                                        children: [
                                          Icon(
                                            Icons.directions_car,
                                            color: ColorHelper.thePunchRed(),
                                          ),
                                          Text(
                                            ' ${truncateWithEllipsis(26, snapshot.data!)}',
                                            style: body1,
                                          ),
                                        ],
                                      );
                                    }
                                  },
                                )
                              else if (!isTravel)
                                FutureBuilder<Location?>(
                                  future: getLocationForGroupedPunchCard(
                                      groupedPunchCard),
                                  builder: (context, snapshot) {
                                    if (snapshot.connectionState ==
                                        ConnectionState.waiting) {
                                      return Text('...',
                                          style: body1); // Show "..."
                                    } else if (snapshot.hasError) {
                                      return Text('Error loading location',
                                          style: body1);
                                    } else if (snapshot.data != null) {
                                      return Text(
                                          truncateWithEllipsis(
                                              20, snapshot.data!.name),
                                          style: body1);
                                    } else if (groupedPunchCard.punchCards.any(
                                        (card) =>
                                            card.jobTypeId ==
                                            JobType.trackingId.toLowerCase())) {
                                      return Text(
                                          truncateWithEllipsis(
                                              20, 'Manager Punch Card'),
                                          style: body1);
                                    } else {
                                      return Text('Location not available',
                                          style: body1);
                                    }
                                  },
                                ),
                              if (groupedPunchCard.jobType != null)
                                Text(
                                    truncateWithEllipsis(
                                        20, groupedPunchCard.jobType!.name),
                                    style: body2),
                              Text('Job Type Filler', style: body2),
                              Row(
                                children: [
                                  Text(startString, style: caption),
                                  Text(' - '),
                                  Text(endString, style: endStringStyle),
                                  // if (scheduleString != null)
                                  //   Text('${truncateWithEllipsis(30, scheduleString)} • ',
                                  //       style: body2)
                                  // else if (isTravel)
                                  //   Text('Travel Time', style: body2),

                                  Text(' • '),
                                  Text(
                                    durationString,
                                    style: durationStyle,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const Align(
                          alignment: Alignment.centerRight,
                          child: Icon(Icons.chevron_right),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          )),
          SizedBox(width: 10),
          //    ),
        ],
      ),
    );
  }
}

class GroupedPunchCard {
  final String linkId;
  final DateTime firstClockIn;
  final DateTime? lastClockOut;
  final Duration? duration;
  final List<PunchCard> punchCards;
  final Schedule? schedule;
  final JobType? jobType;

  GroupedPunchCard({
    required this.linkId,
    required this.firstClockIn,
    required this.lastClockOut,
    required this.duration,
    required this.punchCards,
    this.schedule,
    this.jobType,
  });
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  List<GroupedPunchCard> groupedPunchCards = [];
  Duration totalDuration = Duration.zero;

  final initialDate = DateTime.now().startOfWeek;
  final weekStart = ValueNotifier<DateTime>(DateTime.now().startOfWeek);

  _ViewModel() {
    addListenables([
      DataModel().punchCardModel,
      DataModel().locationModel,
      DataModel().scheduleModel,
      DataModel().jobTypeModel,
      weekStart,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    final weekEnd = weekStart.value.add(const Duration(days: 7));
    final employeeId = LoginState.userId;

    // Fetch all punch cards for the user within the date range
    final allPunchCards = await DataModel()
        .punchCardModel
        .getBetweenByEmployeeId(
            employeeId, weekStart.value.toUtc(), weekEnd.toUtc());

    // Group punch cards by punchCardLinkId
    final Map<String, List<PunchCard>> groupedByLinkId = {};
    for (var card in allPunchCards) {
      final linkId =
          card.punchCardLinkId ?? card.id; // Use id if linkId is null
      groupedByLinkId.putIfAbsent(linkId, () => []).add(card);
    }

    // Find all travel punch cards
    var travelJobTypeId = '2EBCDAA1-7DE7-4106-BC26-D99CA120C820'.toLowerCase();
    List<PunchCard> travelPunchCards = allPunchCards
        .where((card) => card.jobTypeId.toLowerCase() == travelJobTypeId)
        .toList();

    // Prepare to collect all scheduleIds and jobTypeIds
    Set<String> scheduleIds = {};
    Set<String> jobTypeIds = {};

    // First, process all grouped punch cards
    List<GroupedPunchCard> tempGroupedPunchCards = [];

    for (var entry in groupedByLinkId.entries) {
      final linkId = entry.key;
      final cards = entry.value;

      // Sort the cards by clockIn
      cards.sort((a, b) => a.clockedIn.compareTo(b.clockedIn));

      final firstClockIn = cards.first.clockedIn.toLocal();
      final lastClockOut = cards.last.clockedOut?.toLocal();

      Duration? duration;
      if (lastClockOut != null) {
        duration = lastClockOut.difference(cards.first.clockedIn.toLocal());
      }

      // Collect scheduleId and jobTypeId
      if (cards.first.scheduleId != null) {
        scheduleIds.add(cards.first.scheduleId!);
      }
      if (cards.first.jobTypeId.isNotEmpty) {
        jobTypeIds.add(cards.first.jobTypeId);
      }

      tempGroupedPunchCards.add(GroupedPunchCard(
        linkId: linkId,
        firstClockIn: firstClockIn,
        lastClockOut: lastClockOut,
        duration: duration,
        punchCards: cards,
        schedule: null, // To be filled later
        jobType: null, // To be filled later
      ));
    }

    // Now, add travel punch cards that are not already included
    for (var travelCard in travelPunchCards) {
      final linkId = travelCard.punchCardLinkId ?? travelCard.id;
      if (!groupedByLinkId.containsKey(linkId)) {
        // Since it's a travel punch, duration might not be applicable the same way
        Duration? duration;
        if (travelCard.clockedOut != null) {
          duration = travelCard.clockedOut!
              .toLocal()
              .difference(travelCard.clockedIn.toLocal());
        }

        // Collect scheduleId and jobTypeId
        if (travelCard.scheduleId != null) {
          scheduleIds.add(travelCard.scheduleId!);
        }
        if (travelCard.jobTypeId.isNotEmpty) {
          jobTypeIds.add(travelCard.jobTypeId);
        }

        tempGroupedPunchCards.add(GroupedPunchCard(
          linkId: linkId,
          firstClockIn: travelCard.clockedIn.toLocal(),
          lastClockOut: travelCard.clockedOut?.toLocal(),
          duration: duration,
          punchCards: [travelCard],
          schedule: null, // To be filled later
          jobType: null, // To be filled later
        ));
      }
    }

    // Fetch all schedules in batch
    List<Schedule> schedules = [];
    if (scheduleIds.isNotEmpty) {
      schedules =
          (await DataModel().scheduleModel.getByIds(scheduleIds)).toList();
    }

    // Fetch all job types in batch
    List<JobType> jobTypes = [];
    if (jobTypeIds.isNotEmpty) {
      jobTypes = (await DataModel().jobTypeModel.getByIds(jobTypeIds)).toList();
    }

    // Create maps for easy lookup
    final Map<String, Schedule> scheduleMap = {
      for (var schedule in schedules) schedule.id: schedule
    };

    final Map<String, JobType> jobTypeMap = {
      for (var jobType in jobTypes) jobType.id: jobType
    };

    // Assign schedules and jobTypes to groupedPunchCards
    groupedPunchCards = tempGroupedPunchCards.map((groupedCard) {
      final firstCard = groupedCard.punchCards.first;
      final schedule = firstCard.scheduleId != null
          ? scheduleMap[firstCard.scheduleId!]
          : null;
      final jobType = firstCard.jobTypeId.isNotEmpty
          ? jobTypeMap[firstCard.jobTypeId]
          : null;
      return GroupedPunchCard(
        linkId: groupedCard.linkId,
        firstClockIn: groupedCard.firstClockIn,
        lastClockOut: groupedCard.lastClockOut,
        duration: groupedCard.duration,
        punchCards: groupedCard.punchCards,
        schedule: schedule,
        jobType: jobType,
      );
    }).toList();

    // **Sort the groupedPunchCards by firstClockIn**
    groupedPunchCards.sort((a, b) => a.firstClockIn.compareTo(b.firstClockIn));

    // Calculate total duration
    totalDuration = Duration.zero;
    if (groupedPunchCards.isNotEmpty) {
      final durations =
          groupedPunchCards.map((e) => e.duration ?? Duration.zero).toList();
      totalDuration = durations.fold(Duration.zero, (p, c) => p + c);
    }

    notifyListeners();
  }
}
