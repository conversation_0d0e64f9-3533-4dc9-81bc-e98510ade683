// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'post_changes.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PostChangesRequest _$PostChangesRequestFromJson(Map<String, dynamic> json) =>
    PostChangesRequest(
      alerts: (json['Alerts'] as List<dynamic>?)
          ?.map((e) => Alert.fromJson(e as Map<String, dynamic>)),
      locationContacts: (json['LocationContacts'] as List<dynamic>?)
          ?.map((e) => LocationContact.fromJson(e as Map<String, dynamic>)),
      locationNotes: (json['LocationNotes'] as List<dynamic>?)
          ?.map((e) => LocationNote.fromJson(e as Map<String, dynamic>)),
      geoLocations: (json['GeoLocations'] as List<dynamic>?)
          ?.map((e) => GeoLocation.fromJson(e as Map<String, dynamic>)),
      inspections: (json['Inspections'] as List<dynamic>?)
          ?.map((e) => Inspection.fromJson(e as Map<String, dynamic>)),
      inspectionAreas: (json['InspectionAreas'] as List<dynamic>?)
          ?.map((e) => InspectionArea.fromJson(e as Map<String, dynamic>)),
      inspectionItems: (json['InspectionItems'] as List<dynamic>?)
          ?.map((e) => InspectionItem.fromJson(e as Map<String, dynamic>)),
      inspectionImages: (json['InspectionImages'] as List<dynamic>?)
          ?.map((e) => InspectionImage.fromJson(e as Map<String, dynamic>)),
      inspectionTemplates: (json['InspectionTemplates'] as List<dynamic>?)
          ?.map((e) => InspectionTemplate.fromJson(e as Map<String, dynamic>)),
      inspectionTemplateAreas:
          (json['InspectionTemplateAreas'] as List<dynamic>?)?.map((e) =>
              InspectionTemplateArea.fromJson(e as Map<String, dynamic>)),
      inspectionTemplateItems:
          (json['InspectionTemplateItems'] as List<dynamic>?)?.map((e) =>
              InspectionTemplateItem.fromJson(e as Map<String, dynamic>)),
      jobTypes: (json['JobTypes'] as List<dynamic>?)
          ?.map((e) => JobType.fromJson(e as Map<String, dynamic>)),
      locations: (json['Locations'] as List<dynamic>?)
          ?.map((e) => Location.fromJson(e as Map<String, dynamic>)),
      messages: (json['Messages'] as List<dynamic>?)
          ?.map((e) => Message.fromJson(e as Map<String, dynamic>)),
      notes: (json['Notes'] as List<dynamic>?)
          ?.map((e) => Note.fromJson(e as Map<String, dynamic>)),
      punchCards: (json['PunchCards'] as List<dynamic>?)
          ?.map((e) => PunchCard.fromJson(e as Map<String, dynamic>)),
      schedules: (json['Schedules'] as List<dynamic>?)
          ?.map((e) => Schedule.fromJson(e as Map<String, dynamic>)),
      users: (json['Users'] as List<dynamic>?)
          ?.map((e) => User.fromJson(e as Map<String, dynamic>)),
      userTypes: (json['UserTypes'] as List<dynamic>?)
          ?.map((e) => UserType.fromJson(e as Map<String, dynamic>)),
      userTypePermissions: (json['UserTypePermissions'] as List<dynamic>?)
          ?.map((e) => UserTypePermission.fromJson(e as Map<String, dynamic>)),
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$PostChangesRequestToJson(PostChangesRequest instance) {
  final val = <String, dynamic>{
    'Request_ServerIP': instance.serverIP,
    'Request_DatabaseName': instance.databaseName,
    'Request_SessionID': instance.sessionId,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('Alerts', instance.alerts?.toList());
  writeNotNull('LocationContacts', instance.locationContacts?.toList());
  writeNotNull('LocationNotes', instance.locationNotes?.toList());
  writeNotNull('GeoLocations', instance.geoLocations?.toList());
  writeNotNull('Inspections', instance.inspections?.toList());
  writeNotNull('InspectionAreas', instance.inspectionAreas?.toList());
  writeNotNull('InspectionItems', instance.inspectionItems?.toList());
  writeNotNull('InspectionImages', instance.inspectionImages?.toList());
  writeNotNull('InspectionTemplates', instance.inspectionTemplates?.toList());
  writeNotNull(
      'InspectionTemplateAreas', instance.inspectionTemplateAreas?.toList());
  writeNotNull(
      'InspectionTemplateItems', instance.inspectionTemplateItems?.toList());
  writeNotNull('JobTypes', instance.jobTypes?.toList());
  writeNotNull('Locations', instance.locations?.toList());
  writeNotNull('Messages', instance.messages?.toList());
  writeNotNull('Notes', instance.notes?.toList());
  writeNotNull('PunchCards', instance.punchCards?.toList());
  writeNotNull('Schedules', instance.schedules?.toList());
  writeNotNull('Users', instance.users?.toList());
  writeNotNull('UserTypes', instance.userTypes?.toList());
  writeNotNull('UserTypePermissions', instance.userTypePermissions?.toList());
  return val;
}
