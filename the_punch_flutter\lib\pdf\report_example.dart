// import 'dart:math';
// import 'dart:typed_data';

// import 'package:pdf/pdf.dart' as pw;
// import 'package:pdf/widgets.dart' as pw;
// import 'package:printing/printing.dart' as pw;
// import 'package:basics/basics.dart';
// import 'package:the_punch_flutter/pdf/pdfGenerator.dart';

// class ExampleReport implements PdfGenerator {
//   late final pw.Font openSansRegular;
//   late final pw.Font openSansBold;

//   final tableHeaders = ['Column 1', 'Column 2', 'Column 3', 'Column 4', 'Column 5'];
//   final tableRows = [for (var i in 50.range) _PdfRow(i + 1)];

//   final headerHeight = 25.0;
//   final cellHeight = 20.0;

//   @override
//   Future<Uint8List> buildPdf(pw.PdfPageFormat pageFormat) async {
//     openSansRegular = await pw.fontFromAssetBundle('assets/OpenSans-Regular.ttf');
//     openSansBold = await pw.fontFromAssetBundle('assets/OpenSans-Bold.ttf');

//     final themeData = pw.ThemeData.withFont(base: openSansRegular, bold: openSansBold).copyWith(
//       defaultTextStyle: pw.TextStyle(fontSize: 10, color: pw.PdfColors.black),
//     );

//     final tableRowsPages = <List<_PdfRow>>[];

//     final rowsPerPage = (pageFormat.height - pageFormat.marginTop - pageFormat.marginBottom - pageFormat.height / headerHeight - 30) ~/ cellHeight;

//     for (var i = 0; i < tableRows.length / rowsPerPage; i++) {
//       tableRowsPages.add(tableRows.slice(start: i * rowsPerPage, end: min((i + 1) * rowsPerPage, tableRows.length)));
//     }

//     final doc = pw.Document();
//     for (final rows in tableRowsPages) {
//       doc.addPage(pw.Page(
//         pageTheme: pw.PageTheme(theme: themeData, pageFormat: pageFormat),
//         build: (context) => pw.Column(
//           mainAxisSize: pw.MainAxisSize.min,
//           children: [
//             pw.Align(
//                 alignment: pw.Alignment.centerLeft,
//                 child: pw.Column(mainAxisSize: pw.MainAxisSize.min, children: [
//                   pw.Text('some header stuff'),
//                   pw.Text('some header stuff'),
//                 ])),
//             _buildRows(rows, openSansRegular, openSansBold),
//           ],
//         ),
//       ));
//       // doc.addPage(pw.Page(pageFormat: pageFormat, build: (pw.Context context) => pw.Center(child: buildRows(rows))));
//     } // Page

//     return doc.save();
//   }

//   pw.Table _buildRows(List<_PdfRow> rows, pw.Font? font, pw.Font? fontBold) {
//     return pw.Table.fromTextArray(
//       border: null,
//       cellAlignment: pw.Alignment.centerLeft,
//       headerDecoration: pw.BoxDecoration(borderRadius: const pw.BorderRadius.all(pw.Radius.circular(2))),
//       headerHeight: headerHeight,
//       cellHeight: cellHeight,
//       cellPadding: pw.EdgeInsets.zero,
//       headerStyle: pw.TextStyle(fontSize: 10, color: pw.PdfColors.black, fontWeight: pw.FontWeight.bold),
//       cellStyle: pw.TextStyle(fontSize: 8, color: pw.PdfColors.black),
//       cellAlignments: {
//         0: pw.Alignment.centerLeft,
//         1: pw.Alignment.centerLeft,
//         2: pw.Alignment.centerRight,
//         3: pw.Alignment.center,
//         4: pw.Alignment.centerRight,
//       },
//       // rowDecoration: pw.BoxDecoration(border: pw.Border(bottom: pw.BorderSide(width: .5, color: PdfColors.black))),
//       headers: List<String>.generate(tableHeaders.length, (col) => tableHeaders[col]),
//       data: List<List<String>>.generate(
//         rows.length,
//         (row) => List<String>.generate(tableHeaders.length, (col) => rows[row].getIndex(col)),
//       ),
//     );
//   }
// }

// class _PdfRow {
//   int rowIndex;

//   _PdfRow(this.rowIndex);

//   String getIndex(int columnIndex) {
//     switch (columnIndex) {
//       case 0:
//         return 'Row $rowIndex Column 1';
//       case 1:
//         return 'Row $rowIndex Column 2';
//       case 2:
//         return 'Row $rowIndex Column 3';
//       case 3:
//         return 'Row $rowIndex Column 4';
//       case 4:
//         return 'Row $rowIndex Column 5';
//     }
//     return '';
//   }
// }
