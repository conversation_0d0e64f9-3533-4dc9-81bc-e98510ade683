import 'package:json_annotation/json_annotation.dart';
import '../../misc/json_conversion.dart';
import 'system.dart';

part 'create_app_termination_alert_request.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class CreateAppTerminationAlertRequest extends SystemRequest {
  /// The PunchCard UUID (if you have it)
  final String punchCardId;

  /// The user who "had the app terminated" while punched in
  final String userId;

  /// Optional: If you want to store the exact time you noticed termination
  final DateTime? alertOn;

  CreateAppTerminationAlertRequest({
    required this.punchCardId,
    required this.userId,
    this.alertOn,
    required super.serverIP,
    required super.databaseName,
    required super.sessionId,
  });

  /// Helper to auto-fill the standard SystemRequest fields
  static Future<CreateAppTerminationAlertRequest> create({
    required String punchCardId,
    required String userId,
    DateTime? alertOn,
  }) async {
    // get the "system" fields from local config, etc.
    final systemReq = await SystemRequest.create();

    return CreateAppTerminationAlertRequest(
      punchCardId: punchCardId,
      userId: userId,
      alertOn: alertOn,
      serverIP: systemReq.serverIP,
      databaseName: systemReq.databaseName,
      sessionId: systemReq.sessionId,
    );
  }

  factory CreateAppTerminationAlertRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateAppTerminationAlertRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() {
    final data = _$CreateAppTerminationAlertRequestToJson(this);
    // Optionally remove null fields
    data.removeWhere((key, value) => value == null);
    return data;
  }
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class CreateAppTerminationAlertResponse extends SystemResponse {
  final bool success;

  CreateAppTerminationAlertResponse({
    required this.success,
    // Matches your SystemResponse’s constructor style:
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory CreateAppTerminationAlertResponse.fromJson(
    Map<String, dynamic> json,
  ) => _$CreateAppTerminationAlertResponseFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$CreateAppTerminationAlertResponseToJson(this);
}
