import 'package:flutter/widgets.dart';
import 'package:provider/provider.dart';

class ChangeNotifierBuilder<T extends ChangeNotifier?> extends StatelessWidget {
  final Create<T>? create;
  final T? value;
  final Widget Function(BuildContext context, T value, Widget? child) builder;

  const ChangeNotifierBuilder({super.key, required this.create, required this.builder})
      : value = null;

  const ChangeNotifierBuilder.value({super.key, required T this.value, required this.builder})
      : create = null;

  @override
  Widget build(BuildContext context) {
    if (create != null) return ChangeNotifierProvider(create: create!, child: Consumer<T>(builder: builder));
    if (value == null) return Container();
    return ChangeNotifierProvider.value(value: value, child: Consumer<T>(builder: builder));
  }
}
