import 'package:json_annotation/json_annotation.dart';
import 'package:flutter/foundation.dart';
import '../../dataModel/data/travel_pings.dart';
import '../../misc/json_conversion.dart';
import 'system.dart';

part 'fetch_travel_pings_request.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class FetchTravelPingsRequest extends SystemRequest {
  final String punchCardId;

  FetchTravelPingsRequest({
    required this.punchCardId,
    required String serverIP,
    required String databaseName,
    required String sessionId,
  }) : super(serverIP: serverIP, databaseName: databaseName, sessionId: sessionId);

  static Future<FetchTravelPingsRequest> create(String punchCardId) async {
    final systemRequest = await SystemRequest.create();
    return FetchTravelPingsRequest(
      punchCardId: punchCardId,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory FetchTravelPingsRequest.fromJson(Map<String, dynamic> json) =>
      _$FetchTravelPingsRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$FetchTravelPingsRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class FetchTravelPingsResponse extends SystemResponse {
  final List<TravelPing> travelPings;

  FetchTravelPingsResponse({
    required this.travelPings,
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory FetchTravelPingsResponse.fromJson(Map<String, dynamic> json) =>
      _$FetchTravelPingsResponseFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$FetchTravelPingsResponseToJson(this);
}

