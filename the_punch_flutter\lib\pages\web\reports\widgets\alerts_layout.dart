import 'package:flutter/material.dart';
import 'StatsComponent.dart';
import 'TableComponent.dart';

class AlertsLayout extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Container(
      width: 1125,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.only(bottom: 32),
            child: Text(
              'Alerts',
              style: TextStyle(
                fontFamily: 'Poppins',
                fontWeight: FontWeight.w700,
                fontSize: 40,
                letterSpacing: -0.4,
                color: Color(0xFF091F30),
              ),
            ),
          ),
          const StatsComponent(),
          const SizedBox(height: 32),
          TableComponent(
            tableData: const [
              {
                'name': '<PERSON>',
                'totalAlerts': '69',
                'scheduleAlerts': '63',
                'geofenceAlerts': '5',
                'totalBreachedTime': '5m 17s',
                'isHighlighted': true,
              },
              {
                'name': '<PERSON>',
                'totalAlerts': '69',
                'scheduleAlerts': '63',
                'geofenceAlerts': '5',
                'totalBreachedTime': '5m 17s',
                'isHighlighted': false,
              },
              // Add more rows as needed
            ],
          ),
        ],
      ),
    );
}

