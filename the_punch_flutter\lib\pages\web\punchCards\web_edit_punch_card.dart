import 'dart:async';

import 'package:community_material_icon/community_material_icon.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data_model.dart';
import '../../../dataModel/data/alert.dart';
import '../../../dataModel/data/job_type.dart';
import '../../../dataModel/data/location.dart';
import '../../../dataModel/data/punch_card.dart';
import '../../../dataModel/data/schedule.dart';
import '../../../dialogs/web/expanded_employees_dialog.dart';
import '../../../dialogs/web/expanded_job_types_dialog.dart';
import '../../../dialogs/web/expanded_locations_dialog.dart';
import '../../../dialogs/save_dialog.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/change_notification_builder.dart';
import '../../view_model_mixin.dart';
import '../../../widgets/active_toggle.dart';
import '../../../widgets/decorated_text_field.dart';
import '../../../misc/extensions.dart';
import 'package:dartx/dartx.dart';

import '../my_scaffold.dart';

class WebEditPunchCardPage extends StatelessWidget {
  WebEditPunchCardPage(Map<String, String> queryParms, {super.key})
      : punchCardId = queryParms['id'] ?? '',
        anything = queryParms['anything'] ?? '';

  final _scaffoldKey = GlobalKey<ScaffoldState>();
  final String anything;
  final String punchCardId;

  @override
  Widget build(BuildContext context) => ChangeNotifierBuilder<_ViewModel>(
        create: (context) => _ViewModel(punchCardId),
        builder: (context, viewModel, child) => MyScaffold(
          key: _scaffoldKey,
          title: anything == '1'
              ? AppLocalizations.of(context)!.addPunchCard
              : AppLocalizations.of(context)!.editPunchCard,
          body: _Body(),
        ),
      );
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Center(
        child: Container(
          padding: const EdgeInsets.all(16),
          constraints: const BoxConstraints(maxWidth: 1300),
          child: Consumer<_ViewModel>(
            builder: (context, viewModel, child) {
              if (viewModel.punchCard == null) return SizedBox.shrink();

              return SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _BodyHeader(),
                    const SizedBox(height: 16),
                    _FormFields(),
                  ],
                ),
              );
            },
          ),
        ),
      );
}

class _BodyHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          final punchCard = viewModel.punchCard;
          if (punchCard == null) return SizedBox.shrink();

          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Active toggle on the left
              ActiveButton(
                value: punchCard.isActive,
                onChanged: (value) => unawaited(viewModel.setActive(value)),
              ),

              // Row of Back and Save buttons on the right
              Row(
                children: [
                  ElevatedButton(
                    onPressed: () {
                      // Go back to the previous page
                      // If using GoRouter, you can do:
                      context.pop();
                      // Or if using Navigator:
                      // Navigator.of(context).pop();
                    },
                    child: Text('Cancel'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: !viewModel.canSave
                        ? null
                        : () async {
                            await showDialog(
                              context: context,
                              builder: (context) => SaveDialog(
                                save: () {
                                  context.pop(); // closes the SaveDialog
                                  viewModel.save();
                                },
                              ),
                            );
                          },
                    child: Text(AppLocalizations.of(context)!.save),
                  ),
                ],
              ),
            ],
          );
        },
      );
}


class _FormFields extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);

    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final punchCard = viewModel.punchCard;
        if (punchCard == null) return SizedBox.shrink();
        final location = viewModel.location;
        final employee = viewModel.employee;
        final jobType = viewModel.jobType;

        return Wrap(
          spacing: 16,
          runSpacing: 16,
          children: [
            // Location Field
            SizedBox(
              width: _getFieldWidth(context),
              child: InkWell(
                onTap: () => unawaited(showDialog(
                  context: context,
                  builder: (context) => ExpandedLocationsDialog(
                    onSelection: (locationId) async =>
                        await viewModel.setLocation(locationId),
                  ),
                )),
                child: DecoratedText(
                  padding: const EdgeInsets.all(8),
                  text: location?.name ?? 'Location',
                  labelText: AppLocalizations.of(context)!.location,
                  prefixIcon: const Icon(Icons.pin_drop),
                  suffixIcon: IconButton(
                    onPressed: () => unawaited(viewModel.clearLocation()),
                    icon: const Icon(Icons.clear),
                  ),
                  isEmpty: location?.name == null || location!.name.isEmpty,
                  textStyle: location?.name == null || location!.name.isEmpty
                      ? Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.grey.withOpacity(0.5),
                          fontStyle: FontStyle.italic,
                        )
                      : null,
                ),
              ),
            ),
            // Employee Field
            SizedBox(
              width: _getFieldWidth(context),
              child: InkWell(
                onTap: () => unawaited(showDialog(
                  context: context,
                  builder: (context) => ExpandedEmployeesDialog(
                    onSelection: (employeeId) =>
                        viewModel.setEmployee(employeeId),
                  ),
                )),
                child: DecoratedText(
                  padding: const EdgeInsets.all(8),
                  text: employee?.name ?? 'Employee Name',
                  labelText: AppLocalizations.of(context)!.employee,
                  prefixIcon: const Icon(Icons.account_circle),
                  suffixIcon: IconButton(
                    onPressed: () => unawaited(viewModel.clearEmployee()),
                    icon: const Icon(Icons.clear),
                  ),
                  validator: () => employee != null
                      ? null
                      : AppLocalizations.of(context)!.employeeRequired,
                  isEmpty: employee?.name == null || employee!.name.isEmpty,
                  textStyle: employee?.name == null || employee!.name.isEmpty
                      ? Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.grey.withOpacity(0.5),
                          fontStyle: FontStyle.italic,
                        )
                      : null,
                ),
              ),
            ),
            // Date Field
            SizedBox(
              width: _getFieldWidth(context),
              child: InkWell(
                onTap: () async {
                  final pickedDate = await showDatePicker(
                    context: context,
                    builder: (context, child) => Theme(
                      data: ThemeData.light().copyWith(
                        colorScheme: ColorScheme.light(
                          primary: ColorHelper.thePunchRed(),
                        ),
                      ),
                      child: child!,
                    ),
                    initialDate: punchCard.clockedIn.toLocal().dateOnly,
                    firstDate: DateTime.utc(DateTime.now().year - 20),
                    lastDate: DateTime.utc(DateTime.now().year + 20),
                  );
                  if (pickedDate != null) await viewModel.setDate(pickedDate);
                },
                child: DecoratedText(
                  padding: const EdgeInsets.all(8),
                  text: punchCard.clockedIn
                      .toLocal()
                      .dateOnly
                      .toLongFormattedDateWithYear(locale),
                  labelText: AppLocalizations.of(context)!.date,
                  prefixIcon: const Icon(Icons.calendar_today),
                ),
              ),
            ),
            // Clock In Field
            SizedBox(
              width: _getFieldWidth(context),
              child: InkWell(
                onTap: () async {
                  final pickedTime = await showTimePicker(
                    context: context,
                    builder: (context, child) => Theme(
                      data: Theme.of(context).copyWith(
                        colorScheme: ColorScheme.light(
                          primary: ColorHelper.thePunchDesktopLightGray(),
                        ),
                      ),
                      child: child!,
                    ),
                    initialTime:
                        TimeOfDay.fromDateTime(punchCard.clockedIn.toLocal()),
                  );
                  if (pickedTime != null) {
                    await viewModel.setPunchedIn(pickedTime);
                  }
                },
                child: DecoratedText(
                  padding: const EdgeInsets.all(8),
                  text: punchCard.clockedIn.toFormattedTime(locale),
                  labelText: AppLocalizations.of(context)!.clockIn,
                  prefixIcon: const Icon(CommunityMaterialIcons.clock_in),
                  textStyle: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.black87,
                  ),
                ),
              ),
            ),
            // Clock Out Field
            SizedBox(
              width: _getFieldWidth(context),
              child: InkWell(
                onTap: () async {
                  final pickedTime = await showTimePicker(
                    context: context,
                    builder: (context, child) => Theme(
                      data: Theme.of(context).copyWith(
                        colorScheme: ColorScheme.light(
                          primary: ColorHelper.thePunchDesktopLightGray(),
                        ),
                      ),
                      child: child!,
                    ),
                    initialTime: TimeOfDay.fromDateTime(
                        punchCard.clockedOut?.toLocal() ??
                            DateTime.now().toLocal()),
                  );
                  if (pickedTime != null) {
                    await viewModel.setPunchedOut(pickedTime);
                  }
                },
                child: DecoratedText(
                  padding: const EdgeInsets.all(8),
                  text: punchCard.clockedOut?.toFormattedTime(locale) ?? 'Clock Out Time',
                  labelText: AppLocalizations.of(context)!.clockOut,
                  prefixIcon: const Icon(CommunityMaterialIcons.clock_out),
                  suffixIcon: IconButton(
                    onPressed: () => unawaited(viewModel.clearPunchedOut()),
                    icon: const Icon(Icons.clear),
                  ),
                  isEmpty: punchCard.clockedOut == null,
                  textStyle: punchCard.clockedOut == null
                      ? Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.grey.withOpacity(0.5),
                          fontStyle: FontStyle.italic,
                        )
                      : null,
                ),
              ),
            ),
            // Duration Field
            SizedBox(
              width: _getFieldWidth(context),
              child: InkWell(
                onTap: () async {
                  final duration = punchCard.duration?.ceilMinutes ??
                      Duration.zero;
                  final pickedTime = await showTimePicker(
                    helpText: AppLocalizations.of(context)!.duration,
                    context: context,
                    initialTime: duration.timeOfDay,
                    builder: (context, child) => MediaQuery(
                      data: MediaQuery.of(context)
                          .copyWith(alwaysUse24HourFormat: true),
                      child: child!,
                    ),
                  );
                  if (pickedTime != null) {
                    await viewModel.setDuration(pickedTime.duration);
                  }
                },
                child: DecoratedText(
                  padding: const EdgeInsets.all(8),
                  text: punchCard.duration?.ceilMinutes.toFormatted ?? 'Duration',
                  labelText: AppLocalizations.of(context)!.duration,
                  prefixIcon: const Icon(Icons.schedule),
                  suffixIcon: IconButton(
                    onPressed: () => unawaited(viewModel.clearDuration()),
                    icon: const Icon(Icons.clear),
                  ),
                ),
              ),
            ),
            // Job Type Field
            SizedBox(
              width: _getFieldWidth(context),
              child: InkWell(
                onTap: () => unawaited(showDialog(
                  context: context,
                  builder: (context) => ExpandedJobTypesDialog(
                    onSelection: (jobTypeId) => viewModel.setJobType(jobTypeId),
                  ),
                )),
                child: DecoratedText(
                  padding: const EdgeInsets.all(8),
                  text: jobType?.name ?? 'Job Type',
                  labelText: AppLocalizations.of(context)!.jobType,
                  prefixIcon: const Icon(Icons.work),
                  suffixIcon: IconButton(
                    onPressed: () => unawaited(viewModel.clearJobType()),
                    icon: const Icon(Icons.clear),
                  ),
                  validator: () => jobType != null
                      ? null
                      : AppLocalizations.of(context)!.invalidJobType,
                  isEmpty: jobType?.name == null || jobType!.name.isEmpty,
                  textStyle: jobType?.name == null || jobType!.name.isEmpty
                      ? Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.grey.withOpacity(0.5),
                          fontStyle: FontStyle.italic,
                        )
                      : null,
                ),
              ),
            ),
          ],
        );

      },
    );
  }

  double _getFieldWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 800) {
      return (screenWidth - 64) / 3; // For larger screens, display 3 fields per row
    } else if (screenWidth > 600) {
      return (screenWidth - 48) / 2; // For medium screens, display 2 fields per row
    } else {
      return screenWidth - 32; // For small screens, display 1 field per row
    }
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String id;
  PunchCard? punchCard;
  Schedule? schedule;
  Location? location;
  JobType? jobType;
  User? employee;
  Iterable<Alert> alerts = [];
  bool isNew = false;

  _ViewModel(this.id) {
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    if (id.isEmpty) {
      isNew = true;
      punchCard = PunchCard.create();
    } else {
      isNew = false;
      punchCard = await DataModel().punchCardModel.getById(id);
      punchCard = PunchCard.from(punchCard!);

      employee = await DataModel().userModel.getById(punchCard!.userId);
      if (punchCard?.scheduleId != null) {
        schedule = await DataModel().scheduleModel.getById(punchCard!.scheduleId!);
      }
      if (punchCard?.locationId != null) {
        location = await DataModel().locationModel.getById(punchCard!.locationId!);
      } else if (schedule?.locationId != null) {
        location = await DataModel().locationModel.getById(schedule!.locationId);
      }
      jobType = await DataModel().jobTypeModel.getById(punchCard!.jobTypeId);
      alerts = (await DataModel().alertModel.getActiveByPunchCardId(id))
          .distinctBy((e) => e.alertTypeId);
    }
    notifyListeners();
  }

  Future<void> setDate(DateTime date) async {
    if (punchCard == null) return;
    var clockedInLocal = punchCard!.clockedIn.toLocal();
    clockedInLocal = DateTime(
      date.year,
      date.month,
      date.day,
      clockedInLocal.hour,
      clockedInLocal.minute,
    );
    final clockedIn = clockedInLocal.toUtc();
    if (punchCard!.clockedOut != null) {
      final duration = punchCard!.clockedOut!.difference(punchCard!.clockedIn);
      punchCard!.clockedOut = clockedIn.add(duration).toUtc();
    }
    punchCard!.clockedIn = clockedIn;
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> setPunchedIn(TimeOfDay timeOfDay) async {
    if (punchCard == null) return;
    var clockedInLocal = punchCard!.clockedIn.toLocal();
    clockedInLocal = DateTime(
      clockedInLocal.year,
      clockedInLocal.month,
      clockedInLocal.day,
      timeOfDay.hour,
      timeOfDay.minute,
    );
    final clockedIn = clockedInLocal.toUtc();
    if (punchCard!.clockedOut != null) {
      final duration = punchCard!.clockedOut!.difference(punchCard!.clockedIn);
      punchCard!.clockedOut = clockedIn.add(duration).toUtc();
    }
    punchCard!.clockedIn = clockedIn;
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> setPunchedOut(TimeOfDay timeOfDay) async {
    if (punchCard == null) return;
    final clockedInLocal = punchCard!.clockedIn.toLocal();
    var clockedOutLocal = DateTime(
      clockedInLocal.year,
      clockedInLocal.month,
      clockedInLocal.day,
      timeOfDay.hour,
      timeOfDay.minute,
    );
    if (clockedOutLocal.isBefore(clockedInLocal)) {
      clockedOutLocal = clockedOutLocal.add(const Duration(days: 1));
    }
    punchCard!.clockedOut = clockedOutLocal.toUtc();
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> clearPunchedOut() async {
    if (punchCard == null) return;
    punchCard!.clockedOut = null;
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> setDuration(Duration duration) async {
    if (punchCard == null) return;
    punchCard!.clockedOut = punchCard!.clockedIn.add(duration);
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> clearDuration() async {
    if (punchCard == null) return;
    punchCard!.clockedOut = null;
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> setLocation(String locationId) async {
    if (punchCard == null) return;
    punchCard!.locationId = locationId;
    location = await DataModel().locationModel.getById(locationId);
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> clearLocation() async {
    if (punchCard == null) return;
    punchCard!.locationId = null;
    location = null;
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> setEmployee(String employeeId) async {
    if (punchCard == null) return;
    punchCard!.userId = employeeId;
    employee = await DataModel().userModel.getById(employeeId);
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> clearEmployee() async {
    if (punchCard == null) return;
    punchCard!.userId = '';
    employee = null;
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> setJobType(String jobTypeId) async {
    if (punchCard == null) return;
    punchCard!.jobTypeId = jobTypeId;
    jobType = await DataModel().jobTypeModel.getById(jobTypeId);
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> clearJobType() async {
    if (punchCard == null) return;
    punchCard!.jobTypeId = '';
    jobType = null;
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> setActive(bool active) async {
    if (punchCard == null) return;
    punchCard!.isActive = active;
    punchCard!.isDirty = true;
    notifyListeners();
  }

  Future<void> save() async {
    if (punchCard == null || !punchCard!.isDirty) return;
    await DataModel().punchCardModel.saveDirty([punchCard!]);
  }

  bool get canSave {
    if (punchCard == null || !punchCard!.isDirty) return false;
    if (employee == null) return false;
    if (jobType == null) return false;
    return true;
  }
}
