// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'punch_card.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PunchCardAdapter extends TypeAdapter<PunchCard> {
  @override
  final int typeId = 17;

  @override
  PunchCard read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PunchCard(
      isDirty: fields[0] as bool,
      isActive: fields[1] as bool,
      createdOn: fields[2] as DateTime,
      createdByUserId: fields[3] as String?,
      lastChangedOn: fields[4] as DateTime?,
      lastChangedByUserId: fields[5] as String?,
      id: fields[100] as String,
      userId: fields[101] as String,
      jobTypeId: fields[102] as String,
      locationId: fields[103] as String?,
      scheduleId: fields[104] as String?,
      clockedIn: fields[105] as DateTime,
      clockedOut: fields[106] as DateTime?,
      previousLocationId: fields[107] as String?,
      punchCardLinkId: fields[108] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, PunchCard obj) {
    writer
      ..writeByte(15)
      ..writeByte(100)
      ..write(obj.id)
      ..writeByte(101)
      ..write(obj.userId)
      ..writeByte(102)
      ..write(obj.jobTypeId)
      ..writeByte(103)
      ..write(obj.locationId)
      ..writeByte(104)
      ..write(obj.scheduleId)
      ..writeByte(105)
      ..write(obj.clockedIn)
      ..writeByte(106)
      ..write(obj.clockedOut)
      ..writeByte(107)
      ..write(obj.previousLocationId)
      ..writeByte(108)
      ..write(obj.punchCardLinkId)
      ..writeByte(0)
      ..write(obj.isDirty)
      ..writeByte(1)
      ..write(obj.isActive)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdByUserId)
      ..writeByte(4)
      ..write(obj.lastChangedOn)
      ..writeByte(5)
      ..write(obj.lastChangedByUserId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PunchCardAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PunchCard _$PunchCardFromJson(Map<String, dynamic> json) => PunchCard(
      isActive: json['IsActive'] as bool? ?? true,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
      id: idFromJson(json['Id']),
      userId: idFromJson(json['UserId']),
      jobTypeId: idFromJson(json['JobTypeId']),
      locationId: nullableIdFromJson(json['LocationId']),
      scheduleId: nullableIdFromJson(json['ScheduleId']),
      clockedIn: dateTimeFromJson(json['ClockedIn']),
      clockedOut: nullableDateTimeFromJson(json['ClockedOut']),
      previousLocationId: nullableIdFromJson(json['PreviousLocationId']),
      punchCardLinkId: nullableIdFromJson(json['PunchCardLinkId']),
    );

Map<String, dynamic> _$PunchCardToJson(PunchCard instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['UserId'] = instance.userId;
  val['JobTypeId'] = instance.jobTypeId;
  writeNotNull('LocationId', instance.locationId);
  writeNotNull('ScheduleId', instance.scheduleId);
  val['ClockedIn'] = dateTimeToJson(instance.clockedIn);
  writeNotNull('ClockedOut', nullableDateTimeToJson(instance.clockedOut));
  writeNotNull('PreviousLocationId', instance.previousLocationId);
  writeNotNull('PunchCardLinkId', instance.punchCardLinkId);
  return val;
}
