// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_app_termination_alert_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateAppTerminationAlertRequest _$CreateAppTerminationAlertRequestFromJson(
        Map<String, dynamic> json) =>
    CreateAppTerminationAlertRequest(
      punchCardId: json['PunchCardId'] as String,
      userId: json['UserId'] as String,
      alertOn: json['AlertOn'] == null
          ? null
          : DateTime.parse(json['AlertOn'] as String),
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$CreateAppTerminationAlertRequestToJson(
    CreateAppTerminationAlertRequest instance) {
  final val = <String, dynamic>{
    'Request_ServerIP': instance.serverIP,
    'Request_DatabaseName': instance.databaseName,
    'Request_SessionID': instance.sessionId,
    'PunchCardId': instance.punchCardId,
    'UserId': instance.userId,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('AlertOn', instance.alertOn?.toIso8601String());
  return val;
}

CreateAppTerminationAlertResponse _$CreateAppTerminationAlertResponseFromJson(
        Map<String, dynamic> json) =>
    CreateAppTerminationAlertResponse(
      success: json['Success'] as bool,
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$CreateAppTerminationAlertResponseToJson(
    CreateAppTerminationAlertResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['Success'] = instance.success;
  return val;
}
