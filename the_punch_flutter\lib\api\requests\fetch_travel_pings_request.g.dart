// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fetch_travel_pings_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FetchTravelPingsRequest _$FetchTravelPingsRequestFromJson(
        Map<String, dynamic> json) =>
    FetchTravelPingsRequest(
      punchCardId: json['PunchCardId'] as String,
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$FetchTravelPingsRequestToJson(
        FetchTravelPingsRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'PunchCardId': instance.punchCardId,
    };

FetchTravelPingsResponse _$FetchTravelPingsResponseFromJson(
        Map<String, dynamic> json) =>
    FetchTravelPingsResponse(
      travelPings: (json['TravelPings'] as List<dynamic>)
          .map((e) => TravelPing.fromJson(e as Map<String, dynamic>))
          .toList(),
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$FetchTravelPingsResponseToJson(
    FetchTravelPingsResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['TravelPings'] = instance.travelPings;
  return val;
}
