// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'the_punch_notifications.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ThePunchNotification _$ThePunchNotificationFromJson(
        Map<String, dynamic> json) =>
    ThePunchNotification(
      id: idFrom<PERSON>son(json['Id']),
      title: json['Title'] as String,
      message: json['Message'] as String,
      targetDeviceId: json['TargetDeviceId'] as String?,
      targetUserId: json['TargetUserId'] as String,
      sentOn: dateTimeFromJson(json['SentOn']),
      receivedOn: nullableDateTimeFromJson(json['ReceivedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      createdOn: dateTimeFromJson(json['CreatedOn']),
    )
      ..isActive = json['IsActive'] as bool? ?? true
      ..lastChangedOn = nullableDateTimeFromJson(json['LastChangedOn'])
      ..lastChangedByUserId = json['LastChangedByUserId'] as String?;

Map<String, dynamic> _$ThePunchNotificationToJson(
    ThePunchNotification instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['Title'] = instance.title;
  val['Message'] = instance.message;
  writeNotNull('TargetDeviceId', instance.targetDeviceId);
  val['TargetUserId'] = instance.targetUserId;
  val['SentOn'] = dateTimeToJson(instance.sentOn);
  writeNotNull('ReceivedOn', nullableDateTimeToJson(instance.receivedOn));
  return val;
}
