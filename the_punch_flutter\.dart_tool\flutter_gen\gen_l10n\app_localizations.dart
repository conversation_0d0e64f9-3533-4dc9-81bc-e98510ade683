import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_es.dart';
import 'app_localizations_fr.dart';

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'gen_l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('es'),
    Locale('fr')
  ];

  /// No description provided for @locationNotesTitle.
  ///
  /// In en, this message translates to:
  /// **'Location Notes'**
  String get locationNotesTitle;

  /// No description provided for @punchStatus.
  ///
  /// In en, this message translates to:
  /// **'Punch Status'**
  String get punchStatus;

  /// No description provided for @address1.
  ///
  /// In en, this message translates to:
  /// **'Address 1'**
  String get address1;

  /// No description provided for @address2.
  ///
  /// In en, this message translates to:
  /// **'Address 2'**
  String get address2;

  /// No description provided for @addressInformation.
  ///
  /// In en, this message translates to:
  /// **'Address Information'**
  String get addressInformation;

  /// No description provided for @administrator.
  ///
  /// In en, this message translates to:
  /// **'Administrator'**
  String get administrator;

  /// No description provided for @administratorDescription.
  ///
  /// In en, this message translates to:
  /// **'Controls everything in the system.'**
  String get administratorDescription;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @city.
  ///
  /// In en, this message translates to:
  /// **'City'**
  String get city;

  /// No description provided for @configSettingUserSessionExpirationMinsMvc.
  ///
  /// In en, this message translates to:
  /// **'User Session Expiration Admin Site (Mins)'**
  String get configSettingUserSessionExpirationMinsMvc;

  /// No description provided for @configSettingUserSessionExpirationMinsMvcDescription.
  ///
  /// In en, this message translates to:
  /// **'Amount of time in minutes before inactive user sessions will expire on the admin site.'**
  String get configSettingUserSessionExpirationMinsMvcDescription;

  /// No description provided for @clockIn.
  ///
  /// In en, this message translates to:
  /// **'Punch In'**
  String get clockIn;

  /// No description provided for @clockOut.
  ///
  /// In en, this message translates to:
  /// **'Punch Out'**
  String get clockOut;

  /// No description provided for @punch.
  ///
  /// In en, this message translates to:
  /// **'Punch'**
  String get punch;

  /// No description provided for @inW.
  ///
  /// In en, this message translates to:
  /// **'In'**
  String get inW;

  /// No description provided for @outW.
  ///
  /// In en, this message translates to:
  /// **'Out'**
  String get outW;

  /// No description provided for @confirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// No description provided for @confirmPasswordNotMatchPassword.
  ///
  /// In en, this message translates to:
  /// **'The Confirm Password does not match the entered password.'**
  String get confirmPasswordNotMatchPassword;

  /// No description provided for @confirmPasswordRequired.
  ///
  /// In en, this message translates to:
  /// **'The Confirm Password is required.'**
  String get confirmPasswordRequired;

  /// No description provided for @contact.
  ///
  /// In en, this message translates to:
  /// **'Contact'**
  String get contact;

  /// No description provided for @contactTypeDescription.
  ///
  /// In en, this message translates to:
  /// **'Default permission template for a contact.'**
  String get contactTypeDescription;

  /// No description provided for @contactInformation.
  ///
  /// In en, this message translates to:
  /// **'Contact Information'**
  String get contactInformation;

  /// No description provided for @country.
  ///
  /// In en, this message translates to:
  /// **'Country'**
  String get country;

  /// No description provided for @emailAddressAlreadyRegistered.
  ///
  /// In en, this message translates to:
  /// **'This email address is already registered.'**
  String get emailAddressAlreadyRegistered;

  /// No description provided for @firstName.
  ///
  /// In en, this message translates to:
  /// **'First Name'**
  String get firstName;

  /// No description provided for @firstNameRequired.
  ///
  /// In en, this message translates to:
  /// **'The First Name is required.'**
  String get firstNameRequired;

  /// No description provided for @forcedOut.
  ///
  /// In en, this message translates to:
  /// **'Forced Out'**
  String get forcedOut;

  /// No description provided for @forgotPassword.
  ///
  /// In en, this message translates to:
  /// **'Forgot Password'**
  String get forgotPassword;

  /// No description provided for @internalError.
  ///
  /// In en, this message translates to:
  /// **'There was an internal server error. Please try again.'**
  String get internalError;

  /// No description provided for @invalidRequest.
  ///
  /// In en, this message translates to:
  /// **'The request sent was invalid.'**
  String get invalidRequest;

  /// No description provided for @lastName.
  ///
  /// In en, this message translates to:
  /// **'Last Name'**
  String get lastName;

  /// No description provided for @lastNameRequired.
  ///
  /// In en, this message translates to:
  /// **'The Last Name is required.'**
  String get lastNameRequired;

  /// No description provided for @loggedOut.
  ///
  /// In en, this message translates to:
  /// **'Logged Out'**
  String get loggedOut;

  /// No description provided for @sandbox.
  ///
  /// In en, this message translates to:
  /// **'Sandbox'**
  String get sandbox;

  /// No description provided for @login.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// No description provided for @logout.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// No description provided for @mainEmailAddress.
  ///
  /// In en, this message translates to:
  /// **'Main Email Address'**
  String get mainEmailAddress;

  /// No description provided for @mainEmailAddressMustBeValidEmailFormat.
  ///
  /// In en, this message translates to:
  /// **'The Main Email Address must be a valid email format.'**
  String get mainEmailAddressMustBeValidEmailFormat;

  /// No description provided for @mainEmailAddressRequired.
  ///
  /// In en, this message translates to:
  /// **'The Main Email Address is required.'**
  String get mainEmailAddressRequired;

  /// No description provided for @messages.
  ///
  /// In en, this message translates to:
  /// **'Messages'**
  String get messages;

  /// No description provided for @networkUnavailable.
  ///
  /// In en, this message translates to:
  /// **'Network Unavailable'**
  String get networkUnavailable;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @punchedIn.
  ///
  /// In en, this message translates to:
  /// **'Punched In'**
  String get punchedIn;

  /// No description provided for @organizationId.
  ///
  /// In en, this message translates to:
  /// **'Organization Identifier'**
  String get organizationId;

  /// No description provided for @organizationIdNotFound.
  ///
  /// In en, this message translates to:
  /// **'The Organization Identifier was not found.'**
  String get organizationIdNotFound;

  /// No description provided for @organizationIdRequired.
  ///
  /// In en, this message translates to:
  /// **'The Organization Identifier is required.'**
  String get organizationIdRequired;

  /// No description provided for @organizationName.
  ///
  /// In en, this message translates to:
  /// **'Organization Name'**
  String get organizationName;

  /// No description provided for @organizationNameRequired.
  ///
  /// In en, this message translates to:
  /// **'The Organization Name is required.'**
  String get organizationNameRequired;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @passwordNotConformRules.
  ///
  /// In en, this message translates to:
  /// **'The Password does not conform to the password rules.'**
  String get passwordNotConformRules;

  /// No description provided for @passwordRequired.
  ///
  /// In en, this message translates to:
  /// **'The Password is required.'**
  String get passwordRequired;

  /// No description provided for @phone.
  ///
  /// In en, this message translates to:
  /// **'Phone'**
  String get phone;

  /// No description provided for @register.
  ///
  /// In en, this message translates to:
  /// **'Register'**
  String get register;

  /// No description provided for @registerOrganization.
  ///
  /// In en, this message translates to:
  /// **'Register Organization'**
  String get registerOrganization;

  /// No description provided for @schedule.
  ///
  /// In en, this message translates to:
  /// **'Schedule'**
  String get schedule;

  /// No description provided for @sessionForcedOutReason.
  ///
  /// In en, this message translates to:
  /// **'Another session has been started from IP address , if you feel this is in error, please sign in again and change your password.'**
  String get sessionForcedOutReason;

  /// No description provided for @state.
  ///
  /// In en, this message translates to:
  /// **'State'**
  String get state;

  /// No description provided for @sysop.
  ///
  /// In en, this message translates to:
  /// **'Sysop'**
  String get sysop;

  /// No description provided for @sysopDescription.
  ///
  /// In en, this message translates to:
  /// **'Controls everything in the system, and can see extended developer details.'**
  String get sysopDescription;

  /// No description provided for @timedOut.
  ///
  /// In en, this message translates to:
  /// **'Timed Out'**
  String get timedOut;

  /// No description provided for @punchCard.
  ///
  /// In en, this message translates to:
  /// **'Punch Card'**
  String get punchCard;

  /// No description provided for @timeClock.
  ///
  /// In en, this message translates to:
  /// **'Time Clock'**
  String get timeClock;

  /// No description provided for @username.
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get username;

  /// No description provided for @usernameOrPasswordIncorrect.
  ///
  /// In en, this message translates to:
  /// **'The user name or password is incorrect.'**
  String get usernameOrPasswordIncorrect;

  /// No description provided for @usernameRequired.
  ///
  /// In en, this message translates to:
  /// **'The Username is required.'**
  String get usernameRequired;

  /// No description provided for @userMarkedInactive.
  ///
  /// In en, this message translates to:
  /// **'This user is marked as inactive.'**
  String get userMarkedInactive;

  /// No description provided for @punchedThisWeek.
  ///
  /// In en, this message translates to:
  /// **'Total Time Punched'**
  String get punchedThisWeek;

  /// No description provided for @punchedToday.
  ///
  /// In en, this message translates to:
  /// **'Punched Today'**
  String get punchedToday;

  /// No description provided for @zip.
  ///
  /// In en, this message translates to:
  /// **'Zip'**
  String get zip;

  /// No description provided for @deviceIdRequired.
  ///
  /// In en, this message translates to:
  /// **'The Device Identifier is required.'**
  String get deviceIdRequired;

  /// No description provided for @invalidHash.
  ///
  /// In en, this message translates to:
  /// **'The hash value sent in was invalid.'**
  String get invalidHash;

  /// No description provided for @noResultsFound.
  ///
  /// In en, this message translates to:
  /// **'There were no results found.'**
  String get noResultsFound;

  /// No description provided for @pleaseWait.
  ///
  /// In en, this message translates to:
  /// **'Please Wait'**
  String get pleaseWait;

  /// No description provided for @sessionNotFound.
  ///
  /// In en, this message translates to:
  /// **'The session was not found.'**
  String get sessionNotFound;

  /// No description provided for @rotatedOut.
  ///
  /// In en, this message translates to:
  /// **'Rotated Out'**
  String get rotatedOut;

  /// No description provided for @sessionForcedOut.
  ///
  /// In en, this message translates to:
  /// **'This session has been forced out.'**
  String get sessionForcedOut;

  /// No description provided for @sessionLoggedOut.
  ///
  /// In en, this message translates to:
  /// **'This session has been logged out of.'**
  String get sessionLoggedOut;

  /// No description provided for @sessionTimedOut.
  ///
  /// In en, this message translates to:
  /// **'This session has timed out.'**
  String get sessionTimedOut;

  /// No description provided for @userNotFoundOrInactive.
  ///
  /// In en, this message translates to:
  /// **'This user was not found or is inactive.'**
  String get userNotFoundOrInactive;

  /// No description provided for @sessionRotatedOut.
  ///
  /// In en, this message translates to:
  /// **'The session was rotated out.'**
  String get sessionRotatedOut;

  /// No description provided for @configSettingUserSessionExpirationMinsApi.
  ///
  /// In en, this message translates to:
  /// **'User Session Expiration Handheld Devices (Mins)'**
  String get configSettingUserSessionExpirationMinsApi;

  /// No description provided for @configSettingUserSessionExpirationMinsApiDescription.
  ///
  /// In en, this message translates to:
  /// **'Amount of time in minutes before inactive user sessions will expire on the handheld devices.'**
  String get configSettingUserSessionExpirationMinsApiDescription;

  /// No description provided for @home.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// No description provided for @locations.
  ///
  /// In en, this message translates to:
  /// **'Locations'**
  String get locations;

  /// No description provided for @allLocations.
  ///
  /// In en, this message translates to:
  /// **'All Locations'**
  String get allLocations;

  /// No description provided for @changeLocation.
  ///
  /// In en, this message translates to:
  /// **'Change Location'**
  String get changeLocation;

  /// No description provided for @action.
  ///
  /// In en, this message translates to:
  /// **'Action'**
  String get action;

  /// No description provided for @activate.
  ///
  /// In en, this message translates to:
  /// **'Activate'**
  String get activate;

  /// No description provided for @activateAreYouSure.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to activate this record?'**
  String get activateAreYouSure;

  /// No description provided for @activateQuestion.
  ///
  /// In en, this message translates to:
  /// **'Activate?'**
  String get activateQuestion;

  /// No description provided for @active.
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get active;

  /// No description provided for @activeType.
  ///
  /// In en, this message translates to:
  /// **'Active Type'**
  String get activeType;

  /// No description provided for @address.
  ///
  /// In en, this message translates to:
  /// **'Address'**
  String get address;

  /// No description provided for @all.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// No description provided for @deactivate.
  ///
  /// In en, this message translates to:
  /// **'Deactivate'**
  String get deactivate;

  /// No description provided for @deactivateAreYouSure.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to deactivate this record?'**
  String get deactivateAreYouSure;

  /// No description provided for @deactivateQuestion.
  ///
  /// In en, this message translates to:
  /// **'Deactivate?'**
  String get deactivateQuestion;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @inactive.
  ///
  /// In en, this message translates to:
  /// **'Inactive'**
  String get inactive;

  /// No description provided for @locationName.
  ///
  /// In en, this message translates to:
  /// **'Location Name'**
  String get locationName;

  /// No description provided for @locationNotFound.
  ///
  /// In en, this message translates to:
  /// **'This location was not found.'**
  String get locationNotFound;

  /// No description provided for @no.
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// No description provided for @noPermission.
  ///
  /// In en, this message translates to:
  /// **'You do not have permission to access this feature.'**
  String get noPermission;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// No description provided for @successExclamation.
  ///
  /// In en, this message translates to:
  /// **'Success!'**
  String get successExclamation;

  /// No description provided for @view.
  ///
  /// In en, this message translates to:
  /// **'View'**
  String get view;

  /// No description provided for @yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No description provided for @abbreviation.
  ///
  /// In en, this message translates to:
  /// **'Abbreviation'**
  String get abbreviation;

  /// No description provided for @emailAddress.
  ///
  /// In en, this message translates to:
  /// **'Email Address'**
  String get emailAddress;

  /// No description provided for @location.
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// No description provided for @addLocation.
  ///
  /// In en, this message translates to:
  /// **'Add Location'**
  String get addLocation;

  /// No description provided for @locationNameRequired.
  ///
  /// In en, this message translates to:
  /// **'The Location Name is required.'**
  String get locationNameRequired;

  /// No description provided for @submit.
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submit;

  /// No description provided for @viewLocations.
  ///
  /// In en, this message translates to:
  /// **'View Locations'**
  String get viewLocations;

  /// No description provided for @abbreviationAlreadyExists.
  ///
  /// In en, this message translates to:
  /// **'This abbreviation already exists.'**
  String get abbreviationAlreadyExists;

  /// No description provided for @abbreviationRequired.
  ///
  /// In en, this message translates to:
  /// **'The Abbreviation is required.'**
  String get abbreviationRequired;

  /// No description provided for @editLocation.
  ///
  /// In en, this message translates to:
  /// **'Edit Location'**
  String get editLocation;

  /// No description provided for @userpermissionCanEditLocations.
  ///
  /// In en, this message translates to:
  /// **'Can Edit Locations'**
  String get userpermissionCanEditLocations;

  /// No description provided for @userpermissionCanEditLocationsDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to edit location details.'**
  String get userpermissionCanEditLocationsDescription;

  /// No description provided for @userpermissionCanViewLocations.
  ///
  /// In en, this message translates to:
  /// **'Can View Locations'**
  String get userpermissionCanViewLocations;

  /// No description provided for @userpermissionCanViewLocationsDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to view location details.'**
  String get userpermissionCanViewLocationsDescription;

  /// No description provided for @usernameCannotContainSpaces.
  ///
  /// In en, this message translates to:
  /// **'The Username cannot contain spaces.'**
  String get usernameCannotContainSpaces;

  /// No description provided for @chat.
  ///
  /// In en, this message translates to:
  /// **'Chat'**
  String get chat;

  /// No description provided for @chatDescription.
  ///
  /// In en, this message translates to:
  /// **'Chat messages'**
  String get chatDescription;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @emailDescription.
  ///
  /// In en, this message translates to:
  /// **'Email messages'**
  String get emailDescription;

  /// No description provided for @actions.
  ///
  /// In en, this message translates to:
  /// **'Actions'**
  String get actions;

  /// No description provided for @addEmployee.
  ///
  /// In en, this message translates to:
  /// **'Add Employee'**
  String get addEmployee;

  /// No description provided for @employees.
  ///
  /// In en, this message translates to:
  /// **'Employees'**
  String get employees;

  /// No description provided for @employeeName.
  ///
  /// In en, this message translates to:
  /// **'Employee Name'**
  String get employeeName;

  /// No description provided for @employeeType.
  ///
  /// In en, this message translates to:
  /// **'Employee Type'**
  String get employeeType;

  /// No description provided for @userpermissionCanEditEmployees.
  ///
  /// In en, this message translates to:
  /// **'Can Edit Employees'**
  String get userpermissionCanEditEmployees;

  /// No description provided for @userpermissionCanEditEmployeesDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to edit employee details.'**
  String get userpermissionCanEditEmployeesDescription;

  /// No description provided for @userpermissionCanViewEmployees.
  ///
  /// In en, this message translates to:
  /// **'Can View Employees'**
  String get userpermissionCanViewEmployees;

  /// No description provided for @userpermissionCanViewEmployeesDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to view employee details.'**
  String get userpermissionCanViewEmployeesDescription;

  /// No description provided for @viewEmployees.
  ///
  /// In en, this message translates to:
  /// **'View Employees'**
  String get viewEmployees;

  /// No description provided for @editEmployee.
  ///
  /// In en, this message translates to:
  /// **'Edit Employee'**
  String get editEmployee;

  /// No description provided for @employee.
  ///
  /// In en, this message translates to:
  /// **'Employee'**
  String get employee;

  /// No description provided for @employeeId.
  ///
  /// In en, this message translates to:
  /// **'Employee ID'**
  String get employeeId;

  /// No description provided for @employeeNotFound.
  ///
  /// In en, this message translates to:
  /// **'This employee was not found.'**
  String get employeeNotFound;

  /// No description provided for @employeeTypeRequired.
  ///
  /// In en, this message translates to:
  /// **'The Employee Type is required.'**
  String get employeeTypeRequired;

  /// No description provided for @usernameAlreadyRegistered.
  ///
  /// In en, this message translates to:
  /// **'This username is already registered.'**
  String get usernameAlreadyRegistered;

  /// No description provided for @userpermissionCanEditContacts.
  ///
  /// In en, this message translates to:
  /// **'Can Edit Contacts'**
  String get userpermissionCanEditContacts;

  /// No description provided for @userpermissionCanEditContactsDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to edit contact details.'**
  String get userpermissionCanEditContactsDescription;

  /// No description provided for @userpermissionCanViewContacts.
  ///
  /// In en, this message translates to:
  /// **'Can View Contacts'**
  String get userpermissionCanViewContacts;

  /// No description provided for @userpermissionCanViewContactsDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to view contact details.'**
  String get userpermissionCanViewContactsDescription;

  /// No description provided for @addContact.
  ///
  /// In en, this message translates to:
  /// **'Add Contact'**
  String get addContact;

  /// No description provided for @contacts.
  ///
  /// In en, this message translates to:
  /// **'Contacts'**
  String get contacts;

  /// No description provided for @contactName.
  ///
  /// In en, this message translates to:
  /// **'Contact Name'**
  String get contactName;

  /// No description provided for @viewContacts.
  ///
  /// In en, this message translates to:
  /// **'View Contacts'**
  String get viewContacts;

  /// No description provided for @contactId.
  ///
  /// In en, this message translates to:
  /// **'Contact ID'**
  String get contactId;

  /// No description provided for @contactLocationRequired.
  ///
  /// In en, this message translates to:
  /// **'The location is required for this contact.'**
  String get contactLocationRequired;

  /// No description provided for @contactNotFound.
  ///
  /// In en, this message translates to:
  /// **'This contact was not found.'**
  String get contactNotFound;

  /// No description provided for @editContact.
  ///
  /// In en, this message translates to:
  /// **'Edit Contact'**
  String get editContact;

  /// No description provided for @chooseLocation.
  ///
  /// In en, this message translates to:
  /// **'Choose Location'**
  String get chooseLocation;

  /// No description provided for @select.
  ///
  /// In en, this message translates to:
  /// **'Select'**
  String get select;

  /// No description provided for @employeeTypeDescription.
  ///
  /// In en, this message translates to:
  /// **'Default permission template for an employee.'**
  String get employeeTypeDescription;

  /// No description provided for @contactType.
  ///
  /// In en, this message translates to:
  /// **'Contact Type'**
  String get contactType;

  /// No description provided for @contactTypeRequired.
  ///
  /// In en, this message translates to:
  /// **'The Contact Type is required.'**
  String get contactTypeRequired;

  /// No description provided for @chooseContact.
  ///
  /// In en, this message translates to:
  /// **'Choose Contact'**
  String get chooseContact;

  /// No description provided for @addContactToLocation.
  ///
  /// In en, this message translates to:
  /// **'Add Contact To Location'**
  String get addContactToLocation;

  /// No description provided for @addEmployeeToLocation.
  ///
  /// In en, this message translates to:
  /// **'Add Employee To Location'**
  String get addEmployeeToLocation;

  /// No description provided for @contactAlreadyInLocation.
  ///
  /// In en, this message translates to:
  /// **'This contact is already in this location.'**
  String get contactAlreadyInLocation;

  /// No description provided for @areYouSureQuestion.
  ///
  /// In en, this message translates to:
  /// **'Are You Sure?'**
  String get areYouSureQuestion;

  /// No description provided for @areYouSureRemoveContactFromLocation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to remove this contact from this location?'**
  String get areYouSureRemoveContactFromLocation;

  /// No description provided for @contactNotFoundInLocation.
  ///
  /// In en, this message translates to:
  /// **'This contact was not found in this location.'**
  String get contactNotFoundInLocation;

  /// No description provided for @removeContactFromLocation.
  ///
  /// In en, this message translates to:
  /// **'Remove Contact From Location'**
  String get removeContactFromLocation;

  /// No description provided for @notes.
  ///
  /// In en, this message translates to:
  /// **'Manager Notes'**
  String get notes;

  /// No description provided for @employeeTypeActions.
  ///
  /// In en, this message translates to:
  /// **'Employee Type Actions'**
  String get employeeTypeActions;

  /// No description provided for @userpermissionCanViewEmployeeTypes.
  ///
  /// In en, this message translates to:
  /// **'Can View Employee Types'**
  String get userpermissionCanViewEmployeeTypes;

  /// No description provided for @userpermissionCanViewEmployeeTypesDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to view employee type details.'**
  String get userpermissionCanViewEmployeeTypesDescription;

  /// No description provided for @viewEmployeeTypes.
  ///
  /// In en, this message translates to:
  /// **'View Employee Types'**
  String get viewEmployeeTypes;

  /// No description provided for @employeeCount.
  ///
  /// In en, this message translates to:
  /// **'Employee Count'**
  String get employeeCount;

  /// No description provided for @employeeTypes.
  ///
  /// In en, this message translates to:
  /// **'Employee Types'**
  String get employeeTypes;

  /// No description provided for @employeeTypeName.
  ///
  /// In en, this message translates to:
  /// **'Employee Type Name'**
  String get employeeTypeName;

  /// No description provided for @permissionCount.
  ///
  /// In en, this message translates to:
  /// **'Permission Count'**
  String get permissionCount;

  /// No description provided for @addEmployeeType.
  ///
  /// In en, this message translates to:
  /// **'Add Employee Type'**
  String get addEmployeeType;

  /// No description provided for @userpermissionCanEditEmployeeTypes.
  ///
  /// In en, this message translates to:
  /// **'Can Edit Employee Types'**
  String get userpermissionCanEditEmployeeTypes;

  /// No description provided for @userpermissionCanEditEmployeeTypesDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to edit employee type details.'**
  String get userpermissionCanEditEmployeeTypesDescription;

  /// No description provided for @employeeTypeNotFound.
  ///
  /// In en, this message translates to:
  /// **'This employee type was not found.'**
  String get employeeTypeNotFound;

  /// No description provided for @editEmployeeType.
  ///
  /// In en, this message translates to:
  /// **'Edit Employee Type'**
  String get editEmployeeType;

  /// No description provided for @permissions.
  ///
  /// In en, this message translates to:
  /// **'Permissions'**
  String get permissions;

  /// No description provided for @description.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// No description provided for @permission.
  ///
  /// In en, this message translates to:
  /// **'Permission'**
  String get permission;

  /// No description provided for @notEditable.
  ///
  /// In en, this message translates to:
  /// **'This record cannot be edited.'**
  String get notEditable;

  /// No description provided for @employeeTypeNameRequired.
  ///
  /// In en, this message translates to:
  /// **'The Employee Type Name is required.'**
  String get employeeTypeNameRequired;

  /// No description provided for @copyFromEmployeeType.
  ///
  /// In en, this message translates to:
  /// **'Copy From Existing Employee Type'**
  String get copyFromEmployeeType;

  /// No description provided for @addPermissionToEmployeeType.
  ///
  /// In en, this message translates to:
  /// **'Add Permission To Employee Type'**
  String get addPermissionToEmployeeType;

  /// No description provided for @areYouSureRemovePermissionFromEmployeeType.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to remove this permission from this employee type?'**
  String get areYouSureRemovePermissionFromEmployeeType;

  /// No description provided for @choosePermission.
  ///
  /// In en, this message translates to:
  /// **'Choose Permission'**
  String get choosePermission;

  /// No description provided for @permissionAlreadyInEmployeeType.
  ///
  /// In en, this message translates to:
  /// **'This permission is already in this employee type.'**
  String get permissionAlreadyInEmployeeType;

  /// No description provided for @permissionNotFound.
  ///
  /// In en, this message translates to:
  /// **'The Permission was not found.'**
  String get permissionNotFound;

  /// No description provided for @permissionNotFoundInEmployeeType.
  ///
  /// In en, this message translates to:
  /// **'This permission was not found in this employee type.'**
  String get permissionNotFoundInEmployeeType;

  /// No description provided for @remove.
  ///
  /// In en, this message translates to:
  /// **'Remove'**
  String get remove;

  /// No description provided for @userpermissionCanPunchIn.
  ///
  /// In en, this message translates to:
  /// **'Can Punch In'**
  String get userpermissionCanPunchIn;

  /// No description provided for @userpermissionCanPunchInDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user punch in or out.'**
  String get userpermissionCanPunchInDescription;

  /// No description provided for @userpermissionCanEditPunchCards.
  ///
  /// In en, this message translates to:
  /// **'Can Edit Punch Cards'**
  String get userpermissionCanEditPunchCards;

  /// No description provided for @userpermissionCanEditPunchCardsDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to edit other user\'s punch card details.'**
  String get userpermissionCanEditPunchCardsDescription;

  /// No description provided for @userpermissionCanViewPunchCards.
  ///
  /// In en, this message translates to:
  /// **'Can View PunchCards'**
  String get userpermissionCanViewPunchCards;

  /// No description provided for @userpermissionCanViewPunchCardsDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to view other user\'s punch card details.'**
  String get userpermissionCanViewPunchCardsDescription;

  /// No description provided for @punchcardNotFound.
  ///
  /// In en, this message translates to:
  /// **'This punch card was not found.'**
  String get punchcardNotFound;

  /// No description provided for @geoFenceWarning.
  ///
  /// In en, this message translates to:
  /// **'WARNING: You exited the location area without Punching Out.'**
  String get geoFenceWarning;

  /// No description provided for @geoFenceEntered.
  ///
  /// In en, this message translates to:
  /// **'Geo Fence Entered'**
  String get geoFenceEntered;

  /// No description provided for @geoFenceEnteredDescription.
  ///
  /// In en, this message translates to:
  /// **'User entered geo fence area.'**
  String get geoFenceEnteredDescription;

  /// No description provided for @geoFenceExited.
  ///
  /// In en, this message translates to:
  /// **'Geo Fence Exited'**
  String get geoFenceExited;

  /// No description provided for @geoFenceExitedDescription.
  ///
  /// In en, this message translates to:
  /// **'User exited geo fence area.'**
  String get geoFenceExitedDescription;

  /// No description provided for @geoLocationDisabled.
  ///
  /// In en, this message translates to:
  /// **'Geo Location Disabled'**
  String get geoLocationDisabled;

  /// No description provided for @geoLocationDisabledDescription.
  ///
  /// In en, this message translates to:
  /// **'Device geo location disabled.'**
  String get geoLocationDisabledDescription;

  /// No description provided for @geoLocationEnabled.
  ///
  /// In en, this message translates to:
  /// **'Geo Location Enabled'**
  String get geoLocationEnabled;

  /// No description provided for @geoLocationEnabledDescription.
  ///
  /// In en, this message translates to:
  /// **'Device geo location enabled.'**
  String get geoLocationEnabledDescription;

  /// No description provided for @geoLocationUpdate.
  ///
  /// In en, this message translates to:
  /// **'Geo Location Update'**
  String get geoLocationUpdate;

  /// No description provided for @geoLocationUpdateDescription.
  ///
  /// In en, this message translates to:
  /// **'Update to user geo location.'**
  String get geoLocationUpdateDescription;

  /// No description provided for @oldPasswordRequired.
  ///
  /// In en, this message translates to:
  /// **'The Old Password is required.'**
  String get oldPasswordRequired;

  /// No description provided for @oldPasswordIncorrect.
  ///
  /// In en, this message translates to:
  /// **'The old password is incorrect.'**
  String get oldPasswordIncorrect;

  /// No description provided for @updatePassword.
  ///
  /// In en, this message translates to:
  /// **'Update Password'**
  String get updatePassword;

  /// No description provided for @newPassword.
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get newPassword;

  /// No description provided for @oldPassword.
  ///
  /// In en, this message translates to:
  /// **'Old Password'**
  String get oldPassword;

  /// No description provided for @contactTypes.
  ///
  /// In en, this message translates to:
  /// **'Contact Types'**
  String get contactTypes;

  /// No description provided for @contactTypeActions.
  ///
  /// In en, this message translates to:
  /// **'Contact Type Actions'**
  String get contactTypeActions;

  /// No description provided for @contactTypeName.
  ///
  /// In en, this message translates to:
  /// **'Contact Type Name'**
  String get contactTypeName;

  /// No description provided for @contactTypeNameRequired.
  ///
  /// In en, this message translates to:
  /// **'The Contact Type Name is required.'**
  String get contactTypeNameRequired;

  /// No description provided for @contactTypeNotFound.
  ///
  /// In en, this message translates to:
  /// **'This contact type was not found.'**
  String get contactTypeNotFound;

  /// No description provided for @userpermissionCanEditContactTypes.
  ///
  /// In en, this message translates to:
  /// **'Can Edit Contact Types'**
  String get userpermissionCanEditContactTypes;

  /// No description provided for @userpermissionCanEditContactTypesDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to edit contact type details.'**
  String get userpermissionCanEditContactTypesDescription;

  /// No description provided for @userpermissionCanViewContactTypes.
  ///
  /// In en, this message translates to:
  /// **'Can View Contact Types'**
  String get userpermissionCanViewContactTypes;

  /// No description provided for @userpermissionCanViewContactTypesDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to view contact type details.'**
  String get userpermissionCanViewContactTypesDescription;

  /// No description provided for @permissionAlreadyInContactType.
  ///
  /// In en, this message translates to:
  /// **'This permission is already in this contact type.'**
  String get permissionAlreadyInContactType;

  /// No description provided for @permissionNotFoundInContactType.
  ///
  /// In en, this message translates to:
  /// **'This permission was not found in this contact type.'**
  String get permissionNotFoundInContactType;

  /// No description provided for @addContactType.
  ///
  /// In en, this message translates to:
  /// **'Add Contact Type'**
  String get addContactType;

  /// No description provided for @viewContactTypes.
  ///
  /// In en, this message translates to:
  /// **'View Contact Types'**
  String get viewContactTypes;

  /// No description provided for @copyFromContactType.
  ///
  /// In en, this message translates to:
  /// **'Copy From Contact Type'**
  String get copyFromContactType;

  /// No description provided for @contactCount.
  ///
  /// In en, this message translates to:
  /// **'Contact Count'**
  String get contactCount;

  /// No description provided for @addPermissionToContactType.
  ///
  /// In en, this message translates to:
  /// **'Add Permission To Contact Type'**
  String get addPermissionToContactType;

  /// No description provided for @areYouSureRemovePermissionFromContactType.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to remove this permission from this contact type?'**
  String get areYouSureRemovePermissionFromContactType;

  /// No description provided for @editContactType.
  ///
  /// In en, this message translates to:
  /// **'Edit Contact Type'**
  String get editContactType;

  /// No description provided for @daily.
  ///
  /// In en, this message translates to:
  /// **'Daily'**
  String get daily;

  /// No description provided for @day.
  ///
  /// In en, this message translates to:
  /// **'Day'**
  String get day;

  /// No description provided for @firstOccurrence.
  ///
  /// In en, this message translates to:
  /// **'First Occurrence'**
  String get firstOccurrence;

  /// No description provided for @fourthOccurrence.
  ///
  /// In en, this message translates to:
  /// **'Fourth Occurrence'**
  String get fourthOccurrence;

  /// No description provided for @friday.
  ///
  /// In en, this message translates to:
  /// **'Friday'**
  String get friday;

  /// No description provided for @hour.
  ///
  /// In en, this message translates to:
  /// **'Hour'**
  String get hour;

  /// No description provided for @lastOccurrence.
  ///
  /// In en, this message translates to:
  /// **'Last Occurrence'**
  String get lastOccurrence;

  /// No description provided for @minute.
  ///
  /// In en, this message translates to:
  /// **'Minute'**
  String get minute;

  /// No description provided for @monday.
  ///
  /// In en, this message translates to:
  /// **'Monday'**
  String get monday;

  /// No description provided for @month.
  ///
  /// In en, this message translates to:
  /// **'Month'**
  String get month;

  /// No description provided for @saturday.
  ///
  /// In en, this message translates to:
  /// **'Saturday'**
  String get saturday;

  /// No description provided for @second.
  ///
  /// In en, this message translates to:
  /// **'Second'**
  String get second;

  /// No description provided for @secondOccurrence.
  ///
  /// In en, this message translates to:
  /// **'Second Occurrence'**
  String get secondOccurrence;

  /// No description provided for @sunday.
  ///
  /// In en, this message translates to:
  /// **'Sunday'**
  String get sunday;

  /// No description provided for @thirdOccurrence.
  ///
  /// In en, this message translates to:
  /// **'Third Occurrence'**
  String get thirdOccurrence;

  /// No description provided for @thursday.
  ///
  /// In en, this message translates to:
  /// **'Thursday'**
  String get thursday;

  /// No description provided for @tuesday.
  ///
  /// In en, this message translates to:
  /// **'Tuesday'**
  String get tuesday;

  /// No description provided for @wednesday.
  ///
  /// In en, this message translates to:
  /// **'Wednesday'**
  String get wednesday;

  /// No description provided for @week.
  ///
  /// In en, this message translates to:
  /// **'Week'**
  String get week;

  /// No description provided for @weekday.
  ///
  /// In en, this message translates to:
  /// **'Weekday'**
  String get weekday;

  /// No description provided for @weekend.
  ///
  /// In en, this message translates to:
  /// **'Weekend'**
  String get weekend;

  /// No description provided for @addSchedule.
  ///
  /// In en, this message translates to:
  /// **'Add Schedule'**
  String get addSchedule;

  /// No description provided for @employeeSchedule.
  ///
  /// In en, this message translates to:
  /// **'Employee Schedule'**
  String get employeeSchedule;

  /// No description provided for @locationSchedule.
  ///
  /// In en, this message translates to:
  /// **'Location Schedule'**
  String get locationSchedule;

  /// No description provided for @schedules.
  ///
  /// In en, this message translates to:
  /// **'Schedules'**
  String get schedules;

  /// No description provided for @userpermissionCanEditSchedules.
  ///
  /// In en, this message translates to:
  /// **'Can Edit Schedules'**
  String get userpermissionCanEditSchedules;

  /// No description provided for @userpermissionCanViewSchedules.
  ///
  /// In en, this message translates to:
  /// **'Can View Schedules'**
  String get userpermissionCanViewSchedules;

  /// No description provided for @viewSchedules.
  ///
  /// In en, this message translates to:
  /// **'View Schedules'**
  String get viewSchedules;

  /// No description provided for @duration.
  ///
  /// In en, this message translates to:
  /// **'Duration'**
  String get duration;

  /// No description provided for @every.
  ///
  /// In en, this message translates to:
  /// **'Every'**
  String get every;

  /// No description provided for @frequency.
  ///
  /// In en, this message translates to:
  /// **'Frequency'**
  String get frequency;

  /// No description provided for @nextScheduled.
  ///
  /// In en, this message translates to:
  /// **'Next Scheduled'**
  String get nextScheduled;

  /// No description provided for @on.
  ///
  /// In en, this message translates to:
  /// **'On'**
  String get on;

  /// No description provided for @startDate.
  ///
  /// In en, this message translates to:
  /// **'Start Date'**
  String get startDate;

  /// No description provided for @startTime.
  ///
  /// In en, this message translates to:
  /// **'Start Time'**
  String get startTime;

  /// No description provided for @viewSchedule.
  ///
  /// In en, this message translates to:
  /// **'View Schedule'**
  String get viewSchedule;

  /// No description provided for @repeatEvery.
  ///
  /// In en, this message translates to:
  /// **'Repeat every'**
  String get repeatEvery;

  /// No description provided for @showPlannedSchedule.
  ///
  /// In en, this message translates to:
  /// **'Show Planned Schedule'**
  String get showPlannedSchedule;

  /// No description provided for @scheduleNotFound.
  ///
  /// In en, this message translates to:
  /// **'This schedule was not found.'**
  String get scheduleNotFound;

  /// No description provided for @timeZone.
  ///
  /// In en, this message translates to:
  /// **'Time Zone'**
  String get timeZone;

  /// No description provided for @addRepeatingSchedule.
  ///
  /// In en, this message translates to:
  /// **'Add Repeating Schedule'**
  String get addRepeatingSchedule;

  /// No description provided for @repeatingSchedules.
  ///
  /// In en, this message translates to:
  /// **'Repeating Schedules'**
  String get repeatingSchedules;

  /// No description provided for @viewRepeatingSchedule.
  ///
  /// In en, this message translates to:
  /// **'View Repeating Schedule'**
  String get viewRepeatingSchedule;

  /// No description provided for @viewRepeatingSchedules.
  ///
  /// In en, this message translates to:
  /// **'View Repeating Schedules'**
  String get viewRepeatingSchedules;

  /// No description provided for @editSchedule.
  ///
  /// In en, this message translates to:
  /// **'Edit Schedule'**
  String get editSchedule;

  /// No description provided for @endLocationTimeZone.
  ///
  /// In en, this message translates to:
  /// **'End (Location time zone)'**
  String get endLocationTimeZone;

  /// No description provided for @from.
  ///
  /// In en, this message translates to:
  /// **'From'**
  String get from;

  /// No description provided for @invalidDateRange.
  ///
  /// In en, this message translates to:
  /// **'Invalid Date Range'**
  String get invalidDateRange;

  /// No description provided for @invalidDuration.
  ///
  /// In en, this message translates to:
  /// **'Invalid Duration'**
  String get invalidDuration;

  /// No description provided for @invalidRecurringRange.
  ///
  /// In en, this message translates to:
  /// **'Invalid Recurring Range'**
  String get invalidRecurringRange;

  /// No description provided for @invalidTimeOfDay.
  ///
  /// In en, this message translates to:
  /// **'Invalid Time Of Day'**
  String get invalidTimeOfDay;

  /// No description provided for @originalLocationTimeZone.
  ///
  /// In en, this message translates to:
  /// **'Original (Location time zone)'**
  String get originalLocationTimeZone;

  /// No description provided for @scheduledLocalTimeZone.
  ///
  /// In en, this message translates to:
  /// **'Scheduled (Current time zone)'**
  String get scheduledLocalTimeZone;

  /// No description provided for @scheduledLocationTimeZone.
  ///
  /// In en, this message translates to:
  /// **'Scheduled (Location time zone)'**
  String get scheduledLocationTimeZone;

  /// No description provided for @selectAtLeastOneDayOfTheWeek.
  ///
  /// In en, this message translates to:
  /// **'Select at least one day of the week.'**
  String get selectAtLeastOneDayOfTheWeek;

  /// No description provided for @startLocationTimeZone.
  ///
  /// In en, this message translates to:
  /// **'Start (Location time zone)'**
  String get startLocationTimeZone;

  /// No description provided for @startTimeLocationTimeZone.
  ///
  /// In en, this message translates to:
  /// **'Start Time (Location time zone)'**
  String get startTimeLocationTimeZone;

  /// No description provided for @to.
  ///
  /// In en, this message translates to:
  /// **'To'**
  String get to;

  /// No description provided for @schedulegeneratorInvalidRecurringDailyfreqOccurseveryfreqId.
  ///
  /// In en, this message translates to:
  /// **'This schedule has an invalid Recurring_DailyFreq_OccursEveryFreq_ID.'**
  String get schedulegeneratorInvalidRecurringDailyfreqOccurseveryfreqId;

  /// No description provided for @schedulegeneratorInvalidRecurringFrequencyId.
  ///
  /// In en, this message translates to:
  /// **'This schedule has an invalid Recurring_Frequency_ID.'**
  String get schedulegeneratorInvalidRecurringFrequencyId;

  /// No description provided for @schedulegeneratorIsNotRecurringNoOnetimeOccurson.
  ///
  /// In en, this message translates to:
  /// **'This schedule is marked as not recurring and did not have a OneTime_OccursOn value.'**
  String get schedulegeneratorIsNotRecurringNoOnetimeOccurson;

  /// No description provided for @schedulegeneratorIsRecurringNoRecurringDailyfreqOccursonce.
  ///
  /// In en, this message translates to:
  /// **'This schedule is marked as recurring and did not have a Recurring_DailyFreq_OccursOnce value.'**
  String get schedulegeneratorIsRecurringNoRecurringDailyfreqOccursonce;

  /// No description provided for @schedulegeneratorNotRecurringDailyfreqOccursonceNoRecurringDailyfreqStartOrEnd.
  ///
  /// In en, this message translates to:
  /// **'This schedule is marked as not recurring daily once and did not have a Recurring_DailyFreq_StartingOn or Recurring_DailyFreq_EndingOn value.'**
  String get schedulegeneratorNotRecurringDailyfreqOccursonceNoRecurringDailyfreqStartOrEnd;

  /// No description provided for @schedulegeneratorRecurringFrequencyDayNoRecurringFrequencyEverynfreq.
  ///
  /// In en, this message translates to:
  /// **'This schedule is marked as recurring daily and did not have a Recurring_Frequency_EveryNFreq value.'**
  String get schedulegeneratorRecurringFrequencyDayNoRecurringFrequencyEverynfreq;

  /// No description provided for @schedulegeneratorRecurringFrequencyMonthNoRecurringFrequencyMonthlyThedayoftheweekId.
  ///
  /// In en, this message translates to:
  /// **'This schedule is marked as recurring monthly and did not have a Recurring_Frequency_Monthly_TheDayOfTheWeek_ID value.'**
  String get schedulegeneratorRecurringFrequencyMonthNoRecurringFrequencyMonthlyThedayoftheweekId;

  /// No description provided for @schedulegeneratorRecurringFrequencyMonthNoRecurringFrequencyMonthlyTheweekId.
  ///
  /// In en, this message translates to:
  /// **'This schedule is marked as recurring monthly and did not have a Recurring_Frequency_Monthly_TheWeek_ID value.'**
  String get schedulegeneratorRecurringFrequencyMonthNoRecurringFrequencyMonthlyTheweekId;

  /// No description provided for @userpermissionCanSendChatMessage.
  ///
  /// In en, this message translates to:
  /// **'Can Send Chat Messages'**
  String get userpermissionCanSendChatMessage;

  /// No description provided for @userpermissionCanSendChatMessageDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to send chat messages.'**
  String get userpermissionCanSendChatMessageDescription;

  /// No description provided for @userpermissionCanViewChatMessage.
  ///
  /// In en, this message translates to:
  /// **'Can View Chat Messages'**
  String get userpermissionCanViewChatMessage;

  /// No description provided for @userpermissionCanViewChatMessageDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to view chats with other users.'**
  String get userpermissionCanViewChatMessageDescription;

  /// No description provided for @message.
  ///
  /// In en, this message translates to:
  /// **'Message'**
  String get message;

  /// No description provided for @recentChatMessages.
  ///
  /// In en, this message translates to:
  /// **'Recent Chat Messages'**
  String get recentChatMessages;

  /// No description provided for @send.
  ///
  /// In en, this message translates to:
  /// **'Send'**
  String get send;

  /// No description provided for @sendChatMessageTo.
  ///
  /// In en, this message translates to:
  /// **'Send chat message to'**
  String get sendChatMessageTo;

  /// No description provided for @punchCards.
  ///
  /// In en, this message translates to:
  /// **'Punch Cards'**
  String get punchCards;

  /// No description provided for @viewPunchCards.
  ///
  /// In en, this message translates to:
  /// **'View Punch Cards'**
  String get viewPunchCards;

  /// No description provided for @clockedInBy.
  ///
  /// In en, this message translates to:
  /// **'Punched In By'**
  String get clockedInBy;

  /// No description provided for @clockedOutBy.
  ///
  /// In en, this message translates to:
  /// **'Punched Out By'**
  String get clockedOutBy;

  /// No description provided for @editPunchCard.
  ///
  /// In en, this message translates to:
  /// **'Edit Punch Card'**
  String get editPunchCard;

  /// No description provided for @punchCardId.
  ///
  /// In en, this message translates to:
  /// **'Punch Card ID'**
  String get punchCardId;

  /// No description provided for @punchCardNotFound.
  ///
  /// In en, this message translates to:
  /// **'This punch card was not found.'**
  String get punchCardNotFound;

  /// No description provided for @clockOutBeforeClockedIn.
  ///
  /// In en, this message translates to:
  /// **'Punch out must be after punch in.'**
  String get clockOutBeforeClockedIn;

  /// No description provided for @clockedIn.
  ///
  /// In en, this message translates to:
  /// **'Punched In'**
  String get clockedIn;

  /// No description provided for @christmasHoliday.
  ///
  /// In en, this message translates to:
  /// **'Christmas Holiday'**
  String get christmasHoliday;

  /// No description provided for @fourthOfJulyHoliday.
  ///
  /// In en, this message translates to:
  /// **'Fourth Of July Holiday'**
  String get fourthOfJulyHoliday;

  /// No description provided for @newYearsHoliday.
  ///
  /// In en, this message translates to:
  /// **'New Years Holiday'**
  String get newYearsHoliday;

  /// No description provided for @thanksgivingHoliday.
  ///
  /// In en, this message translates to:
  /// **'Thanksgiving Holiday'**
  String get thanksgivingHoliday;

  /// No description provided for @userpermissionCanEditNotes.
  ///
  /// In en, this message translates to:
  /// **'Can Edit Notes'**
  String get userpermissionCanEditNotes;

  /// No description provided for @userpermissionCanViewNotes.
  ///
  /// In en, this message translates to:
  /// **'Can View Notes'**
  String get userpermissionCanViewNotes;

  /// No description provided for @noteRequired.
  ///
  /// In en, this message translates to:
  /// **'Text is required in a note.'**
  String get noteRequired;

  /// No description provided for @addNote.
  ///
  /// In en, this message translates to:
  /// **'Add Note'**
  String get addNote;

  /// No description provided for @editNote.
  ///
  /// In en, this message translates to:
  /// **'Edit Note'**
  String get editNote;

  /// No description provided for @noteNotFound.
  ///
  /// In en, this message translates to:
  /// **'This note was not found.'**
  String get noteNotFound;

  /// No description provided for @changedBy.
  ///
  /// In en, this message translates to:
  /// **'Changed By'**
  String get changedBy;

  /// No description provided for @clear.
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// No description provided for @chooseEmployee.
  ///
  /// In en, this message translates to:
  /// **'Choose Employee'**
  String get chooseEmployee;

  /// No description provided for @employeeOrLocationRequired.
  ///
  /// In en, this message translates to:
  /// **'Either an employee or location must be selected.'**
  String get employeeOrLocationRequired;

  /// No description provided for @userpermissionCanEditChecklists.
  ///
  /// In en, this message translates to:
  /// **'Can Edit Checklists'**
  String get userpermissionCanEditChecklists;

  /// No description provided for @userpermissionCanEditChecklistsDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to edit checklists.'**
  String get userpermissionCanEditChecklistsDescription;

  /// No description provided for @userpermissionCanViewChecklists.
  ///
  /// In en, this message translates to:
  /// **'Can View Checklists'**
  String get userpermissionCanViewChecklists;

  /// No description provided for @userpermissionCanViewChecklistsDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to view checklists.'**
  String get userpermissionCanViewChecklistsDescription;

  /// No description provided for @addChecklist.
  ///
  /// In en, this message translates to:
  /// **'Add Checklist'**
  String get addChecklist;

  /// No description provided for @checklist.
  ///
  /// In en, this message translates to:
  /// **'Checklist'**
  String get checklist;

  /// No description provided for @checklists.
  ///
  /// In en, this message translates to:
  /// **'Checklists'**
  String get checklists;

  /// No description provided for @addChecklistItem.
  ///
  /// In en, this message translates to:
  /// **'Add Item'**
  String get addChecklistItem;

  /// No description provided for @checklistNotFound.
  ///
  /// In en, this message translates to:
  /// **'This checklist was not found.'**
  String get checklistNotFound;

  /// No description provided for @editChecklist.
  ///
  /// In en, this message translates to:
  /// **'Edit Checklist'**
  String get editChecklist;

  /// No description provided for @editChecklistItem.
  ///
  /// In en, this message translates to:
  /// **'Edit Item'**
  String get editChecklistItem;

  /// No description provided for @items.
  ///
  /// In en, this message translates to:
  /// **'Items'**
  String get items;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @endLocalTimeZone.
  ///
  /// In en, this message translates to:
  /// **'End (Local time zone)'**
  String get endLocalTimeZone;

  /// No description provided for @startLocalTimeZone.
  ///
  /// In en, this message translates to:
  /// **'Start (Local time zone)'**
  String get startLocalTimeZone;

  /// No description provided for @createdBy.
  ///
  /// In en, this message translates to:
  /// **'Created By'**
  String get createdBy;

  /// No description provided for @note.
  ///
  /// In en, this message translates to:
  /// **'Note'**
  String get note;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @calendar.
  ///
  /// In en, this message translates to:
  /// **'Calendar'**
  String get calendar;

  /// No description provided for @alerts.
  ///
  /// In en, this message translates to:
  /// **'Alerts'**
  String get alerts;

  /// No description provided for @allPunchCards.
  ///
  /// In en, this message translates to:
  /// **'All Punch Cards'**
  String get allPunchCards;

  /// No description provided for @currentPayPeriod.
  ///
  /// In en, this message translates to:
  /// **'Current Pay Period'**
  String get currentPayPeriod;

  /// No description provided for @customTimePeriod.
  ///
  /// In en, this message translates to:
  /// **'Custom Time Period'**
  String get customTimePeriod;

  /// No description provided for @hours.
  ///
  /// In en, this message translates to:
  /// **'Hours'**
  String get hours;

  /// No description provided for @recentPunchCards.
  ///
  /// In en, this message translates to:
  /// **'Recent Punch Cards'**
  String get recentPunchCards;

  /// No description provided for @addPunchCard.
  ///
  /// In en, this message translates to:
  /// **'Add Punch Card'**
  String get addPunchCard;

  /// No description provided for @timePeriod.
  ///
  /// In en, this message translates to:
  /// **'Time Period'**
  String get timePeriod;

  /// No description provided for @endDateRequired.
  ///
  /// In en, this message translates to:
  /// **'The end date is required.'**
  String get endDateRequired;

  /// No description provided for @employeeRequired.
  ///
  /// In en, this message translates to:
  /// **'The employee is required.'**
  String get employeeRequired;

  /// No description provided for @startDateRequired.
  ///
  /// In en, this message translates to:
  /// **'The start date is required.'**
  String get startDateRequired;

  /// No description provided for @startDateCannotBeAfterEndDate.
  ///
  /// In en, this message translates to:
  /// **'The start date cannot be after the end date.'**
  String get startDateCannotBeAfterEndDate;

  /// No description provided for @locationRequired.
  ///
  /// In en, this message translates to:
  /// **'The location is required.'**
  String get locationRequired;

  /// No description provided for @selectLocationLoadSchedules.
  ///
  /// In en, this message translates to:
  /// **'Select a location to load the schedules.'**
  String get selectLocationLoadSchedules;

  /// No description provided for @takeNote.
  ///
  /// In en, this message translates to:
  /// **'Take Note'**
  String get takeNote;

  /// No description provided for @endTime.
  ///
  /// In en, this message translates to:
  /// **'End Time'**
  String get endTime;

  /// No description provided for @endTimeLocationTimeZone.
  ///
  /// In en, this message translates to:
  /// **'End Time (Location time zone)'**
  String get endTimeLocationTimeZone;

  /// No description provided for @recurring.
  ///
  /// In en, this message translates to:
  /// **'Recurring'**
  String get recurring;

  /// No description provided for @endsBy.
  ///
  /// In en, this message translates to:
  /// **'Ends By'**
  String get endsBy;

  /// No description provided for @invalidEndDate.
  ///
  /// In en, this message translates to:
  /// **'Invalid End Date'**
  String get invalidEndDate;

  /// No description provided for @noEndDate.
  ///
  /// In en, this message translates to:
  /// **'No End Date'**
  String get noEndDate;

  /// No description provided for @nextLocation.
  ///
  /// In en, this message translates to:
  /// **'Next Location'**
  String get nextLocation;

  /// No description provided for @travelTime.
  ///
  /// In en, this message translates to:
  /// **'Travel Time'**
  String get travelTime;

  /// No description provided for @localTime.
  ///
  /// In en, this message translates to:
  /// **'Local Time'**
  String get localTime;

  /// No description provided for @scheduledTime.
  ///
  /// In en, this message translates to:
  /// **'Scheduled Time'**
  String get scheduledTime;

  /// No description provided for @scheduleDetailsFor.
  ///
  /// In en, this message translates to:
  /// **'Schedule Details For'**
  String get scheduleDetailsFor;

  /// No description provided for @days.
  ///
  /// In en, this message translates to:
  /// **'Days'**
  String get days;

  /// No description provided for @weeks.
  ///
  /// In en, this message translates to:
  /// **'Weeks'**
  String get weeks;

  /// No description provided for @months.
  ///
  /// In en, this message translates to:
  /// **'Months'**
  String get months;

  /// No description provided for @dayOfMonth.
  ///
  /// In en, this message translates to:
  /// **'Day Of Month'**
  String get dayOfMonth;

  /// No description provided for @onSpecificDay.
  ///
  /// In en, this message translates to:
  /// **'On Specific Day'**
  String get onSpecificDay;

  /// No description provided for @onSpecificWeek.
  ///
  /// In en, this message translates to:
  /// **'On Specific Week'**
  String get onSpecificWeek;

  /// No description provided for @editRepeatingSchedule.
  ///
  /// In en, this message translates to:
  /// **'Edit Repeating Schedule'**
  String get editRepeatingSchedule;

  /// No description provided for @april.
  ///
  /// In en, this message translates to:
  /// **'April'**
  String get april;

  /// No description provided for @august.
  ///
  /// In en, this message translates to:
  /// **'August'**
  String get august;

  /// No description provided for @december.
  ///
  /// In en, this message translates to:
  /// **'December'**
  String get december;

  /// No description provided for @february.
  ///
  /// In en, this message translates to:
  /// **'February'**
  String get february;

  /// No description provided for @january.
  ///
  /// In en, this message translates to:
  /// **'January'**
  String get january;

  /// No description provided for @july.
  ///
  /// In en, this message translates to:
  /// **'July'**
  String get july;

  /// No description provided for @june.
  ///
  /// In en, this message translates to:
  /// **'June'**
  String get june;

  /// No description provided for @march.
  ///
  /// In en, this message translates to:
  /// **'March'**
  String get march;

  /// No description provided for @may.
  ///
  /// In en, this message translates to:
  /// **'May'**
  String get may;

  /// No description provided for @november.
  ///
  /// In en, this message translates to:
  /// **'November'**
  String get november;

  /// No description provided for @october.
  ///
  /// In en, this message translates to:
  /// **'October'**
  String get october;

  /// No description provided for @september.
  ///
  /// In en, this message translates to:
  /// **'September'**
  String get september;

  /// No description provided for @onSpecificMonth.
  ///
  /// In en, this message translates to:
  /// **'On Specific Month'**
  String get onSpecificMonth;

  /// No description provided for @endTimeRequired.
  ///
  /// In en, this message translates to:
  /// **'The End Time is required.'**
  String get endTimeRequired;

  /// No description provided for @noDescription.
  ///
  /// In en, this message translates to:
  /// **'No Description'**
  String get noDescription;

  /// No description provided for @scheduletemplateInvalidRecurringFrequencyId.
  ///
  /// In en, this message translates to:
  /// **'This schedule has an invalid Day, Week, Month selection.'**
  String get scheduletemplateInvalidRecurringFrequencyId;

  /// No description provided for @scheduletemplateRecurringFrequencyDayNoRecurringFrequencyEverynfreq.
  ///
  /// In en, this message translates to:
  /// **'This schedule is marked as recurring daily and did not have a repeat value.'**
  String get scheduletemplateRecurringFrequencyDayNoRecurringFrequencyEverynfreq;

  /// No description provided for @scheduletemplateRecurringFrequencyMonthNoRecurringFrequencyEverynfreq.
  ///
  /// In en, this message translates to:
  /// **'This schedule is marked as recurring monthly and did not have a repeat value.'**
  String get scheduletemplateRecurringFrequencyMonthNoRecurringFrequencyEverynfreq;

  /// No description provided for @scheduletemplateRecurringFrequencyWeekNoRecurringFrequencyEverynfreq.
  ///
  /// In en, this message translates to:
  /// **'This schedule is marked as recurring weekly and did not have a repeat value.'**
  String get scheduletemplateRecurringFrequencyWeekNoRecurringFrequencyEverynfreq;

  /// No description provided for @scheduletemplateRecurringFrequencyWeekNoWeekdaySelected.
  ///
  /// In en, this message translates to:
  /// **'This schedule is marked as recurring weekly and no weekdays were selected.'**
  String get scheduletemplateRecurringFrequencyWeekNoWeekdaySelected;

  /// No description provided for @startTimeCannotBeAfterEndTime.
  ///
  /// In en, this message translates to:
  /// **'The start time cannot be after the end time.'**
  String get startTimeCannotBeAfterEndTime;

  /// No description provided for @startTimeRequired.
  ///
  /// In en, this message translates to:
  /// **'The Start Time is required.'**
  String get startTimeRequired;

  /// No description provided for @scheduletemplateRecurringFrequencyInvalidMonthDay.
  ///
  /// In en, this message translates to:
  /// **'This schedule is marked as recurring monthly on a specific month day and the day is not valid.'**
  String get scheduletemplateRecurringFrequencyInvalidMonthDay;

  /// No description provided for @scheduletemplateRecurringFrequencyNoMonthlyOnMonth.
  ///
  /// In en, this message translates to:
  /// **'This schedule is marked as recurring monthly on a specific month and did not have a month value.'**
  String get scheduletemplateRecurringFrequencyNoMonthlyOnMonth;

  /// No description provided for @scheduletemplateRecurringFrequencyNoMonthlyWeek.
  ///
  /// In en, this message translates to:
  /// **'This schedule is marked as recurring monthly on a specific month week and did not have a weekly ocurrance value.'**
  String get scheduletemplateRecurringFrequencyNoMonthlyWeek;

  /// No description provided for @scheduletemplateRecurringFrequencyNoMonthlyWeekday.
  ///
  /// In en, this message translates to:
  /// **'This schedule is marked as recurring monthly on a specific month week and did not have a week day value.'**
  String get scheduletemplateRecurringFrequencyNoMonthlyWeekday;

  /// No description provided for @scheduletemplateRecurringFrequencyNoMonthDay.
  ///
  /// In en, this message translates to:
  /// **'This schedule is marked as recurring monthly on a specific month day and did not have a day value.'**
  String get scheduletemplateRecurringFrequencyNoMonthDay;

  /// No description provided for @areYouSureEditSubscriptionTemplateWillRemoveFutureSchedules.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to edit this repeating schedule? This will remove any future existing schedules that are already created.'**
  String get areYouSureEditSubscriptionTemplateWillRemoveFutureSchedules;

  /// No description provided for @addNewSchedule.
  ///
  /// In en, this message translates to:
  /// **'Add New Schedule'**
  String get addNewSchedule;

  /// No description provided for @editThisSchedule.
  ///
  /// In en, this message translates to:
  /// **'Edit This Schedule'**
  String get editThisSchedule;

  /// No description provided for @viewThisSchedule.
  ///
  /// In en, this message translates to:
  /// **'View This Schedule'**
  String get viewThisSchedule;

  /// No description provided for @of0.
  ///
  /// In en, this message translates to:
  /// **'Of'**
  String get of0;

  /// No description provided for @onThe.
  ///
  /// In en, this message translates to:
  /// **'On The'**
  String get onThe;

  /// No description provided for @nd.
  ///
  /// In en, this message translates to:
  /// **'nd\n    As in the 2nd of the month.'**
  String get nd;

  /// No description provided for @rd.
  ///
  /// In en, this message translates to:
  /// **'rd\n    As in the 3rd of the month.'**
  String get rd;

  /// No description provided for @st.
  ///
  /// In en, this message translates to:
  /// **'st\n    As in the 1st of the month.'**
  String get st;

  /// No description provided for @th.
  ///
  /// In en, this message translates to:
  /// **'th\n    As in the 4th of the month.'**
  String get th;

  /// No description provided for @year.
  ///
  /// In en, this message translates to:
  /// **'Year'**
  String get year;

  /// No description provided for @years.
  ///
  /// In en, this message translates to:
  /// **'Years'**
  String get years;

  /// No description provided for @in0.
  ///
  /// In en, this message translates to:
  /// **'In'**
  String get in0;

  /// No description provided for @admin.
  ///
  /// In en, this message translates to:
  /// **'Admin'**
  String get admin;

  /// No description provided for @adminDescription.
  ///
  /// In en, this message translates to:
  /// **'Administrative'**
  String get adminDescription;

  /// No description provided for @locationDescription.
  ///
  /// In en, this message translates to:
  /// **'Unscheduled Job'**
  String get locationDescription;

  /// No description provided for @scheduleDescription.
  ///
  /// In en, this message translates to:
  /// **'Scheduled Job'**
  String get scheduleDescription;

  /// No description provided for @travelTimeDescription.
  ///
  /// In en, this message translates to:
  /// **'Travel Time'**
  String get travelTimeDescription;

  /// No description provided for @invalidJobType.
  ///
  /// In en, this message translates to:
  /// **'Invalid Job Type'**
  String get invalidJobType;

  /// No description provided for @jobType.
  ///
  /// In en, this message translates to:
  /// **'Job Type'**
  String get jobType;

  /// No description provided for @userpermissionCanViewJobTypes.
  ///
  /// In en, this message translates to:
  /// **'Can View Job Types'**
  String get userpermissionCanViewJobTypes;

  /// No description provided for @userpermissionCanViewJobTypeDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to view job types.'**
  String get userpermissionCanViewJobTypeDescription;

  /// No description provided for @addJobType.
  ///
  /// In en, this message translates to:
  /// **'Add Job Type'**
  String get addJobType;

  /// No description provided for @editJobType.
  ///
  /// In en, this message translates to:
  /// **'Edit Job Type'**
  String get editJobType;

  /// No description provided for @jobTypes.
  ///
  /// In en, this message translates to:
  /// **'Job Types'**
  String get jobTypes;

  /// No description provided for @jobTypeActions.
  ///
  /// In en, this message translates to:
  /// **'Job Type Actions'**
  String get jobTypeActions;

  /// No description provided for @jobTypeNotFound.
  ///
  /// In en, this message translates to:
  /// **'This job type was not found.'**
  String get jobTypeNotFound;

  /// No description provided for @userpermissionCanEditJobTypes.
  ///
  /// In en, this message translates to:
  /// **'Can Edit Job Types'**
  String get userpermissionCanEditJobTypes;

  /// No description provided for @userpermissionCanEditJobTypesDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to edit job type details.'**
  String get userpermissionCanEditJobTypesDescription;

  /// No description provided for @viewJobTypes.
  ///
  /// In en, this message translates to:
  /// **'View Job Types'**
  String get viewJobTypes;

  /// No description provided for @scheduletemplateCouldNotBeCreated.
  ///
  /// In en, this message translates to:
  /// **'A new schedule template could not be created. Please try again.'**
  String get scheduletemplateCouldNotBeCreated;

  /// No description provided for @userpermissionCanViewAlerts.
  ///
  /// In en, this message translates to:
  /// **'Can View Alerts'**
  String get userpermissionCanViewAlerts;

  /// No description provided for @userpermissionCanViewReports.
  ///
  /// In en, this message translates to:
  /// **'Can View Reports'**
  String get userpermissionCanViewReports;

  /// No description provided for @userpermissionCanViewReportsDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to view reports.'**
  String get userpermissionCanViewReportsDescription;

  /// No description provided for @reportUserSessions.
  ///
  /// In en, this message translates to:
  /// **'User Sessions'**
  String get reportUserSessions;

  /// No description provided for @reportUserSessionsDescription.
  ///
  /// In en, this message translates to:
  /// **'Shows the login/out times and forced out reasons for user sessions.'**
  String get reportUserSessionsDescription;

  /// No description provided for @administration.
  ///
  /// In en, this message translates to:
  /// **'Administration'**
  String get administration;

  /// No description provided for @reports.
  ///
  /// In en, this message translates to:
  /// **'Reports'**
  String get reports;

  /// No description provided for @reportName.
  ///
  /// In en, this message translates to:
  /// **'Report Name'**
  String get reportName;

  /// No description provided for @run.
  ///
  /// In en, this message translates to:
  /// **'Run'**
  String get run;

  /// No description provided for @reportNotFound.
  ///
  /// In en, this message translates to:
  /// **'This report was not found.'**
  String get reportNotFound;

  /// No description provided for @configSettingAlertClockInLimitMinMvc.
  ///
  /// In en, this message translates to:
  /// **'Alert Punch In Limit (Mins)'**
  String get configSettingAlertClockInLimitMinMvc;

  /// No description provided for @configSettingAlertClockInLimitMinMvcDescription.
  ///
  /// In en, this message translates to:
  /// **'Amount of time in minutes after a schedule starts when an alert is generated if employee fails to punch in.'**
  String get configSettingAlertClockInLimitMinMvcDescription;

  /// No description provided for @report.
  ///
  /// In en, this message translates to:
  /// **'Report'**
  String get report;

  /// No description provided for @setReportParameters.
  ///
  /// In en, this message translates to:
  /// **'Set Report Parameters'**
  String get setReportParameters;

  /// No description provided for @fillInOptionsClickRunReport.
  ///
  /// In en, this message translates to:
  /// **'Fill in the options, then click Run Report.'**
  String get fillInOptionsClickRunReport;

  /// No description provided for @adobeAcrobat.
  ///
  /// In en, this message translates to:
  /// **'Adobe Acrobat'**
  String get adobeAcrobat;

  /// No description provided for @commaSeparatedValues.
  ///
  /// In en, this message translates to:
  /// **'Comma Separated Values'**
  String get commaSeparatedValues;

  /// No description provided for @downloadReport.
  ///
  /// In en, this message translates to:
  /// **'Download Report'**
  String get downloadReport;

  /// No description provided for @microsoftExcel.
  ///
  /// In en, this message translates to:
  /// **'Microsoft Excel'**
  String get microsoftExcel;

  /// No description provided for @microsoftWord.
  ///
  /// In en, this message translates to:
  /// **'Microsoft Word'**
  String get microsoftWord;

  /// No description provided for @reportFormat.
  ///
  /// In en, this message translates to:
  /// **'Report Format'**
  String get reportFormat;

  /// No description provided for @runReport.
  ///
  /// In en, this message translates to:
  /// **'Run Report'**
  String get runReport;

  /// No description provided for @clickHereDownloadPdf.
  ///
  /// In en, this message translates to:
  /// **'Click here to download the PDF'**
  String get clickHereDownloadPdf;

  /// No description provided for @clickHereInstallAdobeReader.
  ///
  /// In en, this message translates to:
  /// **'or click here to install Adobe Reader.'**
  String get clickHereInstallAdobeReader;

  /// No description provided for @noPdfSupportMessage.
  ///
  /// In en, this message translates to:
  /// **'It appears you don\'t have Adobe Reader or PDF support in this web browser.'**
  String get noPdfSupportMessage;

  /// No description provided for @goFullscreen.
  ///
  /// In en, this message translates to:
  /// **'Go Fullscreen'**
  String get goFullscreen;

  /// No description provided for @fillInAboveOptionsClickRunReport.
  ///
  /// In en, this message translates to:
  /// **'Fill in the options above, then click Run Report.'**
  String get fillInAboveOptionsClickRunReport;

  /// No description provided for @reportNotReadyToRun.
  ///
  /// In en, this message translates to:
  /// **'Your report is not ready to run.'**
  String get reportNotReadyToRun;

  /// No description provided for @canAddSchedulesDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to add schedules.'**
  String get canAddSchedulesDescription;

  /// No description provided for @canEditNotesDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to edit notes.'**
  String get canEditNotesDescription;

  /// No description provided for @canEditSchedulesDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to edit schedules.'**
  String get canEditSchedulesDescription;

  /// No description provided for @canViewAlertsDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to view alerts.'**
  String get canViewAlertsDescription;

  /// No description provided for @canViewJobTypesDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to view job types.'**
  String get canViewJobTypesDescription;

  /// No description provided for @canViewSchedulesDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to view schedules.'**
  String get canViewSchedulesDescription;

  /// No description provided for @userpermissionCanViewNotesDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to view notes.'**
  String get userpermissionCanViewNotesDescription;

  /// No description provided for @subordinates.
  ///
  /// In en, this message translates to:
  /// **'Subordinates'**
  String get subordinates;

  /// No description provided for @supervisors.
  ///
  /// In en, this message translates to:
  /// **'Supervisors'**
  String get supervisors;

  /// No description provided for @configSettingDatabaseBackupFolder.
  ///
  /// In en, this message translates to:
  /// **'Database Backup Folder'**
  String get configSettingDatabaseBackupFolder;

  /// No description provided for @configSettingDatabaseBackupFolderDescription.
  ///
  /// In en, this message translates to:
  /// **'Location where the SQL database backups are stored.'**
  String get configSettingDatabaseBackupFolderDescription;

  /// No description provided for @cleaning.
  ///
  /// In en, this message translates to:
  /// **'Cleaning'**
  String get cleaning;

  /// No description provided for @cleaningDescription.
  ///
  /// In en, this message translates to:
  /// **'Cleaning related jobs'**
  String get cleaningDescription;

  /// No description provided for @notListed.
  ///
  /// In en, this message translates to:
  /// **'Not Listed'**
  String get notListed;

  /// No description provided for @notListedDescription.
  ///
  /// In en, this message translates to:
  /// **'Unlisted jobs'**
  String get notListedDescription;

  /// No description provided for @scheduled.
  ///
  /// In en, this message translates to:
  /// **'Scheduled'**
  String get scheduled;

  /// No description provided for @scheduledDescription.
  ///
  /// In en, this message translates to:
  /// **'Scheduled jobs'**
  String get scheduledDescription;

  /// No description provided for @status.
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// No description provided for @unscheduled.
  ///
  /// In en, this message translates to:
  /// **'Unscheduled'**
  String get unscheduled;

  /// No description provided for @unscheduledDescription.
  ///
  /// In en, this message translates to:
  /// **'Unscheduled jobs'**
  String get unscheduledDescription;

  /// No description provided for @mustCompleteChallenge.
  ///
  /// In en, this message translates to:
  /// **'You must complete the challenge!'**
  String get mustCompleteChallenge;

  /// No description provided for @lastMonth.
  ///
  /// In en, this message translates to:
  /// **'Last Month'**
  String get lastMonth;

  /// No description provided for @lastUpdated.
  ///
  /// In en, this message translates to:
  /// **'Last Updated'**
  String get lastUpdated;

  /// No description provided for @lastWeek.
  ///
  /// In en, this message translates to:
  /// **'Last Week'**
  String get lastWeek;

  /// No description provided for @thisMonth.
  ///
  /// In en, this message translates to:
  /// **'This Month'**
  String get thisMonth;

  /// No description provided for @thisWeek.
  ///
  /// In en, this message translates to:
  /// **'This Week'**
  String get thisWeek;

  /// No description provided for @today.
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// No description provided for @yesterday.
  ///
  /// In en, this message translates to:
  /// **'Yesterday'**
  String get yesterday;

  /// No description provided for @noLocation.
  ///
  /// In en, this message translates to:
  /// **'No Location'**
  String get noLocation;

  /// No description provided for @clock.
  ///
  /// In en, this message translates to:
  /// **'Clock'**
  String get clock;

  /// No description provided for @punchedOut.
  ///
  /// In en, this message translates to:
  /// **'Punched Out'**
  String get punchedOut;

  /// No description provided for @punchInAt.
  ///
  /// In en, this message translates to:
  /// **'Punch In at:'**
  String get punchInAt;

  /// No description provided for @date.
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// No description provided for @profile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// No description provided for @punches.
  ///
  /// In en, this message translates to:
  /// **'Punches'**
  String get punches;

  /// No description provided for @calendarDetail.
  ///
  /// In en, this message translates to:
  /// **'Calendar Detail'**
  String get calendarDetail;

  /// No description provided for @punchInOut.
  ///
  /// In en, this message translates to:
  /// **'Punch In/Out'**
  String get punchInOut;

  /// No description provided for @punchesDetail.
  ///
  /// In en, this message translates to:
  /// **'Punches Detail'**
  String get punchesDetail;

  /// No description provided for @confirmPunchOut.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to punch out?'**
  String get confirmPunchOut;

  /// No description provided for @writeNow.
  ///
  /// In en, this message translates to:
  /// **'Write Now...'**
  String get writeNow;

  /// No description provided for @configSettingRegistrationEmailExpirationMinutes.
  ///
  /// In en, this message translates to:
  /// **'Registration Email Expiration (Mins)'**
  String get configSettingRegistrationEmailExpirationMinutes;

  /// No description provided for @configSettingRegistrationEmailExpirationMinutesDescription.
  ///
  /// In en, this message translates to:
  /// **'Amount of time in minutes before an unresponded registration email will expire.'**
  String get configSettingRegistrationEmailExpirationMinutesDescription;

  /// No description provided for @registrationConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Registration Confirmation'**
  String get registrationConfirmation;

  /// No description provided for @confirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// No description provided for @registrationKeyRequired.
  ///
  /// In en, this message translates to:
  /// **'The Registration Key is required.'**
  String get registrationKeyRequired;

  /// No description provided for @registrationKey.
  ///
  /// In en, this message translates to:
  /// **'Registration Key'**
  String get registrationKey;

  /// No description provided for @registrationRequestExpired.
  ///
  /// In en, this message translates to:
  /// **'This registration request has expired and will need to be resent.'**
  String get registrationRequestExpired;

  /// No description provided for @registrationRequestNotFound.
  ///
  /// In en, this message translates to:
  /// **'This registration request could not be found. You will need to resubmit your registration request.'**
  String get registrationRequestNotFound;

  /// No description provided for @resendRegistrationRequest.
  ///
  /// In en, this message translates to:
  /// **'Resend Registration Request'**
  String get resendRegistrationRequest;

  /// No description provided for @viewPunchCardsByDay.
  ///
  /// In en, this message translates to:
  /// **'Punches By Day'**
  String get viewPunchCardsByDay;

  /// No description provided for @configSettingRegistrationConfirmationUrl.
  ///
  /// In en, this message translates to:
  /// **'Registration Confirmation URL'**
  String get configSettingRegistrationConfirmationUrl;

  /// No description provided for @configSettingRegistrationConfirmationUrlDescription.
  ///
  /// In en, this message translates to:
  /// **'The URL for confirming a new registration of an organization.'**
  String get configSettingRegistrationConfirmationUrlDescription;

  /// No description provided for @configSettingRegistrationUrl.
  ///
  /// In en, this message translates to:
  /// **'Registration URL'**
  String get configSettingRegistrationUrl;

  /// No description provided for @configSettingRegistrationUrlDescription.
  ///
  /// In en, this message translates to:
  /// **'The URL for registering a new organization.'**
  String get configSettingRegistrationUrlDescription;

  /// No description provided for @configSettingRegistrationEmail.
  ///
  /// In en, this message translates to:
  /// **'Registration Email'**
  String get configSettingRegistrationEmail;

  /// No description provided for @configSettingRegistrationEmailDescription.
  ///
  /// In en, this message translates to:
  /// **'The email template that gets sent when registering an organization.'**
  String get configSettingRegistrationEmailDescription;

  /// No description provided for @configSettingAdministrationUrl.
  ///
  /// In en, this message translates to:
  /// **'Administration Site URL'**
  String get configSettingAdministrationUrl;

  /// No description provided for @configSettingAdministrationUrlDescription.
  ///
  /// In en, this message translates to:
  /// **'The URL for the administration site.'**
  String get configSettingAdministrationUrlDescription;

  /// No description provided for @configSettingAndroidMobileDeviceUrl.
  ///
  /// In en, this message translates to:
  /// **'Android Mobile App URL'**
  String get configSettingAndroidMobileDeviceUrl;

  /// No description provided for @configSettingAndroidMobileDeviceUrlDescription.
  ///
  /// In en, this message translates to:
  /// **'The URL for the android mobile application.'**
  String get configSettingAndroidMobileDeviceUrlDescription;

  /// No description provided for @configSettingFirebaseMessagingConfiguration.
  ///
  /// In en, this message translates to:
  /// **'Firebase Messaging Configuration'**
  String get configSettingFirebaseMessagingConfiguration;

  /// No description provided for @configSettingFirebaseMessagingConfigurationDescription.
  ///
  /// In en, this message translates to:
  /// **'The configuration information for connecting to he Firebase service.'**
  String get configSettingFirebaseMessagingConfigurationDescription;

  /// No description provided for @configSettingRegistrationThankyouEmail.
  ///
  /// In en, this message translates to:
  /// **'Registration Thank You Email'**
  String get configSettingRegistrationThankyouEmail;

  /// No description provided for @configSettingRegistrationThankyouEmailDescription.
  ///
  /// In en, this message translates to:
  /// **'The email template that gets sent after you uccessfully register an organization.'**
  String get configSettingRegistrationThankyouEmailDescription;

  /// No description provided for @configSettingSmtpConfiguration.
  ///
  /// In en, this message translates to:
  /// **'SMTP Configuration'**
  String get configSettingSmtpConfiguration;

  /// No description provided for @configSettingSmtpConfigurationDescription.
  ///
  /// In en, this message translates to:
  /// **'The configuration information for sending emails via SMTP.'**
  String get configSettingSmtpConfigurationDescription;

  /// No description provided for @created.
  ///
  /// In en, this message translates to:
  /// **'Created'**
  String get created;

  /// No description provided for @deleted.
  ///
  /// In en, this message translates to:
  /// **'Deleted'**
  String get deleted;

  /// No description provided for @earlyPunchIn.
  ///
  /// In en, this message translates to:
  /// **'Early Punch In'**
  String get earlyPunchIn;

  /// No description provided for @earlyPunchInDescription.
  ///
  /// In en, this message translates to:
  /// **'Employee punched in early.'**
  String get earlyPunchInDescription;

  /// No description provided for @earlyPunchOut.
  ///
  /// In en, this message translates to:
  /// **'Early Punch Out'**
  String get earlyPunchOut;

  /// No description provided for @earlyPunchOutDescription.
  ///
  /// In en, this message translates to:
  /// **'Employee punched out early.'**
  String get earlyPunchOutDescription;

  /// No description provided for @latePunchIn.
  ///
  /// In en, this message translates to:
  /// **'Late Punch In'**
  String get latePunchIn;

  /// No description provided for @latePunchInDescription.
  ///
  /// In en, this message translates to:
  /// **'Employee punched in late.'**
  String get latePunchInDescription;

  /// No description provided for @latePunchOut.
  ///
  /// In en, this message translates to:
  /// **'Late Punch Out'**
  String get latePunchOut;

  /// No description provided for @latePunchOutDescription.
  ///
  /// In en, this message translates to:
  /// **'Employee punched out late.'**
  String get latePunchOutDescription;

  /// No description provided for @noPunchIn.
  ///
  /// In en, this message translates to:
  /// **'No Punch In'**
  String get noPunchIn;

  /// No description provided for @noPunchInDescription.
  ///
  /// In en, this message translates to:
  /// **'Employee did not punch in.'**
  String get noPunchInDescription;

  /// No description provided for @outsideGeofence.
  ///
  /// In en, this message translates to:
  /// **'Breached Geofence'**
  String get outsideGeofence;

  /// No description provided for @outsideGeofenceDescription.
  ///
  /// In en, this message translates to:
  /// **'Employee is clocked in to a location, but is outside the location\'s geofence.'**
  String get outsideGeofenceDescription;

  /// No description provided for @pushNotification.
  ///
  /// In en, this message translates to:
  /// **'Push Notification'**
  String get pushNotification;

  /// No description provided for @queued.
  ///
  /// In en, this message translates to:
  /// **'Queued'**
  String get queued;

  /// No description provided for @sent.
  ///
  /// In en, this message translates to:
  /// **'Sent'**
  String get sent;

  /// No description provided for @silentNotification.
  ///
  /// In en, this message translates to:
  /// **'Silent Notification'**
  String get silentNotification;

  /// No description provided for @selectAValue.
  ///
  /// In en, this message translates to:
  /// **'Select a value'**
  String get selectAValue;

  /// No description provided for @userpermissionCanViewEmployeePayRates.
  ///
  /// In en, this message translates to:
  /// **'Can View Employee Pay Rates'**
  String get userpermissionCanViewEmployeePayRates;

  /// No description provided for @userpermissionCanViewEmployeePayRatesDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to view employee pay rates.'**
  String get userpermissionCanViewEmployeePayRatesDescription;

  /// No description provided for @payRate.
  ///
  /// In en, this message translates to:
  /// **'Pay Rate'**
  String get payRate;

  /// No description provided for @userpermissionCanEditEmployeePayRates.
  ///
  /// In en, this message translates to:
  /// **'Can Edit Employee Pay Rates'**
  String get userpermissionCanEditEmployeePayRates;

  /// No description provided for @userpermissionCanEditEmployeePayRatesDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to edit employee pay rates.'**
  String get userpermissionCanEditEmployeePayRatesDescription;

  /// No description provided for @payRateMustBeGreaterThanZero.
  ///
  /// In en, this message translates to:
  /// **'The Pay Rate must be greater than zero.'**
  String get payRateMustBeGreaterThanZero;

  /// No description provided for @hourly.
  ///
  /// In en, this message translates to:
  /// **'Hourly'**
  String get hourly;

  /// No description provided for @weekly.
  ///
  /// In en, this message translates to:
  /// **'Weekly'**
  String get weekly;

  /// No description provided for @yearly.
  ///
  /// In en, this message translates to:
  /// **'Yearly'**
  String get yearly;

  /// No description provided for @configSettingFirstDayOfWeek.
  ///
  /// In en, this message translates to:
  /// **'First Day Of The Week'**
  String get configSettingFirstDayOfWeek;

  /// No description provided for @configSettingFirstDayOfWeekDescription.
  ///
  /// In en, this message translates to:
  /// **'The first day of the week for your organization.'**
  String get configSettingFirstDayOfWeekDescription;

  /// No description provided for @configSettingOvertimeHours.
  ///
  /// In en, this message translates to:
  /// **'Overtime Hours'**
  String get configSettingOvertimeHours;

  /// No description provided for @configSettingOvertimeHoursDescription.
  ///
  /// In en, this message translates to:
  /// **'The amount of hours before ovetime pay is applied.'**
  String get configSettingOvertimeHoursDescription;

  /// No description provided for @timeZoneRequired.
  ///
  /// In en, this message translates to:
  /// **'The Time Zone is required.'**
  String get timeZoneRequired;

  /// No description provided for @reportEmployeesHoursWorked.
  ///
  /// In en, this message translates to:
  /// **'Employees Hours Worked'**
  String get reportEmployeesHoursWorked;

  /// No description provided for @reportEmployeesHoursWorkedDescription.
  ///
  /// In en, this message translates to:
  /// **'Shows the hours worked for employees over a given week.'**
  String get reportEmployeesHoursWorkedDescription;

  /// No description provided for @reportLocationsHoursWorked.
  ///
  /// In en, this message translates to:
  /// **'Locations Hours Worked'**
  String get reportLocationsHoursWorked;

  /// No description provided for @reportLocationsHoursWorkedDescription.
  ///
  /// In en, this message translates to:
  /// **'Shows the hours worked at locations over a given week.'**
  String get reportLocationsHoursWorkedDescription;

  /// No description provided for @languageEnUs.
  ///
  /// In en, this message translates to:
  /// **'English (United States)'**
  String get languageEnUs;

  /// No description provided for @languageEsUs.
  ///
  /// In en, this message translates to:
  /// **'Spanish (United States)'**
  String get languageEsUs;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @languageRequired.
  ///
  /// In en, this message translates to:
  /// **'The Language is required.'**
  String get languageRequired;

  /// No description provided for @userpermissionCanEditPermissions.
  ///
  /// In en, this message translates to:
  /// **'Can Edit Permissions'**
  String get userpermissionCanEditPermissions;

  /// No description provided for @userpermissionCanEditPermissionsDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to edit employee and contact permissions.'**
  String get userpermissionCanEditPermissionsDescription;

  /// No description provided for @userpermissionCanEditReports.
  ///
  /// In en, this message translates to:
  /// **'Can Edit Reports'**
  String get userpermissionCanEditReports;

  /// No description provided for @userpermissionCanEditReportsDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to activate or deactivate reports.'**
  String get userpermissionCanEditReportsDescription;

  /// No description provided for @addPhoto.
  ///
  /// In en, this message translates to:
  /// **'Add Photo'**
  String get addPhoto;

  /// No description provided for @addTemplate.
  ///
  /// In en, this message translates to:
  /// **'Add Template'**
  String get addTemplate;

  /// No description provided for @agenda.
  ///
  /// In en, this message translates to:
  /// **'Agenda'**
  String get agenda;

  /// No description provided for @busy.
  ///
  /// In en, this message translates to:
  /// **'Busy'**
  String get busy;

  /// No description provided for @createNewInspection.
  ///
  /// In en, this message translates to:
  /// **'Create New Inspection'**
  String get createNewInspection;

  /// No description provided for @editInspection.
  ///
  /// In en, this message translates to:
  /// **'Edit Inspection'**
  String get editInspection;

  /// No description provided for @editInspectionItem.
  ///
  /// In en, this message translates to:
  /// **'Edit Inspection Item'**
  String get editInspectionItem;

  /// No description provided for @inspections.
  ///
  /// In en, this message translates to:
  /// **'Inspections'**
  String get inspections;

  /// No description provided for @inspectionTemplates.
  ///
  /// In en, this message translates to:
  /// **'Inspection Templates'**
  String get inspectionTemplates;

  /// No description provided for @addInspectionTemplates.
  ///
  /// In en, this message translates to:
  /// **'Add Inspection Templates'**
  String get addInspectionTemplates;

  /// No description provided for @editInspectionTemplates.
  ///
  /// In en, this message translates to:
  /// **'Edit Inspection Templates'**
  String get editInspectionTemplates;

  /// No description provided for @newChat.
  ///
  /// In en, this message translates to:
  /// **'New Chat'**
  String get newChat;

  /// No description provided for @newInspection.
  ///
  /// In en, this message translates to:
  /// **'New Inspection'**
  String get newInspection;

  /// No description provided for @viewInspections.
  ///
  /// In en, this message translates to:
  /// **'View Inspections'**
  String get viewInspections;

  /// No description provided for @addInspections.
  ///
  /// In en, this message translates to:
  /// **'Add Inspections'**
  String get addInspections;

  /// No description provided for @editInspections.
  ///
  /// In en, this message translates to:
  /// **'Edit Inspections'**
  String get editInspections;

  /// No description provided for @viewInspectionTemplates.
  ///
  /// In en, this message translates to:
  /// **'View Inspection Templates'**
  String get viewInspectionTemplates;

  /// No description provided for @noPunchCardsThisWeek.
  ///
  /// In en, this message translates to:
  /// **'No Punch Cards This Week'**
  String get noPunchCardsThisWeek;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @saveConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to save?'**
  String get saveConfirmation;

  /// No description provided for @sync.
  ///
  /// In en, this message translates to:
  /// **'Sync'**
  String get sync;

  /// No description provided for @takePhoto.
  ///
  /// In en, this message translates to:
  /// **'Take Photo'**
  String get takePhoto;

  /// No description provided for @userpermissionCanEditInspections.
  ///
  /// In en, this message translates to:
  /// **'Can Edit Inspections'**
  String get userpermissionCanEditInspections;

  /// No description provided for @userpermissionCanEditInspectionsDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to create and edit inspections and inspection templates.'**
  String get userpermissionCanEditInspectionsDescription;

  /// No description provided for @userpermissionCanViewInspections.
  ///
  /// In en, this message translates to:
  /// **'Can View Inspections'**
  String get userpermissionCanViewInspections;

  /// No description provided for @userpermissionCanViewInspectionsDescription.
  ///
  /// In en, this message translates to:
  /// **'Allows user to view inspections and inspection templates.'**
  String get userpermissionCanViewInspectionsDescription;

  /// No description provided for @forgotLogin.
  ///
  /// In en, this message translates to:
  /// **'Forgot Login or Password'**
  String get forgotLogin;

  /// No description provided for @emailRequired.
  ///
  /// In en, this message translates to:
  /// **'The Email Address is required.'**
  String get emailRequired;

  /// No description provided for @invalidEmail.
  ///
  /// In en, this message translates to:
  /// **'The Email Address must be a valid email format.'**
  String get invalidEmail;

  /// No description provided for @count.
  ///
  /// In en, this message translates to:
  /// **'Count'**
  String get count;

  /// No description provided for @name.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// No description provided for @payrollId.
  ///
  /// In en, this message translates to:
  /// **'Payroll Id'**
  String get payrollId;

  /// No description provided for @downloadPayroll.
  ///
  /// In en, this message translates to:
  /// **'Download Payroll'**
  String get downloadPayroll;

  /// No description provided for @hidePassword.
  ///
  /// In en, this message translates to:
  /// **'Hide Password'**
  String get hidePassword;

  /// No description provided for @showPassword.
  ///
  /// In en, this message translates to:
  /// **'Show Password'**
  String get showPassword;

  /// No description provided for @liveStatus.
  ///
  /// In en, this message translates to:
  /// **'Live Status'**
  String get liveStatus;

  /// No description provided for @accountInformation.
  ///
  /// In en, this message translates to:
  /// **'Account Information'**
  String get accountInformation;

  /// No description provided for @personalInformation.
  ///
  /// In en, this message translates to:
  /// **'Personal Information'**
  String get personalInformation;

  /// No description provided for @selectLanguage.
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// No description provided for @additionalAddressInformation.
  ///
  /// In en, this message translates to:
  /// **'Additional Address Information'**
  String get additionalAddressInformation;

  /// No description provided for @phoneRequired.
  ///
  /// In en, this message translates to:
  /// **'Phone required.'**
  String get phoneRequired;

  /// No description provided for @invalidPhone.
  ///
  /// In en, this message translates to:
  /// **'Invalid phone number.'**
  String get invalidPhone;

  /// No description provided for @address1Required.
  ///
  /// In en, this message translates to:
  /// **'Address 1 is required.'**
  String get address1Required;

  /// No description provided for @invalidAddress.
  ///
  /// In en, this message translates to:
  /// **'Invalid address.'**
  String get invalidAddress;

  /// No description provided for @cityRequired.
  ///
  /// In en, this message translates to:
  /// **'City is required.'**
  String get cityRequired;

  /// No description provided for @stateRequired.
  ///
  /// In en, this message translates to:
  /// **'State is required.'**
  String get stateRequired;

  /// No description provided for @zipRequired.
  ///
  /// In en, this message translates to:
  /// **'Zip code is required.'**
  String get zipRequired;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'es', 'fr'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
    case 'es': return AppLocalizationsEs();
    case 'fr': return AppLocalizationsFr();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
