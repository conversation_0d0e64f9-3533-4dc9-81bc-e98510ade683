import '../base_data.dart';
import '../data/language.dart';
import '../hive_db.dart';

class LanguageModel extends BaseDataModel<Language> {
  @override
  Future<Iterable<Language>> get all async => (await HiveDb.database).languages.values;

  @override
  Future<void> save(Iterable<Language> t) async {
    await (await HiveDb.database).languages.putAll({for (final e in t) e.id: e});
    notifyListeners();
  }
}
