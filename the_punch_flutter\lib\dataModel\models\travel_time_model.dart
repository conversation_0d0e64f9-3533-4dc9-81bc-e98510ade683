import 'package:hive/hive.dart';
import '../../api/api_model.dart';
import '../../api/requests/fetch_travel_data_request.dart';

class TravelTime {
  final String id;
  final String originLocationId;
  final String destinationLocationId;
  final String encodedPolyline;
  final double miles;

  TravelTime({
    required this.id,
    required this.originLocationId,
    required this.destinationLocationId,
    required this.encodedPolyline,
    required this.miles,
  });
}

class TravelTimesModel {
  static const String _travelTimesBoxName = "travelTimes"; 

  /// Return all known travel times (still from Hive if you want it for offline use).
  Future<List<TravelTime>> get all async {
    final box = await Hive.openBox<TravelTime>(_travelTimesBoxName);
    return box.values.toList();
  }

  /// Use the API to fetch a single TravelTime row by origin/destination.
  /// You mentioned “Optionally also check reversed,” so here’s an example
  /// that tries the direct route first, and if that fails or returns null,
  /// attempts the reverse. If both fail, returns null.
  Future<GetTravelDataResponse?> getOneByOriginAndDestination(
    String origin,
    String destination,
  ) async {
    // 1) Call the .NET endpoint via ApiModel
    final firstAttempt = await ApiModel().fetchTravelData(
      originLocationId: origin,
      destinationLocationId: destination,
    );

    if(firstAttempt != null){
      return firstAttempt;
    }
    


    // 3) If neither call returned a valid encoded polyline, return null.
    return null;
  }

  /// Insert or update a TravelTime in Hive if you still want to store them locally.
  /// (This method is optional, but you can keep it for caching or offline usage.)
  Future<void> save(TravelTime travelTime) async {
    final box = await Hive.openBox<TravelTime>(_travelTimesBoxName);
    await box.put(travelTime.id, travelTime);
  }

  // etc. More CRUD methods as needed...
}
