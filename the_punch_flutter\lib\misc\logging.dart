import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import '../api/api_model.dart';
import 'my_platform.dart';

Future<void> logException(Exception e, StackTrace stack) async {
  if (MyPlatform.isAndroid || MyPlatform.isIOS) await FirebaseCrashlytics.instance.recordError(e, stack);
}

Future<void> logApiException(ApiException e, StackTrace stack) async {
  if (MyPlatform.isAndroid || MyPlatform.isIOS) await FirebaseCrashlytics.instance.recordError(e, stack, reason: e.errorCode);
}
