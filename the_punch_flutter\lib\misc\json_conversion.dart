// ignore_for_file: avoid_dynamic_calls

DateTime dateTimeFromJson(value) {
  final dateString = value as String;
  if (dateString.endsWith('Z')) {
    return DateTime.parse(dateString);
  } else {
    return DateTime.parse('${dateString}Z');
  }
}

String dateTimeToJson(value) => (value as DateTime).toUtc().toIso8601String();

DateTime? nullableDateTimeFromJson(value) {
  try {
    if (value == null) return null;
    final dateString = value as String;
    if (dateString.endsWith('Z')) {
      return DateTime.parse(dateString);
    } else {
      return DateTime.parse('${dateString}Z');
    }
  } catch (e) {
    return null;
  }
}

String? nullableDateTimeToJson(value) {
  try {
    if (value == null) return null;
    return (value as DateTime).toUtc().toIso8601String();
  } catch (e) {
    return null;
  }
}

Duration? nullableDurationFromJson(value) {
  try {
    if (value == null) return null;
    final split = (value as String).split(':');
    final hours = int.parse(split[0]);
    final minutes = int.parse(split[1]);
    final seconds = int.parse(split[2].substring(0, 2));
    return Duration(hours: hours, minutes: minutes, seconds: seconds);
  } catch (e) {
    return null;
  }
}

String? nullableDurationToJson(value) {
  try {
    if (value == null) return null;
    final hours = value.inHours;
    final minutes = value.inMinutes % 60;
    final seconds = value.inSeconds % 60;
    return '$hours:$minutes:$seconds';
  } catch (e) {
    return null;
  }
}

Duration durationFromJson(value) {
  final split = (value as String).split(':');
  final hours = int.parse(split[0]);
  final minutes = int.parse(split[1]);
  final seconds = int.parse(split[2].substring(0, 2));
  return Duration(hours: hours, minutes: minutes, seconds: seconds);
}

String? durationToJson(value) {
  if (value == null) return null;
  try {
    final duration = value as Duration;
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;
    return '$hours:$minutes:$seconds';
  } catch (e) {
    return null;
  }
}

String intIdFromJson(value) {
  final id = value as int;
  if (id == 0) return '';
  return id.toString();
}

int intIdToJson(value) {
  final id = value as String;
  if (id.isEmpty) return 0;
  return int.parse(id);
}

String? nullableIntIdFromJson(value) {
  if (value == null) return null;
  final id = value as int?;
  if (id == 0) return '';
  return id.toString();
}

int? nullableIntIdToJson(value) {
  if (value == null) return null;
  final id = value as String;
  return int.parse(id);
}

String idFromJson(value) {
  final id = value as String;
  if (id == '00000000-0000-0000-0000-000000000000') return '';
  return id.toLowerCase();
}

String? nullableIdFromJson(value) {
  if (value == null) return null;
  final id = value as String;
  if (id == '00000000-0000-0000-0000-000000000000') return '';
  return id.toLowerCase();
}
