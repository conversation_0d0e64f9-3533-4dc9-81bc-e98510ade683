buildscript {
    ext.kotlin_version = '1.7.10'  // Updated to a more recent version

    ext {
        compileSdkVersion   = 34  // Updated to 34
        targetSdkVersion    = 34  // Updated to 34
        minSdkVersion       = 21  // Keep as 21
        appCompatVersion    = "1.4.2"  // Keep as is
        playServicesLocationVersion = "21.0.1"  // Keep as is
    }

    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.4'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        // [required] flutter_background_geolocation
        maven { url "${project(':flutter_background_geolocation').projectDir}/libs" }
        maven { url 'https://developer.huawei.com/repo/' }
        // [required] background_fetch
        maven { url "${project(':background_fetch').projectDir}/libs" }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
