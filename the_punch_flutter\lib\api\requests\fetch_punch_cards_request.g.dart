// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fetch_punch_cards_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FetchPunchCardsRequest _$FetchPunchCardsRequestFromJson(
        Map<String, dynamic> json) =>
    FetchPunchCardsRequest(
      startTime: DateTime.parse(json['StartTime'] as String),
      endTime: DateTime.parse(json['EndTime'] as String),
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$FetchPunchCardsRequestToJson(
        FetchPunchCardsRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'StartTime': instance.startTime.toIso8601String(),
      'EndTime': instance.endTime.toIso8601String(),
    };

FetchPunchCardsResponse _$FetchPunchCardsResponseFromJson(
        Map<String, dynamic> json) =>
    FetchPunchCardsResponse(
      punchCards: (json['PunchCards'] as List<dynamic>)
          .map((e) => PunchCard.fromJson(e as Map<String, dynamic>))
          .toList(),
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$FetchPunchCardsResponseToJson(
    FetchPunchCardsResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['PunchCards'] = instance.punchCards;
  return val;
}
