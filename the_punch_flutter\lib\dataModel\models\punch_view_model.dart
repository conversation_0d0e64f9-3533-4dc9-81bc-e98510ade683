import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../api/sync_model.dart';
import '../../misc/extensions.dart';
import '../../pages/view_model_mixin.dart';
import '../../services/notification_service.dart';
import '../../state/location_state.dart';
import '../../state/login_state.dart';
import '../../state/punch_state.dart';
import '../../state/server_time_state.dart';
import '../data/job_type.dart';
import '../data/location.dart';
import '../data/location_notes.dart';
import '../data/punch_card.dart';
import '../data/schedule.dart';
import '../data/user_type.dart';
import '../data_model.dart';
import 'package:flutter/material.dart';

import 'user_model.dart';

class PunchViewModel extends ChangeNotifier with ViewModelMixin {
  PunchCard? punchCard;
  bool anyNotes = false;
  bool anyContacts = false;
  String? locationId;
  Location? location;
  Schedule? schedule;
  JobType? jobType;
  List<LocationNote> locationNotes = [];
  bool isLoading = false;
  bool isPunchingIn = false;
  Schedule? nearbySchedule;
  Location? nearbyScheduleLocation;
  //String? jobTypeName = '';
  bool prePageLoad = true;
  bool _disposed = false;
  LocationPermission? permissionStatus;

  PunchViewModel() {
    addListenables([
      DataModel().noteModel,
      DataModel().locationModel,
      DataModel().punchCardModel,
      DataModel().scheduleModel,
      DataModel().jobTypeModel,
    ]);
  }

  @override
  void dispose() {
    _disposed = true;
    super.dispose();
  }

  void _notifyListeners() {
    if (!_disposed) {
      notifyListeners();
    }
  }

  void clearLocationNotes() {
      locationNotes.clear();
      notifyListeners();
  }
  
  Future<bool> isLiveTaskActive() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('liveID') != null;
  }

  Future<void> endLiveTask() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('liveID');
    currentTask = null;
    currentTaskLocationName = null;
    notifyListeners();
  }  

  // void updateAfterTaskEnded() {
  //   isTaskActive = false;
  //   notifyListeners();
  // }
  
  String? currentTask;
  String? currentTaskLocationName;

  Future<void> initializeData() async {
    // Mark data load as complete
    //SharedPreferences prefs = await SharedPreferences.getInstance();
    //isTaskActive = prefs.getBool('isTaskActive') ?? false;
    permissionStatus = await LocationState.checkAndRequestPermissions();
    notifyListeners();
    //jobTypeName = '';
    //await LocationState.updateCurrentLocation();
    //print('got location!');
    if (!_disposed) {
      await refresh();
    }
  }

  Future<void> clearScheduleTimer() async {
    schedule = null;
    _notifyListeners();
  }

  Future<void> handlePunchIn(isScheduled, locationId, scheduleId) async {
    prePageLoad = false;
    setLoading(true);
    setPunchingIn(true);
    await clearScheduleTimer();

    final prefs = await SharedPreferences.getInstance();
    final existing = prefs.getString('punchCardSessionId');
    if (existing == null) {
      // Set it to the "currentAppSessionId" from main()
      final current = prefs.getString('currentAppSessionId');
      if (current != null) {
        prefs.setString('punchCardSessionId', current);
      }
    }



    if (isScheduled) {
      await PunchState().schedulePunchIn(locationId: locationId, scheduleId: scheduleId);
    } else {
      final employeeId = LoginState.userId;
      final userTypeId = await UserModel().getCurrentUserTypeId(employeeId);
      final isManager = UserType.managerId.toUpperCase() == userTypeId?.toUpperCase() || UserType.administratorId.toUpperCase() == userTypeId?.toUpperCase();

      final jobTypeId = isManager ? JobType.trackingId : JobType.unscheduledId;

      await PunchState().locationPunchIn(locationId: locationId, jobTypeId: jobTypeId);
    }

    await Future.delayed(Duration(seconds: 1));
    if (!_disposed) {
      checkPunchInState();
    }
  }

  Future<void> checkPunchInState() async {
    var punchInCheck = await PunchState().isPunchedIn;
    if (punchInCheck) {
      setLoading(false);
      setPunchingIn(false);
      await Future.delayed(Duration(seconds: 1));

      await NotificationService.showNotification(
        title: "Punch In",
        body: "You have Punched In!",
      );
    } else {
      await Future.delayed(Duration(seconds: 1));
      if (!_disposed) {
        checkPunchInState();
      }
    }
  }

  Future<void> handlePunchOut() async {
    setLoading(true);
    await clearScheduleTimer();
    clearLocationNotes();
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('punchCardSessionId');
    await PunchState().punchOut(false);
    prePageLoad = false;
    await Future.delayed(Duration(seconds: 2));
    if (!_disposed) {
      await NotificationService.showNotification(
        title: "Punch Out",
        body: "You have Punched Out! Thank you!",
      );
      setLoading(false);
    }
  }

  void setLoading(bool value) {
    isLoading = value;
    _notifyListeners();
  }

  void setPunchingIn(bool value) {
    isPunchingIn = value;
    _notifyListeners();
  }

  // void setJobTypeName(String name) {
  //   jobTypeName = name;
  //   _notifyListeners();
  // }

  @override
  Future<void> refresh() async {
    prePageLoad = true;
    punchCard = PunchState().punchCard;

    // print(punchCard);
    // print(PunchState().isPunchedIn);
    // print(jobTypeName);

    // if (jobTypeName == '') {
    //   final jobTypeId = await PunchState().getJobTypeId();
    //   if (jobTypeId != null) {
    //     jobTypeName = DataModel().jobTypeModel.getJobTypeName(jobTypeId.toUpperCase());
    //   } else {
    //     jobTypeName = 'Unknown';
    //   }
    // }

    locationId = punchCard?.locationId;
    if (punchCard?.scheduleId != null) {
      schedule =
          await DataModel().scheduleModel.getById(punchCard!.scheduleId!);
      locationId = schedule?.locationId;
    }

    if (locationId != null) {
      anyNotes = await DataModel().noteModel.isAnyByLocationId(locationId!);
      anyContacts = await DataModel()
          .locationModel
          .isAnyContactsByLocationId(locationId!);
      location = await DataModel().locationModel.getById(locationId!);
      print(punchCard!.jobTypeId);
      if(punchCard!.jobTypeId != JobType.trackingId){
        locationNotes = await DataModel().locationNoteModel.getByLocationIds([locationId!]);
      } else {
        locationNotes = [];
      }
      //locationNotes = await DataModel().locationNoteModel.getByLocationIds([locationId!]);
    }

    final employeeId = LoginState.userId;
    final start = DateTime.now().dateOnly;
    final end = start.addDays(1);
    final nearbySchedules = (await DataModel()
            .scheduleModel
            .getBetweenByEmployeeId(employeeId, start, end))
        .toList();

    nearbySchedules.removeWhere((e) =>
        e.startDateUtc.difference(ServerTimeState().utcTime).inMinutes.abs() >
        60);

    final scheduleIds = nearbySchedules.map((e) => e.id);
    final punchedScheduleIds =
        (await DataModel().punchCardModel.getByScheduleIds(scheduleIds))
            .map((e) => e.scheduleId);
    nearbySchedules
        .removeWhere((element) => punchedScheduleIds.contains(element.id));

    nearbySchedules.sort((a, b) {
      final aStart = a.startDateUtc.difference(ServerTimeState().utcTime).abs();
      final bStart = b.startDateUtc.difference(ServerTimeState().utcTime).abs();
      return aStart.compareTo(bStart);
    });

    nearbySchedule = nearbySchedules.firstOrNull;

    if (nearbySchedule != null) {
      nearbyScheduleLocation =
          await DataModel().locationModel.getById(nearbySchedule!.locationId);
    }
    await Future.delayed(Duration(seconds: 1));
    print(permissionStatus);
    if (permissionStatus == LocationPermission.always) {
      prePageLoad = false;
    } else {
      prePageLoad = true;
    }
    ;
    //await LoginState.instance;
    _notifyListeners();
  }
}
