import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';
import 'package:collection/collection.dart'; // To use firstWhereOrNull
import '../../../dataModel/data/permission.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data/user_type.dart';
import '../../../dataModel/data/user_type_permission.dart';
import '../../../dataModel/data_model.dart';
import '../../../dialogs/permissions_dialog.dart';
import '../../../dialogs/remove_dialog.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/app_localization.dart';
import '../../../widgets/active_toggle.dart';
import '../../../widgets/decorated_text_field.dart';
import '../../view_model_mixin.dart';
import '../my_scaffold.dart';

// Assuming ColorHelper is defined somewhere in your project.
// If not, you need to define it accordingly.
// Example:
// class ColorHelper {
//   static const Color ThePunchRed = Color(0xFFE53935); // Replace with actual color code
// }

class EditEmployeeTypePage extends StatelessWidget {
  final String userTypeId;
  final String anything;

  EditEmployeeTypePage(Map<String, String> queryParms, {super.key})
      : userTypeId = queryParms['id'] ?? '',
        anything = queryParms['anything'] ?? '';

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
        create: (context) => _ViewModel(userTypeId),
        child: MyScaffold(
          title: anything == '1'
              ? AppLocalization.of(context)!.addEmployeeType
              : AppLocalization.of(context)!.editEmployeeType,
          body: _Body(),
        ),
      );
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1300),
          child: FocusScope(
            child: Column(
              children: [
                _BodyHeader(),
                _ResponsiveRow1(),
                _Row2(),
                Expanded(
                  child: _PermissionsTab(),
                ),
              ],
            ),
          ),
        ),
      );
}

class _BodyHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final headline6 = Theme.of(context)
        .textTheme
        .titleLarge
        ?.copyWith(fontWeight: FontWeight.bold, color: Colors.white);

    return IntrinsicHeight(
      child: Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          final userType = viewModel.userType;
          if (userType == null) return Container();
          final title = userType.name.isEmpty
              ? AppLocalization.of(context).employeeType
              : userType.name;
          return Container(
            // Use a direct reference to a solid red color.
            color: ColorHelper.thePunchRed(),
            child: Stack(
              children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Text(title, style: headline6),
                  ),
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: ActiveSwitch(
                            value: userType.isActive,
                            onChanged: (value) => viewModel.setActive(value),
                            enableDialog: false,
                          ),
                        ),
                        ElevatedButton(
                          onPressed: viewModel.permissions.isEmpty
                              ? null
                              : () async {
                                  await viewModel.save();
                                  Navigator.of(context).pop();
                                },
                          child: Text(AppLocalization.of(context).save),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}


class _ResponsiveRow1 extends StatelessWidget {
  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, constraints) {
          // Check if the screen width is less than 600 pixels (typically mobile)
          final isMobile = constraints.maxWidth < 600;
          return IntrinsicHeight(
            child: Consumer<_ViewModel>(
              builder: (context, viewModel, child) {
                final userType = viewModel.userType;
                if (userType == null) return Container();
                return Flex(
                  direction: isMobile ? Axis.vertical : Axis.horizontal,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Flexible(
                      flex: isMobile ? 0 : 30,
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: DecoratedTextField(
                          initialValue: userType.name,
                          labelText:
                              AppLocalization.of(context).employeeTypeName,
                          validator: (value) => value!.isEmpty
                              ? AppLocalization.of(context)
                                  .employeeTypeNameRequired
                              : null,
                          autofocus: true,
                          onChanged: (value) => viewModel.setName(value),
                        ),
                      ),
                    ),
                    Flexible(
                      flex: isMobile ? 0 : 40,
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: DecoratedTextField(
                          initialValue: userType.description,
                          labelText: AppLocalization.of(context).description,
                          autofocus: true,
                          onChanged: (value) => viewModel.setDescription(value),
                        ),
                      ),
                    ),
                    
                  ],
                );
              },
            ),
          );
        },
      );
}

class _Row2 extends StatelessWidget {
  @override
  Widget build(BuildContext context) => IntrinsicHeight(
        child: Consumer<_ViewModel>(
          builder: (context, viewModel, child) => SizedBox(
            width: 400,
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: ElevatedButton.icon(
                onPressed: () => unawaited(showDialog(
                  context: context,
                  builder: (context) => PermissionsDialog(
                    ignorePermissionKeys:
                        viewModel.permissions.map((e) => e.key).toList(),
                    selected: (key) async {
                      // Close the dialog after selection
                      Navigator.pop(context);
                      await viewModel.addPermission(key);
                    },
                  ),
                )),
                icon: const Icon(Icons.add_circle),
                label: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Text(
                      AppLocalization.of(context).addPermissionToEmployeeType),
                ),
              ),
            ),
          ),
        ),
      );
}

class _PermissionsTab extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final viewModel = context.watch<_ViewModel>();

    return LayoutBuilder(
      builder: (context, constraints) {
        // Define a breakpoint for mobile devices
        const mobileBreakpoint = 600.0;
        final isMobile = constraints.maxWidth < mobileBreakpoint;

        if (isMobile) {
          // Mobile-friendly view using ListView and Cards
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: viewModel.filteredPermissions.isEmpty
                ? Center(
                    child: Text(
                      AppLocalization.of(context)
                          .permissionNotFoundInEmployeeType,
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  )
                : ListView.builder(
                    itemCount: viewModel.filteredPermissions.length,
                    itemBuilder: (context, index) {
                      final permission = viewModel.filteredPermissions[index];
                      return Card(
                        margin: const EdgeInsets.symmetric(vertical: 4.0),
                        child: ListTile(
                          title: Text(
                            permission.name,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          subtitle: Text(permission.description),
                          trailing: IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            tooltip: AppLocalization.of(context).remove,
                            onPressed: () => showDialog(
                              context: context,
                              builder: (context) => RemoveDialog(
                                remove: () {
                                  viewModel.removePermission(permission.key);
                                  Navigator.of(context)
                                      .pop(); // Close the dialog
                                  // Show a snackbar for confirmation
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(AppLocalization.of(context)
                                          .noPermission),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
          );
        } else {
          // Desktop/tablet view using DataTable
          return SingleChildScrollView(
            scrollDirection: Axis.vertical,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                sortColumnIndex: viewModel.permissionsSortColumn,
                sortAscending: viewModel.isPermissionsAscending,
                headingRowColor: MaterialStateProperty.resolveWith<Color?>(
                  // ignore: prefer_expression_function_bodies
                  (Set<MaterialState> states) {
                    return ColorHelper.thePunchRed(); // Red header
                  },
                ),
                columns: [
                  DataColumn(
                    label: Text(
                      AppLocalization.of(context).permission,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.white, // White bold text
                      ),
                    ),
                    onSort: viewModel.onSortPermissions,
                  ),
                  DataColumn(
                    label: Text(
                      AppLocalization.of(context).description,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.white, // White bold text
                      ),
                    ),
                    onSort: viewModel.onSortPermissions,
                  ),
                  DataColumn(
                    label: Text(
                      AppLocalization.of(context).actions,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.white, // White bold text
                      ),
                    ),
                  ),
                ],
                rows:
                    viewModel.filteredPermissions.asMap().entries.map((entry) {
                  int index = entry.key;
                  Permission permission = entry.value;
                  return DataRow(
                    color: MaterialStateProperty.resolveWith<Color?>(
                      (Set<MaterialState> states) => index % 2 == 0
                          ? Colors.grey.withOpacity(0.1)
                          : Colors.white,
                    ),
                    cells: [
                      DataCell(Text(permission.name)),
                      DataCell(Text(permission.description)),
                      DataCell(
                        IconButton(
                          icon: const Icon(Icons.delete, color: Colors.red),
                          tooltip: AppLocalization.of(context).remove,
                          onPressed: () => showDialog(
                            context: context,
                            builder: (context) => RemoveDialog(
                              remove: () {
                                viewModel.removePermission(permission.key);
                                Navigator.of(context).pop(); // Close the dialog
                                // Show a snackbar for confirmation
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(AppLocalization.of(context)
                                        .noPermission),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
          );
        }
      },
    );
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String userTypeId;
  UserType? userType;

  List<UserTypePermission> userTypePermissions = [];
  List<Permission> permissions = []; // Permissions added to the employee type
  List<Permission> filteredPermissions = []; // Start empty

  int permissionsSortColumn = 0;
  bool isPermissionsAscending = true;

  List<User> employees = [];
  int employeesSortColumn = 0;
  bool isEmployeesAscending = true;

  _ViewModel(this.userTypeId) {
    addListenables([
      DataModel().userTypeModel,
      DataModel().userModel,
      DataModel().userTypePermissionModel,
      DataModel().permissionModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    if (userTypeId.isEmpty) {
      // Creating a new employee type
      userType = UserType.create();
      userTypePermissions = [];
      permissions = [];
      filteredPermissions = []; // Ensure table starts empty
    } else {
      // Editing an existing employee type
      userType = await DataModel().userTypeModel.getById(userTypeId) ??
          UserType.blank();
      userTypePermissions = (await DataModel()
              .userTypePermissionModel
              .getByUserTypeId(userType!.id))
          .toList();
      permissions = (await DataModel()
              .permissionModel
              .getByIds(userTypePermissions.map((e) => e.permissionId)))
          .toList();
      filteredPermissions = List.from(permissions); // Show current permissions
    }

    _sortPermissions();
    _sortEmployees();

    notifyListeners();
  }

  void onSortPermissions(int columnIndex, bool ascending) {
    if (permissionsSortColumn == columnIndex) {
      isPermissionsAscending = ascending;
    } else {
      permissionsSortColumn = columnIndex;
      isPermissionsAscending = true;
    }
    _sortPermissions();
    notifyListeners();
  }

  void _sortPermissions() {
    if (isPermissionsAscending) {
      switch (permissionsSortColumn) {
        case 0:
          permissions.sort((a, b) => a.name.compareTo(b.name));
          break;
        case 1:
          permissions.sort((a, b) => a.description.compareTo(b.description));
          break;
      }
    } else {
      switch (permissionsSortColumn) {
        case 0:
          permissions.sort((b, a) => a.name.compareTo(b.name));
          break;
        case 1:
          permissions.sort((b, a) => a.description.compareTo(b.description));
          break;
      }
    }
    // Update the filteredPermissions after sorting
    filteredPermissions = List.from(permissions);
  }

  void _sortEmployees() {
    if (isEmployeesAscending) {
      switch (employeesSortColumn) {
        case 0:
          employees.sort((a, b) => a.name.compareTo(b.name));
          break;
        case 1:
          employees.sort(
              (a, b) => a.isActive.toString().compareTo(b.isActive.toString()));
          break;
      }
    } else {
      switch (employeesSortColumn) {
        case 0:
          employees.sort((b, a) => a.name.compareTo(b.name));
          break;
        case 1:
          employees.sort(
              (b, a) => a.isActive.toString().compareTo(b.isActive.toString()));
          break;
      }
    }
  }

  void setName(String value) {
    if (userType == null) return;
    userType!.name = value;
    userType!.isDirty = true;
    notifyListeners();
  }

  void setDescription(String value) {
    if (userType == null) return;
    userType!.description = value;
    userType!.isDirty = true;
    notifyListeners();
  }

  void setActive(bool value) {
    if (userType == null) return;
    userType!.isActive = value;
    userType!.isDirty = true;
    notifyListeners();
  }

  Future<void> addPermission(String key) async {
    final permission = await DataModel().permissionModel.getByKey(key);
    if (permission == null || userType == null) return;

    // Check if the UserTypePermission already exists
    var userTypePermission = userTypePermissions
        .firstWhereOrNull((e) => e.permissionId == permission.id);

    if (userTypePermission != null) {
      // If it exists and is inactive, set it to active
      if (!userTypePermission.isActive) {
        userTypePermission.isActive = true;
        userTypePermission.isDirty = true;
      }
    } else {
      // If it doesn't exist, create a new UserTypePermission
      userTypePermission = UserTypePermission.create()
        ..id = const Uuid().v4()
        ..userTypeId = userType!.id
        ..permissionId = permission.id
        ..isActive = true
        ..isDirty = true;
      userTypePermissions.add(userTypePermission);
    }

    permissions.add(permission);
    _sortPermissions(); // Ensure sorted list after adding
    filteredPermissions =
        List.from(permissions); // Update to display newly added permissions
    userType!.isDirty = true;

    notifyListeners();
  }

  Future<void> removePermission(String key) async {
    final permission = await DataModel().permissionModel.getByKey(key);
    if (permission == null || userType == null) return;

    // Find the UserTypePermission and deactivate it
    var userTypePermission = userTypePermissions
        .firstWhereOrNull((e) => e.permissionId == permission.id);

    if (userTypePermission != null) {
      userTypePermission.isActive = false;
      userTypePermission.isDirty = true;
    }

    permissions.removeWhere((e) => e.id == permission.id);
    filteredPermissions = List.from(permissions); // Reflect removal in table
    userType!.isDirty = true;

    notifyListeners();
  }

  Future<void> save() async {
    if (userType == null) return;

    disableListenables();

    try {
      final dirtyUserTypePermissions =
          userTypePermissions.where((e) => e.isDirty);
      if (dirtyUserTypePermissions.isNotEmpty) {
        await DataModel()
            .userTypePermissionModel
            .saveDirty(dirtyUserTypePermissions);
      }
      if (userType!.isDirty) {
        await DataModel().userTypeModel.saveDirty([userType!]);
      }
      // Optional: Show a snackbar to indicate successful save
      // Note: Since ViewModel doesn't have direct access to BuildContext, consider using a callback or another method if needed
    } catch (e) {
      // Optional: Handle errors (e.g., show a snackbar)
    } finally {
      enableListenables();
      notifyListeners();
    }
  }

  Future<void> removeEmployeeType() async {
    if (userType == null) return;
    await DataModel().userTypeModel.deleteUserType(userType!.id);
    userType = null; // Clear the deleted userType
    notifyListeners();
  }
}
