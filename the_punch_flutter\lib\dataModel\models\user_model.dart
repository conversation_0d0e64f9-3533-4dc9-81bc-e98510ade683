import 'dart:async';

import '../data/user.dart';
import '../base_data.dart';
import '../hive_db.dart';

class UserModel extends BaseDataModel<User> {
  @override
  Future<Iterable<User>> get all async => (await HiveDb.database).users.values;

  Future<List<User>> getAllUsers() async {
    return (await all).toList();
  }

  @override
  Future<void> save(Iterable<User> t) async {
    await (await HiveDb.database).users.putAll({for (final e in t) e.id: e});
    notifyListeners();
  }

  Future<Iterable<User>> get allEmployees async =>
      (await all).where((e) => !e.isContact);
  Future<Iterable<User>> get allContacts async =>
      (await all).where((e) => e.isContact);
  Future<Iterable<User>> get activeEmployees async =>
      (await active).where((e) => !e.isContact);
  Future<Iterable<User>> get activeContacts async =>
      (await active).where((e) => e.isContact);

  Future<Iterable<User>> getByUserTypeId(String id) async =>
      (await all).where((e) => e.userTypeId == id);

  Future<Iterable<User>> getContactsByLocationId(String locationId) async {
    final userIds = (await HiveDb.database)
        .locationContacts
        .values
        .where((e) => e.locationId == locationId && e.isActive)
        .map((e) => e.userId);
    return (await HiveDb.database)
        .users
        .values
        .where((e) => userIds.contains(e.id));
  }

  Future<Iterable<String>> getMyLocations(String userId) async =>
      (await HiveDb.database)
          .locationContacts
          .values
          .where((e) => e.userId == userId && e.isActive)
          .map((e) => e.locationId);

  Future<Iterable<User>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }

  // Method to fetch current user's type ID
  Future<String?> getCurrentUserTypeId(String userId) async {
    var currentUser = await getCurrentUser(userId); // Updated to accept userId
    return currentUser?.userTypeId;
  }

  Future<User?> getCurrentUser(String userId) async {
    // Updated method to use the provided userId directly
    return (await HiveDb.database).users.get(userId);
  }

  // Helper function to get user's full name by userId
  Future<String> getFullName(String userId) async {
    final user = await getCurrentUser(userId);
    if (user == null) {
      return 'Unknown';
    }
    return '${user.firstName} ${user.lastName}';
  }
}

