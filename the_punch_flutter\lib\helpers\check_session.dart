import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import '../api/api_model.dart';
import '../state/login_state.dart';
import '../state/app_state.dart'; // <— so we can read AppState.appVersion

Future<bool> checkSession(BuildContext context) async {
  debugPrint('checkSession started.');
  try {
    // Retrieve shared preferences
    final prefs = await SharedPreferences.getInstance();
    debugPrint('SharedPreferences instance obtained.');

    // Check if we have a sessionId stored
    final sessionId = prefs.getString('sessionId') ?? '';
    debugPrint('sessionId from SharedPreferences: $sessionId');

    if (sessionId.isEmpty) {
      debugPrint('No sessionId found. No session to check.');
      return true;
    }
    debugPrint('Found sessionId. Checking session validity with server...');

    // 1) Check the session with your server
    final sessionCheck = await ApiModel().checkSession();
    debugPrint('Session check result: $sessionCheck');
    debugPrint(sessionCheck!.errorCode);
    debugPrint(sessionCheck!.errorMessage);
    if (sessionCheck != null && sessionCheck.errorCode == 'SESSION_FORCED_OUT') {
      debugPrint('Session forced out by the server. Logging user out...');
      final loginState = await LoginState.instance;
      await loginState.logout(forcedOut: true);

      if (context.mounted) {
        debugPrint('Showing forced-out dialog.');
        await showDialog(
          context: context,
          builder: (_) => AlertDialog(
            title: const Text('Session Ended'),
            content: const Text('Your session has been ended. Please log in again.'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  debugPrint('User forced-out dialog dismissed. Navigating to /login.');
                  context.go('/login');
                },
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
      return false;
    }

  } catch (e) {
    debugPrint('Error checking session: $e');
    // Optionally handle the error (e.g., show a dialog or do nothing).
  }

  debugPrint('Session and version checks passed. Returning true.');
  // If we get here, the session + version are valid
  return true;
}
