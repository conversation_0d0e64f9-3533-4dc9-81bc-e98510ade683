import 'app_localizations.dart';

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get locationNotesTitle => 'Notas de Ubicación';

  @override
  String get punchStatus => 'Estado de Fichaje';

  @override
  String get address1 => 'Dirección 1';

  @override
  String get address2 => 'Dirección 2';

  @override
  String get addressInformation => 'Información de la Dirección';

  @override
  String get administrator => 'Administrador';

  @override
  String get administratorDescription => 'Controla todo en el sistema.';

  @override
  String get cancel => 'Cancelar';

  @override
  String get city => 'Ciudad';

  @override
  String get configSettingUserSessionExpirationMinsMvc => 'Expiración de Sesión del Usuario en el Sitio Administrativo (Minutos)';

  @override
  String get configSettingUserSessionExpirationMinsMvcDescription => 'Cantidad de tiempo en minutos antes de que las sesiones inactivas de los usuarios expiren en el sitio administrativo.';

  @override
  String get clockIn => 'Fichar Entrada';

  @override
  String get clockOut => 'Fichar Salida';

  @override
  String get punch => 'Fichar';

  @override
  String get inW => 'Entrada';

  @override
  String get outW => 'Salida';

  @override
  String get confirmPassword => 'Confirmar Contraseña';

  @override
  String get confirmPasswordNotMatchPassword => 'La contraseña confirmada no coincide con la contraseña ingresada.';

  @override
  String get confirmPasswordRequired => 'Se requiere confirmar la contraseña.';

  @override
  String get contact => 'Contacto';

  @override
  String get contactTypeDescription => 'Plantilla de permisos predeterminada para un contacto.';

  @override
  String get contactInformation => 'Información de Contacto';

  @override
  String get country => 'País';

  @override
  String get emailAddressAlreadyRegistered => 'Esta dirección de correo electrónico ya está registrada.';

  @override
  String get firstName => 'Nombre';

  @override
  String get firstNameRequired => 'El nombre es requerido.';

  @override
  String get forcedOut => 'Expulsado';

  @override
  String get forgotPassword => 'Olvidó la Contraseña';

  @override
  String get internalError => 'Hubo un error interno. Por favor, intente de nuevo.';

  @override
  String get invalidRequest => 'La solicitud enviada es inválida.';

  @override
  String get lastName => 'Apellido';

  @override
  String get lastNameRequired => 'El apellido es requerido.';

  @override
  String get loggedOut => 'Cerró sesión';

  @override
  String get sandbox => 'Entorno de Pruebas';

  @override
  String get login => 'Iniciar Sesión';

  @override
  String get logout => 'Cerrar Sesión';

  @override
  String get mainEmailAddress => 'Correo Electrónico Principal';

  @override
  String get mainEmailAddressMustBeValidEmailFormat => 'El correo electrónico principal debe tener un formato válido.';

  @override
  String get mainEmailAddressRequired => 'El correo electrónico principal es requerido.';

  @override
  String get messages => 'Mensajes';

  @override
  String get networkUnavailable => 'Red no Disponible';

  @override
  String get ok => 'Aceptar';

  @override
  String get punchedIn => 'Fichado Entrada';

  @override
  String get organizationId => 'Identificador de Organización';

  @override
  String get organizationIdNotFound => 'El identificador de organización no fue encontrado.';

  @override
  String get organizationIdRequired => 'El identificador de organización es requerido.';

  @override
  String get organizationName => 'Nombre de la Organización';

  @override
  String get organizationNameRequired => 'El nombre de la organización es requerido.';

  @override
  String get password => 'Contraseña';

  @override
  String get passwordNotConformRules => 'La contraseña no cumple con las reglas.';

  @override
  String get passwordRequired => 'La contraseña es requerida.';

  @override
  String get phone => 'Teléfono';

  @override
  String get register => 'Registrarse';

  @override
  String get registerOrganization => 'Registrar Organización';

  @override
  String get schedule => 'Horario';

  @override
  String get sessionForcedOutReason => 'Otra sesión ha comenzado desde la dirección IP, si cree que es un error, inicie sesión nuevamente y cambie su contraseña.';

  @override
  String get state => 'Estado';

  @override
  String get sysop => 'Operador del Sistema';

  @override
  String get sysopDescription => 'Controla todo en el sistema y puede ver detalles extendidos del desarrollador.';

  @override
  String get timedOut => 'Tiempo Expirado';

  @override
  String get punchCard => 'Tarjeta de Fichaje';

  @override
  String get timeClock => 'Reloj de Tiempo';

  @override
  String get username => 'Nombre de Usuario';

  @override
  String get usernameOrPasswordIncorrect => 'El nombre de usuario o la contraseña es incorrecto.';

  @override
  String get usernameRequired => 'El nombre de usuario es requerido.';

  @override
  String get userMarkedInactive => 'Este usuario está marcado como inactivo.';

  @override
  String get punchedThisWeek => 'Tiempo Total Fichado';

  @override
  String get punchedToday => 'Fichado Hoy';

  @override
  String get zip => 'Código Postal';

  @override
  String get deviceIdRequired => 'El identificador del dispositivo es requerido.';

  @override
  String get invalidHash => 'El valor de hash enviado es inválido.';

  @override
  String get noResultsFound => 'No se encontraron resultados.';

  @override
  String get pleaseWait => 'Por Favor Espere';

  @override
  String get sessionNotFound => 'La sesión no fue encontrada.';

  @override
  String get rotatedOut => 'Rotado Fuera';

  @override
  String get sessionForcedOut => 'Esta sesión ha sido forzada a salir.';

  @override
  String get sessionLoggedOut => 'Esta sesión ha cerrado sesión.';

  @override
  String get sessionTimedOut => 'Esta sesión ha expirado.';

  @override
  String get userNotFoundOrInactive => 'Este usuario no fue encontrado o está inactivo.';

  @override
  String get sessionRotatedOut => 'La sesión fue rotada.';

  @override
  String get configSettingUserSessionExpirationMinsApi => 'Expiración de Sesión del Usuario en Dispositivos Móviles (Minutos)';

  @override
  String get configSettingUserSessionExpirationMinsApiDescription => 'Cantidad de tiempo en minutos antes de que las sesiones inactivas de los usuarios expiren en los dispositivos móviles.';

  @override
  String get home => 'Inicio';

  @override
  String get locations => 'Ubicaciones';

  @override
  String get allLocations => 'Todas las Ubicaciones';

  @override
  String get changeLocation => 'Cambiar Ubicación';

  @override
  String get action => 'Acción';

  @override
  String get activate => 'Activar';

  @override
  String get activateAreYouSure => '¿Está seguro de que desea activar este registro?';

  @override
  String get activateQuestion => '¿Activar?';

  @override
  String get active => 'Activo';

  @override
  String get activeType => 'Tipo Activo';

  @override
  String get address => 'Dirección';

  @override
  String get all => 'Todos';

  @override
  String get deactivate => 'Desactivar';

  @override
  String get deactivateAreYouSure => '¿Está seguro de que desea desactivar este registro?';

  @override
  String get deactivateQuestion => '¿Desactivar?';

  @override
  String get edit => 'Editar';

  @override
  String get inactive => 'Inactivo';

  @override
  String get locationName => 'Nombre de Ubicación';

  @override
  String get locationNotFound => 'Esta ubicación no fue encontrada.';

  @override
  String get no => 'No';

  @override
  String get noPermission => 'No tiene permiso para acceder a esta función.';

  @override
  String get search => 'Buscar';

  @override
  String get successExclamation => '¡Éxito!';

  @override
  String get view => 'Ver';

  @override
  String get yes => 'Sí';

  @override
  String get abbreviation => 'Abreviatura';

  @override
  String get emailAddress => 'Correo Electrónico';

  @override
  String get location => 'Ubicación';

  @override
  String get addLocation => 'Agregar Ubicación';

  @override
  String get locationNameRequired => 'El nombre de la ubicación es requerido.';

  @override
  String get submit => 'Enviar';

  @override
  String get viewLocations => 'Ver Ubicaciones';

  @override
  String get abbreviationAlreadyExists => 'Esta abreviatura ya existe.';

  @override
  String get abbreviationRequired => 'La abreviatura es requerida.';

  @override
  String get editLocation => 'Editar Ubicación';

  @override
  String get userpermissionCanEditLocations => 'Puede Editar Ubicaciones';

  @override
  String get userpermissionCanEditLocationsDescription => 'Permite al usuario editar los detalles de la ubicación.';

  @override
  String get userpermissionCanViewLocations => 'Puede Ver Ubicaciones';

  @override
  String get userpermissionCanViewLocationsDescription => 'Permite al usuario ver los detalles de la ubicación.';

  @override
  String get usernameCannotContainSpaces => 'El nombre de usuario no puede contener espacios.';

  @override
  String get chat => 'Chat';

  @override
  String get chatDescription => 'Mensajes de Chat';

  @override
  String get email => 'Correo Electrónico';

  @override
  String get emailDescription => 'Mensajes de Correo Electrónico';

  @override
  String get actions => 'Acciones';

  @override
  String get addEmployee => 'Agregar Empleado';

  @override
  String get employees => 'Empleados';

  @override
  String get employeeName => 'Nombre del Empleado';

  @override
  String get employeeType => 'Tipo de Empleado';

  @override
  String get userpermissionCanEditEmployees => 'Puede Editar Empleados';

  @override
  String get userpermissionCanEditEmployeesDescription => 'Permite al usuario editar los detalles del empleado.';

  @override
  String get userpermissionCanViewEmployees => 'Puede Ver Empleados';

  @override
  String get userpermissionCanViewEmployeesDescription => 'Permite al usuario ver los detalles del empleado.';

  @override
  String get viewEmployees => 'Ver Empleados';

  @override
  String get editEmployee => 'Editar Empleado';

  @override
  String get employee => 'Empleado';

  @override
  String get employeeId => 'ID de Empleado';

  @override
  String get employeeNotFound => 'Este empleado no fue encontrado.';

  @override
  String get employeeTypeRequired => 'El tipo de empleado es requerido.';

  @override
  String get usernameAlreadyRegistered => 'Este nombre de usuario ya está registrado.';

  @override
  String get userpermissionCanEditContacts => 'Puede Editar Contactos';

  @override
  String get userpermissionCanEditContactsDescription => 'Permite al usuario editar los detalles del contacto.';

  @override
  String get userpermissionCanViewContacts => 'Puede Ver Contactos';

  @override
  String get userpermissionCanViewContactsDescription => 'Permite al usuario ver los detalles del contacto.';

  @override
  String get addContact => 'Agregar Contacto';

  @override
  String get contacts => 'Contactos';

  @override
  String get contactName => 'Nombre del Contacto';

  @override
  String get viewContacts => 'Ver Contactos';

  @override
  String get contactId => 'ID del Contacto';

  @override
  String get contactLocationRequired => 'Se requiere una ubicación para este contacto.';

  @override
  String get contactNotFound => 'Este contacto no fue encontrado.';

  @override
  String get editContact => 'Editar Contacto';

  @override
  String get chooseLocation => 'Elegir Ubicación';

  @override
  String get select => 'Seleccionar';

  @override
  String get employeeTypeDescription => 'Plantilla de permisos predeterminada para un empleado.';

  @override
  String get contactType => 'Tipo de Contacto';

  @override
  String get contactTypeRequired => 'El tipo de contacto es requerido.';

  @override
  String get chooseContact => 'Elegir Contacto';

  @override
  String get addContactToLocation => 'Agregar Contacto a la Ubicación';

  @override
  String get addEmployeeToLocation => 'Agregar Empleado a la Ubicación';

  @override
  String get contactAlreadyInLocation => 'Este contacto ya está en esta ubicación.';

  @override
  String get areYouSureQuestion => '¿Está Seguro?';

  @override
  String get areYouSureRemoveContactFromLocation => '¿Está seguro de que desea eliminar este contacto de esta ubicación?';

  @override
  String get contactNotFoundInLocation => 'Este contacto no fue encontrado en esta ubicación.';

  @override
  String get removeContactFromLocation => 'Eliminar Contacto de la Ubicación';

  @override
  String get notes => 'Notas del Gerente';

  @override
  String get employeeTypeActions => 'Acciones del Tipo de Empleado';

  @override
  String get userpermissionCanViewEmployeeTypes => 'Puede Ver Tipos de Empleados';

  @override
  String get userpermissionCanViewEmployeeTypesDescription => 'Permite al usuario ver los detalles del tipo de empleado.';

  @override
  String get viewEmployeeTypes => 'Ver Tipos de Empleados';

  @override
  String get employeeCount => 'Conteo de Empleados';

  @override
  String get employeeTypes => 'Tipos de Empleados';

  @override
  String get employeeTypeName => 'Nombre del Tipo de Empleado';

  @override
  String get permissionCount => 'Conteo de Permisos';

  @override
  String get addEmployeeType => 'Agregar Tipo de Empleado';

  @override
  String get userpermissionCanEditEmployeeTypes => 'Puede Editar Tipos de Empleados';

  @override
  String get userpermissionCanEditEmployeeTypesDescription => 'Permite al usuario editar los detalles del tipo de empleado.';

  @override
  String get employeeTypeNotFound => 'Este tipo de empleado no fue encontrado.';

  @override
  String get editEmployeeType => 'Editar Tipo de Empleado';

  @override
  String get permissions => 'Permisos';

  @override
  String get description => 'Descripción';

  @override
  String get permission => 'Permiso';

  @override
  String get notEditable => 'Este registro no puede ser editado.';

  @override
  String get employeeTypeNameRequired => 'El nombre del tipo de empleado es requerido.';

  @override
  String get copyFromEmployeeType => 'Copiar de Tipo de Empleado Existente';

  @override
  String get addPermissionToEmployeeType => 'Agregar Permiso al Tipo de Empleado';

  @override
  String get areYouSureRemovePermissionFromEmployeeType => '¿Está seguro de que desea eliminar este permiso de este tipo de empleado?';

  @override
  String get choosePermission => 'Elegir Permiso';

  @override
  String get permissionAlreadyInEmployeeType => 'Este permiso ya está en este tipo de empleado.';

  @override
  String get permissionNotFound => 'El permiso no fue encontrado.';

  @override
  String get permissionNotFoundInEmployeeType => 'Este permiso no fue encontrado en este tipo de empleado.';

  @override
  String get remove => 'Eliminar';

  @override
  String get userpermissionCanPunchIn => 'Puede Fichar Entrada';

  @override
  String get userpermissionCanPunchInDescription => 'Permite al usuario fichar entrada o salida.';

  @override
  String get userpermissionCanEditPunchCards => 'Puede Editar Tarjetas de Fichaje';

  @override
  String get userpermissionCanEditPunchCardsDescription => 'Permite al usuario editar los detalles de las tarjetas de fichaje de otros usuarios.';

  @override
  String get userpermissionCanViewPunchCards => 'Puede Ver Tarjetas de Fichaje';

  @override
  String get userpermissionCanViewPunchCardsDescription => 'Permite al usuario ver los detalles de las tarjetas de fichaje de otros usuarios.';

  @override
  String get punchcardNotFound => 'Esta tarjeta de fichaje no fue encontrada.';

  @override
  String get geoFenceWarning => 'ADVERTENCIA: Salió del área de la ubicación sin fichar salida.';

  @override
  String get geoFenceEntered => 'Área de Geo-cerca Ingresada';

  @override
  String get geoFenceEnteredDescription => 'El usuario ingresó al área de geo-cerca.';

  @override
  String get geoFenceExited => 'Área de Geo-cerca Salida';

  @override
  String get geoFenceExitedDescription => 'El usuario salió del área de geo-cerca.';

  @override
  String get geoLocationDisabled => 'Geolocalización Desactivada';

  @override
  String get geoLocationDisabledDescription => 'Geolocalización del dispositivo desactivada.';

  @override
  String get geoLocationEnabled => 'Geolocalización Activada';

  @override
  String get geoLocationEnabledDescription => 'Geolocalización del dispositivo activada.';

  @override
  String get geoLocationUpdate => 'Actualización de Geolocalización';

  @override
  String get geoLocationUpdateDescription => 'Actualización de la geolocalización del usuario.';

  @override
  String get oldPasswordRequired => 'La contraseña anterior es requerida.';

  @override
  String get oldPasswordIncorrect => 'La contraseña anterior es incorrecta.';

  @override
  String get updatePassword => 'Actualizar Contraseña';

  @override
  String get newPassword => 'Nueva Contraseña';

  @override
  String get oldPassword => 'Contraseña Anterior';

  @override
  String get contactTypes => 'Tipos de Contacto';

  @override
  String get contactTypeActions => 'Acciones de Tipo de Contacto';

  @override
  String get contactTypeName => 'Nombre del Tipo de Contacto';

  @override
  String get contactTypeNameRequired => 'El nombre del tipo de contacto es requerido.';

  @override
  String get contactTypeNotFound => 'Este tipo de contacto no fue encontrado.';

  @override
  String get userpermissionCanEditContactTypes => 'Puede Editar Tipos de Contacto';

  @override
  String get userpermissionCanEditContactTypesDescription => 'Permite al usuario editar los detalles del tipo de contacto.';

  @override
  String get userpermissionCanViewContactTypes => 'Puede Ver Tipos de Contacto';

  @override
  String get userpermissionCanViewContactTypesDescription => 'Permite al usuario ver los detalles del tipo de contacto.';

  @override
  String get permissionAlreadyInContactType => 'Este permiso ya está en este tipo de contacto.';

  @override
  String get permissionNotFoundInContactType => 'Este permiso no fue encontrado en este tipo de contacto.';

  @override
  String get addContactType => 'Agregar Tipo de Contacto';

  @override
  String get viewContactTypes => 'Ver Tipos de Contacto';

  @override
  String get copyFromContactType => 'Copiar de Tipo de Contacto Existente';

  @override
  String get contactCount => 'Conteo de Contactos';

  @override
  String get addPermissionToContactType => 'Agregar Permiso al Tipo de Contacto';

  @override
  String get areYouSureRemovePermissionFromContactType => '¿Está seguro de que desea eliminar este permiso de este tipo de contacto?';

  @override
  String get editContactType => 'Editar Tipo de Contacto';

  @override
  String get daily => 'Diario';

  @override
  String get day => 'Día';

  @override
  String get firstOccurrence => 'Primera Ocurrencia';

  @override
  String get fourthOccurrence => 'Cuarta Ocurrencia';

  @override
  String get friday => 'Viernes';

  @override
  String get hour => 'Hora';

  @override
  String get lastOccurrence => 'Última Ocurrencia';

  @override
  String get minute => 'Minuto';

  @override
  String get monday => 'Lunes';

  @override
  String get month => 'Mes';

  @override
  String get saturday => 'Sábado';

  @override
  String get second => 'Segundo';

  @override
  String get secondOccurrence => 'Segunda Ocurrencia';

  @override
  String get sunday => 'Domingo';

  @override
  String get thirdOccurrence => 'Tercera Ocurrencia';

  @override
  String get thursday => 'Jueves';

  @override
  String get tuesday => 'Martes';

  @override
  String get wednesday => 'Miércoles';

  @override
  String get week => 'Semana';

  @override
  String get weekday => 'Día de Semana';

  @override
  String get weekend => 'Fin de Semana';

  @override
  String get addSchedule => 'Agregar Horario';

  @override
  String get employeeSchedule => 'Horario del Empleado';

  @override
  String get locationSchedule => 'Horario de Ubicación';

  @override
  String get schedules => 'Horarios';

  @override
  String get userpermissionCanEditSchedules => 'Puede Editar Horarios';

  @override
  String get userpermissionCanViewSchedules => 'Puede Ver Horarios';

  @override
  String get viewSchedules => 'Ver Horarios';

  @override
  String get duration => 'Duración';

  @override
  String get every => 'Cada';

  @override
  String get frequency => 'Frecuencia';

  @override
  String get nextScheduled => 'Siguiente Programado';

  @override
  String get on => 'En';

  @override
  String get startDate => 'Fecha de Inicio';

  @override
  String get startTime => 'Hora de Inicio';

  @override
  String get viewSchedule => 'Ver Horario';

  @override
  String get repeatEvery => 'Repetir cada';

  @override
  String get showPlannedSchedule => 'Mostrar Horario Planificado';

  @override
  String get scheduleNotFound => 'Este horario no fue encontrado.';

  @override
  String get timeZone => 'Zona Horaria';

  @override
  String get addRepeatingSchedule => 'Agregar Horario Recurrente';

  @override
  String get repeatingSchedules => 'Horarios Recurrentes';

  @override
  String get viewRepeatingSchedule => 'Ver Horario Recurrente';

  @override
  String get viewRepeatingSchedules => 'Ver Horarios Recurrentes';

  @override
  String get editSchedule => 'Editar Horario';

  @override
  String get endLocationTimeZone => 'Fin (Zona horaria de la ubicación)';

  @override
  String get from => 'Desde';

  @override
  String get invalidDateRange => 'Rango de fechas inválido';

  @override
  String get invalidDuration => 'Duración inválida';

  @override
  String get invalidRecurringRange => 'Rango recurrente inválido';

  @override
  String get invalidTimeOfDay => 'Hora del día inválida';

  @override
  String get originalLocationTimeZone => 'Original (Zona horaria de la ubicación)';

  @override
  String get scheduledLocalTimeZone => 'Programado (Zona horaria actual)';

  @override
  String get scheduledLocationTimeZone => 'Programado (Zona horaria de la ubicación)';

  @override
  String get selectAtLeastOneDayOfTheWeek => 'Seleccione al menos un día de la semana.';

  @override
  String get startLocationTimeZone => 'Inicio (Zona horaria de la ubicación)';

  @override
  String get startTimeLocationTimeZone => 'Hora de Inicio (Zona horaria de la ubicación)';

  @override
  String get to => 'Hasta';

  @override
  String get schedulegeneratorInvalidRecurringDailyfreqOccurseveryfreqId => 'Este horario tiene un Recurring_DailyFreq_OccursEveryFreq_ID inválido.';

  @override
  String get schedulegeneratorInvalidRecurringFrequencyId => 'Este horario tiene un Recurring_Frequency_ID inválido.';

  @override
  String get schedulegeneratorIsNotRecurringNoOnetimeOccurson => 'Este horario no es recurrente y no tiene un valor de OneTime_OccursOn.';

  @override
  String get schedulegeneratorIsRecurringNoRecurringDailyfreqOccursonce => 'Este horario es recurrente y no tiene un valor de Recurring_DailyFreq_OccursOnce.';

  @override
  String get schedulegeneratorNotRecurringDailyfreqOccursonceNoRecurringDailyfreqStartOrEnd => 'Este horario no es recurrente diario una vez y no tiene un valor de Recurring_DailyFreq_StartingOn o Recurring_DailyFreq_EndingOn.';

  @override
  String get schedulegeneratorRecurringFrequencyDayNoRecurringFrequencyEverynfreq => 'Este horario es recurrente diario y no tiene un valor de Recurring_Frequency_EveryNFreq.';

  @override
  String get schedulegeneratorRecurringFrequencyMonthNoRecurringFrequencyMonthlyThedayoftheweekId => 'Este horario es recurrente mensual y no tiene un valor de Recurring_Frequency_Monthly_TheDayOfTheWeek_ID.';

  @override
  String get schedulegeneratorRecurringFrequencyMonthNoRecurringFrequencyMonthlyTheweekId => 'Este horario es recurrente mensual y no tiene un valor de Recurring_Frequency_Monthly_TheWeek_ID.';

  @override
  String get userpermissionCanSendChatMessage => 'Puede Enviar Mensajes de Chat';

  @override
  String get userpermissionCanSendChatMessageDescription => 'Permite al usuario enviar mensajes de chat.';

  @override
  String get userpermissionCanViewChatMessage => 'Puede Ver Mensajes de Chat';

  @override
  String get userpermissionCanViewChatMessageDescription => 'Permite al usuario ver chats con otros usuarios.';

  @override
  String get message => 'Mensaje';

  @override
  String get recentChatMessages => 'Mensajes Recientes de Chat';

  @override
  String get send => 'Enviar';

  @override
  String get sendChatMessageTo => 'Enviar mensaje de chat a';

  @override
  String get punchCards => 'Tarjetas de Fichaje';

  @override
  String get viewPunchCards => 'Ver Tarjetas de Fichaje';

  @override
  String get clockedInBy => 'Fichado por';

  @override
  String get clockedOutBy => 'Fichado Salida por';

  @override
  String get editPunchCard => 'Editar Tarjeta de Fichaje';

  @override
  String get punchCardId => 'ID de Tarjeta de Fichaje';

  @override
  String get punchCardNotFound => 'Esta tarjeta de fichaje no fue encontrada.';

  @override
  String get clockOutBeforeClockedIn => 'La salida debe ser después de la entrada.';

  @override
  String get clockedIn => 'Fichado Entrada';

  @override
  String get christmasHoliday => 'Vacaciones de Navidad';

  @override
  String get fourthOfJulyHoliday => 'Vacaciones del 4 de Julio';

  @override
  String get newYearsHoliday => 'Vacaciones de Año Nuevo';

  @override
  String get thanksgivingHoliday => 'Vacaciones de Acción de Gracias';

  @override
  String get userpermissionCanEditNotes => 'Puede Editar Notas';

  @override
  String get userpermissionCanViewNotes => 'Puede Ver Notas';

  @override
  String get noteRequired => 'Se requiere texto en la nota.';

  @override
  String get addNote => 'Agregar Nota';

  @override
  String get editNote => 'Editar Nota';

  @override
  String get noteNotFound => 'Esta nota no fue encontrada.';

  @override
  String get changedBy => 'Modificado por';

  @override
  String get clear => 'Limpiar';

  @override
  String get chooseEmployee => 'Elegir Empleado';

  @override
  String get employeeOrLocationRequired => 'Se debe seleccionar un empleado o una ubicación.';

  @override
  String get userpermissionCanEditChecklists => 'Puede Editar Listas de Verificación';

  @override
  String get userpermissionCanEditChecklistsDescription => 'Permite al usuario editar listas de verificación.';

  @override
  String get userpermissionCanViewChecklists => 'Puede Ver Listas de Verificación';

  @override
  String get userpermissionCanViewChecklistsDescription => 'Permite al usuario ver listas de verificación.';

  @override
  String get addChecklist => 'Agregar Lista de Verificación';

  @override
  String get checklist => 'Lista de Verificación';

  @override
  String get checklists => 'Listas de Verificación';

  @override
  String get addChecklistItem => 'Agregar Elemento';

  @override
  String get checklistNotFound => 'Esta lista de verificación no fue encontrada.';

  @override
  String get editChecklist => 'Editar Lista de Verificación';

  @override
  String get editChecklistItem => 'Editar Elemento';

  @override
  String get items => 'Elementos';

  @override
  String get close => 'Cerrar';

  @override
  String get endLocalTimeZone => 'Fin (Zona horaria local)';

  @override
  String get startLocalTimeZone => 'Inicio (Zona horaria local)';

  @override
  String get createdBy => 'Creado por';

  @override
  String get note => 'Nota';

  @override
  String get error => 'Error';

  @override
  String get calendar => 'Calendario';

  @override
  String get alerts => 'Alertas';

  @override
  String get allPunchCards => 'Todas las Tarjetas de Fichaje';

  @override
  String get currentPayPeriod => 'Período de Pago Actual';

  @override
  String get customTimePeriod => 'Período de Tiempo Personalizado';

  @override
  String get hours => 'Horas';

  @override
  String get recentPunchCards => 'Tarjetas de Fichaje Recientes';

  @override
  String get addPunchCard => 'Agregar Tarjeta de Fichaje';

  @override
  String get timePeriod => 'Período de Tiempo';

  @override
  String get endDateRequired => 'La fecha de finalización es requerida.';

  @override
  String get employeeRequired => 'El empleado es requerido.';

  @override
  String get startDateRequired => 'La fecha de inicio es requerida.';

  @override
  String get startDateCannotBeAfterEndDate => 'La fecha de inicio no puede ser después de la fecha de finalización.';

  @override
  String get locationRequired => 'La ubicación es requerida.';

  @override
  String get selectLocationLoadSchedules => 'Seleccione una ubicación para cargar los horarios.';

  @override
  String get takeNote => 'Tomar Nota';

  @override
  String get endTime => 'Hora de Finalización';

  @override
  String get endTimeLocationTimeZone => 'Hora de Finalización (Zona horaria de la ubicación)';

  @override
  String get recurring => 'Recurrente';

  @override
  String get endsBy => 'Termina Por';

  @override
  String get invalidEndDate => 'Fecha de finalización inválida';

  @override
  String get noEndDate => 'Sin Fecha de Finalización';

  @override
  String get nextLocation => 'Siguiente Ubicación';

  @override
  String get travelTime => 'Tiempo de Viaje';

  @override
  String get localTime => 'Hora Local';

  @override
  String get scheduledTime => 'Hora Programada';

  @override
  String get scheduleDetailsFor => 'Detalles del Horario para';

  @override
  String get days => 'Días';

  @override
  String get weeks => 'Semanas';

  @override
  String get months => 'Meses';

  @override
  String get dayOfMonth => 'Día del Mes';

  @override
  String get onSpecificDay => 'En Día Específico';

  @override
  String get onSpecificWeek => 'En Semana Específica';

  @override
  String get editRepeatingSchedule => 'Editar Horario Recurrente';

  @override
  String get april => 'Abril';

  @override
  String get august => 'Agosto';

  @override
  String get december => 'Diciembre';

  @override
  String get february => 'Febrero';

  @override
  String get january => 'Enero';

  @override
  String get july => 'Julio';

  @override
  String get june => 'Junio';

  @override
  String get march => 'Marzo';

  @override
  String get may => 'Mayo';

  @override
  String get november => 'Noviembre';

  @override
  String get october => 'Octubre';

  @override
  String get september => 'Septiembre';

  @override
  String get onSpecificMonth => 'En Mes Específico';

  @override
  String get endTimeRequired => 'La hora de finalización es requerida.';

  @override
  String get noDescription => 'Sin Descripción';

  @override
  String get scheduletemplateInvalidRecurringFrequencyId => 'Este horario tiene una selección inválida de Día, Semana, Mes.';

  @override
  String get scheduletemplateRecurringFrequencyDayNoRecurringFrequencyEverynfreq => 'This schedule is marked as recurring daily and did not have a repeat value.';

  @override
  String get scheduletemplateRecurringFrequencyMonthNoRecurringFrequencyEverynfreq => 'Este horario es recurrente mensual y no tiene un valor de Recurring_Frequency_EveryNFreq.';

  @override
  String get scheduletemplateRecurringFrequencyWeekNoRecurringFrequencyEverynfreq => 'Este horario es recurrente semanal y no tiene un valor de Recurring_Frequency_EveryNFreq.';

  @override
  String get scheduletemplateRecurringFrequencyWeekNoWeekdaySelected => 'Este horario es recurrente semanal y no se seleccionaron días de la semana.';

  @override
  String get startTimeCannotBeAfterEndTime => 'La hora de inicio no puede ser posterior a la hora de finalización.';

  @override
  String get startTimeRequired => 'La hora de inicio es requerida.';

  @override
  String get scheduletemplateRecurringFrequencyInvalidMonthDay => 'Este horario es recurrente mensual en un día específico del mes y el día no es válido.';

  @override
  String get scheduletemplateRecurringFrequencyNoMonthlyOnMonth => 'Este horario es recurrente mensual en un mes específico y no tiene un valor de mes.';

  @override
  String get scheduletemplateRecurringFrequencyNoMonthlyWeek => 'Este horario es recurrente mensual en una semana específica del mes y no tiene un valor de ocurrencia semanal.';

  @override
  String get scheduletemplateRecurringFrequencyNoMonthlyWeekday => 'Este horario es recurrente mensual en una semana específica del mes y no tiene un valor de día de la semana.';

  @override
  String get scheduletemplateRecurringFrequencyNoMonthDay => 'Este horario es recurrente mensual en un día específico del mes y no tiene un valor de día.';

  @override
  String get areYouSureEditSubscriptionTemplateWillRemoveFutureSchedules => '¿Está seguro de que desea editar este horario recurrente? Esto eliminará cualquier horario futuro ya creado.';

  @override
  String get addNewSchedule => 'Agregar Nuevo Horario';

  @override
  String get editThisSchedule => 'Editar Este Horario';

  @override
  String get viewThisSchedule => 'Ver Este Horario';

  @override
  String get of0 => 'De';

  @override
  String get onThe => 'El';

  @override
  String get nd => 'º\n    Como el 2º del mes.';

  @override
  String get rd => 'º\n    Como el 3º del mes.';

  @override
  String get st => 'º\n    Como el 1º del mes.';

  @override
  String get th => 'º\n    Como el 4º del mes.';

  @override
  String get year => 'Año';

  @override
  String get years => 'Años';

  @override
  String get in0 => 'En';

  @override
  String get admin => 'Administrador';

  @override
  String get adminDescription => 'Administrativo';

  @override
  String get locationDescription => 'Trabajo no programado';

  @override
  String get scheduleDescription => 'Trabajo programado';

  @override
  String get travelTimeDescription => 'Tiempo de viaje';

  @override
  String get invalidJobType => 'Tipo de trabajo inválido';

  @override
  String get jobType => 'Tipo de Trabajo';

  @override
  String get userpermissionCanViewJobTypes => 'Puede Ver Tipos de Trabajo';

  @override
  String get userpermissionCanViewJobTypeDescription => 'Permite al usuario ver tipos de trabajo.';

  @override
  String get addJobType => 'Agregar Tipo de Trabajo';

  @override
  String get editJobType => 'Editar Tipo de Trabajo';

  @override
  String get jobTypes => 'Tipos de Trabajo';

  @override
  String get jobTypeActions => 'Acciones del Tipo de Trabajo';

  @override
  String get jobTypeNotFound => 'Este tipo de trabajo no fue encontrado.';

  @override
  String get userpermissionCanEditJobTypes => 'Puede Editar Tipos de Trabajo';

  @override
  String get userpermissionCanEditJobTypesDescription => 'Permite al usuario editar tipos de trabajo.';

  @override
  String get viewJobTypes => 'Ver Tipos de Trabajo';

  @override
  String get scheduletemplateCouldNotBeCreated => 'No se pudo crear una nueva plantilla de horario. Inténtelo de nuevo.';

  @override
  String get userpermissionCanViewAlerts => 'Puede Ver Alertas';

  @override
  String get userpermissionCanViewReports => 'Puede Ver Informes';

  @override
  String get userpermissionCanViewReportsDescription => 'Permite al usuario ver informes.';

  @override
  String get reportUserSessions => 'Sesiones de Usuario';

  @override
  String get reportUserSessionsDescription => 'Muestra los tiempos de inicio/cierre de sesión y las razones de expulsión de las sesiones de usuario.';

  @override
  String get administration => 'Administración';

  @override
  String get reports => 'Informes';

  @override
  String get reportName => 'Nombre del Informe';

  @override
  String get run => 'Ejecutar';

  @override
  String get reportNotFound => 'Este informe no fue encontrado.';

  @override
  String get configSettingAlertClockInLimitMinMvc => 'Límite de Alerta de Fichaje (Minutos)';

  @override
  String get configSettingAlertClockInLimitMinMvcDescription => 'Cantidad de tiempo en minutos después de que comienza un horario en el que se genera una alerta si el empleado no ficha entrada.';

  @override
  String get report => 'Informe';

  @override
  String get setReportParameters => 'Establecer Parámetros del Informe';

  @override
  String get fillInOptionsClickRunReport => 'Complete las opciones, luego haga clic en Ejecutar Informe.';

  @override
  String get adobeAcrobat => 'Adobe Acrobat';

  @override
  String get commaSeparatedValues => 'Valores Separados por Comas';

  @override
  String get downloadReport => 'Download Report';

  @override
  String get microsoftExcel => 'Microsoft Excel';

  @override
  String get microsoftWord => 'Microsoft Word';

  @override
  String get reportFormat => 'Report Format';

  @override
  String get runReport => 'Run Report';

  @override
  String get clickHereDownloadPdf => 'Click here to download the PDF';

  @override
  String get clickHereInstallAdobeReader => 'or click here to install Adobe Reader.';

  @override
  String get noPdfSupportMessage => 'It appears you don\'t have Adobe Reader or PDF support in this web browser.';

  @override
  String get goFullscreen => 'Go Fullscreen';

  @override
  String get fillInAboveOptionsClickRunReport => 'Fill in the options above, then click Run Report.';

  @override
  String get reportNotReadyToRun => 'Your report is not ready to run.';

  @override
  String get canAddSchedulesDescription => 'Allows user to add schedules.';

  @override
  String get canEditNotesDescription => 'Allows user to edit notes.';

  @override
  String get canEditSchedulesDescription => 'Allows user to edit schedules.';

  @override
  String get canViewAlertsDescription => 'Allows user to view alerts.';

  @override
  String get canViewJobTypesDescription => 'Allows user to view job types.';

  @override
  String get canViewSchedulesDescription => 'Allows user to view schedules.';

  @override
  String get userpermissionCanViewNotesDescription => 'Allows user to view notes.';

  @override
  String get subordinates => 'Subordinates';

  @override
  String get supervisors => 'Supervisors';

  @override
  String get configSettingDatabaseBackupFolder => 'Database Backup Folder';

  @override
  String get configSettingDatabaseBackupFolderDescription => 'Location where the SQL database backups are stored.';

  @override
  String get cleaning => 'Cleaning';

  @override
  String get cleaningDescription => 'Cleaning related jobs';

  @override
  String get notListed => 'Not Listed';

  @override
  String get notListedDescription => 'Unlisted jobs';

  @override
  String get scheduled => 'Scheduled';

  @override
  String get scheduledDescription => 'Scheduled jobs';

  @override
  String get status => 'Status';

  @override
  String get unscheduled => 'Unscheduled';

  @override
  String get unscheduledDescription => 'Unscheduled jobs';

  @override
  String get mustCompleteChallenge => 'You must complete the challenge!';

  @override
  String get lastMonth => 'Last Month';

  @override
  String get lastUpdated => 'Last Updated';

  @override
  String get lastWeek => 'Last Week';

  @override
  String get thisMonth => 'This Month';

  @override
  String get thisWeek => 'This Week';

  @override
  String get today => 'Today';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get noLocation => 'No Location';

  @override
  String get clock => 'Clock';

  @override
  String get punchedOut => 'Punched Out';

  @override
  String get punchInAt => 'Punch In at:';

  @override
  String get date => 'Date';

  @override
  String get profile => 'Profile';

  @override
  String get punches => 'Punches';

  @override
  String get calendarDetail => 'Calendar Detail';

  @override
  String get punchInOut => 'Punch In/Out';

  @override
  String get punchesDetail => 'Punches Detail';

  @override
  String get confirmPunchOut => 'Are you sure you want to punch out?';

  @override
  String get writeNow => 'Write Now...';

  @override
  String get configSettingRegistrationEmailExpirationMinutes => 'Registration Email Expiration (Mins)';

  @override
  String get configSettingRegistrationEmailExpirationMinutesDescription => 'Amount of time in minutes before an unresponded registration email will expire.';

  @override
  String get registrationConfirmation => 'Registration Confirmation';

  @override
  String get confirm => 'Confirm';

  @override
  String get registrationKeyRequired => 'The Registration Key is required.';

  @override
  String get registrationKey => 'Registration Key';

  @override
  String get registrationRequestExpired => 'This registration request has expired and will need to be resent.';

  @override
  String get registrationRequestNotFound => 'This registration request could not be found. You will need to resubmit your registration request.';

  @override
  String get resendRegistrationRequest => 'Resend Registration Request';

  @override
  String get viewPunchCardsByDay => 'Punches By Day';

  @override
  String get configSettingRegistrationConfirmationUrl => 'Registration Confirmation URL';

  @override
  String get configSettingRegistrationConfirmationUrlDescription => 'The URL for confirming a new registration of an organization.';

  @override
  String get configSettingRegistrationUrl => 'Registration URL';

  @override
  String get configSettingRegistrationUrlDescription => 'The URL for registering a new organization.';

  @override
  String get configSettingRegistrationEmail => 'Registration Email';

  @override
  String get configSettingRegistrationEmailDescription => 'The email template that gets sent when registering an organization.';

  @override
  String get configSettingAdministrationUrl => 'Administration Site URL';

  @override
  String get configSettingAdministrationUrlDescription => 'The URL for the administration site.';

  @override
  String get configSettingAndroidMobileDeviceUrl => 'Android Mobile App URL';

  @override
  String get configSettingAndroidMobileDeviceUrlDescription => 'The URL for the android mobile application.';

  @override
  String get configSettingFirebaseMessagingConfiguration => 'Firebase Messaging Configuration';

  @override
  String get configSettingFirebaseMessagingConfigurationDescription => 'The configuration information for connecting to he Firebase service.';

  @override
  String get configSettingRegistrationThankyouEmail => 'Registration Thank You Email';

  @override
  String get configSettingRegistrationThankyouEmailDescription => 'The email template that gets sent after you uccessfully register an organization.';

  @override
  String get configSettingSmtpConfiguration => 'SMTP Configuration';

  @override
  String get configSettingSmtpConfigurationDescription => 'The configuration information for sending emails via SMTP.';

  @override
  String get created => 'Created';

  @override
  String get deleted => 'Deleted';

  @override
  String get earlyPunchIn => 'Early Punch In';

  @override
  String get earlyPunchInDescription => 'Employee punched in early.';

  @override
  String get earlyPunchOut => 'Early Punch Out';

  @override
  String get earlyPunchOutDescription => 'Employee punched out early.';

  @override
  String get latePunchIn => 'Late Punch In';

  @override
  String get latePunchInDescription => 'Employee punched in late.';

  @override
  String get latePunchOut => 'Late Punch Out';

  @override
  String get latePunchOutDescription => 'Employee punched out late.';

  @override
  String get noPunchIn => 'No Punch In';

  @override
  String get noPunchInDescription => 'Employee did not punch in.';

  @override
  String get outsideGeofence => 'Breached Geofence';

  @override
  String get outsideGeofenceDescription => 'Employee is clocked in to a location, but is outside the location\'s geofence.';

  @override
  String get pushNotification => 'Push Notification';

  @override
  String get queued => 'Queued';

  @override
  String get sent => 'Sent';

  @override
  String get silentNotification => 'Silent Notification';

  @override
  String get selectAValue => 'Select a value';

  @override
  String get userpermissionCanViewEmployeePayRates => 'Can View Employee Pay Rates';

  @override
  String get userpermissionCanViewEmployeePayRatesDescription => 'Allows user to view employee pay rates.';

  @override
  String get payRate => 'Pay Rate';

  @override
  String get userpermissionCanEditEmployeePayRates => 'Can Edit Employee Pay Rates';

  @override
  String get userpermissionCanEditEmployeePayRatesDescription => 'Allows user to edit employee pay rates.';

  @override
  String get payRateMustBeGreaterThanZero => 'The Pay Rate must be greater than zero.';

  @override
  String get hourly => 'Hourly';

  @override
  String get weekly => 'Weekly';

  @override
  String get yearly => 'Yearly';

  @override
  String get configSettingFirstDayOfWeek => 'First Day Of The Week';

  @override
  String get configSettingFirstDayOfWeekDescription => 'The first day of the week for your organization.';

  @override
  String get configSettingOvertimeHours => 'Overtime Hours';

  @override
  String get configSettingOvertimeHoursDescription => 'The amount of hours before ovetime pay is applied.';

  @override
  String get timeZoneRequired => 'The Time Zone is required.';

  @override
  String get reportEmployeesHoursWorked => 'Employees Hours Worked';

  @override
  String get reportEmployeesHoursWorkedDescription => 'Shows the hours worked for employees over a given week.';

  @override
  String get reportLocationsHoursWorked => 'Locations Hours Worked';

  @override
  String get reportLocationsHoursWorkedDescription => 'Shows the hours worked at locations over a given week.';

  @override
  String get languageEnUs => 'English (United States)';

  @override
  String get languageEsUs => 'Spanish (United States)';

  @override
  String get language => 'Language';

  @override
  String get languageRequired => 'The Language is required.';

  @override
  String get userpermissionCanEditPermissions => 'Can Edit Permissions';

  @override
  String get userpermissionCanEditPermissionsDescription => 'Allows user to edit employee and contact permissions.';

  @override
  String get userpermissionCanEditReports => 'Can Edit Reports';

  @override
  String get userpermissionCanEditReportsDescription => 'Allows user to activate or deactivate reports.';

  @override
  String get addPhoto => 'Add Photo';

  @override
  String get addTemplate => 'Add Template';

  @override
  String get agenda => 'Agenda';

  @override
  String get busy => 'Busy';

  @override
  String get createNewInspection => 'Create New Inspection';

  @override
  String get editInspection => 'Edit Inspection';

  @override
  String get editInspectionItem => 'Edit Inspection Item';

  @override
  String get inspections => 'Inspections';

  @override
  String get inspectionTemplates => 'Inspection Templates';

  @override
  String get addInspectionTemplates => 'Add Inspection Templates';

  @override
  String get editInspectionTemplates => 'Edit Inspection Templates';

  @override
  String get newChat => 'New Chat';

  @override
  String get newInspection => 'New Inspection';

  @override
  String get viewInspections => 'View Inspections';

  @override
  String get addInspections => 'Add Inspections';

  @override
  String get editInspections => 'Edit Inspections';

  @override
  String get viewInspectionTemplates => 'View Inspection Templates';

  @override
  String get noPunchCardsThisWeek => 'No Punch Cards This Week';

  @override
  String get save => 'Save';

  @override
  String get saveConfirmation => 'Are you sure you want to save?';

  @override
  String get sync => 'Sync';

  @override
  String get takePhoto => 'Take Photo';

  @override
  String get userpermissionCanEditInspections => 'Can Edit Inspections';

  @override
  String get userpermissionCanEditInspectionsDescription => 'Allows user to create and edit inspections and inspection templates.';

  @override
  String get userpermissionCanViewInspections => 'Can View Inspections';

  @override
  String get userpermissionCanViewInspectionsDescription => 'Allows user to view inspections and inspection templates.';

  @override
  String get forgotLogin => 'Forgot Login or Password';

  @override
  String get emailRequired => 'The Email Address is required.';

  @override
  String get invalidEmail => 'The Email Address must be a valid email format.';

  @override
  String get count => 'Count';

  @override
  String get name => 'Name';

  @override
  String get payrollId => 'Payroll Id';

  @override
  String get downloadPayroll => 'Download Payroll';

  @override
  String get hidePassword => 'Hide Password';

  @override
  String get showPassword => 'Show Password';

  @override
  String get liveStatus => 'Live Status';

  @override
  String get accountInformation => 'Account Information';

  @override
  String get personalInformation => 'Personal Information';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get additionalAddressInformation => 'Additional Address Information';

  @override
  String get phoneRequired => 'Phone required.';

  @override
  String get invalidPhone => 'Invalid phone number.';

  @override
  String get address1Required => 'Address 1 is required.';

  @override
  String get invalidAddress => 'Invalid address.';

  @override
  String get cityRequired => 'City is required.';

  @override
  String get stateRequired => 'State is required.';

  @override
  String get zipRequired => 'Zip code is required.';
}
