import 'package:json_annotation/json_annotation.dart';
import '../../dataModel/data/location_contact.dart';
import '../../dataModel/data/location_notes.dart';
import '../../dataModel/data/user.dart';
import '../../dataModel/data/user_type.dart';
import '../../dataModel/data/geo_location.dart';
import '../../dataModel/data/inspection.dart';
import '../../dataModel/data/inspection_template.dart';
import '../../dataModel/data/job_type.dart';
import '../../dataModel/data/location.dart';
import '../../dataModel/data/message.dart';
import '../../dataModel/data/note.dart';
import '../../dataModel/data/punch_card.dart';
import '../../dataModel/data/schedule.dart';
import '../../dataModel/data/alert.dart';
import '../../dataModel/data/user_type_permission.dart';

import 'system.dart';

part 'post_changes.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class PostChangesRequest extends SystemRequest {
  Iterable<Alert>? alerts;
  Iterable<LocationContact>? locationContacts;
  Iterable<LocationNote>? locationNotes;
  Iterable<GeoLocation>? geoLocations;
  Iterable<Inspection>? inspections;
  Iterable<InspectionArea>? inspectionAreas;
  Iterable<InspectionItem>? inspectionItems;
  Iterable<InspectionImage>? inspectionImages;
  Iterable<InspectionTemplate>? inspectionTemplates;
  Iterable<InspectionTemplateArea>? inspectionTemplateAreas;
  Iterable<InspectionTemplateItem>? inspectionTemplateItems;
  Iterable<JobType>? jobTypes;
  Iterable<Location>? locations;
  Iterable<Message>? messages;
  Iterable<Note>? notes;
  Iterable<PunchCard>? punchCards;
  Iterable<Schedule>? schedules;
  Iterable<User>? users;
  Iterable<UserType>? userTypes;
  Iterable<UserTypePermission>? userTypePermissions;

  PostChangesRequest({
    required this.alerts,
    required this.locationContacts,
    required this.locationNotes,
    required this.geoLocations,
    required this.inspections,
    required this.inspectionAreas,
    required this.inspectionItems,
    required this.inspectionImages,
    required this.inspectionTemplates,
    required this.inspectionTemplateAreas,
    required this.inspectionTemplateItems,
    required this.jobTypes,
    required this.locations,
    required this.messages,
    required this.notes,
    required this.punchCards,
    required this.schedules,
    required this.users,
    required this.userTypes,
    required this.userTypePermissions,
    required super.serverIP,
    required super.databaseName,
    required super.sessionId,
  });

  static Future<PostChangesRequest> create({
    Iterable<Alert>? alerts,
    Iterable<LocationContact>? locationContacts,
    Iterable<LocationNote>? locationNotes,
    Iterable<GeoLocation>? geoLocations,
    Iterable<Inspection>? inspections,
    Iterable<InspectionArea>? inspectionAreas,
    Iterable<InspectionItem>? inspectionItems,
    Iterable<InspectionImage>? inspectionImages,
    Iterable<InspectionTemplate>? inspectionTemplates,
    Iterable<InspectionTemplateArea>? inspectionTemplateAreas,
    Iterable<InspectionTemplateItem>? inspectionTemplateItems,
    Iterable<JobType>? jobTypes,
    Iterable<Location>? locations,
    Iterable<Message>? messages,
    Iterable<Note>? notes,
    Iterable<PunchCard>? punchCards,
    Iterable<Schedule>? schedules,
    Iterable<User>? users,
    Iterable<UserType>? userTypes,
    Iterable<UserTypePermission>? userTypePermissions,
  }) async {
    final systemRequest = await SystemRequest.create();

    return PostChangesRequest(
      alerts: alerts,
      locationContacts: locationContacts,
      locationNotes: locationNotes,
      geoLocations: geoLocations,
      inspections: inspections,
      inspectionAreas: inspectionAreas,
      inspectionItems: inspectionItems,
      inspectionImages: inspectionImages,
      inspectionTemplates: inspectionTemplates,
      inspectionTemplateAreas: inspectionTemplateAreas,
      inspectionTemplateItems: inspectionTemplateItems,
      jobTypes: jobTypes,
      locations: locations,
      notes: notes,
      messages: messages,
      punchCards: punchCards,
      schedules: schedules,
      users: users,
      userTypes: userTypes,
      userTypePermissions: userTypePermissions,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory PostChangesRequest.fromJson(Map<String, dynamic> json) => _$PostChangesRequestFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$PostChangesRequestToJson(this);
}
