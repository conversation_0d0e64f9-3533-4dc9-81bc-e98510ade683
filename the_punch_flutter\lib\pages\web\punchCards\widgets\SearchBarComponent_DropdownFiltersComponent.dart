

import 'package:flutter/material.dart';

import '../../../../helpers/color_helper.dart';

import '../punch_cards.dart';

class SearchBarComponent_DropdownFiltersComponent extends StatelessWidget {
  final ViewModelPunch viewModel;

  const SearchBarComponent_DropdownFiltersComponent({super.key, required this.viewModel});

  @override
  Widget build(BuildContext context) => Row(
      children: [
        if(!(MediaQuery.of(context).size.width < 600))
        Expanded(
          flex: 2,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextField(
              onChanged: (value) => viewModel.searchNotifier.value = value,
              decoration: InputDecoration(
                hintText: 'Search employees...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ),
        if(!(MediaQuery.of(context).size.width < 600))
        SizedBox(width:32),
        Expanded(
            flex:1,
            child: ValueListenableBuilder(
            valueListenable: viewModel.activeNotifier,
            builder: (context, value, child) => DropdownButtonFormField<ActiveToggleStatePunch>(
              value: value,
              onChanged: (newValue) {
              if (newValue != null) {
              viewModel.activeNotifier.value = newValue;
              }
              },
              icon: Icon(Icons.arrow_drop_down_rounded, color: Colors.white),
              decoration: InputDecoration(
              filled: true,
              fillColor: ColorHelper.thePunchDarkBlue(),
              border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              ),
              iconColor: Colors.white,
              ),
              style: const TextStyle(color: Colors.white),
              dropdownColor: ColorHelper.thePunchDarkBlue(),
              isExpanded: true,
              items: const [
              DropdownMenuItem(
              value: ActiveToggleStatePunch.both,
              child: Text('All', style: TextStyle(color: Colors.white), overflow: TextOverflow.ellipsis),
              ),
              DropdownMenuItem(
              value: ActiveToggleStatePunch.active,
              child: Text('Live', style: TextStyle(color: Colors.white), overflow: TextOverflow.ellipsis),
              ),
              DropdownMenuItem(
              value: ActiveToggleStatePunch.inactive,
              child: Text('Closed', style: TextStyle(color: Colors.white), overflow: TextOverflow.ellipsis),
              ),
              ],
            ),
          ),
        ),
      ],
    );
}

// ignore: unused_element
enum _TimePeriod {
  today,
  yesterday,
  thisWeek,
  lastWeek,
  thisMonth,
  lastMonth,
  all,
  custom,
  singleDay,
}


class _PunchCardGroup {
  final String punchCardLinkId;
  final String firstPunchCardId;
  final Duration closedDuration;
  final List<DateTime> openClockIns;
  final bool isLive;
  final String jobTypeName;
  final String locationName;

  final DateTime earliestClockIn;
  final DateTime? latestClockOut;
  final int alertCount;
  final int taskCount;

  _PunchCardGroup({
    required this.punchCardLinkId,
    required this.firstPunchCardId,
    required this.closedDuration,
    required this.openClockIns,
    required this.isLive,
    required this.jobTypeName,
    required this.locationName,
    required this.earliestClockIn,
    required this.latestClockOut,
    required this.alertCount,
    required this.taskCount,
  });
}





