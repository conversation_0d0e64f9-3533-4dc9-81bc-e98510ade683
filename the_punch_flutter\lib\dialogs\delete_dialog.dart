import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';

import '../helpers/color_helper.dart';

class DeleteDialog extends StatelessWidget {
  final String? title;
  final String? message;
  final String? buttonText;
  final Function() delete;

  const DeleteDialog(
      {super.key,
      this.title,
      this.message,
      this.buttonText,
      required this.delete});

  @override
  Widget build(BuildContext context) => AlertDialog(
        title: title != null ? Text(title!) : null,
        content: Text(message ?? 'Are you sure you want to delete this item?'),
        actions: [
          OutlinedButton.icon(
            style: OutlinedButton.styleFrom(
                side: BorderSide(
              color: ColorHelper.thePunchRed(),
            )),
            icon: const Icon(Icons.cancel),
            label: Text(AppLocalizations.of(context)!.no),
            onPressed: () => Navigator.pop(context),
          ),
          ElevatedButton.icon(
            icon: const Icon(Icons.delete),
            label: Text(buttonText ?? 'Delete'),
            onPressed: () {
              Navigator.pop(context);
              delete();
            },
          ),
        ],
      );
}
