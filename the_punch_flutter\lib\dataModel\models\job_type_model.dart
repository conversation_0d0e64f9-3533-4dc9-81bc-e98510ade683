import '../base_data.dart';
import '../data/job_type.dart';
import '../hive_db.dart';

class JobTypeModel extends BaseDataModel<JobType> {
  @override
  Future<Iterable<JobType>> get all async => (await HiveDb.database).jobTypes.values;

  @override
  Future<void> save(Iterable<JobType> t) async {
    await (await HiveDb.database).jobTypes.putAll({for (final e in t) e.id: e});
    notifyListeners();
  }

  // Implementing getDirty to fetch dirty job types, limited by a given size.
  Future<Iterable<JobType>> getDirty({required int limit}) async {
    var allRecords = (await HiveDb.database).jobTypes.values;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }

  Future<Iterable<JobType>> get visible async => (await all).where((e) => e.id != JobType.scheduledId && e.id != JobType.unscheduledId);

  Future<Iterable<JobType>> get activeVisible async => (await visible).where((e) => e.isActive);

  // Method to get job type name by UUID
  String getJobTypeName(String uuid) {

    switch (uuid) {
      case JobType.scheduledId:
        return 'Scheduled';
      case JobType.unscheduledId:
        return 'Unscheduled';
      case JobType.administrativeId:
        return 'Administrative';
      case JobType.travelTimeId:
        return 'Travel Time';
      case JobType.specialEventId:
        return 'Special Event';
      case JobType.inspectionId:
        return 'Inspection';
      case JobType.trainingId:
        return 'Training';
      case JobType.projectId:
        return 'Project';
      case JobType.customerVisitId:
        return 'Customer Visit';
      case JobType.employeeVisitId:
        return 'Employee Visit';
      case JobType.trackingId:
        return 'Tracking';        
      default:
        return 'Unknown';
    }
  }

  getAll() {}
}
