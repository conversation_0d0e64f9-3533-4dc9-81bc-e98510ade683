import 'dart:async';
import 'package:flutter/material.dart';
import '../../api/api_model.dart';
import '../../state/server_time_state.dart';
import '../web/my_scaffold.dart';

class TimeTestsPage extends StatelessWidget {
  const TimeTestsPage({super.key});
  @override
  Widget build(BuildContext context) => MyScaffold(
        title: 'Time Tests',
        enableBottomBar: false,
        body: _Body(),
      );
}

class _Body extends StatefulWidget {
  @override
  __BodyState createState() => __BodyState();
}

class __BodyState extends State<_Body> {
  late Timer _timer;
  DateTime _serverTime = DateTime.utc(1970);

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      final serverTime = await ApiModel().getServerTime();
      if (mounted) {
        setState(() => _serverTime = serverTime);
      }
    });
  }

  @override
  Widget build(BuildContext context) => Center(
        child: Column(
          children: [
            Text(_serverTime.toString()),
            Text(ServerTimeState().utcTime.toString()),
          ],
        ),
      );

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }
}
