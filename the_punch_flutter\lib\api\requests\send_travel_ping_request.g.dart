// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'send_travel_ping_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SendTravelPingRequest _$SendTravelPingRequestFromJson(
        Map<String, dynamic> json) =>
    SendTravelPingRequest(
      ping: TravelPing.fromJson(json['Ping'] as Map<String, dynamic>),
      pingSource: json['PingSource'] as String?,
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$SendTravelPingRequestToJson(
    SendTravelPingRequest instance) {
  final val = <String, dynamic>{
    'Request_ServerIP': instance.serverIP,
    'Request_DatabaseName': instance.databaseName,
    'Request_SessionID': instance.sessionId,
    'Ping': instance.ping,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('PingSource', instance.pingSource);
  return val;
}

SendTravelPingResponse _$SendTravelPingResponseFromJson(
        Map<String, dynamic> json) =>
    SendTravelPingResponse(
      success: json['Success'] as bool,
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$SendTravelPingResponseToJson(
    SendTravelPingResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['Success'] = instance.success;
  return val;
}
