import 'package:flutter/material.dart';
import 'package:the_punch_flutter/helpers/check_session.dart';
import 'package:the_punch_flutter/helpers/color_helper.dart';
import 'package:the_punch_flutter/state/punch_state.dart';

class EndTaskDialog extends StatefulWidget {
  final PunchState punchState;

  const EndTaskDialog({required this.punchState});

  @override
  _EndTaskDialogState createState() => _EndTaskDialogState();
}

class _EndTaskDialogState extends State<EndTaskDialog> {
  bool isEndingTask = false;

  Future<void> _confirmEndTask() async {

    final sessionOk = await checkSession(context);
    if (!sessionOk) {
      Navigator.of(context, rootNavigator: true).pop();
      return;
    }

    setState(() {
      isEndingTask = true;
    });

    // Perform the punch-out operation
    await widget.punchState.managerTaskPunchOut();

    // Optionally, show a confirmation SnackBar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text("Task ended! Returning to Manager Tracking."),
        backgroundColor: ColorHelper.thePunchBlue(),
      ),
    );

    // Close the dialog after completing the task
    if (mounted) {
      Navigator.of(context, rootNavigator: true).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isEndingTask) {
      // Display a loading indicator while ending the task
      return AlertDialog(
        content: Container(
          height: 150,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Ending Task...',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: ColorHelper.thePunchGray(),
                ),
              ),
              SizedBox(height: 20),
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  ColorHelper.thePunchBlue(),
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      // Confirmation dialog before ending the task
      return AlertDialog(
        title: Text(
          'End Task',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: ColorHelper.thePunchRed(),
              ),
        ),
        content: Text(
          'Would you like to end the current task?',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: ColorHelper.thePunchLightGray(),
              ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('No'),
          ),
          TextButton(
            onPressed: _confirmEndTask,
            child: Text('Yes'),
          ),
        ],
      );
    }
  }
}
