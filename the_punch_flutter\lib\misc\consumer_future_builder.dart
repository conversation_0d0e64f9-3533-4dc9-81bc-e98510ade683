import 'package:flutter/widgets.dart';
import 'package:provider/provider.dart';

class ConsumerFutureBuilder<M, N> extends StatelessWidget {
  final Future<N> Function(M) future;
  final Widget Function(BuildContext, N?) builder;

  const ConsumerFutureBuilder({super.key, required this.future, required this.builder});

  @override
  Widget build(BuildContext context) => Consumer<M>(
        builder: (context, value, child) => FutureBuilder<N>(
          future: Future(() async => await future(value)),
          builder: (context, snapshot) => builder(context, snapshot.data),
        ),
      );
}
