// import 'dart:math';
// import 'dart:typed_data';

// import 'package:flutter/widgets.dart';
// import 'package:pdf/pdf.dart' as pw;
// import 'package:pdf/widgets.dart' as pw;
// import 'package:printing/printing.dart' as pw;
// import 'package:the_punch_flutter/api/requests/reportEmployeeHours.dart';
// import 'package:the_punch_flutter/dataModel/dataModel.dart';
// import 'package:the_punch_flutter/misc/appLocalization.dart';
// import 'package:the_punch_flutter/pdf/pdfGenerator.dart';
// import 'package:the_punch_flutter/misc/extensions.dart';
// import 'package:basics/basics.dart';
// import 'package:collection/collection.dart';

// class ReportEmployeeHoursGenerator implements PdfGenerator {
//   final String dateRange;
//   List<ReportEmployeeHoursEntity> entities;
//   BuildContext context;

//   ReportEmployeeHoursGenerator({required this.context, required this.dateRange, required this.entities});

//   static const headerHeight = 30.0;
//   static const rowHeight = 20.0;

//   @override
//   Future<Uint8List> buildPdf(pw.PdfPageFormat pageFormat) async {
//     final openSansRegular = await pw.fontFromAssetBundle('assets/OpenSans-Regular.ttf');
//     final openSansBold = await pw.fontFromAssetBundle('assets/OpenSans-Bold.ttf');
//     final themeData = pw.ThemeData.withFont(base: openSansRegular, bold: openSansBold);

//     final rows = await _buildRows();

//     final rowsPerPages = <List<pw.TableRow>>[];
//     final rowCountPerPage = (pageFormat.height - pageFormat.marginTop - pageFormat.marginBottom - pageFormat.height / headerHeight - 30) ~/ rowHeight;
//     for (var i = 0; i < rows.length / rowCountPerPage; i++) {
//       rowsPerPages.add(rows.sublist(i * rowCountPerPage, min((i + 1) * rowCountPerPage, rows.length)));
//     }

//     final doc = pw.Document();
//     for (var i in rowsPerPages.length.range) {
//       final pageRows = rowsPerPages[i];
//       doc.addPage(pw.Page(
//         pageFormat: pageFormat,
//         theme: themeData,
//         build: (pwContext) => pw.Stack(children: [
//           pw.Align(alignment: pw.Alignment.topRight, child: pw.Text('Page ${i + 1} of ${rowsPerPages.length}')),
//           pw.Column(crossAxisAlignment: pw.CrossAxisAlignment.center, mainAxisAlignment: pw.MainAxisAlignment.start, children: [
//             pw.Text(AppLocalization.of(context).reportLocationsHoursWorked, style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold)),
//             pw.Align(alignment: pw.Alignment.centerLeft, child: pw.Text(dateRange)),
//             _buildTable(pageRows),
//           ]),
//         ]),
//       ));
//     } // Page

//     return doc.save();
//   }

//   pw.Table _buildTable(List<pw.TableRow> rows) {
//     return pw.Table(columnWidths: {
//       0: pw.FractionColumnWidth(2.5),
//       1: pw.FractionColumnWidth(2.5),
//       2: pw.FractionColumnWidth(1),
//       3: pw.FractionColumnWidth(1),
//       4: pw.FractionColumnWidth(1),
//       5: pw.FractionColumnWidth(1),
//       6: pw.FractionColumnWidth(1),
//       7: pw.FractionColumnWidth(1),
//       8: pw.FractionColumnWidth(1),
//       9: pw.FractionColumnWidth(1),
//     }, children: [
//       _headerRow(),
//       ...rows,
//     ]);
//   }

//   pw.TableRow _headerRow() {
//     final style = pw.TextStyle(fontSize: 10, color: pw.PdfColors.black, fontWeight: pw.FontWeight.bold);

//     final headerText = [
//       AppLocalization.of(context).employeeName,
//       AppLocalization.of(context).locationName,
//       'Hours Worked',
//       'Overtime Worked',
//       'Total Hours',
//       AppLocalization.of(context).payRate,
//       'Regular Labor Cost',
//       'Overtime Labor Cost',
//       'Total Labor Cost',
//     ];
//     return pw.TableRow(children: [
//       for (var text in headerText)
//         pw.Container(
//           height: headerHeight,
//           decoration: pw.BoxDecoration(color: pw.PdfColors.lightBlue),
//           child: pw.Text(text, style: style, textAlign: pw.TextAlign.center),
//         ),
//     ]);
//   }

//   Future<List<pw.TableRow>> _buildRows() async {
//     var userIds = entities.map((e) => e.userId).toSet();
//     var users = await DataModel().userModel.getByIds(userIds);
//     var userMap = {for (var e in users) e.id: e};
//     var locationIds = entities.map((e) => e.locationId).toSet();
//     var locations = await DataModel().locationModel.getByIds(locationIds);
//     var locationMap = {for (var e in locations) e.id: e};
//     final rows = <pw.TableRow>[];

//     var locationEntities = entities.groupListsBy((e) => e.userId).values;
//     for (var e in locationEntities) {
//       // first row gets employee name
//       rows.add(_normalRow([
//         userMap[e.first.userId]?.name ?? '',
//         locationMap[e.first.locationId]?.name ?? '',
//         e.first.regular == Duration.zero ? '' : e.first.regular.toFormatted,
//         e.first.overtime == Duration.zero ? '' : e.first.overtime.toFormatted,
//         e.first.totalHours == Duration.zero ? '' : e.first.totalHours.toFormatted,
//         e.first.payRate == 0.0 ? '' : e.first.payRate.toCurrency,
//         e.first.regularCost == 0 ? '' : e.first.regularCost.toCurrency,
//         e.first.overtimeCost == 0 ? '' : e.first.overtimeCost.toCurrency,
//         e.first.totalCost == 0.0 ? '' : e.first.totalCost.toCurrency,
//       ]));

//       // remaining rows have no name
//       for (var e0 in e.skip(1)) {
//         rows.add(_normalRow([
//           '',
//           locationMap[e0.locationId]?.name ?? '',
//           e0.regular == Duration.zero ? '' : e0.regular.toFormatted,
//           e0.overtime == Duration.zero ? '' : e0.overtime.toFormatted,
//           e0.totalHours == Duration.zero ? '' : e0.totalHours.toFormatted,
//           e0.payRate == 0 ? '' : e0.payRate.toCurrency,
//           e0.regularCost == 0 ? '' : e0.regularCost.toCurrency,
//           e0.overtimeCost == 0 ? '' : e0.overtimeCost.toCurrency,
//           e0.totalCost == 0 ? '' : e0.totalCost.toCurrency,
//         ]));
//       }

//       // last row is totals
//       var totalRegular = Duration(minutes: e.sum((f) => f.regular.inMinutes).toInt());
//       var totalOvertime = Duration(minutes: e.sum((f) => f.overtime.inMinutes).toInt());
//       var totalHours = Duration(minutes: e.sum((f) => f.totalHours.inMinutes).toInt());
//       var totalRegularCost = e.sum((f) => f.regularCost).toDouble();
//       var totalOvertimeCost = e.sum((f) => f.overtimeCost).toDouble();
//       var totalCost = e.sum((f) => f.totalCost).toDouble();
//       rows.add(_totalRow([
//         totalRegular == Duration.zero ? '' : totalRegular.toFormatted,
//         totalOvertime == Duration.zero ? '' : totalOvertime.toFormatted,
//         totalHours == Duration.zero ? '' : totalHours.toFormatted,
//         totalRegularCost == 0 ? '' : totalRegularCost.toCurrency,
//         totalOvertimeCost == 0 ? '' : totalOvertimeCost.toCurrency,
//         totalCost == 0 ? '' : totalCost.toCurrency,
//       ]));
//     }

//     // final row is grand totals
//     var totalRegular = Duration(minutes: entities.sum((f) => f.regular.inMinutes).toInt());
//     var totalOvertime = Duration(minutes: entities.sum((f) => f.overtime.inMinutes).toInt());
//     var totalHours = Duration(minutes: entities.sum((f) => f.totalHours.inMinutes).toInt());
//     var totalRegularCost = entities.sum((f) => f.regularCost).toDouble();
//     var totalOvertimeCost = entities.sum((f) => f.overtimeCost).toDouble();
//     var totalCost = entities.sum((f) => f.totalCost).toDouble();

//     rows.add(_grandTotalRow([
//       totalRegular == Duration.zero ? '' : totalRegular.toFormatted,
//       totalOvertime == Duration.zero ? '' : totalOvertime.toFormatted,
//       totalHours == Duration.zero ? '' : totalHours.toFormatted,
//       totalRegularCost == 0 ? '' : totalRegularCost.toCurrency,
//       totalOvertimeCost == 0 ? '' : totalOvertimeCost.toCurrency,
//       totalCost == 0 ? '' : totalCost.toCurrency,
//     ]));

//     return rows;
//   }

//   pw.TableRow _normalRow(List<String> cells) {
//     final style = pw.TextStyle(fontSize: 8, color: pw.PdfColors.black);
//     return pw.TableRow(children: [
//       for (var cell in cells.take(2))
//         pw.Container(
//           height: rowHeight,
//           child: pw.Align(alignment: pw.Alignment.center, child: pw.Text(cell, style: style)),
//         ),
//       for (var cell in cells.skip(2).take(3))
//         pw.Container(
//           height: rowHeight,
//           child: pw.Align(alignment: pw.Alignment.centerRight, child: pw.Text(cell, style: style)),
//         ),
//       pw.Container(
//         height: rowHeight,
//         child: pw.Align(alignment: pw.Alignment.center, child: pw.Text(cells[5], style: style)),
//       ),
//       for (var cell in cells.skip(6))
//         pw.Container(
//           height: rowHeight,
//           child: pw.Align(alignment: pw.Alignment.centerRight, child: pw.Text(cell, style: style)),
//         ),
//     ]);
//   }

//   pw.TableRow _totalRow(List<String> cells) {
//     final style = pw.TextStyle(fontSize: 8, color: pw.PdfColors.black);
//     final boldStyle = style.copyWith(fontWeight: pw.FontWeight.bold);
//     final decoration = pw.BoxDecoration(border: pw.Border(bottom: pw.BorderSide()));
//     return pw.TableRow(children: [
//       pw.Container(),
//       pw.Container(
//         height: rowHeight,
//         decoration: decoration,
//         child: pw.Align(alignment: pw.Alignment.centerRight, child: pw.Text('Employee Total', style: boldStyle)),
//       ),
//       for (var cell in cells.take(3))
//         pw.Container(
//           height: rowHeight,
//           decoration: decoration,
//           child: pw.Align(alignment: pw.Alignment.centerRight, child: pw.Text(cell, style: boldStyle)),
//         ),
//       pw.Container(height: rowHeight, decoration: decoration),
//       for (var cell in cells.skip(3))
//         pw.Container(
//           height: rowHeight,
//           decoration: decoration,
//           child: pw.Align(alignment: pw.Alignment.centerRight, child: pw.Text(cell, style: boldStyle)),
//         ),
//     ]);
//   }

//   pw.TableRow _grandTotalRow(List<String> cells) {
//     final style = pw.TextStyle(fontSize: 8, color: pw.PdfColors.black);
//     final boldStyle = style.copyWith(fontWeight: pw.FontWeight.bold);
//     return pw.TableRow(children: [
//       pw.Container(
//         height: rowHeight,
//         child: pw.Align(alignment: pw.Alignment.centerRight, child: pw.Text('Final Total', style: boldStyle)),
//       ),
//       pw.Container(),
//       for (var cell in cells.take(3))
//         pw.Container(
//           height: rowHeight,
//           child: pw.Align(alignment: pw.Alignment.centerRight, child: pw.Text(cell, style: boldStyle)),
//         ),
//       pw.Container(),
//       for (var cell in cells.skip(3))
//         pw.Container(
//           height: rowHeight,
//           child: pw.Align(alignment: pw.Alignment.centerRight, child: pw.Text(cell, style: boldStyle)),
//         ),
//     ]);
//   }
// }
