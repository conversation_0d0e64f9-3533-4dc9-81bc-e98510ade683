import 'dart:async';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../helpers/color_helper.dart';
import 'extensions.dart';
import 'closest_date.dart';
import '../helpers/screen_helper.dart';
class DateScroller extends StatefulWidget {
  final Map<DateTime, int>? pipsByDate;
  final DateTime initialDate;
  final ValueNotifier<DateTime> selectedDate;

  DateScroller(
      {super.key,
      required this.initialDate,
      required this.selectedDate,
      Map<DateTime, int>? pipsByDate})
      : pipsByDate = pipsByDate?.map<DateTime, int>(
            (key, value) => MapEntry(key.forceLocal, value));
  @override
  _DateScrollerState createState() => _DateScrollerState();
}

class _DateScrollerState extends State<DateScroller> {
final List<DateTime> _dates = List.generate(
  30,
  (index) {
    DateTime date = DateTime.now().subtract(Duration(days: 15 - index));
    return DateTime(date.year, date.month, date.day); // Ensures time is 00:00:00
  },
);
 
  final ScrollController _scrollController = ScrollController();
  final Map<int, GlobalKey> _itemKeys = {};
  final Map<int, double> _itemPositions = {}; // Store item positions dynamically
  
  static const initialPage = 1073741824;
  final pageController = PageController(initialPage: initialPage);
 int _selectedIndex = 15;

  @override
  void initState() {
    super.initState();
    for (int i = 0; i < _dates.length; i++) {
      _itemKeys[i] = GlobalKey();
    }
    print('VIEW LIST OF DATES  ${widget.initialDate}  ${widget.selectedDate} ${widget.pipsByDate?.entries}');
    _scrollController.addListener(_updateItemPositions);
    //_scrollToSelectedIndex();
      // Wait until the first frame is rendered
    WidgetsBinding.instance.addPostFrameCallback((_) {
    _scrollToSelectedIndex(); 
    
    });


  }
  

  @override
  void dispose() {
   // _scrollController.removeListener(_updateItemPositions);
   // _scrollController.dispose();
    super.dispose();
  }
  void _scrollToSelectedIndex() {
    print('Scrolling to selected index');
    // Jump to the middle of the list when the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollController.jumpTo((_scrollController.position.maxScrollExtent / 2) + 30);
      print('VIEW SELECTED CHANGE ${widget.selectedDate.value}');
      print('VIEW 1 ${widget.pipsByDate}');
      if(!widget.pipsByDate!.containsKey(widget.selectedDate.value)){
        print('VIEW 2 ${widget.selectedDate.value}');
        _selectedIndex = _dates.indexOf(widget.pipsByDate!.keys.first);
        print('VIEW 3 $_selectedIndex');
      }
     
    });
  
  }
  void _updateItemPositions() {
    if (!mounted) return;
    setState(() {
      
      for (int index in _itemKeys.keys) {
        final key = _itemKeys[index];
        if (key == null) continue;

        final renderBox = key.currentContext?.findRenderObject() as RenderBox?;
        if (renderBox != null) {
          final position = renderBox.localToGlobal(Offset.zero);
          _itemPositions[index] = position.dx;
        }
      }
    });
  }
  bool _isPartiallyOffscreen(int index) {
    if (!_itemPositions.containsKey(index)) return false;

    double dx = _itemPositions[index]!;
    double screenWidth = MediaQuery.of(context).size.width;

    return dx < 0 || dx > screenWidth - 50; // 50 is the assumed item width
  }

  @override
  Widget build(BuildContext context) =>Column(
            children: [
              Text(
                DateFormat.yMMMM()
                    .format(DateTime.now()), // Displays current month and year
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              const SizedBox(height: 10),
              Container(
                height:90,
                child: ListView.builder(
                  controller: _scrollController,
                  scrollDirection: Axis.horizontal,
                  
                  itemCount: _dates.length,
                  itemBuilder: (context, index) {
                    bool isEnabled = widget.pipsByDate?.containsKey(_dates[index]) ?? true;
                    //bool isSelected = index == _selectedIndex;
                    //bool isOffscreen = _isPartiallyOffscreen(index);                    
                    
                    //bool isEnabled = widget.pipsByDate?.containsKey(_dates[index]) ?? false;
                    print('VIEW ${widget.pipsByDate}  $isEnabled');
                    bool isSelected = index == _selectedIndex ;
                    bool isOffscreen = _isPartiallyOffscreen(index);
                    //print('VIEW _date: ${_dates[index]} enabled: $isEnabled selected; $_selectedIndex  Index:  $index, isSelected: $isSelected   ${widget.selectedDate} ${widget.pipsByDate} ${widget.selectedDate} ');
                    //if(isEnabled && isSelected)
                    //print ('VIEW $index $_selectedIndex $isEnabled $isSelected ');
                    return 
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          
                        if (isEnabled){
                          _selectedIndex =  index;
                          widget.selectedDate.value = _dates[index];
                        }
                        });
                      },
                    child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
 
                          Text(
                            DateFormat.E().format(
                                _dates[index])[0], // One-letter day abbreviation
                              style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: isSelected
                                  ? ColorHelper.thePunchBlueGray()
                                  : Colors.grey,
                            ),
                          ),
                          const SizedBox(height: 5),
                          Container(
                            key: _itemKeys[index], // Assign key to track position
                            width: isSelected ? 50 : 40,
                            height: isSelected ? 50 : 40,
                            margin: const EdgeInsets.symmetric(horizontal: 8),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? ColorHelper.thePunchBlueGray()
                                  : isEnabled? ColorHelper.thePunchLightBlue(): Colors.white,
                              borderRadius: BorderRadius.circular(25),
                              border: Border.all(
                                color: isSelected
                                    ? ColorHelper.thePunchBlueGray()
                                    : ColorHelper.thePunchLighterGray(),
                              ),
                            ),
                            alignment: Alignment.center,
                            child: Text(
                              _dates[index].day.toString(),
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: isSelected
                                    ? Colors.white
                                    : isOffscreen
                                        ? Colors.black54
                                        : ColorHelper.thePunchBlueGray(),
                              ),
                            ),
                          ),
                          // if (isOffscreen) // Debug indicator for offscreen items
                          //   const Icon(Icons.warning, color: Colors.red, size: 16),
                        

                        ],
                      ),
                    );
                  
                  },
                ),
              ),

            ],
          );

  


  PageView buildPageView() => PageView.builder(
        controller: pageController,
        itemBuilder: (context, index) {
          final offset = index - initialPage;
          final newDate = widget.initialDate.addDays(7 * offset);

          final dates = [for (var i = 0; i < 7; i += 1) newDate.addDays(i)];

          return Flex(
            direction: Axis.horizontal,
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              for (final date in dates)
                Flexible(
                  child: _DateBarTile(
                    date: date,
                    selectedDate: widget.selectedDate,
                    isEnabled: (widget.pipsByDate?.containsKey(date) ?? true),
                    pips: widget.pipsByDate?[date] ?? 0,
                  ),
                ),
            ],
          );
        },
      );


}


class _DateBarTile extends StatelessWidget {
  final bool isEnabled;
  final DateTime date;
  final ValueNotifier<DateTime> selectedDate;
  final int pips;

  const _DateBarTile({
    required this.date,
    required this.selectedDate,
    this.pips = 0,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onPrimary = theme.colorScheme.onPrimary;

    return GestureDetector(
      onTap: () {
        if (isEnabled) selectedDate.value = date;
      },
      child: ConstrainedBox(
          constraints: const BoxConstraints(minWidth: 25),
          child: Column(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(25),
                child: ValueListenableBuilder<DateTime>(
                  valueListenable: selectedDate,
                  builder: (context, selectedDate, child) {
                    final isSelected = selectedDate == date;
                    final onPrimaryDisabled = onPrimary.withOpacity(0.5);
                    final color = isSelected
                        ? onPrimary
                        : ColorHelper.thePunchLightGray();

                    final Color textColor;
                    if (isSelected) {
                      textColor = ColorHelper.thePunchGray();
                    } else if (isEnabled) {
                      textColor = Colors.white;
                    } else {
                      textColor = onPrimaryDisabled;
                    }
                    final textStyle = theme.textTheme.bodySmall
                        ?.copyWith(color: textColor, height: 1);

                    final languageCode =
                        Localizations.localeOf(context).toLanguageTag();
                    final dow =
                        DateFormat(DateFormat.ABBR_WEEKDAY, languageCode)
                            .format(date);
                    final day =
                        DateFormat(DateFormat.DAY, languageCode).format(date);
                    final month =
                        DateFormat(DateFormat.ABBR_MONTH, languageCode)
                            .format(date);

                    return ColoredBox(
                      color: color,
                      child: Padding(
                        padding: const EdgeInsets.only(
                            top: 8, bottom: 8, left: 4, right: 4),
                        child: IntrinsicWidth(
                          child: Flex(
                              direction: Axis.vertical,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(dow, style: textStyle),
                                Text(day, style: textStyle),
                                Text(month, style: textStyle),
                              ]),
                        ),
                      ),
                    );
                  },
                ),
              ),
              _Pips(pips: pips),
            ],
          )),
    );
  }
}

class _Pips extends StatelessWidget {
  const _Pips({required this.pips});

  final int pips;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onPrimary = theme.colorScheme.onPrimary;
    final caption = theme.textTheme.bodySmall
        ?.copyWith(color: onPrimary, fontSize: 10, height: .75);

    final Widget pipsWidget;
    if (pips <= 5) {
      pipsWidget = Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          for (var i = 0; i < pips && i < 5; i++)
            Icon(Icons.circle, size: 5, color: onPrimary)
        ],
      );
    } else {
      pipsWidget = Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Icon(Icons.more_horiz, size: 5, color: onPrimary),
          Text(pips.toString(), style: caption),
          Icon(Icons.more_horiz, size: 5, color: onPrimary),
        ],
      );
    }
    return ConstrainedBox(
      constraints: const BoxConstraints(minHeight: 10),
      child: Padding(
        padding: const EdgeInsets.only(top: 3),
        child: pipsWidget,
      ),
    );
  }
}
