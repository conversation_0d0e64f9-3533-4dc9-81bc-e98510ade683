import 'package:flutter/widgets.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../api/sync_model.dart';
import '../misc/extensions.dart';
import '../misc/json_conversion.dart';
import '../state/login_state.dart';
import '../state/server_time_state.dart';

abstract class BaseData {
  abstract String id;

  @HiveField(0)
  @JsonKey(ignore: true)
  bool isDirty;

  @HiveField(1)
  @Json<PERSON>ey(defaultValue: true)
  bool isActive;

  @HiveField(2)
  @JsonKey(toJson: dateTimeToJson, fromJson: dateTimeFromJson)
  DateTime createdOn;

  @HiveField(3)
  String? createdByUserId;

  @HiveField(4)
  @JsonKey(toJson: nullableDateTimeToJson, fromJson: nullableDateTimeFromJson)
  DateTime? lastChangedOn;

  @HiveField(5)
  String? lastChangedByUserId;

  BaseData({
    this.isDirty = false,
    this.isActive = true,
    required this.createdByUserId,
    required this.createdOn,
    this.lastChangedOn,
    this.lastChangedByUserId,
  });
}

abstract class BaseDataModel<T extends BaseData> extends ChangeNotifier {
  Future<Iterable<T>> get all;

  Future<Iterable<T>> get active async => (await all).where((e) => (e.isActive));

  Future<List<T>> get dirty async => (await all).where((e) => (e.isDirty)).toList();

  Future<Iterable<T>> getByIds(Iterable<String> ids) async {
    if (ids.isEmpty) return [];
    return (await all).where((e) => ids.contains(e.id));
  }

  Future<T?> getById(String id) async => (await getByIds([id])).firstOrNull;

  Future<void> save(Iterable<T> t);

  Future<void> saveDirty(Iterable<T> t) async {
    if (t.isEmpty) return;
    for (final e in t) {
      e.isDirty = true;
      e.lastChangedOn = ServerTimeState().utcTime;
      e.lastChangedByUserId = LoginState.userId;
    }
    await save(t);
    await SyncModel().sync();
  }

  Future<bool> saveClean(Iterable<T>? t, {DateTime? sendStarted}) async {
    if (t == null || t.isEmpty) return true;
    if (sendStarted != null) {
      final unchanged = await getUnchangedSince(t, sendStarted);
      for (final e in unchanged) {
        e.isDirty = false;
      }
      await save(unchanged);
      return t.length == unchanged.length;
    } else {
      await save(t);
      return true;
    }
  }

  Future<void> activate(Iterable<T> t) {
    for (final e in t) {
      e.isActive = true;
    }
    return saveDirty(t);
  }

  Future<void> deactivate(Iterable<T> t) {
    for (final e in t) {
      e.isActive = false;
    }
    return saveDirty(t);
  }

  Future<Iterable<T>> getUnchangedSince(Iterable<T> t, DateTime dateTime) async {
    final ids = t.map((e) => e.id);
    return (await all).where((e) => ids.contains(e.id)).where((e) => e.lastChangedOn == null || e.lastChangedOn! <= dateTime);
  }
}
