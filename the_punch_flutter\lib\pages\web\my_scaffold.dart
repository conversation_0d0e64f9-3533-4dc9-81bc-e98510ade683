import 'package:flutter/material.dart';

import '../../helpers/screen_helper.dart';
import '../../menus/my_app_bar.dart';
import '../../menus/my_bottom_bar.dart';
import '../../misc/my_platform.dart';
import 'home/desktop_header.dart';

class MyScaffold extends StatelessWidget {
  const MyScaffold({
    super.key,
    required this.title,
    this.titleWidget,               // <-- New optional widget param
    this.floatingActionButton,
    this.enableBottomBar,
    required this.body,
    this.showLoggedOutDrawer = false,
    this.showTitle = false,
    this.showBackButton = false,
    this.showDesktopHeader = true,
    this.showDrawer = true,
    this.hideBar = false,
    //this.totalMembers = 6,
    this.showNotificationButton = false,
  });

  /// Existing String title (required for backward compatibility).
  final String title;

  /// **New** optional Widget for an interactive/title.
  /// If provided, this takes precedence in `MyAppBar`.
  final Widget? titleWidget;

  final Widget? floatingActionButton;
  final bool? enableBottomBar;
  final Widget body;
  final bool showLoggedOutDrawer;
  final bool showTitle;
  final bool showBackButton;
  final bool showDesktopHeader;
  //final int totalMembers;
  final bool showDrawer;
  final bool hideBar;
  final bool showNotificationButton;

  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, constraints) {
          final bottomBarEnabled =
              enableBottomBar ?? MyPlatform.isHandheld;

          return Scaffold(
            backgroundColor: Colors.white,
            appBar: !hideBar
                ? MyAppBar(
                    context: context,
                    title: title,
                    titleWidget: titleWidget, // <-- Pass it here
                    showTitle: showTitle,
                    showBackButton: showBackButton,
                    //totalMembers: totalMembers,
                    showSubTitle: true,
                    hideBar: hideBar,
                    showNotificationButton: showNotificationButton,
                  ).appBar
                : null,
            drawer: showDrawer
                ? (showLoggedOutDrawer
                    ? LoggedOutDrawer().drawer
                    : MyDrawer().drawer)
                : null,
            floatingActionButton: floatingActionButton,
            body: constraints.maxWidth < 1024
                ? body
                : Row(
                    children: [
                      if (showDrawer)
                        showLoggedOutDrawer
                            ? LoggedOutDrawer().drawer!
                            : MyDrawer().drawer!,
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (showDesktopHeader) const DesktopHeader(showSalutation: false,),
                            Expanded(child: body),
                          ],
                        ),
                      ),
                    ],
                  ),
            bottomNavigationBar:
                bottomBarEnabled ? MyBottomBar().bottomBar : null,
          );
        },
      );
}
