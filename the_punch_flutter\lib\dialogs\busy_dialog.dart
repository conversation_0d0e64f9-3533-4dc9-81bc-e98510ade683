import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'constrained_dialog.dart';

class BusyDialog extends StatefulWidget {
  final Future<void> Function()? future;
  final String? subTitle;

  const BusyDialog({super.key, required this.future, this.subTitle});

  @override
  State<BusyDialog> createState() => _BusyDialogState();
}

class _BusyDialogState extends State<BusyDialog> {
  Future<void> Function()? future;

  @override
  void initState() {
    future = widget.future;
    super.initState();
  }

  @override
  Widget build(BuildContext context) => FutureBuilder<void>(
      future: Future<void>(() async {
        final future = this.future;
        this.future = null;
        if (future != null) {
          await future();
          if (!mounted) return;
          Navigator.of(context).pop();
        }
      }),
      builder: (context, snapshot) => ConstrainedDialog(
          title: AppLocalizations.of(context)!.busy,
          subTitle: widget.subTitle,
          child: const Padding(
            padding: EdgeInsets.all(8),
            child: CircularProgressIndicator(),
          ),
        ),
    );
}
