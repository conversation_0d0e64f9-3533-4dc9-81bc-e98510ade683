import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../base_data.dart';
import 'location.dart';
import '../../misc/app_localization.dart';
import '../../misc/json_conversion.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import '../../misc/extensions.dart';
import 'package:timezone/standalone.dart' as tz;
import 'package:uuid/uuid.dart';

part 'schedule_template.g.dart';

@HiveType(typeId: 22)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class ScheduleTemplate extends BaseData {
  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  @JsonKey(name: 'Recurring_Frequency_ID')
  int recurringFrequencyId;

  @HiveField(102)
  @JsonKey(name: 'Recurring_Frequency_EveryNFreq')
  int recurringFrequencyEveryNFreq;

  @HiveField(103)
  @JsonKey(name: 'Recurring_Frequency_Weekly_OnMonday')
  bool recurringFrequencyWeeklyOnMonday;

  @HiveField(104)
  @JsonKey(name: 'Recurring_Frequency_Weekly_OnTuesday')
  bool recurringFrequencyWeeklyOnTuesday;

  @HiveField(105)
  @JsonKey(name: 'Recurring_Frequency_Weekly_OnWednesday')
  bool recurringFrequencyWeeklyOnWednesday;

  @HiveField(106)
  @JsonKey(name: 'Recurring_Frequency_Weekly_OnThursday')
  bool recurringFrequencyWeeklyOnThursday;

  @HiveField(107)
  @JsonKey(name: 'Recurring_Frequency_Weekly_OnFriday')
  bool recurringFrequencyWeeklyOnFriday;

  @HiveField(108)
  @JsonKey(name: 'Recurring_Frequency_Weekly_OnSaturday')
  bool recurringFrequencyWeeklyOnSaturday;

  @HiveField(109)
  @JsonKey(name: 'Recurring_Frequency_Weekly_OnSunday')
  bool recurringFrequencyWeeklyOnSunday;

  @HiveField(111)
  @JsonKey(name: 'Recurring_Frequency_Monthly_OccursOnSpecificMonth')
  bool recurringFrequencyMonthlyOccursOnSpecificMonth;

  @HiveField(112)
  @JsonKey(name: 'Recurring_Frequency_Monthly_OnMonth')
  int recurringFrequencyMonthlyOnMonth;

  @HiveField(113)
  @JsonKey(name: 'Recurring_Frequency_Monthly_OccursOnSpecificDay')
  bool recurringFrequencyMonthlyOccursOnSpecificDay;

  @HiveField(114)
  @JsonKey(name: 'Recurring_Frequency_Monthly_OnDay')
  int recurringFrequencyMonthlyOnDay;

  @HiveField(115)
  @JsonKey(name: 'Recurring_Frequency_Monthly_TheWeek_ID')
  int recurringFrequencyMonthlyTheWeekId;

  @HiveField(116)
  @JsonKey(name: 'Recurring_Frequency_Monthly_TheDayOfTheWeek_ID')
  int recurringFrequencyMonthlyTheDayOfTheWeekId;

  @HiveField(117)
  @JsonKey(fromJson: idFromJson)
  String userId;

  @HiveField(118)
  @JsonKey(fromJson: idFromJson)
  String locationId;

  @HiveField(119)
  @JsonKey(fromJson: durationFromJson, toJson: durationToJson)
  Duration duration;

  @HiveField(120)
  @JsonKey(fromJson: dateTimeFromJson, toJson: dateTimeToJson)
  DateTime startDateLocal;

  @HiveField(121)
  @JsonKey(fromJson: nullableDateTimeFromJson, toJson: nullableDateTimeToJson)
  DateTime? endDateLocal;

  ScheduleTemplate({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.recurringFrequencyId,
    required this.recurringFrequencyEveryNFreq,
    required this.recurringFrequencyWeeklyOnMonday,
    required this.recurringFrequencyWeeklyOnTuesday,
    required this.recurringFrequencyWeeklyOnWednesday,
    required this.recurringFrequencyWeeklyOnThursday,
    required this.recurringFrequencyWeeklyOnFriday,
    required this.recurringFrequencyWeeklyOnSaturday,
    required this.recurringFrequencyWeeklyOnSunday,
    required this.recurringFrequencyMonthlyOccursOnSpecificMonth,
    required this.recurringFrequencyMonthlyOnMonth,
    required this.recurringFrequencyMonthlyOccursOnSpecificDay,
    required this.recurringFrequencyMonthlyOnDay,
    required this.recurringFrequencyMonthlyTheWeekId,
    required this.recurringFrequencyMonthlyTheDayOfTheWeekId,
    required this.userId,
    required this.locationId,
    required this.duration,
    required this.startDateLocal,
    this.endDateLocal,
  });

  factory ScheduleTemplate.fromJson(Map<String, dynamic> json) {
    try {
      return _$ScheduleTemplateFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() => _$ScheduleTemplateToJson(this);

  factory ScheduleTemplate.from(ScheduleTemplate o) => ScheduleTemplate(
        isDirty: o.isDirty,
        isActive: o.isActive,
        createdOn: o.createdOn,
        createdByUserId: o.createdByUserId,
        lastChangedOn: o.lastChangedOn,
        lastChangedByUserId: o.lastChangedByUserId,
        id: o.id,
        recurringFrequencyId: o.recurringFrequencyId,
        recurringFrequencyEveryNFreq: o.recurringFrequencyEveryNFreq,
        recurringFrequencyWeeklyOnSunday: o.recurringFrequencyWeeklyOnSunday,
        recurringFrequencyWeeklyOnMonday: o.recurringFrequencyWeeklyOnMonday,
        recurringFrequencyWeeklyOnTuesday: o.recurringFrequencyWeeklyOnTuesday,
        recurringFrequencyWeeklyOnWednesday: o.recurringFrequencyWeeklyOnWednesday,
        recurringFrequencyWeeklyOnThursday: o.recurringFrequencyWeeklyOnThursday,
        recurringFrequencyWeeklyOnFriday: o.recurringFrequencyWeeklyOnFriday,
        recurringFrequencyWeeklyOnSaturday: o.recurringFrequencyWeeklyOnSaturday,
        recurringFrequencyMonthlyOccursOnSpecificMonth: o.recurringFrequencyMonthlyOccursOnSpecificMonth,
        recurringFrequencyMonthlyOnMonth: o.recurringFrequencyMonthlyOnMonth,
        recurringFrequencyMonthlyOccursOnSpecificDay: o.recurringFrequencyMonthlyOccursOnSpecificDay,
        recurringFrequencyMonthlyOnDay: o.recurringFrequencyMonthlyOnDay,
        recurringFrequencyMonthlyTheWeekId: o.recurringFrequencyMonthlyTheWeekId,
        recurringFrequencyMonthlyTheDayOfTheWeekId: o.recurringFrequencyMonthlyTheDayOfTheWeekId,
        userId: o.userId,
        locationId: o.locationId,
        duration: o.duration,
        startDateLocal: o.startDateLocal,
        endDateLocal: o.endDateLocal);

  factory ScheduleTemplate.create() {
    final now = DateTime.now();
    final startDateLocal = DateTime.utc(now.year, now.month, now.day, now.hour, (now.minute ~/ 15) * 15);
    return ScheduleTemplate(
      id: const Uuid().v4(),
      createdOn: ServerTimeState().utcTime,
      createdByUserId: LoginState.userId,
      recurringFrequencyId: 2,
      recurringFrequencyEveryNFreq: 1,
      recurringFrequencyWeeklyOnMonday: false,
      recurringFrequencyWeeklyOnTuesday: false,
      recurringFrequencyWeeklyOnWednesday: false,
      recurringFrequencyWeeklyOnThursday: false,
      recurringFrequencyWeeklyOnFriday: false,
      recurringFrequencyWeeklyOnSaturday: false,
      recurringFrequencyWeeklyOnSunday: false,
      recurringFrequencyMonthlyOccursOnSpecificMonth: false,
      recurringFrequencyMonthlyOccursOnSpecificDay: false,
      recurringFrequencyMonthlyTheWeekId: 4,
      recurringFrequencyMonthlyTheDayOfTheWeekId: 11,
      recurringFrequencyMonthlyOnMonth: 1,
      recurringFrequencyMonthlyOnDay: 1,
      userId: '',
      locationId: '',
      duration: const Duration(hours: 1),
      startDateLocal: startDateLocal,
    );
  }

  String displayName(BuildContext context, Location? location) {
    final locale = Localizations.localeOf(context);

    final sb = StringBuffer();
    sb.write('${AppLocalization.of(context).repeatEvery} ');
    switch (recurringFrequencyId) {
      case 1: // Day
        if ((recurringFrequencyEveryNFreq) > 1) {
          sb.write('$recurringFrequencyEveryNFreq ');
          sb.write('${AppLocalization.of(context).days} ');
        } else {
          sb.write('${AppLocalization.of(context).day} ');
        }
        break;
      case 2: // Week
        if ((recurringFrequencyEveryNFreq) > 1) {
          sb.write('$recurringFrequencyEveryNFreq ');
          sb.write('${AppLocalization.of(context).weeks} ');
        } else {
          sb.write('${AppLocalization.of(context).week} ');
        }
        if ((recurringFrequencyWeeklyOnMonday) || (recurringFrequencyWeeklyOnTuesday) || (recurringFrequencyWeeklyOnWednesday) || (recurringFrequencyWeeklyOnThursday) || (recurringFrequencyWeeklyOnFriday) || (recurringFrequencyWeeklyOnSaturday) || (recurringFrequencyWeeklyOnSunday)) {
          sb.write('${AppLocalization.of(context).on} ');
        }
        sb.write([
          if (recurringFrequencyWeeklyOnMonday) AppLocalization.of(context).monday,
          if (recurringFrequencyWeeklyOnTuesday) AppLocalization.of(context).tuesday,
          if (recurringFrequencyWeeklyOnWednesday) AppLocalization.of(context).wednesday,
          if (recurringFrequencyWeeklyOnThursday) AppLocalization.of(context).thursday,
          if (recurringFrequencyWeeklyOnFriday) AppLocalization.of(context).friday,
          if (recurringFrequencyWeeklyOnSaturday) AppLocalization.of(context).saturday,
          if (recurringFrequencyWeeklyOnSunday) AppLocalization.of(context).sunday,
        ].join(', '));
        sb.write(' ');
        break;
      case 3: // Month
        if ((recurringFrequencyEveryNFreq) > 1) {
          if (recurringFrequencyMonthlyOccursOnSpecificMonth) {
            sb.write('$recurringFrequencyEveryNFreq ');
            sb.write('${AppLocalization.of(context).years} ');
          } else {
            sb.write('$recurringFrequencyEveryNFreq ');
            sb.write('${AppLocalization.of(context).months} ');
          }
        } else {
          if (recurringFrequencyMonthlyOccursOnSpecificMonth) {
            sb.write('${AppLocalization.of(context).year} ');
          } else {
            sb.write('${AppLocalization.of(context).month} ');
          }
        }
        if (recurringFrequencyMonthlyOccursOnSpecificMonth) {
          sb.write('${AppLocalization.of(context).in0} ');
          switch (recurringFrequencyMonthlyOnMonth) {
            case 1:
              sb.write('${AppLocalization.of(context).january} ');
              break;
            case 2:
              sb.write('${AppLocalization.of(context).february} ');
              break;
            case 3:
              sb.write('${AppLocalization.of(context).march} ');
              break;
            case 4:
              sb.write('${AppLocalization.of(context).april} ');
              break;
            case 5:
              sb.write('${AppLocalization.of(context).may} ');
              break;
            case 6:
              sb.write('${AppLocalization.of(context).june} ');
              break;
            case 7:
              sb.write('${AppLocalization.of(context).july} ');
              break;
            case 8:
              sb.write('${AppLocalization.of(context).august} ');
              break;
            case 9:
              sb.write('${AppLocalization.of(context).september} ');
              break;
            case 10:
              sb.write('${AppLocalization.of(context).october} ');
              break;
            case 11:
              sb.write('${AppLocalization.of(context).november} ');
              break;
            case 12:
              sb.write('${AppLocalization.of(context).december} ');
              break;
          }
          sb.write('${AppLocalization.of(context).onThe} ');
          sb.write(recurringFrequencyMonthlyOnDay.toOrdinal(context));
        } else if ((recurringFrequencyMonthlyOccursOnSpecificDay) && !(recurringFrequencyMonthlyOccursOnSpecificMonth)) {
          sb.write('${AppLocalization.of(context).onThe} ');
          sb.write(recurringFrequencyMonthlyOnDay.toOrdinal(context));
        } else {
          sb.write('${AppLocalization.of(context).onThe} ');
          switch (recurringFrequencyMonthlyTheWeekId) {
            case 4: // First Occurrence
              sb.write('${AppLocalization.of(context).firstOccurrence} ');
              break;
            case 5: // Second Occurrence
              sb.write('${AppLocalization.of(context).secondOccurrence} ');
              break;
            case 6: // Third Occurrence
              sb.write('${AppLocalization.of(context).thirdOccurrence} ');
              break;
            case 7: // Fourth Occurrence
              sb.write('${AppLocalization.of(context).fourthOccurrence} ');
              break;
            case 8: // Last Occurrence
              sb.write('${AppLocalization.of(context).lastOccurrence} ');
              break;
          }
          sb.write('${AppLocalization.of(context).of0} ');
          switch (recurringFrequencyMonthlyTheDayOfTheWeekId) {
            case 11: // Monday
              sb.write('${AppLocalization.of(context).monday} ');
              break;
            case 12: // Tuesday
              sb.write('${AppLocalization.of(context).tuesday} ');
              break;
            case 13: // Wednesday
              sb.write('${AppLocalization.of(context).wednesday} ');
              break;
            case 14: // Thursday
              sb.write('${AppLocalization.of(context).thursday} ');
              break;
            case 15: // Friday
              sb.write('${AppLocalization.of(context).friday} ');
              break;
            case 9: // Saturday
              sb.write('${AppLocalization.of(context).saturday} ');
              break;
            case 10: // Sunday
              sb.write('${AppLocalization.of(context).sunday} ');
              break;
          }
        }
        break;
    }
    sb.writeln();
    if (location != null) {
      var startTime = DateTime.utc(2021, 7, 7, startDateLocal.hour, startDateLocal.minute);
      var endTime = startTime.add(duration);
      sb.write('${AppLocalization.of(context).scheduledTime} ');
      sb.write('${startTime.toFormattedTimeNoLocal(locale)} - ');
      sb.write('${endTime.toFormattedTimeNoLocal(locale)} ');
      sb.write('(${duration.toFormatted})');
      // Get the client local dates and see if they match or not;
      final locationOffset = Duration(milliseconds: tz.getLocation(location.timeZone).currentTimeZone.offset);
      final localOffset = DateTime(2021, 7, 7).timeZoneOffset;
      if (localOffset != locationOffset) {
        startTime = startTime.toUTC(location.timeZone);
        endTime = endTime.toUTC(location.timeZone);
        sb.writeln();
        sb.write('${AppLocalization.of(context).localTime}: ');
        sb.write('${startTime.toFormattedTime(locale)} - ');
        sb.write('${endTime.toFormattedTime(locale)} ');
      }
    } else {
      sb.write('${AppLocalization.of(context).scheduledTime}:');
    }
    final result = sb.toString().trim();
    if (result.isNotEmpty) return result;
    sb.clear();
    sb.writeln(AppLocalization.of(context).noDescription);
    sb.write('${AppLocalization.of(context).scheduledTime}:');
    return sb.toString();
  }
}
