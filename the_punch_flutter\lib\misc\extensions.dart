import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'app_localization.dart';
import 'package:timezone/standalone.dart' as tz;
import 'package:timezone/timezone.dart' as tz;
import 'package:tuple/tuple.dart';

extension IterableExtension<T> on Iterable<T> {
  T? get firstOrNull => isEmpty ? null : first;
}

extension DateTimeExtension on DateTime {
  String toFormattedTime(Locale locale) =>
      DateFormat(DateFormat.HOUR_MINUTE, locale.toLanguageTag())
          .format(toLocal());
  String toFormattedDate(Locale locale) =>
      DateFormat(DateFormat.ABBR_MONTH_DAY, locale.toLanguageTag())
          .format(toLocal());
  String toFormattedDateTime(Locale locale) =>
      '${toFormattedDate(locale)} ${toFormattedTime(locale)}';
  String toFormattedDateTimeWithYear(Locale locale) =>
      '${toFormattedDateWithYear(locale)} ${toFormattedTime(locale)}';
  String toFormattedDateWithYear(Locale locale) =>
      DateFormat(DateFormat.YEAR_ABBR_MONTH_DAY, locale.toLanguageTag())
          .format(toLocal());
  String toLongFormattedDate(Locale locale) =>
      DateFormat(DateFormat.MONTH_WEEKDAY_DAY, locale.toLanguageTag())
          .format(toLocal());
  String toLongFormattedDateWithYear(Locale locale) =>
      DateFormat(DateFormat.YEAR_MONTH_WEEKDAY_DAY, locale.toLanguageTag())
          .format(toLocal());
  String toFormattedDay(Locale locale) =>
      DateFormat(DateFormat.ABBR_WEEKDAY, locale.toLanguageTag())
          .format(toLocal());

  String toFormattedTimeNoLocal(Locale locale) =>
      DateFormat(DateFormat.HOUR_MINUTE, locale.toLanguageTag()).format(this);
  String toFormattedDateNoLocal(Locale locale) =>
      DateFormat(DateFormat.ABBR_MONTH_DAY, locale.toLanguageTag())
          .format(this);
  String toFormattedDateWithYearNoLocal(Locale locale) =>
      DateFormat(DateFormat.YEAR_ABBR_MONTH_DAY, locale.toLanguageTag())
          .format(this);
  String toFormattedDateTimeNoLocal(Locale locale) =>
      '${toFormattedDateNoLocal(locale)} ${toFormattedTimeNoLocal(locale)}';
  String toFormattedDayNoLocal(Locale locale) =>
      DateFormat(DateFormat.ABBR_WEEKDAY, locale.toLanguageTag()).format(this);

  DateTime get floorMinutes => DateTime.utc(year, month, day, hour, minute);

  DateTime get ceilMinutes =>
      DateTime.utc(year, month, day, hour, minute + (second + 59) ~/ 60);

  bool operator <(DateTime other) => compareTo(other) < 0;
  bool operator <=(DateTime other) => compareTo(other) <= 0;
  bool operator >=(DateTime other) => compareTo(other) >= 0;
  bool operator >(DateTime other) => compareTo(other) > 0;

  DateTime get startOfWeek => subtract(Duration(days: weekday - 1)).dateOnly;
  DateTime get startOfMonth => DateTime(year, month);
  DateTime get startOfNextMonth => DateTime(year, month + 1);
  DateTime get dateOnly =>
      isUtc ? DateTime.utc(year, month, day) : DateTime(year, month, day);

  DateTime addDays(int days) => DateUtils.addDaysToDate(this, days);
  DateTime addMonths(int months) =>
      DateUtils.addMonthsToMonthDate(this, months);
  DateTime setTimeOfDay(TimeOfDay timeOfDay) =>
      dateOnly.add(timeOfDay.duration);
  TimeOfDay get timeOfDay => TimeOfDay.fromDateTime(this);

  /// covert a utc time to a local time
  tz.TZDateTime fromUTC(String timeZone) =>
      tz.TZDateTime.from(this, tz.getLocation(timeZone));

  /// convert a time that's local to timezone to the correct utc time
  DateTime toUTC(String timeZone) {
    final dateTimeUtc = DateTime.utc(
        year, month, day, hour, minute, second, millisecond, microsecond);
    return DateTime.fromMillisecondsSinceEpoch(
        tz
            .getLocation(timeZone)
            .translateToUtc(dateTimeUtc.millisecondsSinceEpoch),
        isUtc: true);
  }

  DateTime get forceUtc => DateTime.utc(
      year, month, day, hour, minute, second, millisecond, microsecond);
  DateTime get forceLocal => DateTime(
      year, month, day, hour, minute, second, millisecond, microsecond);

  int get secondsSinceEpoch => millisecondsSinceEpoch ~/ 1000;
  int get yearAndMonth => year * 100 + month;

  static Tuple2<DateTime, DateTime> get todayLocal {
    final start = DateTime.now().dateOnly;
    final end = start.addDays(1).add(const Duration(minutes: -1));
    return Tuple2(start, end);
  }

  static Tuple2<DateTime, DateTime> get yesterdayLocal {
    final start = DateTime.now().addDays(-1).dateOnly;
    final end = start.addDays(1).add(const Duration(minutes: -1));
    return Tuple2(start, end);
  }

  static Tuple2<DateTime, DateTime> get thisWeekLocal {
    final start = DateTime.now().startOfWeek.dateOnly;
    final end = start.addDays(7).add(const Duration(minutes: -1));
    return Tuple2(start, end);
  }

  static Tuple2<DateTime, DateTime> get lastWeekLocal {
    final start = DateTime.now().addDays(-7).startOfWeek.dateOnly;
    final end = start.addDays(7).add(const Duration(minutes: -1));
    return Tuple2(start, end);
  }

  static Tuple2<DateTime, DateTime> get thisMonthLocal {
    final start = DateTime.now().startOfMonth.dateOnly;
    final end = start.addMonths(1).add(const Duration(minutes: -1));
    return Tuple2(start, end);
  }

  static Tuple2<DateTime, DateTime> get lastMonthLocal {
    final start = DateTime.now().addMonths(-1).startOfMonth.dateOnly;
    final end = start.addMonths(1).add(const Duration(minutes: -1));
    return Tuple2(start, end);
  }

  static Tuple2<DateTime, DateTime> get todayUTC =>
      Tuple2(todayLocal.item1.toUtc(), todayLocal.item2.toUtc());
  static Tuple2<DateTime, DateTime> get yesterdayUTC =>
      Tuple2(yesterdayLocal.item1.toUtc(), yesterdayLocal.item2.toUtc());
  static Tuple2<DateTime, DateTime> get thisWeekUTC =>
      Tuple2(thisWeekLocal.item1.toUtc(), thisWeekLocal.item2.toUtc());
  static Tuple2<DateTime, DateTime> get lastWeekUTC =>
      Tuple2(lastWeekLocal.item1.toUtc(), lastWeekLocal.item2.toUtc());
  static Tuple2<DateTime, DateTime> get thisMonthUTC =>
      Tuple2(thisMonthLocal.item1.toUtc(), thisMonthLocal.item2.toUtc());
  static Tuple2<DateTime, DateTime> get lastMonthUTC =>
      Tuple2(lastMonthLocal.item1.toUtc(), lastMonthLocal.item2.toUtc());
}

extension DurationExtension on Duration {
  String get toFormattedWithSeconds {
    final buffer = StringBuffer();
    var formatter = NumberFormat('0');

    if (inDays > 0) {
      buffer.write('${inDays}d ');
      formatter = NumberFormat('00');
    }
    if (inHours > 0) {
      buffer.write('${formatter.format(inHours % 24)}:');
      formatter = NumberFormat('00');
    }
    if (inMinutes > 0) {
      buffer.write('${formatter.format(inMinutes % 60)}:');
      formatter = NumberFormat('00');
    }
    buffer.write('${formatter.format(inSeconds % 60)} ');

    return buffer.toString().substring(0, buffer.length - 1);
  }

  String get toFormatted {
    final buffer = StringBuffer();
    var formatter = NumberFormat('0');

    if (inDays > 0) {
      buffer.write('${inDays}d ');
      formatter = NumberFormat('00');
    }
    if (inHours > 0) {
      buffer.write('${formatter.format(inHours % 24)}h ');
      formatter = NumberFormat('00');
    }
    buffer.write('${formatter.format(inMinutes % 60)}m ');

    return buffer.toString().substring(0, buffer.length - 1);
  }

  String get toAgo {
    final buffer = StringBuffer();
    buffer.write(abs().toFormatted);
    if (isNegative) buffer.write(' Ago');
    return buffer.toString();
  }

  String get toAgoWithSeconds {
    final buffer = StringBuffer();
    buffer.write(abs().toFormattedWithSeconds);
    if (isNegative) buffer.write(' Ago');
    return buffer.toString();
  }

  Duration get ceilMinutes {
    final round = (inSeconds % 60 == 0) ? 0 : 1;
    return Duration(
        days: inDays, hours: inHours % 24, minutes: inMinutes % 60 + round);
  }

  TimeOfDay get timeOfDay => TimeOfDay(hour: inHours, minute: inMinutes % 60);
}

extension TimeOfDayExtension on TimeOfDay {
  Duration get duration => Duration(hours: hour, minutes: minute);

  String toFormatted(BuildContext context) => DateTime.now()
      .setTimeOfDay(this)
      .toFormattedTimeNoLocal(Localizations.localeOf(context));
}

extension DoubleExtension on double {
  String get toPercent => NumberFormat.percentPattern().format(this);
  String get toCurrency => NumberFormat.simpleCurrency().format(this);
}

extension NullableDoubleExtension on double? {
  String? get toPercent =>
      this == null ? null : NumberFormat.percentPattern().format(this);
  String? get toCurrency =>
      this == null ? null : NumberFormat.simpleCurrency().format(this);
}

extension BooleanExtension on bool {
  String toActive(BuildContext context) => this
      ? AppLocalization.of(context).active
      : AppLocalization.of(context).inactive;
}

extension IntExtension on int {
  String toOrdinal(BuildContext context) => ordinal(this);

  String ordinal(int value) {
    final valueSpecial = [11, 12, 13];

    if (valueSpecial.contains(value % 100)) {
      return '${value}th';
    }

    String suffix;
    switch (value % 10) {
      case 1:
        suffix = 'st';
        break;
      case 2:
        suffix = 'nd';
        break;
      case 3:
        suffix = 'rd';
        break;
      default:
        suffix = 'th';
        break;
    }
    return '$this$suffix';
  }

  String toPayRateFrequencyString(BuildContext context) {
    switch (this) {
      case 0:
        return AppLocalization.of(context).hourly;
      case 1:
        return AppLocalization.of(context).weekly;
      case 2:
        return AppLocalization.of(context).yearly;
      default:
        return '';
    }
  }
}

extension StringExtension on String {
  String ellipsis(int length) =>
      this.length < length ? this : replaceRange(length, this.length, '...');
}

extension NullableStringExtension on String? {
  String? ellipsis(int length) {
    if (this == null) return null;
    return this!.length < length
        ? this!
        : this!.replaceRange(length, this!.length, '...');
  }

  bool get validatePhone {
    if (this == null) return false;
    if (this!.length > 20) return false;
    // this is microsoft's sql check, but because of the negative lookbehind (?<!\+.*), it doesn't work on the safari browser
    // final exp = RegExp(r'^(\+\s?)?((?<!\+.*)\(\+?\d+([\s\-\.]?\d+)?\)|\d+)([\s\-\.]?(\(\d+([\s\-\.]?\d+)?\)|\d+))*(\s?(x|ext\.?)\s?\d+)?$', caseSensitive: false);
    //this is much simpler and just makes sure the user didn't enter something grotesquely wrong
    final exp =
        RegExp(r'^(([0-9\ \+\_\-\,\.\^\*\?\$\^\#\(\)])|(ext|x)){1,45}$');
    return exp.hasMatch(this!);
  }

  bool get validateEmail {
    if (this == null) return false;
    if (this!.length > 254) return false;
    // this is microsoft's sql check for a valid email
    final exp = RegExp(
        r"^((([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*)|((\x22)((((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(([\x01-\x08\x0b\x0c\x0e-\x1f\x7f]|\x21|[\x23-\x5b]|[\x5d-\x7e]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(\\([\x01-\x09\x0b\x0c\x0d-\x7f]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))))*(((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(\x22)))@((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?$",
        caseSensitive: false);
    return exp.hasMatch(this!);
  }

  double? get parseCurrency {
    if (this == null) return null;
    try {
      return NumberFormat.simpleCurrency().parse(this!).toDouble();
    } catch (_) {
      return null;
    }
  }

  bool get validateCurrency {
    try {
      if (this == null) return false;
      NumberFormat.simpleCurrency().parse(this!);
      return true;
    } catch (_) {
      return false;
    }
  }
}

extension IterableExtensions<T> on Iterable<T> {
  Iterable<T>? get nullIfEmpty => isEmpty ? null : this;
}
