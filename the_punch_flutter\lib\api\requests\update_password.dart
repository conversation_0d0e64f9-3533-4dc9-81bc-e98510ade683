import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';
import 'system.dart';

part 'update_password.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class UpdatePasswordRequest extends SystemRequest {
  final String oldPassword;
  final String password;
  final String confirmPassword;

  UpdatePasswordRequest({
    required this.oldPassword,
    required this.password,
    required this.confirmPassword,
    required super.serverIP,
    required super.databaseName,
    required super.sessionId,
  });

  static Future<UpdatePasswordRequest> create(String oldPassword, String password, String confirmPassword) async {
    final systemRequest = await SystemRequest.create();

    return UpdatePasswordRequest(
      oldPassword: oldPassword,
      password: password,
      confirmPassword: confirmPassword,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory UpdatePasswordRequest.fromJson(Map<String, dynamic> json) {
    try {
      return _$UpdatePasswordRequestFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }
  @override
  Map<String, dynamic> toJson() => _$UpdatePasswordRequestToJson(this);
}
