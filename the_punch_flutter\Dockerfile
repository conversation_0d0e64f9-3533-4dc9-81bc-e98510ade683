# this dockerfile is used to develop the web version of the punch app; it is not used in production
# to use it with visual studio code, install microsoft's remote containers extension and open the project in a container
# to run the web version of the app, use the following command and open the browser at localhost:4200
# flutter run -d web-server --web-port 4200
# we did this because the current version of the app requires flutter version 3.19.6
# deleting this file will not affect the app; it is only used for development purposes

FROM debian:latest AS build-env

RUN apt-get update 
RUN apt-get install -y curl git wget unzip libgconf-2-4 gdb libstdc++6 libglu1-mesa fonts-droid-fallback
RUN apt-get install -y python3
RUN apt-get clean
# RUN apt-get update
# RUN apt-get install -y curl git


RUN git clone https://github.com/flutter/flutter.git /usr/local/flutter
RUN cd /usr/local/flutter && git fetch && git checkout 3.19.6
ENV PATH="/usr/local/flutter/bin:/usr/local/flutter/bin/cache/dart-sdk/bin:${PATH}"
RUN flutter precache

# RUN mkdir /app/
# COPY . /app/
# WORKDIR /app/
# RUN flutter build web

# FROM nginx:1.21.1-alpine
# COPY --from=build-env /app/build/web /usr/share/nginx/html

# docker build -t flutter-web .

