import 'package:json_annotation/json_annotation.dart';

part 'forgot_login.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class ForgotLoginRequest {
  String organizationId;
  String email;
  String captchaResponse;

  ForgotLoginRequest({required this.organizationId, required this.email, required this.captchaResponse});

  factory ForgotLoginRequest.fromJson(Map<String, dynamic> json) => _$ForgotLoginRequestFromJson(json);
  Map<String, dynamic> toJson() => _$ForgotLoginRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class ForgotLoginConfirmationRequest {
  String organizationId;
  String password;
  String confirmPassword;
  String confirmationKey;

  ForgotLoginConfirmationRequest({required this.organizationId, required this.password, required this.confirmPassword, required this.confirmationKey});

  factory ForgotLoginConfirmationRequest.fromJson(Map<String, dynamic> json) => _$ForgotLoginConfirmationRequestFromJson(json);
  Map<String, dynamic> toJson() => _$ForgotLoginConfirmationRequestToJson(this);
}
