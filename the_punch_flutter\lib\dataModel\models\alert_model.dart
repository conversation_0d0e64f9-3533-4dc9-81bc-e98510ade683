import '../data/alert.dart';
import '../base_data.dart';
import '../hive_db.dart';

class AlertModel extends BaseDataModel<Alert> {
  @override
  Future<Iterable<Alert>> get all async => (await HiveDb.database).alerts.values;

  @override
  Future<void> save(Iterable<Alert> t) async {
    await (await HiveDb.database).alerts.putAll({for (final e in t) e.id: e});
    // Mark records as not dirty after saving (assuming you reset the dirty flag here)
    for (final e in t) {
      e.isDirty = false;
    }
    notifyListeners();
  }

  // Method to retrieve dirty alerts, with an optional limit
  Future<Iterable<Alert>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty); // Assuming `isDirty` is a boolean property of Alert
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }

  Future<Iterable<Alert>> getActiveByPunchCardId(String id) => getActiveByPunchCardIds([id]);

  Future<Iterable<Alert>> getActiveByPunchCardIds(Iterable<String> ids) async => ids.isEmpty ? [] : (await all).where((e) => ids.contains(e.punchCardId));

  Future<Iterable<Alert>> getByScheduleIds(Iterable<String> ids) async => ids.isEmpty ? [] : (await all).where((e) => ids.contains(e.scheduleId));

  Future<Iterable<Alert>> getActiveByUserId(String id) => getActiveByUserIds([id]);

  Future<Iterable<Alert>> getActiveByUserIds(Iterable<String> ids) async => (await active).where((e) => ids.contains(e.userId));

  Future<Iterable<Alert>> getByLocationIds(Iterable<String> ids) async => ids.isEmpty ? [] : (await all).where((e) => ids.contains(e.locationId));

  Future<Iterable<Alert>> getActiveByLocationId(String id) => getActiveByLocationIds([id]);

  Future<Iterable<Alert>> getActiveByLocationIds(Iterable<String> ids) async => ids.isEmpty ? [] : (await active).where((e) => ids.contains(e.locationId));
}
