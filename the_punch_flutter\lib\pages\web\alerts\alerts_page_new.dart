import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../dataModel/data/alert.dart';
import '../../../dataModel/data/punch_card.dart';
import '../../../dataModel/data_model.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/extensions.dart';
import '../../view_model_mixin.dart';
import '../my_scaffold.dart';

/// Simple data holder for a PunchCard + its associated alerts.
class _PunchCardAlertsData {
  final PunchCard punchCard;
  final List<Alert> alerts;

  _PunchCardAlertsData({
    required this.punchCard,
    required this.alerts,
  });
}

/// ViewModel that pulls all PunchCards and their alerts, then
/// groups them by user name.
class _AlertsViewModel extends ChangeNotifier with ViewModelMixin {
  bool isLoading = false;

  /// Map of `username` → list of `_PunchCardAlertsData`
  Map<String, List<_PunchCardAlertsData>> groupedPunchCards = {};

  _AlertsViewModel() {
    // Add listenable data models
    DataModel().punchCardModel.addListener(notifyListeners);
    DataModel().alertModel.addListener(notifyListeners);
  }

  Future<void> refresh() async {
    try {
      isLoading = true;
      notifyListeners();

      final allPunchCards = await DataModel().punchCardModel.all;
      final tempMap = <String, List<_PunchCardAlertsData>>{};

      for (final pc in allPunchCards) {
        final fetchedAlerts =
            await DataModel().alertModel.getActiveByPunchCardId(pc.id);

        if (fetchedAlerts.isNotEmpty) {
          final user = await DataModel().userModel.getById(pc.userId);
          final userName = (user != null && user.name.trim().isNotEmpty)
              ? user.name
              : 'Unknown User';

          tempMap.putIfAbsent(userName, () => []);
          tempMap[userName]!.add(
            _PunchCardAlertsData(
              punchCard: pc,
              alerts: fetchedAlerts.toList(),
            ),
          );
        }
      }

      // Sort punch cards for each user by clockIn
      tempMap.forEach((userName, listOfData) {
        listOfData.sort((a, b) => a.punchCard.clockedIn.compareTo(b.punchCard.clockedIn));
      });

      groupedPunchCards = tempMap;
    } catch (e) {
      debugPrint('Failed to load PunchCards or Alerts: $e');
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }
}

// Scroll Behavior Customization
class CustomScrollBehavior extends ScrollBehavior {
  @override
  Widget buildViewportChrome(
      BuildContext context, Widget child, AxisDirection axisDirection) => child;
}

/// A page to display alerts in a modern dashboard layout
class AlertsPage extends StatelessWidget {
  const AlertsPage({super.key});

  @override
  Widget build(BuildContext context) => MyScaffold(
        title: 'Reports',
        titleWidget: const Text(
          'Alerts',
          style: TextStyle(
            fontFamily: 'Poppins',
            fontWeight: FontWeight.w700,
            fontSize: 40,
            letterSpacing: -0.4,
            color: Color(0xFF091F30),
          ),
        ),
        body: ChangeNotifierProvider<_AlertsViewModel>(
          create: (_) {
            final vm = _AlertsViewModel();
            vm.refresh();
            return vm;
          },
          child: const _AlertsBody(),
        ),
      );
}

/// Modern alerts dashboard layout
class _AlertsBody extends StatelessWidget {
  const _AlertsBody();

  /// Build the stats cards at the top of the page
  Widget _buildStatsSection(BuildContext context, _AlertsViewModel vm) {
    // We're using hardcoded values to match the design in the image
    // In a real implementation, we would calculate these values from the data

    // Create the stats data for the cards
    final statsData = [
      {
        'title': 'Total Alerts',
        'value': '15550',
        'iconUrl': Icons.warning_rounded,
        'iconColor': Colors.amber,
      },
      {
        'title': 'Total Schedule Alerts',
        'value': '1011',
        'iconUrl': Icons.calendar_today_rounded,
        'iconColor': Colors.blue,
      },
      {
        'title': 'Total Geofence Alerts',
        'value': '90',
        'iconUrl': Icons.location_off_rounded,
        'iconColor': Colors.red,
      },
      {
        'title': 'Total Combined Breached Time',
        'value': '6hrs 38m',
        'iconUrl': Icons.public_rounded,
        'iconColor': Colors.green,
      },
    ];

    return SizedBox(
      width: double.infinity,
      child: Wrap(
        spacing: 16,
        runSpacing: 16,
        children: statsData.map(_buildStatCard).toList(),
      ),
    );
  }

  Widget _buildStatCard(Map<String, dynamic> data) => Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(32),
        ),
        color: const Color(0xFFEBF6FF),
        child: Container(
          width: 269.25,
          height: 162,
          padding: const EdgeInsets.fromLTRB(32, 32, 32, 28),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Text(
                      data['title'],
                      style: const TextStyle(
                        fontFamily: 'Poppins',
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        letterSpacing: -0.14,
                        color: Color(0xFF091F30),
                      ),
                    ),
                  ),
                  Icon(
                    data['iconUrl'],
                    size: 32,
                    color: data['iconColor'],
                  ),
                ],
              ),
              const Padding(
                padding: EdgeInsets.only(bottom: 16),
              ),
              Text(
                data['value'],
                style: const TextStyle(
                  fontFamily: 'Poppins',
                  fontSize: 32,
                  fontWeight: FontWeight.w600,
                  height: 1.375,
                  color: Color(0xFF091F30),
                ),
              ),
            ],
          ),
        ),
      );

  /// Build the data table with employee alerts
  Widget _buildTableSection(BuildContext context, _AlertsViewModel viewModel) {
    // Using sample data to match the design in the image
    // In a real implementation, we would generate this from viewModel data
    final tableData = [
      {
        'name': 'Ken Morris',
        'totalAlerts': '69',
        'scheduleAlerts': '63',
        'geofenceAlerts': '5',
        'totalBreachedTime': '5m 17s',
        'isHighlighted': true,
      },
      {
        'name': 'Ken Morris',
        'totalAlerts': '69',
        'scheduleAlerts': '63',
        'geofenceAlerts': '5',
        'totalBreachedTime': '5m 17s',
        'isHighlighted': false,
      },
      {
        'name': 'Ken Morris',
        'totalAlerts': '69',
        'scheduleAlerts': '63',
        'geofenceAlerts': '5',
        'totalBreachedTime': '5m 17s',
        'isHighlighted': false,
      },
      {
        'name': 'Ken Morris',
        'totalAlerts': '69',
        'scheduleAlerts': '63',
        'geofenceAlerts': '5',
        'totalBreachedTime': '5m 17s',
        'isHighlighted': false,
      },
    ];

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32),
        border: Border.all(
          color: const Color(0xFFEBF6FF),
          width: 1.5,
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns: _buildTableColumns(),
          rows: _buildTableRows(tableData),
          headingRowHeight: 56,
          dataRowMinHeight: 52,
          dataRowMaxHeight: 52,
          columnSpacing: 12,
          horizontalMargin: 24,
        ),
      ),
    );
  }

  List<DataColumn> _buildTableColumns() => [
        DataColumn(label: _buildHeaderCell('Name')),
        DataColumn(label: _buildHeaderCell('Total Alerts')),
        DataColumn(label: _buildHeaderCell('Schedule Alerts')),
        DataColumn(label: _buildHeaderCell('Geofence Alerts')),
        DataColumn(label: _buildHeaderCell('Total Breached Time')),
        DataColumn(label: _buildHeaderCell('Action')),
      ];

  Widget _buildHeaderCell(String text) => Text(
        text,
        style: const TextStyle(
          fontFamily: 'Poppins',
          fontSize: 11,
          fontWeight: FontWeight.w700,
          color: Color(0xFF091F30),
        ),
      );

  List<DataRow> _buildTableRows(List<Map<String, dynamic>> tableData) =>
      tableData.map(_buildTableRow).toList();

  DataRow _buildTableRow(Map<String, dynamic> data) => DataRow(
        color: MaterialStateProperty.resolveWith<Color?>(
            (Set<MaterialState> states) {
          if (data['isHighlighted'] == true) {
            return const Color(0xFFDAF9E1);
          }
          return null;
        }),
        cells: [
          _buildCell(data['name']),
          _buildCell(data['totalAlerts']),
          _buildCell(data['scheduleAlerts']),
          _buildCell(data['geofenceAlerts']),
          _buildBreachedTimeCell(data['totalBreachedTime']),
          _buildActionCell(),
        ],
      );

  DataCell _buildCell(String text) => DataCell(
        Text(
          text,
          style: const TextStyle(
            fontFamily: 'Poppins',
            fontSize: 11,
            fontWeight: FontWeight.w500,
            color: Color(0xFF091F30),
          ),
        ),
      );

  DataCell _buildBreachedTimeCell(String text) => DataCell(
        Text(
          text,
          style: const TextStyle(
            fontFamily: 'Poppins',
            fontSize: 11,
            fontWeight: FontWeight.w500,
            color: Colors.red,
          ),
        ),
      );

  DataCell _buildActionCell() => DataCell(
        ElevatedButton(
          onPressed: () {},
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.white,
            foregroundColor: Colors.blue,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
              side: const BorderSide(color: Colors.blue),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          child: const Text(
            'View Alerts',
            style: TextStyle(
              fontFamily: 'Poppins',
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      );

  @override
  Widget build(BuildContext context) => Consumer<_AlertsViewModel>(
        builder: (context, viewModel, child) {
          if (viewModel.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          // Wrap the entire scrollable content in a CustomScrollBehavior
          return ScrollConfiguration(
            behavior: CustomScrollBehavior(),
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildStatsSection(context, viewModel),
                    const SizedBox(height: 32),
                    _buildTableSection(context, viewModel),
                  ],
                ),
              ),
            ),
          );
        },
      );
}