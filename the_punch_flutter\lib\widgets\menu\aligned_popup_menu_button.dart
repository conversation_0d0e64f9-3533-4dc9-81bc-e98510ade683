import 'package:flutter/material.dart';

import '../../helpers/color_helper.dart';

class AlignedPopupMenuButton<T> extends StatefulWidget {
  final PopupMenuItemBuilder<T> itemBuilder;
  final PopupMenuItemSelected<T>? onSelected;
  final T? initialValue;
  final PopupMenuCanceled? onCanceled;
  final String? tooltip;
  final double? elevation;
  final EdgeInsetsGeometry padding;
  final Widget? child;
  final Widget? icon;
  final bool enabled;
  final ShapeBorder? shape;
  final Color? color;
  final bool? enableFeedback;
  final double? iconSize;
  final Alignment alignment;

  const AlignedPopupMenuButton({
    super.key,
    required this.itemBuilder,
    this.initialValue,
    this.onSelected,
    this.onCanceled,
    this.tooltip,
    this.elevation,
    this.padding = const EdgeInsets.all(8),
    this.child,
    this.icon,
    this.iconSize,
    this.enabled = true,
    this.shape,
    this.color,
    this.enableFeedback,
    this.alignment = Alignment.bottomCenter,
  });

  @override
  AlignedPopupMenuButtonState<T> createState() => AlignedPopupMenuButtonState<T>();
}

class AlignedPopupMenuButtonState<T> extends State<AlignedPopupMenuButton<T>> {
  Offset offset = Offset.zero;

  // @override
  // void initState() {
  //   super.initState();
  //   WidgetsBinding.instance.addPostFrameCallback((_) {
  //     final renderBox = context.findRenderObject() as RenderBox?;
  //     if (renderBox == null) return;
  //     final size = renderBox.size;
  //     setState(() {
  //       if (widget.alignment == Alignment.bottomCenter) {
  //         offset = Offset(0, size.height);
  //       } else if (widget.alignment == Alignment.bottomLeft) {
  //         offset = Offset(-size.width, size.height);
  //       } else if (widget.alignment == Alignment.bottomRight) {
  //         offset = Offset(size.width, size.height);
  //       } else if (widget.alignment == Alignment.center) {
  //         offset = Offset.zero;
  //       } else if (widget.alignment == Alignment.centerLeft) {
  //         offset = Offset(-size.width, 0);
  //       } else if (widget.alignment == Alignment.centerRight) {
  //         offset = Offset(size.width, 0);
  //       } else if (widget.alignment == Alignment.topCenter) {
  //         offset = Offset(0, -size.height);
  //       } else if (widget.alignment == Alignment.topLeft) {
  //         offset = Offset(-size.width, -size.height);
  //       } else if (widget.alignment == Alignment.topRight) {
  //         offset = Offset(size.width, -size.height);
  //       }
  //     });
  //   });
  // }

  @override
  Widget build(BuildContext context) => Theme(
        data: Theme.of(context).copyWith(
            tooltipTheme:  TooltipThemeData(
          decoration: BoxDecoration(
            color: Colors.transparent,
          
          ),
        )),
        child: PopupMenuButton(
          itemBuilder: widget.itemBuilder,
          offset: offset,
          initialValue: widget.initialValue,
          onSelected: widget.onSelected,
          onCanceled: widget.onCanceled,
          tooltip: widget.tooltip ?? '',
          elevation: widget.elevation,
          padding: widget.padding,
          icon: widget.icon,
          iconSize: widget.iconSize,
          enabled: widget.enabled,
          shape: widget.shape,
          color: widget.color,
          enableFeedback: widget.enableFeedback,
          child: widget.child,
        ));
}
