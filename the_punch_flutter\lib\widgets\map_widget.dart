import 'dart:async';

import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../dataModel/data/location.dart';
import '../dataModel/data_model.dart';
import '../misc/bitmap_descriptor_extensions.dart';
import '../misc/change_notification_builder.dart';
import '../pages/view_model_mixin.dart';
import '../pages/web/locations/location_mixin.dart';
import 'package:uuid/uuid.dart';

class MapWidget extends StatefulWidget {
  final String locationId;

  const MapWidget({super.key, required this.locationId});

  @override
  State<MapWidget> createState() => _MapWidgetState();
}

class _MapWidgetState extends State<MapWidget> {
  BitmapDescriptor? bluePinDrop;

  @override
  void initState() {
    super.initState();
    unawaited(BitmapDescriptorExtension.fromIconData(Icons.pin_drop, Colors.blue, 32)
        .then((value) => setState(() => bluePinDrop = value)));
  }

  @override
  Widget build(BuildContext context) => ChangeNotifierBuilder<_ViewModel>(
      create: (context) => _ViewModel(widget.locationId),
      builder: (context, viewModel, _) {
        final location = viewModel.location;
        if (location == null) return Container();

        return GoogleMap(
            markers: {
              Marker(
                markerId: MarkerId(const Uuid().v4()),
                icon: bluePinDrop ?? BitmapDescriptor.defaultMarker,
                position: LatLng(location.latitude, location.longitude),
              )
            },
            mapType: MapType.hybrid,
            circles: {
              Circle(
                circleId: CircleId(const Uuid().v4()),
                center: LatLng(viewModel.location!.latitude, viewModel.location!.longitude),
                radius: viewModel.location!.geoFenceRadius,
                fillColor: Colors.black.withAlpha(64),
                strokeWidth: 1,
              ),
            },
            initialCameraPosition: CameraPosition(
              target: LatLng(location.latitude, location.longitude),
              zoom: 18,
            ));
      });
}

class _ViewModel extends ChangeNotifier with ViewModelMixin, LocationMixin {
  final String locationId;
  Location? location;

  _ViewModel(this.locationId) {
    addListenables([
      DataModel().locationModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    location = await DataModel().locationModel.getById(locationId);
    notifyListeners();
  }
}
