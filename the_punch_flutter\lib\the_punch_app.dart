import 'dart:async';
import 'dart:core';

// For detecting if we are on web
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:universal_html/html.dart' as html;

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_localized_locales/flutter_localized_locales.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

// --------------
// Import checkSession
// --------------
import 'helpers/check_session.dart';
//import 'session_utils.dart'; // <-- This is where your checkSession() is
// --------------

import 'api/api_model.dart';
import 'dataModel/data_model.dart';
import 'dataModel/models/job_type_model.dart';
import 'dataModel/models/location_model.dart';
import 'dataModel/models/punch_view_model.dart';
import 'dataModel/models/user_model.dart';
import 'helpers/color_helper.dart';
import 'helpers/text_style_helper.dart';
import 'main.dart';
import 'menus/my_app_bar.dart';
import 'routing/go_router_definition.dart';
import 'services/storage_manager_service.dart';
import 'state/login_state.dart';
import 'state/page_title_state.dart';
import 'state/permissions_state.dart';
import 'state/punch_state.dart';
import 'state/app_state.dart';
import 'misc/my_platform.dart';
import 'widgets/calendar/calendar_widget.dart';

class ThePunchApp extends StatefulWidget {
  final AppState appState;
  const ThePunchApp({Key? key, required this.appState}) : super(key: key);

  @override
  State<ThePunchApp> createState() => _ThePunchAppState();
}

class _ThePunchAppState extends State<ThePunchApp> with WidgetsBindingObserver {
  late final StorageManager _storageManager;

  @override
  void initState() {
    super.initState();
    _storageManager = StorageManager();

    // Listen for app lifecycle changes
    WidgetsBinding.instance.addObserver(this);

    // Check session when app starts
    _checkSessionOnStartup();

    // Check session on ANY click (for Web).
    if (MyPlatform.isWeb || kIsWeb) {
      html.window.onClick.listen((event) async => _checkSessionOnClick());
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  /// Called whenever the app’s lifecycle state changes (foreground/background).
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);

    // When app comes back to foreground
    if (state == AppLifecycleState.resumed) {
      await _checkSessionOnResume();
    }
  }

  /// Checks the session as soon as the app starts (in `initState`).
  Future<void> _checkSessionOnStartup() async {
    try {
      // Instead of manually checking session, use our helper:
      final stillValid = await checkSession(context);
      if (!stillValid) {
        // The user was forced out => do nothing else
        return;
      }
      // If you need additional logic upon valid session, do it here
    } catch (e) {
      debugPrint('Error checking session on startup: $e');
    }
  }

  /// Checks the session each time the app is resumed from background.
  Future<void> _checkSessionOnResume() async {
    try {
      final stillValid = await checkSession(context);
      if (!stillValid) {
        // The user was forced out => do nothing else
        return;
      }
      // If you need additional logic upon valid session, do it here
    } catch (e) {
      debugPrint('Error checking session on resume: $e');
    }
  }

  /// Checks the session on ANY web click event.
  Future<void> _checkSessionOnClick() async {
    try {
      final stillValid = await checkSession(context);
      if (!stillValid) {
        // The user was forced out => do nothing else
        return;
      }
      // If you need additional logic upon valid session, do it here
    } catch (e) {
      debugPrint('Error checking session on click: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final pageTitleProvider = PageTitleProvider();

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => PunchState()),
        ChangeNotifierProvider(create: (context) => DataModel().locationModel),
        ChangeNotifierProvider(
            create: (_) => CalendarViewModel(false, DateTime.now())),
        ChangeNotifierProvider(create: (_) => pageTitleProvider),
        ChangeNotifierProvider(create: (_) => DataModel().scheduleModel),
        ChangeNotifierProvider(create: (_) => PermissionsState()),
        ChangeNotifierProvider(create: (_) => PunchViewModel()),
        ChangeNotifierProvider(create: (_) => JobTypeModel()),
        ChangeNotifierProvider(create: (_) => LocationModel()),
        ChangeNotifierProvider<UserModel>(
          create: (_) => UserModel(),
        ),
        ChangeNotifierProvider(
          create: (_) => DrawerStateProvider(8),
        ),
      ],
      child: MaterialApp.router(
        routerConfig: goRouterDeclaration,
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          LocaleNamesLocalizationsDelegate(),
        ],
        supportedLocales: AppLocalizations.supportedLocales,
        debugShowCheckedModeBanner: false,
        title: 'The Punch',
        theme: ThemeData.light().copyWith(
          applyElevationOverlayColor: false,
          highlightColor: ColorHelper.thePunchRed(),
          cardColor: ColorHelper.lightThemeWhite(),
          colorScheme: ThemeData.light().colorScheme.copyWith(
                primary: Colors.white,
                secondary: ColorHelper.thePunchGray(),
              ),
          inputDecorationTheme: InputDecorationTheme(
            hintStyle: TextStyle(
              color: ColorHelper.thePunchGray(),
            ),
          ),
          textSelectionTheme: TextSelectionThemeData(
            cursorColor: ColorHelper.thePunchBlue(),
            selectionColor: ColorHelper.thePunchBlue(),
            selectionHandleColor: ColorHelper.thePunchBlue(),
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorHelper.thePunchRed(),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              textStyle: const TextStyle(
                  color: Colors.green, fontWeight: FontWeight.w700),
            ),
          ),
          textButtonTheme: TextButtonThemeData(
            style: TextButton.styleFrom(
              foregroundColor: ColorHelper.thePunchRed(),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
          outlinedButtonTheme: OutlinedButtonThemeData(
            style: OutlinedButton.styleFrom(
              textStyle: TextStyle(color: ColorHelper.thePunchBlue()),
              foregroundColor: ColorHelper.thePunchRed(),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              side: BorderSide(
                color: ColorHelper.thePunchBlue(),
              ),
            ),
          ),
          scrollbarTheme: const ScrollbarThemeData(
            thumbVisibility: MaterialStatePropertyAll(true),
            trackVisibility: MaterialStatePropertyAll(true),
          ),
          bottomAppBarTheme:
              BottomAppBarTheme(color: ColorHelper.lightThemeWhite()),
          textTheme: TextTheme(
            titleLarge: TextStyleHelper.grayTitle(context),
            titleMedium: TextStyleHelper.lightGrayTitle(context),
            titleSmall: TextStyleHelper.whiteTitle(context),
            headlineSmall: TextStyleHelper.whiteTitleSmall(context),
            bodyMedium: TextStyleHelper.grayBodyText(context),
          ),
          dialogBackgroundColor: Colors.white,
          dialogTheme: DialogTheme(
            titleTextStyle: const TextStyle(color: Colors.red),
            contentTextStyle: TextStyleHelper.grayTitle(context),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            elevation: 1,
          ),
        ),
      ),
    );
  }
}
