import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../helpers/color_helper.dart';
import 'constrained_dialog.dart';

class LoginTrackerDialog extends StatefulWidget {
  final Future<void> Function()? future;
  final String? subTitle;
  final ValueNotifier<bool> loginComplete;
  final ValueNotifier<bool> dataLoadComplete;

  const LoginTrackerDialog({
    super.key,
    required this.future,
    this.subTitle,
    required this.loginComplete,
    required this.dataLoadComplete,
  });

  @override
  State<LoginTrackerDialog> createState() => _LoginTrackerDialogState();
}

class _LoginTrackerDialogState extends State<LoginTrackerDialog> {
  Future<void>? _future;
  String _title = 'Loading...';
  String _loginText = 'Logging in...';
  String _dataLoadText = 'Data';
  Stopwatch loginTimer = Stopwatch();
  Stopwatch dataLoadTimer = Stopwatch();
  Timer? updateTimer;

  void _loginCompleteListener() {
    if (widget.loginComplete.value) {
      loginTimer.stop();
      dataLoadTimer.start();
      setState(() {
        _loginText = 'Login Successful!';
        _title = 'Loading...';
        _dataLoadText = 'Loading Data...';
      });
    }
  }

  void _dataLoadCompleteListener() {
    if (widget.dataLoadComplete.value) {
      dataLoadTimer.stop();
      setState(() {
        _dataLoadText = 'Load Successful!';
        _title = 'Completed!';
      });
      updateTimer?.cancel();
    }
  }

  @override
  void initState() {
    super.initState();
    _future = widget.future?.call();
    loginTimer.start();

    widget.loginComplete.addListener(_loginCompleteListener);
    widget.dataLoadComplete.addListener(_dataLoadCompleteListener);

    updateTimer = Timer.periodic(Duration(milliseconds: 50), (timer) {
      setState(() {}); // Refresh to update the timer display
    });
  }

  @override
  void dispose() {
    widget.loginComplete.removeListener(_loginCompleteListener);
    widget.dataLoadComplete.removeListener(_dataLoadCompleteListener);
    updateTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => FutureBuilder<void>(
        future: _future,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return ConstrainedDialog(
              width: 400,
              title: Text(_title),
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: SizedBox(
                  height: 100,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Expanded(child: Text(_loginText)),
                          const SizedBox(width: 8),
                          buildTimerWidget(widget.loginComplete, loginTimer, true),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(child: Text(_dataLoadText)),
                          const SizedBox(width: 8),
                          buildTimerWidget(widget.dataLoadComplete, dataLoadTimer, false),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          } else if (snapshot.hasError) {
            return ConstrainedDialog(
              width: 400,
              title: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 20),
                  SizedBox(width: 10), // Adds some spacing between the icon and the text
                  Text(AppLocalizations.of(context)!.error),
                ],
              ),
              subTitle: '',
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(snapshot.error.toString()),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(AppLocalizations.of(context)!.close),
                    ),
                  ],
                ),
              ),
            );
          } else {
            return Container();
          }
        },
      );

  Widget buildTimerWidget(ValueNotifier<bool> complete, Stopwatch stopwatch, bool isLoginTimer) => ValueListenableBuilder<bool>(
      valueListenable: complete,
      builder: (context, isComplete, child) {
        var textStyle = TextStyle(color: isComplete ? Colors.green : Colors.black);
        double seconds = stopwatch.elapsedMilliseconds / 1000.0;
        return Row(
          children: [
            Text('${seconds.toStringAsFixed(2)} s', style: textStyle),
            const SizedBox(width: 8),
            isComplete
                ? Icon(Icons.check_circle, color: ColorHelper.thePunchBlue(), size: 24)
                : isLoginTimer
                    ? SizedBox(
                        height: 24,
                        width: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 3,
                          color: ColorHelper.thePunchBorderRed(),
                        ),
                      )
                    : ValueListenableBuilder<bool>(
                        valueListenable: widget.loginComplete,
                        builder: (context, loginComplete, child) => loginComplete
                            ? SizedBox(
                                height: 24,
                                width: 24,
                                child: CircularProgressIndicator(
                                  strokeWidth: 3,
                                  color: ColorHelper.thePunchBorderRed(),
                                ),
                              )
                            : _buildX(),
                      ),
          ],
        );
      },
    );

  Widget _buildCheckmarkWithDepth(Color color) => Icon(
        Icons.check_circle,
        color: color,
        size: 30,
      );

  Widget _buildX() => Icon(
        Icons.close,
        color: ColorHelper.thePunchDesktopLightGray(),
        size: 30,
      );
}

class ConstrainedDialog extends StatelessWidget {
  final Widget title; // Changed from String to Widget to accept more complex layouts
  final Widget child;
  final String? subTitle;
  final double width;

  const ConstrainedDialog({
    required this.title,
    required this.child,
    this.subTitle,
    required this.width,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => Dialog(
        child: Container(
          width: width,
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              title, // Now directly using the Widget provided
              if (subTitle != null) ...[
                const SizedBox(height: 8),
                Text(subTitle!, style: Theme.of(context).textTheme.titleMedium),
              ],
              const SizedBox(height: 16),
              child,
            ],
          ),
        ),
      );
}
