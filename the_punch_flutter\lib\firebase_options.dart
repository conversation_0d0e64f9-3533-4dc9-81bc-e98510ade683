// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCiYBTGPcDjRqsjZ8Q11HW-KZ9f8rDpQfE',
    appId: '1:885786697865:web:1c76abcd3bd5a156e2e658',
    messagingSenderId: '885786697865',
    projectId: 'the-punch-1bc0b',
    authDomain: 'the-punch-1bc0b.firebaseapp.com',
    databaseURL: 'https://the-punch-1bc0b.firebaseio.com',
    storageBucket: 'the-punch-1bc0b.appspot.com',
    measurementId: 'G-34R7315HY4',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBAqlUxe-Z7crJzNW71HRAWBa-9oB0KCRQ',
    appId: '1:885786697865:android:4547362b7968fb83e2e658',
    messagingSenderId: '885786697865',
    projectId: 'the-punch-1bc0b',
    databaseURL: 'https://the-punch-1bc0b.firebaseio.com',
    storageBucket: 'the-punch-1bc0b.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDow27TeLC4QRh7Gh4dskb2YmHcE78O9WU',
    appId: '1:885786697865:ios:7e18c0d6d49837cee2e658',
    messagingSenderId: '885786697865',
    projectId: 'the-punch-1bc0b',
    databaseURL: 'https://the-punch-1bc0b.firebaseio.com',
    storageBucket: 'the-punch-1bc0b.appspot.com',
    androidClientId: '885786697865-chfp119u1f91litj4fqeokuajcgk9o54.apps.googleusercontent.com',
    iosClientId: '885786697865-2cahcfo43ke79p89lblfnpj4p62ns2tm.apps.googleusercontent.com',
    iosBundleId: 'app.thepunch.mobile',
  );
}
