// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'job_type.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class JobTypeAdapter extends TypeAdapter<JobType> {
  @override
  final int typeId = 12;

  @override
  JobType read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return JobType(
      isDirty: fields[0] as bool,
      isActive: fields[1] as bool,
      createdOn: fields[2] as DateTime,
      createdByUserId: fields[3] as String?,
      lastChangedOn: fields[4] as DateTime?,
      lastChangedByUserId: fields[5] as String?,
      id: fields[100] as String,
      name: fields[101] as String,
      description: fields[102] as String?,
      order: fields[103] as int,
    );
  }

  @override
  void write(BinaryWriter writer, JobType obj) {
    writer
      ..writeByte(10)
      ..writeByte(100)
      ..write(obj.id)
      ..writeByte(101)
      ..write(obj.name)
      ..writeByte(102)
      ..write(obj.description)
      ..writeByte(103)
      ..write(obj.order)
      ..writeByte(0)
      ..write(obj.isDirty)
      ..writeByte(1)
      ..write(obj.isActive)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdByUserId)
      ..writeByte(4)
      ..write(obj.lastChangedOn)
      ..writeByte(5)
      ..write(obj.lastChangedByUserId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is JobTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

JobType _$JobTypeFromJson(Map<String, dynamic> json) => JobType(
      isActive: json['IsActive'] as bool? ?? true,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
      id: idFromJson(json['Id']),
      name: json['Name'] as String,
      description: json['Description'] as String?,
      order: (json['Order'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$JobTypeToJson(JobType instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['Name'] = instance.name;
  writeNotNull('Description', instance.description);
  val['Order'] = instance.order;
  return val;
}
