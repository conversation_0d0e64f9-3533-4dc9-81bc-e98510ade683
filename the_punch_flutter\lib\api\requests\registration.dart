import 'package:json_annotation/json_annotation.dart';
import 'system.dart';
import '../../misc/json_conversion.dart';

part 'registration.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class AddressInfo {
  String address1;
  String address2;
  String city;
  String state;
  String zip;

  AddressInfo({
    required this.address1,
    required this.address2,
    required this.city,
    required this.state,
    required this.zip,
  });

  factory AddressInfo.create() => AddressInfo(
        address1: '',
        address2: '',
        city: '',
        state: '',
        zip: '',
      );

  factory AddressInfo.fromJson(Map<String, dynamic> json) => _$AddressInfoFromJson(json);
  Map<String, dynamic> toJson() => _$AddressInfoToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class Registration {
  String emailAddress;
  String organizationName;
  String username;
  String firstName;
  String lastName;
  String phone;
  AddressInfo addressInfo;
  String timeZone;
  String languageKey;
  String password;
  String confirmPassword;

  Registration({
    required this.emailAddress,
    required this.organizationName,
    required this.username,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.addressInfo,
    required this.timeZone,
    required this.languageKey,
    required this.password,
    required this.confirmPassword,
  });

  factory Registration.create() => Registration(
        emailAddress: '',
        organizationName: '',
        username: '',
        firstName: '',
        lastName: '',
        phone: '',
        addressInfo: AddressInfo.create(),
        timeZone: '',
        languageKey: 'en-US',
        password: '',
        confirmPassword: '',
      );

  factory Registration.fromJson(Map<String, dynamic> json) => _$RegistrationFromJson(json);
  Map<String, dynamic> toJson() => _$RegistrationToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class RegistrationRequest {
  Registration registration;
  String captchaResponse;

  RegistrationRequest({required this.registration, required this.captchaResponse});

  factory RegistrationRequest.fromJson(Map<String, dynamic> json) => _$RegistrationRequestFromJson(json);
  Map<String, dynamic> toJson() => _$RegistrationRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class CompleteRegistrationRequest {
  String registrationKey;
  String captchaResponse;
  String timeZone;

  CompleteRegistrationRequest({required this.registrationKey, required this.captchaResponse, required this.timeZone});

  factory CompleteRegistrationRequest.fromJson(Map<String, dynamic> json) => _$CompleteRegistrationRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CompleteRegistrationRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class CompleteRegistrationResponse extends SystemResponse {
  @JsonKey(name: 'organizationID', defaultValue: '')
  String organizationId;
  @JsonKey(defaultValue: '')
  String username;

  CompleteRegistrationResponse({
    required this.organizationId,
    required this.username,
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory CompleteRegistrationResponse.fromJson(Map<String, dynamic> json) => _$CompleteRegistrationResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$CompleteRegistrationResponseToJson(this);
}
