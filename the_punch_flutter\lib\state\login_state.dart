import 'dart:async';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../api/api_model.dart';
import '../api/requests/system.dart';
import '../api/sync_model.dart';
import '../dataModel/data/user.dart';
import '../dataModel/data_model.dart';
import '../dataModel/models/punch_view_model.dart';
import '../misc/my_platform.dart';
import 'session_timeout.dart';

class LoginState extends ChangeNotifier {
  static final userNotifier = ValueNotifier(User.empty());
  static String get userId => userNotifier.value.id;
  static bool get isLoggedIn => userNotifier.value.id.isNotEmpty;

  static LoginState? _instance;
  LoginState._();

  final _completer = Completer();

  static Future<LoginState> get instance async {
    if (_instance == null) {
      _instance = LoginState._();
      await _instance!._initialize();
    }
    await _instance!._completer.future;
    return _instance!;
  }

  Future<void> _initialize() async {
    ApiModel().logoutErrorCode.addListener(() {
      if (ApiModel().logoutErrorCode.value == null) return;
      logout(forcedOut: true);
    });

    final prefs = await SharedPreferences.getInstance();
    final employeeId = prefs.getString('userId');

    if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) {
      await FirebaseAnalytics.instance.setUserId(id: employeeId);
    }
    if (employeeId == null) {
    } else {
      final user = await DataModel().userModel.getById(employeeId);
      if (user != null) userNotifier.value = user;
    }
    _completer.complete();
  }

  Future<void> login(User user, String organizationId, SystemRequest systemRequest, int userSessionExpirationMinsWeb) async {
    //await DataModel().delete();
     try {
      await DataModel().delete();
    } catch (e) {
      if (kDebugMode) print('failed to delete db $e');
    }

    await DataModel().userModel.save([user]);

    if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) {
      await FirebaseAnalytics.instance.setUserId(id: user.id);
    }

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('userId', user.id);
    await prefs.setString('serverIP', systemRequest.serverIP);
    await prefs.setString('databaseName', systemRequest.databaseName);
    await prefs.setString('sessionId', systemRequest.sessionId);
    await prefs.setString('organizationId', organizationId);
    await prefs.setString('username', user.username ?? '');
    await prefs.setInt('sessionTimeout', userSessionExpirationMinsWeb);
    if (prefs.getString('lastSynced') != null) await prefs.remove('lastSynced');

    SessionTimeout().sessionTimeout = Duration(minutes: userSessionExpirationMinsWeb);

    userNotifier.value = user;

    //await SyncModel().sync();

    notifyListeners();
  }

  Future<void> logout({bool forcedOut = false}) async {
    if (!forcedOut) {
      final request = await SystemRequest.create();
      unawaited(Future<void>(() async {
        try {
          await ApiModel().logout(request);
        } catch (e) {
          if (kDebugMode) print('failed to log out of server $e');
        }
      }));
    }

    try {
      if (MyPlatform.isAndroid || MyPlatform.isIOS || MyPlatform.isWeb) await FirebaseAnalytics.instance.setUserId();

      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('userId');
      await prefs.remove('serverIP');
      await prefs.remove('databaseName');
      await prefs.remove('sessionId');
      await prefs.remove('lastSynced');
    } catch (e) {
      if (kDebugMode) print('failed to delete preferences $e');
    }

    userNotifier.value = User.empty();

    notifyListeners();
  }
}
