// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delete_chat_group_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeleteChatGroupRequest _$DeleteChatGroupRequestFromJson(
        Map<String, dynamic> json) =>
    DeleteChatGroupRequest(
      groupId: json['GroupId'] as String,
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$DeleteChatGroupRequestToJson(
        DeleteChatGroupRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'GroupId': instance.groupId,
    };

DeleteChatGroupResponse _$DeleteChatGroupResponseFromJson(
        Map<String, dynamic> json) =>
    DeleteChatGroupResponse(
      errorCode: json['ErrorCode'] as String,
      errorMessage: json['ErrorMessage'] as String,
    );

Map<String, dynamic> _$DeleteChatGroupResponseToJson(
        DeleteChatGroupResponse instance) =>
    <String, dynamic>{
      'ErrorCode': instance.errorCode,
      'ErrorMessage': instance.errorMessage,
    };
