{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Flutter: Attach to <PERSON><PERSON>",
            "type": "dart",
            "request": "attach"
        }
        
    //     {
    //     "name": "Flutter WebServer",
    //     "request": "launch",
    //     "type": "dart",
    //     "args": ["-d", "web-server","--web-port", "9876"],
    // }
    // {
    //     "name": "Flutter Chrome",
    //     "request": "launch",
    //     "type": "dart",
    //     "args": ["-d", "chrome","--web-port", "9876"],
    // }
]
}