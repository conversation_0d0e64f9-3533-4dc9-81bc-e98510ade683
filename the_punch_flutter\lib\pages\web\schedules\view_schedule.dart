import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../../dataModel/data/location.dart';
import '../../../dataModel/data/schedule.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data_model.dart';
import '../../../dialogs/material_dialog.dart';
import '../../../misc/app_localization.dart';
import '../../view_model_mixin.dart';
import '../../../state/permissions_state.dart';
import '../../../widgets/active_toggle.dart';
import '../../../widgets/decorated_text_field.dart';
import '../../../misc/extensions.dart';
import '../../../widgets/notes_widget.dart';
import '../home/<USER>';
import '../my_scaffold.dart';

class ViewSchedulePage extends StatelessWidget {
  final String scheduleId;

  ViewSchedulePage(Map<String, String> queryParms, {super.key})
      : scheduleId = queryParms['id'] ?? '';

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
      create: (context) => _ViewModel(scheduleId),
      child: MyScaffold(
        title: AppLocalizations.of(context)!.viewSchedule,
        body: _Body(),
      ));
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1300),
          child: Column(
            children: [
              _BodyHeader(),
              _Row1(),
              _Row2(),
              _Row3(),
            ],
          ),
        ),
      );
}

class _BodyHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);

    final headline6 = Theme.of(context)
        .textTheme
        .titleLarge
        ?.copyWith(fontWeight: FontWeight.bold);
    return IntrinsicHeight(child: Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final schedule = viewModel.schedule;
        if (schedule == null) return Container();
        final s = '${AppLocalizations.of(context)!.scheduledTime}: '
            '(${schedule.startDateLocal.toFormattedDayNoLocal(locale)}) '
            '${schedule.startDateLocal.toFormattedDateTimeNoLocal(locale)} - '
            '${schedule.endDateLocal.toFormattedTimeNoLocal(locale)} '
            '(${schedule.endDateUtc.difference(schedule.startDateUtc).toFormatted})';
        return Stack(
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Text(s, style: headline6),
              ),
            ),
            Align(
              alignment: Alignment.centerRight,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: ElevatedButton.icon(
                      onPressed: () => unawaited(showDialog(
                        context: context,
                        builder: (context) => MaterialDialog(
                            child:
                                NotesWidget(scheduleId: viewModel.scheduleId)),
                      )),
                      icon: const Icon(Icons.edit),
                      label: Text(AppLocalization.of(context).notes),
                    ),
                  ),
                  if (PermissionsState().editSchedules)
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8),
                          child: ActiveButton(
                            value: schedule.isActive,
                            onChanged: (value) =>
                                unawaited(viewModel.setActive(value)),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8),
                          child: ElevatedButton(
                            onPressed: () async => context.pushNamed(
                                '/schedules/edit',
                                queryParameters: {'id': schedule.id}),
                            child: Text(
                                AppLocalizations.of(context)!.editSchedule),
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
          ],
        );
      },
    ));
  }
}

class _Row1 extends StatelessWidget {
  @override
  Widget build(BuildContext context) => IntrinsicHeight(
        child: Consumer<_ViewModel>(
          builder: (context, viewModel, child) {
            final location = viewModel.location;
            final employee = viewModel.employee;
            if (location == null || employee == null) return Container();
            return Flex(
              direction: Axis.horizontal,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Flexible(
                    flex: 2,
                    child: DecoratedText(
                      padding: const EdgeInsets.all(8),
                      text: location.name,
                      labelText: AppLocalizations.of(context)!.location,
                    )),
                Flexible(
                    flex: 2,
                    child: DecoratedText(
                      padding: const EdgeInsets.all(8),
                      text: employee.name,
                      labelText: AppLocalizations.of(context)!.employee,
                    )),
              ],
            );
          },
        ),
      );
}

class _Row2 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);

    return IntrinsicHeight(
      child: Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          final schedule = viewModel.schedule;
          if (schedule == null) return Container();
          final timeZone = viewModel.location?.timeZone ?? '';

          return Flex(
            direction: Axis.horizontal,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Flexible(
                flex: 8,
                child: DecoratedText(
                  padding: const EdgeInsets.all(8),
                  text: schedule.startDateLocal.toFormattedDateNoLocal(locale),
                  labelText: AppLocalizations.of(context)!.date,
                ),
              ),
              Flexible(
                  flex: 10,
                  child: DecoratedText(
                    padding: const EdgeInsets.all(8),
                    text:
                        schedule.startDateLocal.toFormattedTimeNoLocal(locale),
                    labelText:
                        AppLocalizations.of(context)!.startLocationTimeZone,
                  )),
              Flexible(
                  flex: 10,
                  child: DecoratedText(
                    padding: const EdgeInsets.all(8),
                    text: schedule.endDateLocal.toFormattedTimeNoLocal(locale),
                    labelText:
                        AppLocalizations.of(context)!.endTimeLocationTimeZone,
                  )),
              Flexible(
                  flex: 8,
                  child: DecoratedText(
                    padding: const EdgeInsets.all(8),
                    text: schedule.endDateUtc
                        .difference(schedule.startDateUtc)
                        .toFormatted,
                    labelText: AppLocalizations.of(context)!.duration,
                  )),
              Flexible(
                  flex: 10,
                  child: DecoratedText(
                    padding: const EdgeInsets.all(8),
                    text: timeZone,
                    labelText: AppLocalizations.of(context)!.timeZone,
                  )),
            ],
          );
        },
      ),
    );
  }
}

class _Row3 extends StatelessWidget {
  @override
  Widget build(BuildContext context) => IntrinsicHeight(
        child: Consumer<_ViewModel>(
          builder: (context, viewModel, child) {
            final schedule = viewModel.schedule;
            if (schedule == null) return Container();
            return Flex(
              direction: Axis.horizontal,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Flexible(
                    child: DecoratedText(
                  padding: const EdgeInsets.all(8),
                  isEmpty: false,
                  text: viewModel.nextLocation?.name ?? '',
                  labelText: AppLocalizations.of(context)!.nextLocation,
                )),
                Flexible(
                    child: DecoratedText(
                  padding: const EdgeInsets.all(8),
                  isEmpty: false,
                  text: schedule.travelTime?.toFormatted ?? '',
                  labelText: AppLocalizations.of(context)!.travelTime,
                )),
              ],
            );
          },
        ),
      );
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String scheduleId;
  Schedule? schedule;
  Location? location;
  User? employee;
  Location? nextLocation;

  _ViewModel(this.scheduleId) {
    addListenables([
      DataModel().scheduleModel,
      DataModel().locationModel,
      DataModel().userModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    schedule = await DataModel().scheduleModel.getById(scheduleId);
    if (schedule == null) return;
    location = await DataModel().locationModel.getById(schedule!.locationId);
    employee = await DataModel().userModel.getById(schedule!.userId);
    if (schedule?.nextLocationId != null) {
      nextLocation =
          await DataModel().locationModel.getById(schedule!.nextLocationId!);
    }
    notifyListeners();
  }

  Future<void> setActive(bool isActive) async {
    schedule!.isActive = isActive;
    await DataModel().scheduleModel.saveDirty([schedule!]);
  }
}
