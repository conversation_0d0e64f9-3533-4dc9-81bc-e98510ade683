import 'dart:async';
import 'package:async/async.dart';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import '../dataModel/data/travel_pings.dart';
import '../dataModel/data_model.dart';
import '../dataModel/data/geo_location.dart';
import '../dataModel/data/punch_card.dart';
import '../misc/my_platform.dart';
import 'login_state.dart';
import 'punch_state.dart';
import 'server_time_state.dart';

class LocationState extends ChangeNotifier {
  static LocationState? _singleton;

  factory LocationState() {
    _singleton ??= LocationState._();
    return _singleton!;
  }

  LocationState._();

  static LocationState get instance => _singleton!;

  CancelableOperation? getPositionOperation;
  var _isInitialized = false;
  Position? _currentPosition;

  Future<void> _initialize() async {
    if (!MyPlatform.isAndroid && !MyPlatform.isIOS && !MyPlatform.isWeb) {
      return Future.error(
          'Location services only available on android, ios and web.');
    }

    if (_isInitialized) return;

    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    print('Service enabled: $serviceEnabled');
    _isInitialized = true;
    if (!serviceEnabled) {
      return Future.error('Location services are disabled.');
    }

    permission = await Geolocator.checkPermission();
    print('Initial permission: $permission');
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      print('Requested permission: $permission');
      if (permission == LocationPermission.deniedForever) {
        return Future.error(
            'Location permissions are permanently denied, we cannot request permissions.');
      }

      if (permission == LocationPermission.denied) {
        return Future.error('Location permissions are denied');
      }
    }

    // Specifically request background location permission for Android 10 and above
    if (permission == LocationPermission.whileInUse && MyPlatform.isAndroid) {
      permission = await Geolocator.requestPermission();
      print('Background location permission: $permission');
      if (permission != LocationPermission.always) {
        await openAppSettings();
        return Future.error('Background location permissions are denied');
      }
    } else if (permission == LocationPermission.whileInUse &&
        MyPlatform.isIOS) {
      permission = await Geolocator.requestPermission();
      print('Requested Always permission: $permission');
      if (permission != LocationPermission.always) {
        await openAppSettings();
        return Future.error('Always location permissions are denied.');
      }
    }

    // Now check for motion permissions
    await _checkAndRequestMotionPermission(); // Call to check and request motion permission
  }

  Future<void> _checkAndRequestMotionPermission() async {
    if (MyPlatform.isAndroid) {
      // Request activity recognition permission for Android 10 and above
      var motionPermissionStatus = await Permission.activityRecognition.status;

      if (motionPermissionStatus.isDenied) {
        var result = await Permission.activityRecognition.request();
        if (result.isDenied || result.isPermanentlyDenied) {
          print('Motion activity permission denied.');
          await openAppSettings(); // Prompt the user to open app settings if permanently denied
          return Future.error('Motion activity permissions are denied.');
        } else {
          print('Motion activity permission granted on Android.');
        }
      } else if (motionPermissionStatus.isGranted) {
        print('Motion activity permission already granted on Android.');
      }
    } else if (MyPlatform.isIOS) {
      // iOS-specific handling for motion and sensor permissions

      //var sensorResult = await Permission.sensors.request();

      var sensorPermissionStatus = await Permission.sensors.status;
      if (sensorPermissionStatus.isDenied) {
        var result = await Permission.sensors.request();
        if (result.isDenied || result.isPermanentlyDenied) {
          print('Sensor permission denied on iOS.');
          await openAppSettings();
          return Future.error('Sensor permissions are denied.');
        } else {
          print('Sensor permission granted on iOS.');
        }
      } else {
        print('Sensor permission already granted on iOS.');
      }
    }
  }

  static Future<void> updateCurrentLocation() async {
    if (!MyPlatform.isAndroid && !MyPlatform.isIOS && !MyPlatform.isWeb) {
      return;
    }

    try {
      await LocationState()
          ._initialize(); // Ensure initialization happens before getting location
      LocationState()._currentPosition = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.best);
      print('Updated current position: ${LocationState()._currentPosition}');
      _singleton
          ?.notifyListeners(); // Notify listeners when location is updated
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching location: $e');
      }
    }
  }

  static Future<LocationPermission> checkAndRequestPermissions() async {
    if (!MyPlatform.isAndroid && !MyPlatform.isIOS && !MyPlatform.isWeb) {
      return Future.error(
          'Location services only available on android, ios and web.');
    }

    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return Future.error('Location services are disabled.');
    }

    permission = await Geolocator.checkPermission();

    // Check for location permissions first
    if (permission != LocationPermission.always) {
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.deniedForever) {
          return LocationPermission.deniedForever;
        }

        if (permission == LocationPermission.denied) {
          return LocationPermission.denied;
        }
      }

      // Request background location permissions on Android and iOS
      if (permission == LocationPermission.whileInUse && MyPlatform.isAndroid) {
        permission = await Geolocator.requestPermission();
        if (permission != LocationPermission.always) {
          await openAppSettings();
          return LocationPermission.whileInUse;
        }
      } else if (permission == LocationPermission.whileInUse &&
          MyPlatform.isIOS) {
        permission = await Geolocator.requestPermission();
        if (permission != LocationPermission.always) {
          await openAppSettings();
          return LocationPermission.whileInUse;
        }
      }
    } else {
      print('Location permissions are good!');
    }

    // Check for motion activity permission (needed for both iOS and Android)
    await LocationState()._checkAndRequestMotionPermission();

    return permission;
  }

  Position? get currentPosition => _currentPosition;

  Future<DateTime> getUtcTime() async => _initialize()
      .then((_) =>
          Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.low))
      .then((position) => position.timestamp);

  Future<Position?> get lastLocationData async {
    try {
      await _initialize();
      return await Geolocator.getLastKnownPosition();
    } catch (e) {
      if (kDebugMode) print('Error fetching last known position: $e');
      return null;
    }
  }
}
