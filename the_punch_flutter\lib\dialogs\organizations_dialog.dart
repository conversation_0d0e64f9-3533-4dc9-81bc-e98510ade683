import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import '../api/api_model.dart';
import '../dataModel/data/organization.dart';
import 'constrained_search_dialog.dart';
import '../widgets/padded_card.dart';

class OrganizationsDialog extends StatelessWidget {
  const OrganizationsDialog({super.key, required this.organizationSelected});

  final Function(String) organizationSelected;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final secondaryBodyText1 = theme.textTheme.bodyLarge?.copyWith(color: theme.colorScheme.secondary);

    return ConstrainedSearchDialog(
      hintText: AppLocalizations.of(context)!.organizationId,
      autofocus: true,
      builder: (search) => [
        FutureBuilder<Iterable<Organization>>(
            future: Future(() async => await ApiModel().getOrganizations(search)),
            builder: (context, snapshot) {
              if (!snapshot.hasData) return Container();
              final organizations = snapshot.data!;
              if (search.isNotEmpty && organizations.isEmpty) {
                return Padding(
                  padding: const EdgeInsets.all(8),
                  child: Text(AppLocalizations.of(context)!.organizationIdNotFound),
                );
              }
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  for (final organization in organizations)
                    GestureDetector(
                        onTap: () {
                          organizationSelected(organization.organizationId);
                          Navigator.pop(context);
                        },
                        child: PaddedCard(
                            child: Column(children: [
                          Center(child: Text(organization.name, style: secondaryBodyText1)),
                          Center(child: Text(organization.organizationId)),
                        ])))
                ],
              );
            })
      ],
    );
  }
}
