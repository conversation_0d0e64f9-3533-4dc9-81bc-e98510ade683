import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import '../dataModel/data/permission.dart';
import '../dataModel/data_model.dart';
import 'constrained_search_dialog.dart';
import '../misc/change_notification_builder.dart';
import '../pages/view_model_mixin.dart';
import '../widgets/padded_card.dart';

class PermissionsDialog extends StatelessWidget {
  final Function(String key) selected;
  final Iterable<String> ignorePermissionKeys;

  const PermissionsDialog({super.key, required this.selected, this.ignorePermissionKeys = const []});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline6 = theme.textTheme.titleLarge;

    return ChangeNotifierBuilder<_ViewModel>(
        create: (context) => _ViewModel(ignorePermissionKeys: ignorePermissionKeys),
        builder: (context, viewModel, child) {
          if (viewModel.permissions.isEmpty) return Container();
          return ConstrainedSearchDialog(
              title: Text(AppLocalizations.of(context)!.permissions, style: headline6),
              builder: (search) {
                final visiblePermissions = <Permission>[];
                visiblePermissions.addAll(viewModel.permissions.where((e) => _permissionMatches(e, search)));
                return visiblePermissions.map((e) => _Tile(permission: e, permissionSelected: selected));
              });
        });
  }

  bool _permissionMatches(Permission permission, String search) {
    if (permission.name.toLowerCase().contains(search.toLowerCase())) return true;
    if (permission.description.toLowerCase().contains(search.toLowerCase())) return true;
    return false;
  }
}

class _Tile extends StatelessWidget {
  const _Tile({required this.permissionSelected, required this.permission});

  final Permission permission;
  final Function(String key) permissionSelected;

  @override
  Widget build(BuildContext context) => GestureDetector(
        onTap: () => permissionSelected(permission.key),
        child: PaddedCard(
          child: Center(
              child: Column(
            children: [
              Text(permission.name),
              Text(permission.description),
            ],
          )),
        ),
      );
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  Iterable<Permission> permissions = [];
  final Iterable<String> ignorePermissionKeys;

  _ViewModel({this.ignorePermissionKeys = const []}) {
    addListenables([DataModel().permissionModel]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    permissions = (await DataModel().permissionModel.active).where((e) => !ignorePermissionKeys.contains(e.key));
    notifyListeners();
  }
}
