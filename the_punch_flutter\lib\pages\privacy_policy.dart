import 'package:flutter/material.dart';
import 'package:separated_column/separated_column.dart';
import '../menus/my_app_bar.dart';

class PrivacyPolicyPage extends StatelessWidget {
  const PrivacyPolicyPage({super.key});

  @override
  Widget build(BuildContext context) {
    final headlineMedium = Theme.of(context).textTheme.headlineMedium;
    final headlineSmall = Theme.of(context).textTheme.headlineSmall;

    return Scaffold(
      appBar: MyAppBar(context: context, title: 'Privacy Policy').appBar,
      
      body: Center(
          child: SingleChildScrollView(
        child: SeparatedColumn(
          separatorBuilder: (_, __) => const SizedBox(height: 8),
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Align(child: Image.asset('images/the_punch_logo_small.png', scale: 2)),
            Align(child: Text('Privacy Policy for The Punch', style: headlineMedium)),
            Text('Effective date', style: headlineSmall),
            const Text('March 6, 2023'),
            const Text(
                'Our cleaning service app, The Punch (the "App"), is committed to protecting your privacy. This Privacy Policy explains how we collect, use, and protect your personal information. By using the App, you consent to the collection, use, and disclosure of your personal information as described in this Privacy Policy.'),
            Text('Information we collect', style: headlineSmall),
            const Text('We collect information that you provide directly to us, such as your name, email address, phone number, and payment information. We also collect information about your use of the App, such as your location data and usage statistics.'),
            Text('Location tracking', style: headlineSmall),
            const Text(
                'We may collect and use your location data to provide you with location-based services, such as matching you with a nearby cleaner or providing you with directions to a cleaning appointment. We may also use your location data for analytics purposes, such as analyzing usage patterns to improve the App.'),
            Text('Disclosure of information', style: headlineSmall),
            const Text('We do not disclose your personal information to third-party service providers or other third parties, except as required by law or to protect our rights or property.'),
            Text('Security', style: headlineSmall),
            const Text(
                'We take reasonable measures to protect your personal information from unauthorized access, disclosure, or destruction. However, no method of transmission over the Internet or electronic storage is completely secure, so we cannot guarantee the absolute security of your information.'),
            Text('Your choices', style: headlineSmall),
            const Text('You may choose not to provide us with certain information, but this may limit your ability to use certain features of the App. You may also disable location tracking in your device settings, but this may affect the functionality of the App.'),
            Text('Updates to this policy', style: headlineSmall),
            const Text('We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page.'),
            Text('Contact us', style: headlineSmall),
            const Text('If you have any questions or concerns about this Privacy Policy or our privacy practices, please contact us at [Insert contact information].'),
          ],
        ),
      )),
    );
  }
}
