import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../misc/json_conversion.dart';
import 'system.dart';

part 'edit_group_members_request.g.dart';

@JsonSerializable(includeIfNull: false, fieldRename: FieldRename.none)
class EditGroupMembersRequest extends SystemRequest {
  @JsonKey(name: 'GroupId')
  final String groupId;

  @JsonKey(name: 'AddUserIds')
  final List<String> addUserIds;

  @JsonKey(name: 'RemoveUserIds')
  final List<String> removeUserIds;

  EditGroupMembersRequest({
    required this.groupId,
    required this.addUserIds,
    required this.removeUserIds,
    required String serverIP,
    required String databaseName,
    required String sessionId,
  }) : super(
          serverIP: serverIP,
          databaseName: databaseName,
          sessionId: sessionId,
        );

  static Future<EditGroupMembersRequest> create({
    required String groupId,
    required List<String> addUserIds,
    required List<String> removeUserIds,
  }) async {
    final systemReq = await SystemRequest.create();
    return EditGroupMembersRequest(
      groupId: groupId,
      addUserIds: addUserIds,
      removeUserIds: removeUserIds,
      serverIP: systemReq.serverIP,
      databaseName: systemReq.databaseName,
      sessionId: systemReq.sessionId,
    );
  }

  factory EditGroupMembersRequest.fromJson(Map<String, dynamic> json) =>
      _$EditGroupMembersRequestFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$EditGroupMembersRequestToJson(this);
}

@JsonSerializable(includeIfNull: false, fieldRename: FieldRename.none)
class EditGroupMembersResponse extends SystemResponse {
  @JsonKey(name: 'SuccessMessage')
  final String? successMessage;

  @JsonKey(name: 'AddedCount')
  final int? addedCount;

  @JsonKey(name: 'RemovedCount')
  final int? removedCount;

  EditGroupMembersResponse({
    this.successMessage,
    this.addedCount,
    this.removedCount,
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory EditGroupMembersResponse.fromJson(Map<String, dynamic> json) =>
      _$EditGroupMembersResponseFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$EditGroupMembersResponseToJson(this);
}
