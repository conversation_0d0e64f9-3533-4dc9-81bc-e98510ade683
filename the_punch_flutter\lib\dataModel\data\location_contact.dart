import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../misc/json_conversion.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import 'package:uuid/uuid.dart';

import '../base_data.dart';

part 'location_contact.g.dart';

@HiveType(typeId: 19)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class LocationContact extends BaseData {
  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  @Json<PERSON>ey(fromJson: idFromJson)
  String locationId;

  @HiveField(102)
  @JsonKey(fromJson: idFromJson)
  String userId;

  @HiveField(103)
  @JsonKey(name: 'ContactLocationRank')
  int order;

  LocationContact({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.locationId,
    required this.userId,
    required this.order,
  });

  factory LocationContact.fromJson(Map<String, dynamic> json) {
    try {
      return _$LocationContactFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }
  Map<String, dynamic> toJson() => _$LocationContactToJson(this);

  factory LocationContact.create() => LocationContact(
        id: const Uuid().v4(),
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
        userId: '',
        locationId: '',
        order: 0,
      );
}
