import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:system_clock/system_clock.dart';
import '../api/api_model.dart';
import 'location_state.dart';
import 'login_state.dart';

class ServerTimeState {
  static ServerTimeState? _singleton;
  factory ServerTimeState() {
    _singleton ??= ServerTimeState._();
    return _singleton!;
  }
  ServerTimeState._() {
    ApiModel().serverTime.addListener(() {
      if (ApiModel().serverTime.value != null) _singleton!.setUtcTimeFromServer(ApiModel().serverTime.value!);
    });
    unawaited(_initialize());
  }

  DateTime? _bootTime;

  Future<void> _initialize() async {
    if (!LoginState.isLoggedIn) return;

    // if the device rebooted since the last update, we need to update the boot time

    // what is the current boot time
    final bootTime = DateTime.now().subtract(SystemClock.elapsedRealtime()).toUtc();

    // what was the boot time when we last updated utc
    final prefs = await SharedPreferences.getInstance();
    final lastBootString = prefs.getString('bootTime');
    DateTime lastBootTime;
    if (lastBootString != null) {
      lastBootTime = DateTime.parse(lastBootString);
    } else {
      lastBootTime = DateTime(1970);
    }

    // if the last boot time is the same as the current, then we can use the last
    // otherwise, we need to calculate when the boot time was in utc
    if (bootTime.difference(lastBootTime).abs().inMinutes < 2) {
      _bootTime = lastBootTime;
      return;
    }
    // find the actual utc time from the server and set the utcTime field
    try {
      setUtcTimeFromServer(await ApiModel().getServerTime());
      return;
    } catch (e) {
      if (kDebugMode) print('can not find utc time from server $e');
    }

    // if we couldn't get the utc time from the server, try gps
    try {
      setUtcTimeFromGPS(await LocationState().getUtcTime());
    } catch (e) {
      if (kDebugMode) print('can not find utc time from gps $e');
    }
  }

  DateTime get utcTime {
    if (_bootTime == null) return DateTime.now().toUtc();
    return _bootTime!.add(SystemClock.elapsedRealtime());
  }

  void setUtcTimeFromServer(DateTime value) {
    _bootTime = value.subtract(SystemClock.elapsedRealtime());
    unawaited(_update());
  }

  void setUtcTimeFromGPS(DateTime value) {
    if (_bootTime == null) {
      _bootTime = value.subtract(SystemClock.elapsedRealtime());
      unawaited(_update());
    }
  }

  Future<void> _update() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('bootTime', _bootTime!.toIso8601String());
  }
}
