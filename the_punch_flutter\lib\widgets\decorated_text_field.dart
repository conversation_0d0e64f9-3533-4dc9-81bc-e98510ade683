import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../helpers/color_helper.dart';
import '../misc/app_localization.dart';

class DecoratedTextField extends StatefulWidget {
  final String? initialValue;
  final String? labelText;
  final String? hintText;
  final bool enabled;
  final Function(String value)? onChanged;
  final EdgeInsets padding;
  final String? Function(String? value)? validator;
  final bool autofocus;
  final int? minLines;
  final int? maxLines;
  final bool obscureText;
  final Iterable<String>? autofillHints;
  final Iterable<Widget>? suffixIcons;
  final TextEditingController? controller;
  final List<TextInputFormatter>? inputFormatters;
  final TextInputType? keyboardType;
  final Function(String value)? onFieldSubmitted;
  final TextStyle? textStyle; // Add this line

  const DecoratedTextField({
    super.key,
    this.initialValue,
    this.labelText,
    this.onChanged,
    this.enabled = true,
    this.padding = EdgeInsets.zero,
    this.validator,
    this.autofocus = false,
    this.minLines,
    this.maxLines = 1,
    this.obscureText = false,
    this.autofillHints,
    this.suffixIcons,
    this.controller,
    this.inputFormatters,
    this.keyboardType,
    this.onFieldSubmitted,
    this.hintText,
    this.textStyle, // Add this line
  });

  @override
  State<DecoratedTextField> createState() => _DecoratedTextFieldState();
}

class _DecoratedTextFieldState extends State<DecoratedTextField> {
  bool _obscureText = true;

  @override
  Widget build(BuildContext context) => Padding(
        padding: widget.padding,
        child: TextFormField(
          controller: widget.controller,
          initialValue: widget.initialValue,
          onChanged: widget.onChanged,
          enabled: widget.enabled,
          validator: widget.validator,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          autofocus: widget.autofocus,
          minLines: widget.minLines,
          maxLines: widget.maxLines,
          obscureText: widget.obscureText && _obscureText,
          autofillHints: widget.autofillHints,
          inputFormatters: widget.inputFormatters,
          keyboardType: widget.keyboardType,
          onFieldSubmitted: widget.onFieldSubmitted,
          style: widget.textStyle ?? Theme.of(context).textTheme.bodyMedium,
          decoration: InputDecoration(
            labelText: widget.labelText,
            hintText: widget.hintText,
            labelStyle: Theme.of(context).textTheme.titleMedium,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: Colors.blue,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Colors.grey.shade300,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: ColorHelper.thePunchBlue(),
                width: 2,
              ),
            ),
            filled: true,
            fillColor: Colors.white,
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            suffixIcon: !widget.obscureText && widget.suffixIcons == null
                ? null
                : Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (widget.suffixIcons != null) ...widget.suffixIcons!,
                      if (widget.obscureText)
                        IconButton(
                          icon: Icon(_obscureText
                              ? Icons.visibility
                              : Icons.visibility_off),
                          onPressed: showPasswordPressed,
                        ),
                    ],
                  ),
          ),
        ),
      );

  void showPasswordPressed() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }
}

class DecoratedText extends StatelessWidget {
  final String text;
  final String? labelText;
  final EdgeInsets padding;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? Function()? validator;
  final bool? filled;
  final bool? isEmpty;
  final bool centered;
  final TextStyle? textStyle;

  const DecoratedText({
    super.key,
    required this.text,
    this.labelText,
    this.prefixIcon,
    this.suffixIcon,
    this.validator,
    this.filled,
    this.isEmpty,
    this.padding = EdgeInsets.zero,
    this.centered = false,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    Widget child = Text(
      text,
      style: textStyle ?? Theme.of(context).textTheme.titleMedium?.copyWith(
            color: ColorHelper.thePunchLightGray(),
            fontWeight: FontWeight.normal,

          ),
    );
    if (centered) child = Center(child: child);
    return Padding(
      padding: padding,
      child: DecoratedContainer(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15),
          borderSide: BorderSide(
            color: ColorHelper.thePunchAdminButtonBlue(),
          ),
        ),
        labelText: labelText,
        validator: validator,
        isEmpty: isEmpty ?? text.isEmpty,
        prefixIcon: prefixIcon,
        suffixIcon: suffixIcon,
        filled: filled ?? true, // Default to filled
        child: child,
      ),
    );
  }
}

class DecoratedContainer extends StatelessWidget {
  final String? labelText;
  final Widget? child;
  final EdgeInsets padding;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? Function()? validator;
  final bool isEmpty;
  final bool filled;
  final InputBorder? border;
  final bool centered;
  final double? maxHeight;

  const DecoratedContainer({
    super.key,
    this.child,
    this.labelText,
    this.padding = EdgeInsets.zero,
    this.prefixIcon,
    this.suffixIcon,
    this.validator,
    this.isEmpty = false,
    this.filled = true,
    this.border,
    this.centered = false,
    this.maxHeight,
  });

  @override
  Widget build(BuildContext context) {
    var child0 = child;
    if (centered) child0 = Center(child: child0);
    if (maxHeight != null) {
      child0 = Container(
          constraints: BoxConstraints(maxHeight: maxHeight!), child: child0);
    }
    return Padding(
      padding: padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 2),
          InputDecorator(
            isEmpty: child == null || isEmpty,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: ColorHelper.thePunchAdminButtonBlue(),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: Colors.grey.shade300,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: ColorHelper.thePunchAdminButtonBlue(),
                  width: 2,
                ),
              ),
              labelStyle: const TextStyle(fontWeight: FontWeight.w100),
              hintStyle: const TextStyle(fontWeight: FontWeight.w100),
              prefixIcon: prefixIcon,
              suffixIcon: suffixIcon,
              filled: filled,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              errorText: validator?.call(),
            ),
            child: child0,
          )
        ]
      )
    );
  }
}
