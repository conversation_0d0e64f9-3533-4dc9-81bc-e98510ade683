import 'dart:async';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_background_geolocation/flutter_background_geolocation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:geolocator/geolocator.dart' as geolocator;
import 'package:flutter_background_geolocation/flutter_background_geolocation.dart'
    as bg;
import 'package:shared_preferences/shared_preferences.dart';
import '../dataModel/data/geo_location.dart';
import '../dataModel/data_model.dart';
import '../firebase_options.dart';
import '../state/app_state.dart';
import '../state/location_ping_state.dart';
import '../state/server_time_state.dart';
import '../the_punch_app.dart';
import '../misc/my_platform.dart';

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

bool? geoFenceBreached;
StreamController<String?> geoFenceStreamController =
    StreamController<String?>.broadcast();

// ----------------------------------------------------------------------------
// ORIGINAL: locationUpdateTimer, changed to unify the single timer approach
// ----------------------------------------------------------------------------
Timer? locationUpdateTimer;
DateTime? breachTime;
/// Returns `true` if our periodic timer is active. 
bool get isLocationUpdateTimerRunning =>
    locationUpdateTimer != null && locationUpdateTimer!.isActive;

// ----------------------------------------------------------------------------
// We'll keep your original logic for "isLocationUpdatesRunning" (SharedPref key).
// This can be used as well if you want. 
// ----------------------------------------------------------------------------
const _kIsLocationUpdatesRunningKey = 'isLocationUpdatesRunningKey';

Future<bool> getIsLocationUpdatesRunning() async {
  final prefs = await SharedPreferences.getInstance();
  return prefs.getBool(_kIsLocationUpdatesRunningKey) ?? false;
}

Future<void> setIsLocationUpdatesRunning(bool value) async {
  final prefs = await SharedPreferences.getInstance();
  await prefs.setBool(_kIsLocationUpdatesRunningKey, value);
  print('setIsLocationUpdatesRunning: $value');
}

// ----------------------------------------------------------------------------
// Additional SharedPreferences keys & helpers
// ----------------------------------------------------------------------------
Future<void> setServiceRunning(bool value) async {
  print('setServiceRunning called with value: $value');
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  await prefs.setBool('isServiceRunning', value);
}

Future<bool> getServiceRunning() async {
  print('getServiceRunning called');
  final SharedPreferences prefs = await SharedPreferences.getInstance();
  bool value = prefs.getBool('isServiceRunning') ?? false;
  return value;
}

// ----------------------------------------------------------------------------
// Single Timer Approach: start and stop the periodic location updates timer
// ----------------------------------------------------------------------------
Future<void> startLocationUpdateTimer() async {
  // If we already have a timer active, just skip
  if (isLocationUpdateTimerRunning) {
    print('Location update timer is already running; not starting a new one.');
    return;
  }

  // We also set the "isLocationUpdatesRunning" to true in SharedPrefs
  await setIsLocationUpdatesRunning(true);

  print('Starting periodic Timer for location updates...');
  locationUpdateTimer = Timer.periodic(const Duration(seconds: 60), (timer) async {
    // Attempt to fetch the current location from BG (sample approach)
    try {
      final bg.Location current = await bg.BackgroundGeolocation.getCurrentPosition(
        samples: 1,
        persist: false,
        desiredAccuracy: bg.Config.DESIRED_ACCURACY_NAVIGATION,
        timeout: 30,
      );
      // Then call your existing saveLocationPing
      await saveLocationPing(current, pingSource: 'TimerPeriodic');
    } catch (e) {
      print('Error in timer periodic getCurrentPosition: $e');
    }
  });
}

Future<void> stopLocationUpdateTimer() async {
  if (locationUpdateTimer != null) {
    print('Cancelling periodic Timer for location updates...');
    locationUpdateTimer!.cancel();
    locationUpdateTimer = null;
  }
}

// ----------------------------------------------------------------------------
// initializeService
// ----------------------------------------------------------------------------
Future<void> initializeService() async {
  print('initializeService called');
  await bg.BackgroundGeolocation.ready(
    bg.Config(
      enableHeadless: true, // Enable headless mode
      debug: true,
      logLevel: bg.Config.LOG_LEVEL_VERBOSE,
      desiredAccuracy: bg.Config.DESIRED_ACCURACY_HIGH,
      distanceFilter: 50,
      stationaryRadius: 25,
      preventSuspend: true,
      stopTimeout: 1440, // in minutes (24 hours)
      heartbeatInterval: 60, // Heartbeat every 60 seconds when stationary
      stopOnTerminate: false, // Ensure service does not stop on terminate
      startOnBoot: false,     // Optionally start the service on boot
      disableMotionActivityUpdates: false,
      foregroundService: true, // Force Android foreground service
    ),
  ).then((bg.State state) async {
    print('BackgroundGeolocation.ready completed with state: $state');
    print('MyAppDebug: Service initialized and ready.');

    // ----------------------------------------------------------------------
    // Auto-resume if we already had location updates running.
    // ----------------------------------------------------------------------
    bool wasRunning = await getIsLocationUpdatesRunning();
    print('MyAppDebug: wasRunning from SharedPrefs? $wasRunning');
    print('MyAppDebug: Current BG state: enabled=${state.enabled}');

    // If we had marked them as "wasRunning", but the BG plugin isn't started, 
    // then start it again:
    if (wasRunning && !state.enabled) {
      print('MyAppDebug: Auto-resuming plugin because wasRunning == true');
      await bg.BackgroundGeolocation.start();
      print('MyAppDebug: BackgroundGeolocation is now started.');
    }

    // If we were previously running, start (or resume) our periodic timer:
    if (wasRunning) {
      await startLocationUpdateTimer();
    }

  }).catchError((error) {
    print('Error initializing BackgroundGeolocation: $error');
  });

  // ----------------------------------------------------------------------
  // onLocation
  // Instead of calling "saveLocationPing" here, we will just ensure 
  // the timer is running:
  // ----------------------------------------------------------------------
  bg.BackgroundGeolocation.onLocation((bg.Location location) async {
    print('[onLocation] lat=${location.coords.latitude}, '
          'lng=${location.coords.longitude}, '
          'accuracy=${location.coords.accuracy}, '
          'isMoving=${location.isMoving}');

    // *** New Approach: Just ensure the timer is started ***
    if (!isLocationUpdateTimerRunning) {
      await startLocationUpdateTimer();
    }

    // If you truly want to do an immediate ping here, you could call 
    // "saveLocationPing(location)" directly. But your request was 
    // "only the Timer saves location pings," so we skip that.
    // ------------------------------------------------------------------
    // OLD CODE (commented out):
    // await saveLocationPing(location, pingSource: 'onLocation');
    // ------------------------------------------------------------------
  }, (bg.LocationError error) {
    print('[onLocation] ERROR - $error');
  });

  // ----------------------------------------------------------------------
  // onMotionChange
  // same approach
  // ----------------------------------------------------------------------
  bg.BackgroundGeolocation.onMotionChange((bg.Location location) async {
    print('onMotionChange event triggered with location: $location');

    // *** Just ensure the timer is started ***
    if (!isLocationUpdateTimerRunning) {
      await startLocationUpdateTimer();
    }

    // OLD CODE (commented out):
    // if (MyPlatform.isIOS) {
    //   await saveLocationPing(location, pingSource: 'onMotionChange');
    // } else if(MyPlatform.isAndroid){
    //   await saveLocationPing(location, pingSource: 'onMotionChange');
    // }
  });

  // ----------------------------------------------------------------------
  // onHeartbeat
  // same approach
  // ----------------------------------------------------------------------
  bg.BackgroundGeolocation.onHeartbeat((bg.HeartbeatEvent event) async {
    print('onHeartbeat event triggered with event: $event');
    print('MyAppDebug: Heartbeat triggered.');

    // *** Just ensure the timer is started ***
    if (!isLocationUpdateTimerRunning) {
      await startLocationUpdateTimer();
    }

    // OLD CODE (commented out):
    // bg.Location? location = event.location;
    // if (location != null) {
    //   await saveLocationPing(location, pingSource: 'HeartbeatPing');
    // }
  });

  // ----------------------------------------------------------------------
  // Register the headless task for Android
  // ----------------------------------------------------------------------
  if (MyPlatform.isAndroid) {
    await bg.BackgroundGeolocation.registerHeadlessTask(headlessTask);
    print('headlessTask registered successfully for Android.');
  }

  print('initializeService completed');
}

// ----------------------------------------------------------------------------
// This is your original method to stop location updates. 
// We now also stop the Timer in here.
// ----------------------------------------------------------------------------
Future<void> stopLocationUpdates() async {
  print('stopLocationUpdates called');
  await setIsLocationUpdatesRunning(false);

  // Stop the timer:
  await stopLocationUpdateTimer();
}

// ----------------------------------------------------------------------------
// handleManagerTracking & handleNonManagerTracking remain the same
// ----------------------------------------------------------------------------
Future<void> handleManagerTracking(
  LocationPingState locationPingState,
  bg.Location? location,
  DateTime pingTime, {
  String pingSource = 'UnknownManagerPing',
}) async {
  print('[handleManagerTracking] called with pingSource: $pingSource');
  String motionState = await determineMotionState(location);

  final userId = await locationPingState.getUserId();
  final punchCardId = await locationPingState.getPunchCardId();
  final Map<String, String?> lastLocation =
      await locationPingState.getLastPingLocation();
  final lastPingEndTime = await locationPingState.getLastPingEndTime();

  if (userId == null) {
    print('handleManagerTracking: userId is null; exiting.');
    return;
  }

  // Convert background_geolocation.Location to geolocator.Position
  geolocator.Position position;
  if (location != null) {
    position = geolocator.Position(
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
      timestamp: DateTime.now(),
      accuracy: location.coords.accuracy,
      altitude: location.coords.altitude,
      heading: location.coords.heading,
      speed: location.coords.speed,
      speedAccuracy: location.coords.speedAccuracy,
      altitudeAccuracy: 0.0,
      headingAccuracy: 0.0,
    );
  } else {
    // Fallback to geolocator or last known position
    try {
      position = await geolocator.Geolocator.getCurrentPosition(
        desiredAccuracy: geolocator.LocationAccuracy.best,
      );
    } catch (e) {
      print('MyAppDebug: Failed to fetch location via Geolocator: $e');
      position = defaultPosition(lastLocation);
      motionState = 'failed';
    }
  }

  print('[handleManagerTracking] Creating/saving manager ping...');
  await DataModel().travelPingModel.createAndSavePing(
    position,
    userId,
    punchCardId,
    lastLocation['latitude'],
    lastLocation['longitude'],
    lastPingEndTime,
    motionState,
    pingTime,
    pingSource: pingSource, // Pass the source
  );
}

Future<String> determineMotionState(bg.Location? location) async {
  if (location != null) {
    return location.isMoving ? "moving" : "stationary";
  } else {
    return 'failed';
  }
}

geolocator.Position defaultPosition(Map<String, String?> lastLocation) =>
    geolocator.Position(
      latitude: double.parse(lastLocation['latitude'] ?? '0.0'),
      longitude: double.parse(lastLocation['longitude'] ?? '0.0'),
      timestamp: DateTime.now(),
      accuracy: 0.0,
      altitude: 0.0,
      heading: 0.0,
      speed: 0.0,
      speedAccuracy: 0.0,
      altitudeAccuracy: 0.0,
      headingAccuracy: 0.0,
    );

Future<void> handleNonManagerTracking(
  LocationPingState locationPingState,
  bg.Location? location, {
  String pingSource = 'UnknownNonManagerPing',
}) async {
  print('[handleNonManagerTracking] called with pingSource: $pingSource');
  try {
    final punchCardId = await locationPingState.getPunchCardId();
    if (punchCardId == null) {
      print('handleNonManagerTracking: punchCardId is null; exiting.');
      return;
    }

    geolocator.Position? position;
    if (location != null) {
      position = geolocator.Position(
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        timestamp: DateTime.now(),
        accuracy: location.coords.accuracy,
        altitude: location.coords.altitude,
        heading: location.coords.heading,
        speed: location.coords.speed,
        speedAccuracy: location.coords.speedAccuracy,
        altitudeAccuracy: 0.0,
        headingAccuracy: 0.0,
      );
    } else {
      try {
        position = await geolocator.Geolocator.getCurrentPosition(
          desiredAccuracy: geolocator.LocationAccuracy.best,
        );
      } catch (e) {
        print('MyAppDebug: Failed to fetch location via Geolocator: $e');
        position = geolocator.Position(
          latitude: 0.0,
          longitude: 0.0,
          timestamp: DateTime.now(),
          accuracy: 0.0,
          altitude: 0.0,
          heading: 0.0,
          speed: 0.0,
          speedAccuracy: 0.0,
          altitudeAccuracy: 0.0,
          headingAccuracy: 0.0,
        );
      }
    }

    final locationData = await locationPingState.getLocation();
    final geoFenceRadius = await locationPingState.getLocationGeofence();
    double distance = geolocator.Geolocator.distanceBetween(
      position.latitude,
      position.longitude,
      double.parse(locationData['latitude'] ?? '0'),
      double.parse(locationData['longitude'] ?? '0'),
    );

    bool isInsideGeofence = false;
    if (geoFenceRadius != null && geoFenceRadius['radius'] != null) {
      isInsideGeofence =
          distance <= (double.tryParse(geoFenceRadius['radius']!.toString()) ?? 0.0);
    }

    if (isInsideGeofence) {
      print('User is inside the geofence.');
      if (geoFenceBreached == true) {
        // re-entered
        await DataModel().geoLocationModel.saveGeoFencePing(
          punchCardId,
          position.latitude,
          position.longitude,
          distance,
          GeoLocation.geoFenceEnteredId,
        );
        geoFenceBreached = false;
        geoFenceStreamController.add('false');
        await showNotification(
          'Re-entry Detected',
          'You have re-entered the location.',
          0,
        );
      }
    } else {
      print('User is outside the geofence.');
      if (geoFenceBreached != true) {
        geoFenceBreached = true;
        geoFenceStreamController.add('true');
        breachTime = DateTime.now();
      }
      await DataModel().geoLocationModel.saveGeoFencePing(
        punchCardId,
        position.latitude,
        position.longitude,
        distance,
        GeoLocation.geoFenceExitedId,
      );

      if (breachTime != null) {
        int minutesElapsed = DateTime.now().difference(breachTime!).inMinutes;
        await showNotification(
          'WARNING: Breach Detected',
          'You left the location without Punching out! - Please re-enter the site or Punch Out.',
          minutesElapsed,
        );
      } else {
        await showNotification(
          'WARNING: Breach Detected',
          'You left the location without Punching out! - Please re-enter the site or Punch Out.',
          0,
        );
      }
    }
  } catch (e) {
    print('Error occurred while handling non-manager tracking: $e');
  }
}

Future<void> resetGeoFenceBreached() async {
  geoFenceBreached = null;
  geoFenceStreamController.add(null);
}

Future<void> showNotification(
  String title,
  String body,
  int minutesElapsed,
) async {
  String updatedTitle = '$title';
  String message = minutesElapsed > 0
      ? '$minutesElapsed minute${minutesElapsed > 1 ? 's' : ''} ago - $body'
      : body;

  const AndroidNotificationDetails androidPlatformChannelSpecifics =
      AndroidNotificationDetails(
    'geofence_channel_id',
    'Geofence Alerts',
    channelDescription: 'Notifications for geofence entry and exit alerts',
    importance: Importance.max,
    priority: Priority.high,
    ticker: 'ticker',
  );

  const DarwinNotificationDetails iosPlatformChannelSpecifics =
      DarwinNotificationDetails();

  const NotificationDetails platformChannelSpecifics = NotificationDetails(
    android: androidPlatformChannelSpecifics,
    iOS: iosPlatformChannelSpecifics,
  );

  await flutterLocalNotificationsPlugin.show(
    0,
    updatedTitle,
    message,
    platformChannelSpecifics,
    payload: 'item x',
  );
}

// ----------------------------------------------------------------------------
// HEADLESS TASK
// In the new approach, we do NOT directly call saveLocationPing for each event,
// but we do *keep* all lines in your code. We'll comment out direct calls, 
// and add a check to start the timer if not running.
// ----------------------------------------------------------------------------
@pragma('vm:entry-point')
void headlessTask(bg.HeadlessEvent headlessEvent) async {
  print('[HeadlessTask]: Event received: ${headlessEvent}');

  // Initialize Firebase
  try {
    await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
    print('Firebase initialized successfully in headlessTask.');
  } catch (e) {
    print('Error initializing Firebase in headlessTask: $e');
  }

  // Ensure AppState is initialized
  if (!AppState.isInitialized) {
    print("MyAppDebug: AppState is not initialized. Initializing now...");
    await AppState.initialize();
    print("MyAppDebug: AppState initialized.");
  } else {
    print("MyAppDebug: AppState is already initialized.");
  }

  // Instead of calling saveLocationPing below, we ensure the timer is running:
  if (!isLocationUpdateTimerRunning) {
    await startLocationUpdateTimer();
  }

  // --------------------------------------------------------------------------
  // Below is your original switch that calls saveLocationPing. 
  // We'll comment it out to rely on the single Timer approach:
  // --------------------------------------------------------------------------
  switch (headlessEvent.name) {
    case bg.Event.TERMINATE:
      print('HeadlessTask: App terminated event received.');
      // Handle termination if needed
      break;
    case bg.Event.HEARTBEAT:
      print('HeadlessTask: Heartbeat event received.');
      // if (headlessEvent.event is bg.HeartbeatEvent) {
      //   bg.HeartbeatEvent heartbeatEvent =
      //       headlessEvent.event as bg.HeartbeatEvent;
      //   bg.Location? location = heartbeatEvent.location;
      //   if (location != null) {
      //     await saveLocationPing(location, pingSource: 'HeadlessHeartbeatPing');
      //   } else {
      //     print('No location data available in HeartbeatEvent.');
      //   }
      // }
      break;
    case bg.Event.LOCATION:
      print('HeadlessTask: Location event received.');
      // if (headlessEvent.event is bg.Location) {
      //   await saveLocationPing(
      //     headlessEvent.event as bg.Location,
      //     pingSource: 'HeadlessLocationEventPing',
      //   );
      // }
      break;
    case bg.Event.MOTIONCHANGE:
      print('HeadlessTask: MotionChange event received.');
      // if (headlessEvent.event is bg.Location) {
      //   await saveLocationPing(
      //     headlessEvent.event as bg.Location,
      //     pingSource: 'HeadlessMotionChangePing',
      //   );
      // }
      break;
    default:
      print('HeadlessTask: Unhandled event type: ${headlessEvent.name}');
      break;
  }

  print('HeadlessTask processing completed for event: ${headlessEvent.name}');
}

// ----------------------------------------------------------------------------
// Storing last ping time in SharedPreferences
// ----------------------------------------------------------------------------
const _kLastLocationPingTimeKey = 'lastLocationPingTimeKey';

@pragma('vm:entry-point')
Future<DateTime?> getLastPingTime() async {
  final prefs = await SharedPreferences.getInstance();
  final millis = prefs.getInt(_kLastLocationPingTimeKey);
  if (millis == null) return null;
  return DateTime.fromMillisecondsSinceEpoch(millis);
}

Future<void> setLastPingTime(DateTime time) async {
  final prefs = await SharedPreferences.getInstance();
  await prefs.setInt(_kLastLocationPingTimeKey, time.millisecondsSinceEpoch);
}

// ----------------------------------------------------------------------------
// ORIGINAL checkTimer method
// We leave it entirely intact, but comment out the logic that calls 
// saveLocationPing so it does not conflict with the single timer approach.
// ----------------------------------------------------------------------------
Timer? locationUpdateTimerOld; // Just to preserve the original variable name usage

@pragma('vm:entry-point')
Future<void> checkTimer(
  bg.Location location, {
  String pingSource = 'saveLocationPing',
}) async {
  bool wasRunning = await getIsLocationUpdatesRunning();
  if (!wasRunning) {
    print('[checkTimer] would start a Timer here, but we rely on startLocationUpdateTimer now.');
    // locationUpdateTimerOld = Timer.periodic(Duration(seconds: 60), (Timer t) async {
    //   await saveLocationPing(location, pingSource: pingSource);
    // });
  }
}

// ----------------------------------------------------------------------------
// The main method that saves the location ping. 
// We keep your code but note that in the single-timer approach, this 
// gets called only in the Timer callback (and possibly if you re-enable 
// in headlessTask, etc.).
// ----------------------------------------------------------------------------
@pragma('vm:entry-point')
Future<void> saveLocationPing(
  bg.Location location, {
  String pingSource = 'saveLocationPing',
}) async {
  // If the plugin flagged this location as `sample:true`, skip it entirely
  if (location.sample == true) {
    return;
  }

  // 1) Get the last ping time from SharedPreferences
  final lastPingTime = await getLastPingTime();
  final now = DateTime.now();

  if (lastPingTime != null) {
    final difference = now.difference(lastPingTime).inSeconds;

    // 2) If it has been less than 60s since last ping, skip
    // (You may keep or remove this check, depending on whether you want a 
    //  "one-minute minimum" in addition to the Timer.)
    if (difference < 60) {
      print(
          '[saveLocationPing]: Skipping ping, only $difference seconds since last ping');
      return;
    }
  }

  // 3) If we reach here, it means >= 1 min has passed or no last ping time
  print('saveLocationPing triggered with location: $location from $pingSource');

  // 4) Update last ping time to "now"
  await setLastPingTime(now);

  // 5) Continue your existing logic
  DateTime pingTime = ServerTimeState().utcTime;
  final locationPingState = LocationPingState();
  final isManager = await locationPingState.getIsManager();

  if (isManager) {
    await handleManagerTracking(
      locationPingState,
      location,
      pingTime,
      pingSource: pingSource,
    );
  } else {
    await handleNonManagerTracking(
      locationPingState,
      location,
      pingSource: pingSource,
    );
  }
}
