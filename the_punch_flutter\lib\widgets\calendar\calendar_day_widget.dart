import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../helpers/color_helper.dart';
import '../../misc/extensions.dart';
import '../../pages/web/schedules/widgets/calendar_body.dart';
import '../../pages/web/schedules/widgets/calendar_header_component.dart';
import 'calendar_widget.dart';
import '../menu/aligned_popup_menu_button.dart';

class CalendarDayWidget extends StatelessWidget {
  final Future<Iterable<CalendarEvent>> Function(
      BuildContext context, DateTime start, DateTime end) builder;
  final bool scrollable;
  final Widget suffix;
  final double height;

  const CalendarDayWidget({
    super.key,
    required this.builder,
    required this.suffix,
    this.scrollable = true,
    required this.height,
  });

  @override
  Widget build(BuildContext context) => Row(
        children: [

          Flexible(child: _Body(builder: builder, scrollable: scrollable)),
        ],
      );
}

class _Header extends StatefulWidget {
  final Widget suffix;

  const _Header({required this.suffix});

  @override
  State<_Header> createState() => _HeaderState();
}

class _HeaderState extends State<_Header> {
  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);

    return Consumer<CalendarViewModel>(
      builder: (context, viewModel, _) => Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CalendarHeaderPrefix(
            previousPage: () => viewModel.setDate(viewModel.date.addDays(-1)),
            nextPage: () => viewModel.setDate(viewModel.date.addDays(1)),
            today: () => viewModel.setDate(viewModel.date),
          ),
          DropdownButton<DateTime>(
            onChanged: (value) {
              if (value == null || viewModel.date == value) return;
              viewModel.setDate(value);
            },
            value: viewModel.date,
            items: [
              for (var i = -7; i < 7; i++)
                DropdownMenuItem<DateTime>(
                  value: viewModel.date.addDays(i),
                  child: Text(
                    viewModel.date.addDays(i).toFormattedDate(locale),
                  ),
                ),
            ],
          ),
          widget.suffix,
        ],
      ),
    );
  }
}

class _Body extends StatefulWidget {
  final Future<Iterable<CalendarEvent>> Function(
      BuildContext context, DateTime start, DateTime end) builder;
  final bool scrollable;

  const _Body({required this.builder, this.scrollable = true});

  @override
  _BodyState createState() => _BodyState();
}

class _BodyState extends State<_Body> {
  static const _initialPage = 1073741824;
  final controller = PageController(initialPage: _initialPage);
  final DateTime initialDay = DateTime.now().dateOnly;

  @override
  void initState() {
    super.initState();
    controller.addListener(() {
      if (controller.page != null &&
          controller.page!.toInt() == controller.page) {
        final offset = controller.page!.toInt() - _initialPage;
        final day = initialDay.addDays(offset);
        final viewModel = context.read<CalendarViewModel>();
        if (viewModel.date != day) viewModel.setDate(day);
      }
    });
  }

  @override
  Widget build(BuildContext context) =>
      Consumer<CalendarViewModel>(builder: (context, viewModel, _) {
        if (controller.hasClients) {
          final offset =
              (viewModel.date.difference(initialDay).inHours / 24).round();
          final page = _initialPage + offset;
          if (page != controller.page) {
            if ((page - controller.page!).abs() > 2) {
              controller.jumpToPage(page);
            } else {
              unawaited(controller.animateToPage(page,
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.ease));
            }
          }
        }
        return PageView.builder(
            physics:
                widget.scrollable ? null : const NeverScrollableScrollPhysics(),
            controller: controller,
            itemBuilder: (context, index) {
              final offset = index - _initialPage;
              final day = initialDay.addDays(offset);
              return _Page(day: day, builder: widget.builder);
            });
      });
}

class _Page extends StatelessWidget {
  final DateTime day;
  final Future<Iterable<CalendarEvent>> Function(
      BuildContext context, DateTime start, DateTime end) builder;
  final _controller = ScrollController(
      initialScrollOffset: (CalendarViewModel.pageHeight / 48) * 18);

  _Page({required this.day, required this.builder});

  @override
  Widget build(BuildContext context) => SingleChildScrollView(
        controller: _controller,

            child: Row(
            children: [
              Expanded(
              child: _Events(day:day, builder:builder),
              ),
            ],
          ),

      );
}

class _HoursColumn extends StatelessWidget {
  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, constraints) {
          final times = <String>[];
          for (var i = 0; i < 48; i++) {
            final hour = i ~/ 2;
            final minute = (i % 2) * 30;
            final date = DateTime.now()
                .setTimeOfDay(TimeOfDay(hour: hour, minute: minute));
            times.add(DateFormat(DateFormat.HOUR_MINUTE).format(date));
          }

          final height = constraints.maxHeight -
              (constraints.maxHeight / times.length) / 2;

          return Stack(
            children: [
              for (var i = 0; i < times.length; i++)
                Positioned(
                  top: height / times.length * i + (height / times.length) / 2,
                  left: 8,
                  child: Text(times[i]),
                ),
            ],
          );
        },
      );
}

class _HoizontalDividers extends StatelessWidget {
  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, constraints) {
          const rows = 48;

          final height =
              constraints.maxHeight - (constraints.maxHeight / rows) / 2;

          return Stack(
            children: [
              for (var i = 2; i < rows; i += 2)
                Positioned(
                  top: height / rows * i + (height / rows) / 4,
                  left: 8,
                  right: 8,
                  child: Divider(
                    height: 1,
                    thickness: 1,
                    indent: 0,
                    endIndent: 0,
                    color: Theme.of(context).dividerColor.withAlpha(31),
                  ),
                ),
              for (var i = 1; i < rows; i += 2)
                Positioned(
                  top: height / rows * i + (height / rows) / 4,
                  left: 8,
                  right: 8,
                  child: Divider(
                    height: 1,
                    thickness: 1,
                    indent: 0,
                    endIndent: 0,
                    color: Theme.of(context).dividerColor.withAlpha(10),
                  ),
                ),
            ],
          );
        },
      );
}

class _Events extends StatelessWidget {
  final Future<Iterable<CalendarEvent>> Function(
      BuildContext context, DateTime start, DateTime end) builder;
  final DateTime day;
 final List<TimeSlot> timeSlots;
  const _Events({required this.day, required this.builder, this.timeSlots = const [
      TimeSlot(label: '12am', time: 0),
      TimeSlot(label: '3am', time: 3),
      TimeSlot(label: '6am', time: 6),
      TimeSlot(label: '9am', time: 9),
      TimeSlot(label: '12pm', time: 12),
      TimeSlot(label: '3pm', time: 15),
      TimeSlot(label: '6pm', time: 18),
      TimeSlot(label: '9pm', time: 21),
    ]});

  @override
  Widget build(BuildContext context) {
    final start = day.addDays(0);
    final end = day.addDays(3);
    return FutureBuilder<Iterable<CalendarEvent>>(
        future: Future(() async => await builder(context, start, end)),
        builder: (context, snapshot) {
          if (!snapshot.hasData) return Container();
          final events = snapshot.data!.toList();
          if (events.isEmpty) return Container();


          return LayoutBuilder(
            builder: (context, constraints) {
              const timesWidth = 75;

            
              
              final splitEvents = <CalendarEvent>[];
              for (final event in events) {
               
              if (event.start.toLocal().timeOfDay.duration >
                event.end.toLocal().timeOfDay.duration) {
                 
                final splitEvent = CalendarEvent.from(event);
                event.splitEnd =
                  splitEvent.splitStart = event.end.toLocal().dateOnly;
                splitEvents.add(splitEvent);
              }
              }
              events.addAll(splitEvents);

              // Group events by day, then by location, then by overlapping times
              final eventGroups = <List<CalendarEvent>>[];
              // Create a map to group events by day
              final dayGroups = <DateTime, Map<String, List<CalendarEvent>>>{};
              var location = <String>[];

              // First, group by day and location
              for (final event in events) {
                final eventDay = event.splitStart.toLocal().dateOnly;
                // Initialize the day group if it doesn't exist
                dayGroups.putIfAbsent(eventDay, () => <String, List<CalendarEvent>>{});

                final d = event.detailedText ?? event.text;
                location = d.split(RegExp(r'(?<=\s.*?)\s'));
                // Add event to the appropriate location group within the day
                dayGroups[eventDay]!.putIfAbsent(location[1], () => []).add(event);
              }



              // Then, for each day and location, group overlapping events
              for (final dayGroup in dayGroups.entries) {
                for (final locationEvents in dayGroup.value.values) {
                  final locationEventGroups = <List<CalendarEvent>>[];
                  for (final event in locationEvents) {
                    var added = false;
                    for (final eventGroup in locationEventGroups) {
                      if (eventGroup.any((e) =>
                          e.splitStart <= event.splitEnd &&
                          e.splitEnd > event.splitStart)) {
                        eventGroup.add(event);
                        added = true;
                        break;
                      }
                    }
                    if (!added) locationEventGroups.add([event]);
                  }
                  eventGroups.addAll(locationEventGroups);
                }
              }
                return Expanded(
                child: SingleChildScrollView(
                  child: Column(
                  children: [
                    // Loop through locations
                    for (final location in dayGroups.values
                      .expand((dayMap) => dayMap.keys)
                      .toSet()
                      .toList()
                    ..sort())
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                      // Location header
                      Container(
                        width: double.infinity,
                        color: const Color.fromARGB(255, 235, 245, 255),
                        padding: const EdgeInsets.all(8),
                        child: Text(
                        location,
                        style: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        ),
                      ),

                      // Loop through employees at this location
                      for (final employee in events
                        .where((e) => (e.detailedText ?? e.text)
                          .split(RegExp(r'(?<=\s.*?)\s'))[1] == location)
                        .map((e) => (e.detailedText ?? e.text)
                          .split(RegExp(r'(?<=\s.*?)\s'))[0])
                        .toSet()
                        .toList()
                        ..sort())
                        Container(
                          decoration: BoxDecoration(
                          border: Border.all(color: Colors.blue[50]!),
                          ),
                          child: Row( 
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Employee header
                            Container(
                            width: 154,
                            padding: const EdgeInsets.all(8),
                            child: Text(
                            employee,
                            style: const TextStyle(color: Colors.black, fontSize: 12),
                            overflow: TextOverflow.ellipsis,
                            ),
                            ),

                            // Loop through days for this employee
                            // for (final dayGroup in dayGroups.entries
                            // .where((entry) => entry.value.containsKey(location))
                            // .toList()
                            // ..sort((a, b) => a.key.compareTo(b.key)))
                            // // Loop through events for this day and employee
                            // for (final eventGroup in eventGroups)
                            // for (var j = 0; j < eventGroup.length; j++)
                            // if (
                            //    eventGroup[j].splitStart.toLocal().dateOnly == dayGroup.key 
                            //  &&
                            //   (eventGroup[j].detailedText ?? eventGroup[j].text)
                            //     .split(RegExp(r'(?<=\s.*?)\s'))[1] ==
                            //   location &&
                            //   (eventGroup[j].detailedText ?? eventGroup[j].text)
                            //     .split(RegExp(r'(?<=\s.*?)\s'))[0] ==
                            //   employee
                            //   )
                              for (final dayGroup in dayGroups.entries
                           // .where((entry) => entry.value.containsKey(location))
                            .toList()
                            ..sort((a, b) => a.key.compareTo(b.key)))
                              Expanded(
                                child: Container(
                                width: double.infinity,
                                child: Padding(
                                  padding: const EdgeInsets.all(0),
                                  child:
                              Stack(
                            alignment: Alignment.center,
                            children: [
                            CustomPaint(
                              size: const Size(double.infinity, 37),
                              painter: HourLinesPainter(),
                            ),
                      
                            // Loop through events for this day and employee
                            for (final eventGroup in eventGroups)
                            for (var j = 0; j < eventGroup.length; j++)
                            if (
                               eventGroup[j].splitStart.toLocal().dateOnly == dayGroup.key 
                             &&
                              (eventGroup[j].detailedText ?? eventGroup[j].text)
                                .split(RegExp(r'(?<=\s.*?)\s'))[1] ==
                              location &&
                              (eventGroup[j].detailedText ?? eventGroup[j].text)
                                .split(RegExp(r'(?<=\s.*?)\s'))[0] ==
                              employee
                              )
                            Positioned(
                              left: ((constraints.maxWidth-154)/72)
                              * ((eventGroup[j].splitStart.toLocal().timeOfDay.duration.inHours + eventGroup[j].splitEnd.toLocal().timeOfDay.duration.inHours)/2) - 18,
                              top: 18.5 -12,
                              child:
                              // Text("${dayGroups.length}")
                              _EventTile(eventGroup[j]),
                            ),
                            ],)
                                ),
                                ),
                              ),
                              // Add empty containers if less than 3 daygroups
                         
                          ],
                          ),
                        )],
                    ),
                  ],
                  ),
                ),
                );
              },
          );
        });
  }

  Widget _buildTimeSlot(TimeSlot slot) => Expanded(
      child: Container(
        padding: const EdgeInsets.only(left: 0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
           
            Text(
              '',
              style: const TextStyle(
                fontFamily: 'Poppins',
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: Color(0xFF091F30),
              ),
            )
  ],
        ),
      ),
    );


}
class TimeSlot {
  final String label;
  final int time;

  const TimeSlot({
    required this.label,
    required this.time,
  });
}
class _EventTile extends StatelessWidget {
  final CalendarEvent event;

  const _EventTile(this.event);

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);

    Widget child = Container(
      padding: const EdgeInsets.all(0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.green,
            borderRadius: BorderRadius.circular(4),
          ),
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          child: Text(
          '${event.end.difference(event.start).inHours}hr${event.end.difference(event.start).inHours>1?'s':''}',
          style: event.textStyle!.copyWith(color: Colors.white)
          ),
        ),
        ],
      ),
      );
    if (event.onTap != null) {
      child = InkWell(
        onTap: event.onTap,
        child: child,
      );
    }

    if (event.popupItemBuilder != null) {
      child = AlignedPopupMenuButton<int>(
        itemBuilder: event.popupItemBuilder!,
        onSelected: event.popupOnSelected,
        child: child,
      );
    }

    if (event.tooltip != null) {
      child = Tooltip(
          message: event.tooltip,
          textStyle: event.tooltipTextStyle,
          child: child);
    }
    return child;
  }
}
