import 'package:flutter/material.dart';
import '../helpers/color_helper.dart';

class SearchTextField extends StatefulWidget {
  final ValueNotifier<String> notifier;
  final String? hintText;
  final bool autofocus;
  final BoxConstraints? constraints;
  final BorderRadius? borderRadius;

  const SearchTextField({
    super.key,
    required this.notifier,
    this.hintText,
    this.autofocus = false,
    this.constraints,
    this.borderRadius,
  });

  @override
  State<SearchTextField> createState() => _SearchTextFieldState();
}

class _SearchTextFieldState extends State<SearchTextField> {
  final TextEditingController searchController = TextEditingController();
  final FocusNode focusNode = FocusNode();
  bool isFocused = false;

  void clearSearch() {
    searchController.clear();
    widget.notifier.value = '';
  }

  @override
  void initState() {
    super.initState();
    focusNode.addListener(() {
      setState(() {
        isFocused = focusNode.hasFocus;
      });
    });
  }

  @override
  void dispose() {
    searchController.dispose();
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;

    return ConstrainedBox(
      constraints: widget.constraints ??
          BoxConstraints(
            minHeight: isMobile ? 36 : 44,
            minWidth: isMobile ? 80 : 100,
          ),
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
        color: isFocused 
            ? ColorHelper.thePunchRed() 
            : ColorHelper.thePunchDesktopLightGray(),
        width: 1.0,
          ),
          borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
        ),
        child: Row(
          children: [
        const SizedBox(width: 8),
        Icon(
          Icons.search_rounded,
          color: isFocused
          ? ColorHelper.thePunchRed()
          : const Color.fromRGBO(0, 0, 0, 1),
          size: isMobile ? 16 : 20,
        ),
        Expanded(
          child: TextField(
            style: Theme.of(context)
            .textTheme
            .bodyMedium
            ?.copyWith(fontSize: isMobile ? 14 : 16, color:Colors.black),
            cursorColor: Theme.of(context).colorScheme.secondary,
            autofocus: widget.autofocus,
            controller: searchController,
            focusNode: focusNode,
            decoration: InputDecoration(
          border: InputBorder.none,
          hintText: widget.hintText ?? 'Search',
          hintStyle: TextStyle(color: ColorHelper.thePunchDesktopLightGray()),
          contentPadding: EdgeInsets.symmetric(
            vertical: 8,
            horizontal: isMobile ? 6 : 8,
          ),
          isDense: true,
            ),
            onChanged: (value) {
          widget.notifier.value = value;
            },
          ),
        ),
        IconButton(
          icon: Icon(
            Icons.cancel,
            color: isFocused
            ? const Color.fromRGBO(158, 21, 21, 1)
            :  ColorHelper.thePunchDesktopLightGray(),
            size: isMobile ? 16 : 20,
          ),
          onPressed: clearSearch,
          padding: EdgeInsets.all(isMobile ? 4 : 8),
          constraints: BoxConstraints(),
        ),
          ],
        ),
      ),
        
      
    );
  }
}
