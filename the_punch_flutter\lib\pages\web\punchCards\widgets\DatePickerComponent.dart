import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../punch_cards.dart';
import 'package:provider/provider.dart';

class DatePickerComponent extends StatelessWidget {
  const DatePickerComponent({super.key});

  @override
  Widget build(BuildContext context) => Consumer<ViewModelPunch>(
      builder: (context, viewModel, _) => Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          SizedBox(
            width: 170,
            child: InkWell(
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: viewModel.start,
                  firstDate: DateTime(2020),
                  lastDate: DateTime(2025),
                );
                if (date != null) {
                  await viewModel.setTimePeriod(date, viewModel.end);
                }
              },
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.transparent,
                    borderRadius: BorderRadius.circular(15),
                  border: Border.all(color: Colors.black!,width: 2),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
      
                    Text(
                        DateFormat('MMM d, y').format(viewModel.start),
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(width:8),
                    const Icon(
                      Icons.calendar_month_outlined,
                      color: Colors.black,
                      size: 20,
                    )
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          SizedBox(
            width: 170,
            child: InkWell(
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: viewModel.end,
                  firstDate: DateTime(2020),
                  lastDate: DateTime(2025),
                );
                if (date != null) {
                  await viewModel.setTimePeriod(viewModel.start, date);
                }
              },
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.black!,width: 2),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [

                    Text(
                            DateFormat('MMM d, y').format(viewModel.end),
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(width:8),
                                        const Icon(
                      Icons.calendar_month_outlined,
                      color: Colors.black,
                      size: 20,
                    )
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
}

