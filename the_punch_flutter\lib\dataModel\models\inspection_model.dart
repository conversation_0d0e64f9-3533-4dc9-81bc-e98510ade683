import '../base_data.dart';
import '../data/inspection.dart';
import '../hive_db.dart';
import '../../misc/extensions.dart';

class InspectionModel extends BaseDataModel<Inspection> {
  @override
  Future<Iterable<Inspection>> get all async => (await HiveDb.database).inspections.values;

  @override
  Future<void> save(Iterable<Inspection> t) async {
    await (await HiveDb.database).inspections.putAll({for (final e in t) e.id: e});
    for (var inspection in t) {
      inspection.isDirty = false;
    }
    notifyListeners();
  }

  Future<Iterable<DateTime>> getAllStarts() async => (await all).map((e) => e.dateTime);
  Future<Iterable<Inspection>> getInspectionsBetween(DateTime startUtc, DateTime endUtc) async => (await all).where((e) => e.dateTime >= startUtc && e.dateTime < endUtc);
  Future<Iterable<DateTime>> getActiveStarts() async => (await active).map((e) => e.dateTime);
  Future<Iterable<Inspection>> getActiveInspectionsBetween(DateTime startUtc, DateTime endUtc) async => (await active).where((e) => e.dateTime >= startUtc && e.dateTime < endUtc);

  Future<Iterable<Inspection>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }
}

class InspectionAreaModel extends BaseDataModel<InspectionArea> {
  @override
  Future<Iterable<InspectionArea>> get all async => (await HiveDb.database).inspectionAreas.values;

  @override
  Future<void> save(Iterable<InspectionArea> t) async {
    await (await HiveDb.database).inspectionAreas.putAll({for (final e in t) e.id: e});
    for (var area in t) {
      area.isDirty = false;
    }
    notifyListeners();
  }

  Future<Iterable<InspectionArea>> getByInspectionId(String id) async => (await all).where((e) => e.inspectionId == id);

  Future<Iterable<InspectionArea>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }
}

class InspectionItemModel extends BaseDataModel<InspectionItem> {
  @override
  Future<Iterable<InspectionItem>> get all async => (await HiveDb.database).inspectionItems.values;

  @override
  Future<void> save(Iterable<InspectionItem> t) async {
    await (await HiveDb.database).inspectionItems.putAll({for (final e in t) e.id: e});
    for (var item in t) {
      item.isDirty = false;
    }
    notifyListeners();
  }

  Future<Iterable<InspectionItem>> getByInspectionId(String id) async => (await all).where((e) => e.inspectionId == id);
  Future<int> countByInspectionId(String id) async => (await getByInspectionId(id)).length;
  Future<int> countCompletedByInspectionIds(String id) async => (await getByInspectionId(id)).where((e) => e.grade != null).length;

  Future<Iterable<InspectionItem>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }
}

class InspectionImageModel extends BaseDataModel<InspectionImage> {
  @override
  Future<Iterable<InspectionImage>> get all async => (await HiveDb.database).inspectionImages.values;

  @override
  Future<void> save(Iterable<InspectionImage> t) async {
    await (await HiveDb.database).inspectionImages.putAll({for (final e in t) e.id: e});
    for (var image in t) {
      image.isDirty = false;
    }
    notifyListeners();
  }

  Future<Iterable<String>> getAllIds() async => (await HiveDb.database).inspectionImages.keys.map((e) => e as String);
  Future<Iterable<InspectionImage>> getByInspectionItemId(String id) async => (await all).where((e) => e.inspectionItemId == id);
  Future<Iterable<InspectionImage>> getActiveByInspectionItemId(String id) async => (await all).where((e) => e.inspectionItemId == id && e.isActive);
  Future<Iterable<String>> getInspectionItemIdsWithImages(Iterable<String> ids) async => ids.isEmpty ? [] : (await all).where((e) => e.isActive && ids.contains(e.inspectionItemId)).map((e) => e.inspectionItemId).toSet();

  Future<Iterable<InspectionImage>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }
}