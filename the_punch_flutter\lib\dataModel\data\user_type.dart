import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../base_data.dart';
import '../../misc/json_conversion.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import 'package:uuid/uuid.dart';

part 'user_type.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
@HiveType(typeId: 20)
class UserType extends BaseData {
  static final sysopId = '6D2B898A-3931-4BC3-B0AF-53A140F6DE19'.toLowerCase();
  static final administratorId = '9A09BAE2-0F51-45EB-B749-B8D244FB738B'.toLowerCase();
  static final employeeId = '13CA6C0B-7B04-4C6F-A27F-1FE6B2338AED'.toLowerCase();
  static final contactId = '31618E0A-F351-4170-814A-A6FEB7EB3183'.toLowerCase();
  static final managerId = 'AA201E36-397F-42DC-AAA5-4347BF8073FF'.toLowerCase();

  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  String name;

  @HiveField(102)
  @JsonKey(defaultValue: '')
  String description;

  @HiveField(103)
  bool isContact;

  UserType({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.name,
    required this.description,
    required this.isContact,
  });

  factory UserType.blank() => UserType(
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
        id: '',
        name: '',
        description: '',
        isContact: false,
      );

  @override
  String toString() => name;

  factory UserType.fromJson(Map<String, dynamic> json) {
    try {
      return _$UserTypeFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }
  Map<String, dynamic> toJson() => _$UserTypeToJson(this);

  factory UserType.create() => UserType(
        id: const Uuid().v4(),
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
        name: '',
        description: '',
        isContact: false,
      );
}
