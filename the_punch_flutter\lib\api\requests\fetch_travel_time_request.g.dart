// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fetch_travel_time_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FetchTravelTimeRequest _$FetchTravelTimeRequestFromJson(
        Map<String, dynamic> json) =>
    FetchTravelTimeRequest(
      originLocationId: json['OriginLocationId'] as String,
      destinationLocationId: json['DestinationLocationId'] as String,
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$FetchTravelTimeRequestToJson(
        FetchTravelTimeRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'OriginLocationId': instance.originLocationId,
      'DestinationLocationId': instance.destinationLocationId,
    };

FetchTravelTimeResponse _$FetchTravelTimeResponseFromJson(
        Map<String, dynamic> json) =>
    FetchTravelTimeResponse(
      json['ErrorCode'] as String,
      nullableDateTimeFromJson(json['ServerTime']),
      duration:
          FetchTravelTimeResponse._durationFromJson(json['Duration'] as String),
      message: json['Message'] as String? ?? '',
    );

Map<String, dynamic> _$FetchTravelTimeResponseToJson(
    FetchTravelTimeResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['Duration'] = FetchTravelTimeResponse._durationToJson(instance.duration);
  val['Message'] = instance.message;
  return val;
}
