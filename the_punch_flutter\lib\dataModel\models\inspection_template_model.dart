import '../base_data.dart';
import '../data/inspection_template.dart';
import '../hive_db.dart';

class InspectionTemplateModel extends BaseDataModel<InspectionTemplate> {
  @override
  Future<Iterable<InspectionTemplate>> get all async => (await HiveDb.database).inspectionTemplates.values;

  @override
  Future<void> save(Iterable<InspectionTemplate> t) async {
    await (await HiveDb.database).inspectionTemplates.putAll({for (final e in t) e.id: e});
    notifyListeners();
  }

  Future<Iterable<InspectionTemplate>> getByLocationId(String? id) async {
    if (id == null || id.isEmpty) return (await active).where((e) => e.locationId == null || e.locationId!.isEmpty);
    return (await active).where((e) => e.locationId == id);
  }

  Future<Iterable<InspectionTemplate>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }
}

class InspectionTemplateAreaModel extends BaseDataModel<InspectionTemplateArea> {
  @override
  Future<Iterable<InspectionTemplateArea>> get all async => (await HiveDb.database).inspectionTemplateAreas.values;

  @override
  Future<void> save(Iterable<InspectionTemplateArea> t) async {
    await (await HiveDb.database).inspectionTemplateAreas.putAll({for (final e in t) e.id: e});
    notifyListeners();
  }

  Future<Iterable<InspectionTemplateArea>> getActiveByInspectionTemplateId(String id) async => (await active).where((e) => e.inspectionTemplateId == id);

  Future<Iterable<InspectionTemplateArea>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }
}

class InspectionTemplateItemModel extends BaseDataModel<InspectionTemplateItem> {
  @override
  Future<Iterable<InspectionTemplateItem>> get all async => (await HiveDb.database).inspectionTemplateItems.values;

  @override
  Future<void> save(Iterable<InspectionTemplateItem> t) async {
    await (await HiveDb.database).inspectionTemplateItems.putAll({for (final e in t) e.id: e});
    notifyListeners();
  }

  Future<Iterable<InspectionTemplateItem>> getActiveByInspectionTemplateId(String id) async => (await active).where((e) => e.inspectionTemplateId == id);

  Future<Iterable<InspectionTemplateItem>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }
}
