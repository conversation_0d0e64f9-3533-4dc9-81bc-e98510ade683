import 'dart:async';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import '../dataModel/data/note.dart';
import '../dataModel/data_model.dart';
import '../helpers/color_helper.dart';
import 'constrained_dialog.dart';

class EditNoteDialog extends StatefulWidget {
  final Note note;
  final bool isNew;

  const EditNoteDialog({super.key, required this.note, this.isNew = false});

  @override
  State<EditNoteDialog> createState() => _EditNoteDialogState();
}

class _EditNoteDialogState extends State<EditNoteDialog> {
  late final _textController = TextEditingController(text: widget.note.text);

  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    final maxWidth = isMobile ? double.infinity : 600.0;

    return ConstrainedDialog(
      constraints: BoxConstraints(maxWidth: maxWidth),
      title: '',
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title Text
              Center(
                child: Text(
                  widget.isNew
                      ? AppLocalizations.of(context)!.addNote
                      : AppLocalizations.of(context)!.editNote,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              // Bordered Note TextField Area
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade400, width: 1.5),
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.all(8),
                child: TextField(
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context)!.note,
                    labelStyle: TextStyle(color: Colors.grey.shade600),
                    border: InputBorder.none,
                  ),
                  keyboardType: TextInputType.multiline,
                  minLines: 6,
                  maxLines: 10,
                  controller: _textController,
                  autofocus: true,
                ),
              ),
              const SizedBox(height: 20),
              // Buttons Row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Cancel Button
                  Expanded(
                    child: OutlinedButton(
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        side: BorderSide(color: Colors.grey.shade400),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        AppLocalizations.of(context)!.cancel,
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Save Button
                  Expanded(
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        backgroundColor: ColorHelper.thePunchRed(),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      onPressed: () async => await _saveTapped(context),
                      child: Text(
                        widget.isNew
                            ? AppLocalizations.of(context)!.addNote
                            : AppLocalizations.of(context)!.save,
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveTapped(BuildContext context) async {
    Navigator.pop(context);
    widget.note.text = _textController.text;
    await DataModel().noteModel.saveDirty([widget.note]);
  }
}
