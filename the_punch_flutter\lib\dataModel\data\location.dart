import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../base_data.dart';
import '../../misc/json_conversion.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import 'package:uuid/uuid.dart';
import '../../misc/extensions.dart';

part 'location.g.dart';

@HiveType(typeId: 13)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class Location extends BaseData {
  static const primaryLocationId = 'aa48352c-a563-4717-b230-4cb8ddb7d5f6';

  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  String name;

  @HiveField(103)
  @JsonKey(defaultValue: '')
  String address1;

  @HiveField(104)
  @Json<PERSON>ey(defaultValue: '')
  String address2;

  @HiveField(105)
  @Json<PERSON>ey(defaultValue: '')
  String city;

  @HiveField(106)
  @JsonKey(defaultValue: '')
  String state;

  @HiveField(107)
  @JsonKey(defaultValue: '')
  String zip;

  @HiveField(108)
  @JsonKey(defaultValue: '')
  String country;

  @HiveField(109)
  @JsonKey(defaultValue: '')
  String phone;

  @HiveField(110)
  @JsonKey(defaultValue: '')
  String emailAddress;

  @HiveField(111)
  @JsonKey(defaultValue: 0)
  double latitude;

  @HiveField(112)
  @JsonKey(defaultValue: 0)
  double longitude;

  @HiveField(113)
  @JsonKey(defaultValue: 100)
  double geoFenceRadius;

  @HiveField(114)
  @JsonKey(name: 'TimeZone_IANA')
  String timeZone;

  Location({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.name,
    required this.address1,
    required this.address2,
    required this.city,
    required this.state,
    required this.zip,
    required this.country,
    required this.phone,
    required this.emailAddress,
    this.latitude = 0,
    this.longitude = 0,
    this.geoFenceRadius = 100, //meters
    required this.timeZone,
  });

  String get address3 => city.isEmpty && state.isEmpty && zip.isEmpty ? '' : '$city, $state $zip';

  factory Location.fromJson(Map<String, dynamic> json) {
    var location = _$LocationFromJson(json);
    print('Deserialized Location: $location');
    return location;
  }

  Map<String, dynamic> toJson() => _$LocationToJson(this);

  factory Location.from(Location o) => Location(
    isDirty: o.isDirty,
    isActive: o.isActive,
    createdOn: o.createdOn,
    createdByUserId: o.createdByUserId,
    lastChangedOn: o.lastChangedOn,
    lastChangedByUserId: o.lastChangedByUserId,
    id: o.id,
    name: o.name,
    timeZone: o.timeZone,
    address1: o.address1,
    address2: o.address2,
    city: o.city,
    state: o.state,
    zip: o.zip,
    country: o.country,
    phone: o.phone,
    emailAddress: o.emailAddress,
    latitude: o.latitude,
    longitude: o.longitude,
    geoFenceRadius: o.geoFenceRadius,
  );

  factory Location.create() => Location(
    id: const Uuid().v4(),
    createdOn: ServerTimeState().utcTime,
    createdByUserId: LoginState.userId,
    name: '',
    timeZone: '',
    address1: '',
    address2: '',
    city: '',
    state: '',
    zip: '',
    country: '',
    phone: '',
    emailAddress: '',
  );

  bool get validateName => name.isNotEmpty;

  bool get validatePhone => phone.isNotEmpty && phone.validatePhone;

  bool get validateEmail => emailAddress.isEmpty || emailAddress.validateEmail;
}
