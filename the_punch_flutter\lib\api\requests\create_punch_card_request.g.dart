// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_punch_card_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreatePunchCardRequest _$CreatePunchCardRequestFromJson(
        Map<String, dynamic> json) =>
    CreatePunchCardRequest(
      punchCard: PunchCard.fromJson(json['PunchCard'] as Map<String, dynamic>),
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$CreatePunchCardRequestToJson(
        CreatePunchCardRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'PunchCard': instance.punchCard,
    };

CreatePunchCardResponse _$CreatePunchCardResponseFromJson(
        Map<String, dynamic> json) =>
    CreatePunchCardResponse(
      success: json['Success'] as bool,
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$CreatePunchCardResponseToJson(
    CreatePunchCardResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['Success'] = instance.success;
  return val;
}
