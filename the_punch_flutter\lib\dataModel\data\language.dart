import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../base_data.dart';
import '../../misc/json_conversion.dart';

part 'language.g.dart';

@HiveType(typeId: 25)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class Language extends BaseData {
  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  String key;

  @HiveField(102)
  bool isDefault;

  @HiveField(103) // Add this for Hive persistence if needed
  String name; // Properly include the `name` field

  Language({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.key,
    required this.isDefault,
    required this.name, // Ensure this matches the constructor
  });

  factory Language.fromJson(Map<String, dynamic> json) {
    try {
      return _$LanguageFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }

  @override
  Map<String, dynamic> toJson() => _$LanguageToJson(this);
}
