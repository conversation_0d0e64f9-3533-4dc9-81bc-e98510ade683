import 'dart:async';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../dataModel/data/location_contact.dart';
import '../dataModel/data/user.dart';
import '../dataModel/data_model.dart';
import '../dialogs/remove_dialog.dart';
import '../dialogs/web/expanded_contacts_dialog.dart';
import '../misc/app_localization.dart';
import '../misc/change_notification_builder.dart';
import '../pages/view_model_mixin.dart';
import '../misc/extensions.dart';

class ContactsWidget extends StatelessWidget {
  final String locationId;

  const ContactsWidget({super.key, required this.locationId});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final highlightColor = theme.highlightColor;
    return ChangeNotifierBuilder<_ViewModel>(
        create: (context) => _ViewModel(locationId),
        builder: (context, viewModel, _) => Card(
              elevation: 0,
              child: Column(
                children: [
                  Align(
                    alignment: Alignment.centerRight,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(8),
                          child: ElevatedButton.icon(
                            onPressed: () async => context.pushNamed('/contacts/edit',
                                queryParameters: {
                                  'locationId': locationId,
                                  'anything': '1'
                                }),
                            icon: const Icon(Icons.add),
                            label: const Text('Add New Contact'),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8),
                          child: ElevatedButton.icon(
                              onPressed: () => unawaited(showDialog(
                                  context: context,
                                  builder: (context) => ExpandedContactsDialog(
                                      onSelection: (userId) =>
                                          viewModel.addUserId(userId),
                                      ignoreUserIds: viewModel.userIds))),
                              icon: const Icon(Icons.add),
                              label: Text(AppLocalization.of(context)
                                  .addContactToLocation)),
                        ),
                      ],
                    ),
                  ),
                  SingleChildScrollView(
                    child: SizedBox(
                      width: double.maxFinite,
                      child: DataTable(
                        showCheckboxColumn: false,
                        dataRowHeight: 50,
                        showBottomBorder: true,
                        dividerThickness: 3,
                        headingRowColor: MaterialStateColor.resolveWith(
                            (states) => highlightColor),
                        columns: _getColumns(context),
                        rows: _getRows(viewModel, context),
                      ),
                    ),
                  ),
                ],
              ),
            ));
  }

  List<DataColumn> _getColumns(BuildContext context) => [
        DataColumn(label: Text(AppLocalization.of(context).contactName)),
        DataColumn(label: Text(AppLocalization.of(context).phone)),
        DataColumn(label: Text(AppLocalization.of(context).email)),
        DataColumn(label: Text(AppLocalization.of(context).active)),
        DataColumn(label: Container()),
      ];

  List<DataRow> _getRows(_ViewModel viewModel, BuildContext context) =>
      viewModel.contacts
          .map((e) => DataRow(
                onSelectChanged: (value) async => context
                    .pushNamed('/contacts/view', queryParameters: {'id': e.id}),
                cells: [
                  DataCell(Text(e.name)),
                  DataCell(Text(e.phone ?? '')),
                  DataCell(Text(e.emailAddress ?? '')),
                  DataCell(Text(e.isActive.toActive(context))),
                  DataCell(Row(
                    children: [
                      ElevatedButton(
                        onPressed: () async => context.pushNamed('/contacts/view',
                            queryParameters: {'id': e.id}),
                        child: Text(AppLocalization.of(context).view),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 8),
                        child: ElevatedButton.icon(
                          onPressed: () => unawaited(showDialog(
                            context: context,
                            builder: (context) => RemoveDialog(
                              remove: () => viewModel.removeUserId(e.id),
                            ),
                          )),
                          icon: const Icon(Icons.remove_circle),
                          label: Text(AppLocalization.of(context).remove),
                        ),
                      ),
                    ],
                  )),
                ],
              ))
          .toList();
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String locationId;
  Map<String, User> contactMap = {};

  Iterable<LocationContact> locationContacts = [];
  Iterable<String> get userIds => contactMap.keys;
  Iterable<User> get contacts => contactMap.values;

  _ViewModel(this.locationId) {
    addListenables([
      DataModel().locationModel,
      DataModel().locationModel,
      DataModel().locationContactModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    locationContacts =
        await DataModel().locationContactModel.getByLocationIds([locationId]);
    final userIds = locationContacts.map((e) => e.userId);
    final contacts = await DataModel().userModel.getByIds(userIds);
    contactMap = {for (final e in contacts) e.id: e};

    notifyListeners();
  }

  Future<void> addUserId(String id) async {
    final locationContact = LocationContact.create()
      ..locationId = locationId
      ..userId = id;
    await DataModel().locationContactModel.saveDirty([locationContact]);
    final contact = await DataModel().userModel.getById(id);
    contactMap[id] = contact!;
    notifyListeners();
  }

  Future<void> removeUserId(String id) async {
    final locationContact = locationContacts.firstWhere((e) => e.userId == id);
    locationContact.isActive = false;
    await DataModel().locationContactModel.saveDirty([locationContact]);
    contactMap.remove(id);
    notifyListeners();
  }
}
