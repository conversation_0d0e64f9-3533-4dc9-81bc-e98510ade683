import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../base_data.dart';
import '../../misc/json_conversion.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import 'package:uuid/uuid.dart';

part 'note.g.dart';

@HiveType(typeId: 15)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class Note extends BaseData {
  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  @Json<PERSON>ey(defaultValue: '')
  String text;

  @HiveField(102)
  @JsonKey(fromJson: nullableIdFromJson)
  String? userId;

  @HiveField(103)
  @JsonKey(fromJson: nullableIdFromJson)
  String? locationId;

  @HiveField(104)
  @Json<PERSON>ey(fromJson: nullableIdFromJson)
  String? scheduleTemplateId;

  @HiveField(105)
  @JsonKey(fromJson: nullableIdFromJson)
  String? scheduleId;

  @HiveField(106)
  @JsonKey(fromJson: nullableIdFromJson)
  String? punchCardId;

  Note({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.text,
    this.userId,
    this.locationId,
    this.scheduleTemplateId,
    this.scheduleId,
    this.punchCardId,
  });

  factory Note.fromJson(Map<String, dynamic> json) {
    try {
      return _$NoteFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }
  Map<String, dynamic> toJson() => _$NoteToJson(this);

  factory Note.create() => Note(
        id: const Uuid().v4(),
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
        text: '',
      );

  factory Note.createUser(String id) => Note.create()..userId = id;
  factory Note.createPunchCard(String id) => Note.create()..punchCardId = id;
  factory Note.createSchedule(String id) => Note.create()..scheduleId = id;
  factory Note.createLocation(String id) => Note.create()..locationId = id;
}
