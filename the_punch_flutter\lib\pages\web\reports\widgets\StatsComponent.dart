import 'package:flutter/material.dart';

class StatsComponent extends StatelessWidget {
  final List<Map<String, String>> statsData;

  const StatsComponent({
    Key? key,
    this.statsData = const [
      {'title': 'Total Alerts', 'value': '1555', 'iconUrl': 'https://dashboard.codeparrot.ai/api/image/aCIanY_8PH23qyz3/warning.png'},
      {'title': 'Total Schedule Alerts', 'value': '1011', 'iconUrl': 'https://dashboard.codeparrot.ai/api/image/aCIanY_8PH23qyz3/calendar.png'},
      {'title': 'Total Geofence Alerts', 'value': '90', 'iconUrl': 'https://dashboard.codeparrot.ai/api/image/aCIanY_8PH23qyz3/fmd-bad.png'},
      {'title': 'Total Combined Breached Time', 'value': '6hrs 38m', 'iconUrl': 'https://dashboard.codeparrot.ai/api/image/aCIanY_8PH23qyz3/globe-lo.png'},
    ],
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Wrap(
            spacing: 16,
            runSpacing: 16,
            children: statsData.map((data) => _buildPanel(data)).toList(),
          );
        },
      ),
    );
  }

  Widget _buildPanel(Map<String, String> data) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(32),
      ),
      color: Color(0xFFEBF6FF),
      child: Container(
        width: 269.25,
        height: 162,
        padding: EdgeInsets.fromLTRB(32, 32, 32, 28),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Text(
                    data['title']!,
                    style: TextStyle(
                      fontFamily: 'Poppins',
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      letterSpacing: -0.14,
                      color: Color(0xFF091F30),
                    ),
                  ),
                ),
                Image.network(
                  data['iconUrl']!,
                  width: 32,
                  height: 32,
                ),
              ],
            ),
            Padding(
              padding: EdgeInsets.only(bottom: 16),
            ),
            Text(
              data['value']!,
              style: TextStyle(
                fontFamily: 'Poppins',
                fontSize: 32,
                fontWeight: FontWeight.w600,
                height: 1.375, // 44px line height / 32px font size
                color: Color(0xFF091F30),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

