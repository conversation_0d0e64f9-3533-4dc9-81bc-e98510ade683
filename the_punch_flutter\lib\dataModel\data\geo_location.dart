import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../base_data.dart';
import '../../misc/json_conversion.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import 'package:uuid/uuid.dart';

part 'geo_location.g.dart';

@HiveType(typeId: 4)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class GeoLocation extends BaseData {
  static const geoLocationUpdateId = '294be331-3f99-4088-a1bb-a178d77f5a97';
  static const geoFenceEnteredId = 'c45474ec-5b0e-4311-967c-bf28f98d6328';
  static const geoFenceExitedId = '6e6792fa-3255-48ac-b8f6-78c3834577c7';
  static const geoLocationEnabledId = '0cf58439-5999-4ba0-be30-7d364d0020f0';
  static const geoLocationDisabledId = 'e32ae577-767e-4113-b779-111e1445d0ec';

  static const geoLocationPunchInId = 'fb839fa9-ae2d-4f22-8dce-df117a94b5ff';
  static const geoLocationPunchOutId = 'aa9f1b2f-ce2d-49b2-b4e6-bc60c636cc9b';


  @HiveField(100)
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  @JsonKey(fromJson: idFromJson)
  String punchCardId;

  @HiveField(102)
  @JsonKey(fromJson: dateTimeFromJson, toJson: dateTimeToJson)
  DateTime recordedOn;

  @HiveField(103)
  double? latitude;

  @HiveField(104)
  double? longitude;

  @HiveField(105)
  @JsonKey(fromJson: idFromJson)
  String geoLocationTypeId;

  @HiveField(106)
  double? distanceFromLocation;

  GeoLocation({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.punchCardId,
    required this.recordedOn,
    this.latitude,
    this.longitude,
    required this.geoLocationTypeId,
    this.distanceFromLocation, // Optional parameter for constructor
  });

  factory GeoLocation.fromJson(Map<String, dynamic> json) => _$GeoLocationFromJson(json);
  Map<String, dynamic> toJson() => _$GeoLocationToJson(this);

  factory GeoLocation.create() => GeoLocation(
        id: const Uuid().v4(),
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
        geoLocationTypeId: '',
        punchCardId: '',
        recordedOn: ServerTimeState().utcTime,
      );
}
