import 'package:json_annotation/json_annotation.dart';
import '../../misc/json_conversion.dart';
import '../../dataModel/data/punch_cards_history.dart';
import 'system.dart';

part 'fetch_punch_card_history_request.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class FetchPunchCardHistoryRequest extends SystemRequest {
  final String punchCardId;

  FetchPunchCardHistoryRequest({
    required this.punchCardId,
    required String serverIP,
    required String databaseName,
    required String sessionId,
  }) : super(serverIP: serverIP, databaseName: databaseName, sessionId: sessionId);

  static Future<FetchPunchCardHistoryRequest> create(String punchCardId) async {
    final systemRequest = await SystemRequest.create(); // Fetch system details (server IP, database name, session ID)
    return FetchPunchCardHistoryRequest(
      punchCardId: punchCardId,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory FetchPunchCardHistoryRequest.fromJson(Map<String, dynamic> json) =>
      _$FetchPunchCardHistoryRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$FetchPunchCardHistoryRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class FetchPunchCardHistoryResponse extends SystemResponse {
  final List<PunchCardsHistory> punchCardHistory;

  FetchPunchCardHistoryResponse({
    required this.punchCardHistory,
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory FetchPunchCardHistoryResponse.fromJson(Map<String, dynamic> json) =>
      _$FetchPunchCardHistoryResponseFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$FetchPunchCardHistoryResponseToJson(this);
}
