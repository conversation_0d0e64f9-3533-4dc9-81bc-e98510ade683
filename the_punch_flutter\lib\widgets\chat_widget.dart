import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:material_symbols_icons/material_symbols_icons.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

import '../api/api_model.dart';
import '../dataModel/data/message.dart';
import '../dataModel/data_model.dart';
import '../helpers/color_helper.dart';
import '../misc/extensions.dart';
import '../services/cached_image_service.dart';
import '../state/login_state.dart';

class ChatWidget extends StatefulWidget {
  final String groupId;
  final String groupName;
  final Set<String> groupMemberUserIds;

  const ChatWidget({
    Key? key,
    required this.groupId,
    required this.groupName,
    required this.groupMemberUserIds,
  }) : super(key: key);

  @override
  State<ChatWidget> createState() => _ChatWidgetState();
}

class _ChatWidgetState extends State<ChatWidget> {
  final _textController = TextEditingController();
  final _scrollController = ScrollController();

  // Whether the send button is enabled
  final _sendEnabled = ValueNotifier<bool>(false);

  /// Whether we're currently loading messages the very first time.
  bool _isInitialLoading = true;

  /// Whether we're currently uploading the selected image (shows spinner).
  bool _isUploadingImage = false;

  /// The image user selected (after compression).
  File? _selectedFile;

  /// Our local list of messages
  List<Message> messages = [];

  /// For displaying sender names
  final Map<String, String> _userNameCache = {};

  /// A local map from `message.body` -> final resolved image URL
  final Map<String, String> _finalUrls = {};

  @override
  void initState() {
    super.initState();
    _textController.addListener(_updateSendEnabled);

    // Initial load
    _refreshMessages();

    // Listen for DB changes
    DataModel().messageModel.addListener(_onExternalMessageChange);
  }

  void _onExternalMessageChange() {
    // Refresh if new messages appear
    _refreshMessages();
  }

  @override
  void dispose() {
    DataModel().messageModel.removeListener(_onExternalMessageChange);
    _textController.dispose();
    super.dispose();
  }

  // ---------------------------------------------------------------------------
  // LOADING MESSAGES & CACHING
  // ---------------------------------------------------------------------------

  Future<void> _refreshMessages() async {
    if (_isInitialLoading) {
      setState(() => _isInitialLoading = true);
    }

    // 1) Load and sort
    final raw = await DataModel().messageModel.findByGroupId(widget.groupId);
    final newMessages = raw.toList()
      ..sort((a, b) => a.sentOn.compareTo(b.sentOn));

    // 2) Mark unread as read
    await _markUnreadAsRead(newMessages);

    // 3) Populate user names
    await _populateUserNameCache(newMessages);

    // 4) Ensure we have final image URLs
    await _cacheImageUrlsForMessages(newMessages);

    // 5) Update UI
    if (!mounted) return;
    setState(() {
      messages = newMessages;
      _isInitialLoading = false;
    });

    // Scroll to bottom
    WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToBottom());
  }

  Future<void> _markUnreadAsRead(List<Message> newMessages) async {
    final me = LoginState.userId;
    final unread = newMessages
        .where((m) => m.fromUserId != me && m.openedOn == null)
        .toList();
    if (unread.isNotEmpty) {
      for (final msg in unread) {
        msg.openedOn = DateTime.now();
      }
      await DataModel().messageModel.updateMessages(unread);
    }
  }

  Future<void> _populateUserNameCache(List<Message> allMessages) async {
    for (final msg in allMessages) {
      final userId = msg.fromUserId;
      if (_userNameCache.containsKey(userId)) continue;

      try {
        final fullName = await DataModel().userModel.getFullName(userId);
        _userNameCache[userId] = fullName;
      } catch (_) {
        _userNameCache[userId] = 'Unknown User';
      }
    }
  }

  Future<void> _cacheImageUrlsForMessages(List<Message> msgs) async {
    for (final msg in msgs) {
      final body = msg.body;
      // If we already have it, skip
      if (_finalUrls.containsKey(body)) continue;

      if (_isGuid(body)) {
        // Build or retrieve from disk
        final resolvedUrl = await CachedImageService().ensureUrl(body, () {
          return ApiModel().buildAssetImage(body);
        });
        _finalUrls[body] = resolvedUrl;
      } else if (_isUrl(body)) {
        // If it's direct, store it
        await CachedImageService().setUrl(body, body);
        _finalUrls[body] = body;
      }
    }
  }

  // ---------------------------------------------------------------------------
  // BUILD
  // ---------------------------------------------------------------------------

  @override
  Widget build(BuildContext context) {
    if (_isInitialLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: ColorHelper.thePunchAccentRed(),
            ),
            const SizedBox(height: 8),
            const Text(
              "loading chat assets...",
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        Center(
          child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
          Text(widget.groupName,
                style: TextStyle(fontSize: 20, color: Colors.black)),
            IconButton(
              onPressed: () {
              context.pushNamed(
                '/chat/edit',
                extra: widget.groupId,
              );
              },
              icon: const Icon(Symbols.edit_square),
            )
          
          ]),
        ),

        // If no messages exist at all
        if (messages.isEmpty)
          Expanded(
            child: Center(
              child: Text(
                'No Messages',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
          )
        else
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              itemCount: messages.length,
              itemBuilder: (context, index) {
                final msg = messages[index];
                final tileKey = msg.id?.toString() ??
                    '${msg.body}_${msg.sentOn.millisecondsSinceEpoch}';

                return _MessageTile(
                  key: ValueKey(tileKey),
                  message: msg,
                  senderName: _senderNameFor(msg),
                  groupMemberUserIds: widget.groupMemberUserIds,
                  finalUrl: _finalUrls[msg.body] ?? '',
                  onImageLoaded: _autoScrollIfNearBottom,
                );
              },
            ),
          ),

        _buildInputRow(context),
      ],
    );
  }

  Widget _buildInputRow(BuildContext context) {
    final theme = Theme.of(context);
    return Padding(
        padding: const EdgeInsets.all(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(40),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Row(
            children: [
              ValueListenableBuilder<bool>(
                valueListenable: _sendEnabled,
                builder: (_, canSend, __) => CircleAvatar(
                  backgroundColor: !canSend
                      ? Colors.blue
                      : ColorHelper.thePunchDesktopGray(),
                  radius: 22,
                  child: IconButton(
                    icon: const Icon(Icons.image, color: Colors.white),
                    onPressed: _pickImage,
                  ),
                ),
              ),
              Expanded(
                child: _selectedFile == null
                    ? TextField(
                        controller: _textController,
                        textInputAction: TextInputAction.send,
                        onSubmitted: (_) => _sendMessage(),
                        style: theme.textTheme.bodyMedium,
                        decoration: InputDecoration(
                          hintText: AppLocalizations.of(context)!
                              .message, // Localized hint text
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(24),
                            borderSide: const BorderSide(
                                color:
                                    Colors.transparent), // Transparent border
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(24),
                            borderSide: const BorderSide(
                              color: Colors.transparent,
                              width: 2,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(24),
                            borderSide: const BorderSide(
                                color: Colors
                                    .transparent), // Transparent on enabled state
                          ),
                        ),
                      )
                    : Container(
                        width: 150,
                        height: 150,
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          border: Border.all(color: Colors.blue),
                          borderRadius: BorderRadius.circular(24),
                        ),
                        child: Stack(
                          children: [
                            SizedBox(
                              width: 150,
                              height: 150,
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: Image.file(
                                  _selectedFile!,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),

                            // "X" button
                            Positioned(
                              top: 4,
                              right: 4,
                              child: GestureDetector(
                                onTap: _removeSelectedImage,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.black54,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  padding: const EdgeInsets.all(4),
                                  child: const Icon(
                                    Icons.close,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                              ),
                            ),

                            // Spinner if uploading
                            if (_isUploadingImage)
                              const SizedBox(
                                width: 150,
                                height: 150,
                                child: Center(
                                  child: CircularProgressIndicator(),
                                ),
                              ),
                          ],
                        ),
                      ),
              ),
              ValueListenableBuilder<bool>(
                valueListenable: _sendEnabled,
                builder: (_, canSend, __) => CircleAvatar(
                  backgroundColor:
                      canSend ? Colors.blue : ColorHelper.thePunchDesktopGray(),
                  radius: 22,
                  child: IconButton(
                    icon: const Icon(Icons.send, color: Colors.white),
                    onPressed: canSend ? _sendMessage : null,
                  ),
                ),
              ),
            ],
          ),
        ));
  }

  String _senderNameFor(Message msg) {
    final userId = msg.fromUserId;
    final name = _userNameCache[userId] ?? 'Unknown User';
    final inGroup = widget.groupMemberUserIds.contains(userId);
    return inGroup ? name : '$name (Removed)';
  }

  // ---------------------------------------------------------------------------
  // SCROLLING
  // ---------------------------------------------------------------------------

  void _autoScrollIfNearBottom() {
    if (!_scrollController.hasClients) return;
    const threshold = 100.0;
    final position = _scrollController.position;
    final distanceToBottom = position.maxScrollExtent - position.pixels;
    if (distanceToBottom < threshold) {
      _scrollToBottom();
    }
  }

  Future<void> _scrollToBottom() async {
    if (!_scrollController.hasClients) return;

    // First pass
    await Future.delayed(const Duration(milliseconds: 50));
    if (_scrollController.hasClients) {
      final maxScroll = _scrollController.position.maxScrollExtent;
      _scrollController.jumpTo(maxScroll);
    }

    // Second pass to ensure we don't stop short
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        final maxScroll = _scrollController.position.maxScrollExtent;
        _scrollController.jumpTo(maxScroll);
      }
    });
  }

  // ---------------------------------------------------------------------------
  // IMAGE PICKING & COMPRESSING
  // ---------------------------------------------------------------------------

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final xfile = await picker.pickImage(source: ImageSource.gallery);
    if (xfile == null) return; // user cancelled

    final originalFile = File(xfile.path);
    final compressedFile = await _compressImage(originalFile);

    setState(() {
      _selectedFile = compressedFile;
    });
    _updateSendEnabled();
  }

  Future<File> _compressImage(File file) async {
    final dir = await getTemporaryDirectory();
    final targetPath =
        '${dir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';

    final XFile? compressedXFile =
        await FlutterImageCompress.compressAndGetFile(
      file.absolute.path,
      targetPath,
      quality: 60,
      format: CompressFormat.jpeg,
    );
    return compressedXFile == null ? file : File(compressedXFile.path);
  }

  void _removeSelectedImage() {
    setState(() {
      _selectedFile = null;
      _isUploadingImage = false;
    });
    _updateSendEnabled();
  }

  // ---------------------------------------------------------------------------
  // SENDING MESSAGES
  // ---------------------------------------------------------------------------

  Future<void> _sendMessage() async {
    final body = _textController.text.trim();

    // 1) If there's text
    if (body.isNotEmpty) {
      final textMsg = Message.create()
        ..body = body
        ..toGroupId = widget.groupId;

      setState(() {
        messages.add(textMsg);
      });

      // If it's a direct URL, store that mapping
      if (_isUrl(body)) {
        await CachedImageService().setUrl(body, body);
        _finalUrls[body] = body;
      }

      // Save to DB
      await DataModel().messageModel.saveDirty([textMsg]);
    }

    // 2) If there's an image
    if (_selectedFile != null) {
      try {
        setState(() {
          _isUploadingImage = true;
          _updateSendEnabled();
        });

        final guid = const Uuid().v4();
        // Upload
        await ApiModel().uploadAsset(_selectedFile!, guid);

        // Build final URL
        final url = await ApiModel().buildAssetImage(guid);
        // Cache locally
        await CachedImageService().setUrl(guid, url);
        _finalUrls[guid] = url;

        // Create DB entry
        final imgMsg = Message.create()
          ..body = guid
          ..toGroupId = widget.groupId;

        setState(() {
          messages.add(imgMsg);
        });
        await DataModel().messageModel.saveDirty([imgMsg]);
      } catch (e) {
        debugPrint('Error uploading asset: $e');
      } finally {
        setState(() {
          _isUploadingImage = false;
          _updateSendEnabled();
        });
      }
    }

    // Clear text & image
    _textController.clear();
    _removeSelectedImage();

    _autoScrollIfNearBottom();
  }

  void _updateSendEnabled() {
    final hasText = _textController.text.trim().isNotEmpty;
    final hasImage = _selectedFile != null;
    // Disable if uploading an image (lock the send button)
    _sendEnabled.value = (hasText || hasImage) && !_isUploadingImage;
  }

  bool _isGuid(String text) {
    final regexGuid = RegExp(
      r'^[0-9a-fA-F]{8}-'
      r'[0-9a-fA-F]{4}-'
      r'[0-9a-fA-F]{4}-'
      r'[0-9a-fA-F]{4}-'
      r'[0-9a-fA-F]{12}$',
    );
    return regexGuid.hasMatch(text);
  }

  bool _isUrl(String text) {
    final uri = Uri.tryParse(text);
    return (uri != null && uri.hasAbsolutePath);
  }
}

// ---------------------------------------------------------------------------
// A single message bubble in the chat list
// ---------------------------------------------------------------------------

class _MessageTile extends StatefulWidget {
  final Message message;
  final String senderName;
  final String finalUrl; // The already-resolved URL
  final VoidCallback? onImageLoaded;
  final Set<String> groupMemberUserIds;

  const _MessageTile({
    Key? key,
    required this.message,
    required this.senderName,
    required this.groupMemberUserIds,
    required this.finalUrl,
    this.onImageLoaded,
  }) : super(key: key);

  @override
  _MessageTileState createState() => _MessageTileState();
}

class _MessageTileState extends State<_MessageTile>
    with AutomaticKeepAliveClientMixin {
  bool get isFromMe => widget.message.fromUserId == LoginState.userId;

  // A simple counter that forces Flutter to refetch the image
  // whenever we change its value.
  int _retryCounter = 0;

  @override
  bool get wantKeepAlive => true; // preserve state off-screen

  @override
  Widget build(BuildContext context) {
    super.build(context);
    print('$isFromMe');
    final theme = Theme.of(context);
    final locale = Localizations.localeOf(context);

    final alignment = isFromMe ? Alignment.centerRight : Alignment.centerLeft;
    final bubbleColor = isFromMe
        ? ColorHelper.thePunchLightBlue()
        : ColorHelper.thePunchDesktopGray();
    final textColor = Colors.white;

    final shape = const RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(8),
        bottomLeft: Radius.circular(8),
        topRight: Radius.circular(8),
        bottomRight: Radius.circular(8),
      ),
    );

    final messageBodyStyle =
        theme.textTheme.bodyMedium?.copyWith(color: Colors.black);
    final captionStyle =
        theme.textTheme.bodySmall?.copyWith(color: Colors.grey[700]);

    final body = widget.message.body;

    // If finalUrl is non-empty, treat as image
    Widget content;
    if (widget.finalUrl.isNotEmpty) {
      // Use a key that changes when _retryCounter changes,
      // forcing a new fetch when we increment _retryCounter.
      content = GestureDetector(
        onTap: () => _openFullScreen(context, widget.finalUrl),
        child: Image.network(
          widget.finalUrl,
          key: ValueKey('img-${widget.finalUrl}-$_retryCounter'),
          width: 200,
          height: 200,
          fit: BoxFit.cover,
          loadingBuilder: (ctx, child, loadingProgress) {
            if (loadingProgress == null) {
              // Once loaded, let parent know so it can auto-scroll if needed
              WidgetsBinding.instance.addPostFrameCallback((_) {
                widget.onImageLoaded?.call();
              });
              return child;
            }
            // Keep the same 200×200 space while loading
            return const SizedBox(
              width: 200,
              height: 200,
              child: Center(
                child: CircularProgressIndicator(),
              ),
            );
          },
          errorBuilder: (ctx, error, stack) {
            // Give the user a way to retry
            return InkWell(
              onTap: () {
                setState(() => _retryCounter++);
              },
              child: SizedBox(
                width: 200,
                height: 200,
                child: Center(
                  child: Text(
                    'Could not load image.\nTap to Retry.',
                    textAlign: TextAlign.center,
                    style:
                        theme.textTheme.bodySmall?.copyWith(color: Colors.red),
                  ),
                ),
              ),
            );
          },
        ),
      );
    } else {
      // Plain text
      content = Text(body, style: messageBodyStyle);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment:
            isFromMe ? MainAxisAlignment.start : MainAxisAlignment.start,
        textDirection: isFromMe ? TextDirection.ltr : TextDirection.rtl,
        children: [
          Align(
            alignment: alignment,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width *
                    0.60, // Max width = 75% of screen width
              ),
              child: Card(
                shape: shape,
                color: bubbleColor,
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child: content,
                ),
              ),
            ),
          ),
          Align(
            alignment: alignment,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Text(
                widget.message.sentOn.toFormattedDateTime(locale),
                style: captionStyle,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _openFullScreen(BuildContext context, String url) {
    Navigator.of(context).push(
      PageRouteBuilder(
        opaque: false,
        transitionDuration: Duration.zero,
        reverseTransitionDuration: Duration.zero,
        pageBuilder: (_, __, ___) => FullScreenImagePage(imageUrl: url),
      ),
    );
  }
}

// ---------------------------------------------------------------------------
// Full-screen image viewer
// ---------------------------------------------------------------------------
class FullScreenImagePage extends StatelessWidget {
  final String imageUrl;

  const FullScreenImagePage({
    Key? key,
    required this.imageUrl,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => Scaffold(
        backgroundColor: Colors.black54,
        body: SafeArea(
          child: Stack(
            children: [
              Center(
                child: InteractiveViewer(
                  child: Image.network(
                    imageUrl,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
              Positioned(
                top: 5,
                right: 24,
                child: GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: const BoxDecoration(
                      color: Colors.black54,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.close,
                      color: ColorHelper.thePunchAccentRed(),
                      size: 28,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
}
