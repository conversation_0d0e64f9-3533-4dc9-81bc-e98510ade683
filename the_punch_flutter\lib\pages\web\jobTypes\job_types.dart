import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../../dataModel/data/job_type.dart';
import '../../../dataModel/data_model.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/app_localization.dart';
import '../../view_model_mixin.dart';
import '../my_scaffold.dart';
import 'job_type_mixin.dart';
import '../../../state/permissions_state.dart';
import '../../../widgets/active_toggle.dart';
import '../../../widgets/search_text_field.dart';
import '../../../misc/extensions.dart';
import '../../../widgets/tables_global.dart'; // Import the CustomTable

class JobTypesPage extends StatelessWidget {
  const JobTypesPage({super.key});

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
        create: (context) => _ViewModel(),
        child: MyScaffold(
          title: AppLocalization.of(context).jobTypes,
          body: _Body(),
        ),
      );
}

class _TableHeader extends StatefulWidget {
  @override
  _TableHeaderState createState() => _TableHeaderState();
}

class _TableHeaderState extends State<_TableHeader> {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
      builder: (context, viewModel, child) => Card(
            elevation: 0,
            color: Colors.transparent,
            shadowColor: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Flex(
                direction: Axis.horizontal,
                children: [
                  Flexible(
                    child: SearchTextField(notifier: viewModel.searchNotifier),
                  ),
                  const SizedBox(width: 20),
                  Padding(
                    padding: const EdgeInsets.all(8),
                    child: ActiveToggle(viewModel.activeNotifier),
                  ),
                  const SizedBox(width: 20),
                  if (PermissionsState().editJobTypes)
                    Padding(
                      padding: const EdgeInsets.all(4),
                      child: ElevatedButton.icon(
                        onPressed: () => context.go('/jobTypes/edit?anything=1'),
                        icon: const Icon(Icons.add, size: 18),
                        label: Text(
                          AppLocalization.of(context).addJobType,
                          style: const TextStyle(fontSize: 10),
                        ),
                        
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 6),
                          minimumSize: const Size(20,56),
                          backgroundColor: ColorHelper.thePunchDarkBlue()
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ));
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Align(
        alignment: Alignment.centerLeft,
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1300),
          child: Column(
            children: [
              _TableHeader(),
              const SizedBox(height: 12), // Add spacing for better layout
              Flexible(child: _JobTypesTable()),
            ],
          ),
        ),
      );
}

class _JobTypesTable extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          final rows = getRows(viewModel, context); // Pass context here
          return Card(
            margin: const EdgeInsets.all(8),
            elevation: 3,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            child: Container(
              color: Colors.transparent, // or Colors.white
              padding: const EdgeInsets.all(16),
              child: CustomTable(
              columns: getColumns(context), // Pass context here
              rows: rows,
              mobileTableTitle: 'Job Types',
              onRowClick: (DataRow row) {
                final jobTypeName = (row.cells[0].child as Text).data;

                final selectedJobType = viewModel.jobTypes
                  .firstWhere((jobType) => jobType.name == jobTypeName);

                unawaited(context.pushNamed(
                '/jobTypes/view',
                queryParameters: {'id': selectedJobType.id},
                ));
              },
              ),
            ),
          );
        },
      );

  // Define columns for the table
  List<DataColumn> getColumns(BuildContext context) => [
        DataColumn(
          label: Text(
            AppLocalization.of(context).jobType,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
        ),
        DataColumn(
          label: Text(
            AppLocalization.of(context).description,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
        ),
        const DataColumn(
          label: Text(
            'Display Order',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
        ),
        DataColumn(
          label: Text(
            AppLocalization.of(context).activate,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
        ),
        const DataColumn(
          label: Text(
            'Actions',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
        ),
      ];

  // Define rows for the table
  List<DataRow> getRows(_ViewModel viewModel, BuildContext context) {
    if (!viewModel.isRefreshed) return [];

    Iterable<JobType> jobTypes = viewModel.jobTypes;

    // Filter job types based on active/inactive toggle
    switch (viewModel.activeNotifier.value) {
      case ActiveToggleState.active:
        jobTypes = jobTypes.where((e) => e.isActive);
        break;
      case ActiveToggleState.inactive:
        jobTypes = jobTypes.where((e) => !e.isActive);
        break;
      default:
        break;
    }

    // Apply search filter
    final search = viewModel.searchNotifier.value;
    if (search.isNotEmpty) {
      jobTypes = jobTypes.where((e) {
        if (e.name.toLowerCase().contains(search.toLowerCase())) return true;
        if (e.description != null && e.description!.contains(search)) {
          return true;
        }
        return false;
      });
    }

    // Sorting job types by order
    final visibleJobTypes = jobTypes.toList();
    final ordinalMap = <String, int>{};
    final ordinalSortedJobTypes = visibleJobTypes.toList();
    ordinalSortedJobTypes.sort((a, b) => a.order.compareTo(b.order));
    for (var i = 0; i < ordinalSortedJobTypes.length; i++) {
      ordinalMap[ordinalSortedJobTypes[i].id] = i + 1;
    }

    // Return the rows for the table
    return visibleJobTypes
        .map((e) => DataRow(
              onSelectChanged: (value) async => context.pushNamed(
                '/jobTypes/view',
                queryParameters: {'id': e.id},
              ),
              cells: [
                DataCell(Text(e.name, style: const TextStyle(fontSize: 14))),
                DataCell(Text(e.description ?? '', style: const TextStyle(fontSize: 14))),
                DataCell(Text(
                  ordinalMap[e.id]!.toString(),
                  style: const TextStyle(fontSize: 14),
                )),
                DataCell(Text(
                  e.isActive.toActive(context),
                  style: const TextStyle(
                    fontSize: 14,
                  ),
                )),
                DataCell(
                  ElevatedButton(
                    onPressed: () async => context.pushNamed(
                      '/jobTypes/view',
                      queryParameters: {'id': e.id},
                    ),
                    child: Text(
                      AppLocalization.of(context).view,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ),
              ],
            ))
        .toList();
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin, JobTypeMixin {
  List<JobType> jobTypes = [];
  var activeNotifier = ValueNotifier(ActiveToggleState.active);
  var searchNotifier = ValueNotifier('');

  _ViewModel() {
    addListenables([
      DataModel().jobTypeModel,
      activeNotifier,
      searchNotifier,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    jobTypes = (await DataModel().jobTypeModel.visible).toList();
    notifyListeners();
  }

  @override
  bool get isRefreshed => jobTypes.isNotEmpty;
}
