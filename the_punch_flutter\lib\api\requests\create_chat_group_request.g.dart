// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_chat_group_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateChatGroupRequest _$CreateChatGroupRequestFromJson(
        Map<String, dynamic> json) =>
    CreateChatGroupRequest(
      groupId: json['GroupId'] as String,
      name: json['Name'] as String,
      createdByUserId: json['CreatedByUserId'] as String?,
      memberUserIds: (json['MemberUserIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$CreateChatGroupRequestToJson(
    CreateChatGroupRequest instance) {
  final val = <String, dynamic>{
    'Request_ServerIP': instance.serverIP,
    'Request_DatabaseName': instance.databaseName,
    'Request_SessionID': instance.sessionId,
    'GroupId': instance.groupId,
    'Name': instance.name,
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  val['MemberUserIds'] = instance.memberUserIds;
  return val;
}

CreateChatGroupResponse _$CreateChatGroupResponseFromJson(
        Map<String, dynamic> json) =>
    CreateChatGroupResponse(
      errorCode: json['ErrorCode'] as String,
      errorMessage: json['ErrorMessage'] as String,
    );

Map<String, dynamic> _$CreateChatGroupResponseToJson(
        CreateChatGroupResponse instance) =>
    <String, dynamic>{
      'ErrorCode': instance.errorCode,
      'ErrorMessage': instance.errorMessage,
    };
