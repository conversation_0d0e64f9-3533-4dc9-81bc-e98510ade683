import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import '../dataModel/data/note.dart';
import 'material_dialog.dart';
import '../misc/app_localization.dart';
import '../widgets/decorated_text_field.dart';

import 'edit_note_dialog.dart';

class ViewNoteDialog extends StatefulWidget {
  final Note note;
  const ViewNoteDialog({super.key, required this.note});

  @override
  State<ViewNoteDialog> createState() => _ViewNoteDialogState();
}

class _ViewNoteDialogState extends State<ViewNoteDialog> {
  @override
  Widget build(BuildContext context) => MaterialDialog(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 500),
        //expandChild: false,
        title: AppLocalizations.of(context)!.note,
        child: Column(
          children: [
            Align(
                alignment: Alignment.centerRight,
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child:
                      ElevatedButton.icon(onPressed: () => unawaited(showDialog(context: context, builder: (context) => EditNoteDialog(note: widget.note))), icon: const Icon(Icons.edit), label: Text(AppLocalization.of(context).editNote)),
                )),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: DecoratedContainer(
                  labelText: AppLocalizations.of(context)!.note,
                  child: SingleChildScrollView(
                    child: Text(widget.note.text),
                  ),
                ),
              ),
            ),
          ],
        ),
      );
}
