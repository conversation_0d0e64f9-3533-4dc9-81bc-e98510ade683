import 'package:json_annotation/json_annotation.dart';
import '../../dataModel/data/user.dart';
import '../../misc/json_conversion.dart';
import 'system.dart';

part 'login.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class LoginRequest {
  @JsonKey(name: 'OrganizationID', defaultValue: '')
  final String organizationId;
  @JsonKey(defaultValue: '')
  final String username;
  @JsonKey(defaultValue: '')
  final String password;
  @Json<PERSON>ey(name: 'deviceID', defaultValue: '')
  final String deviceId;

  LoginRequest({required this.organizationId, required this.username, required this.password, required this.deviceId});

  factory LoginRequest.fromJson(Map<String, dynamic> json) => _$LoginRequestFromJson(json);
  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class LoginResponse extends SystemResponse {
  SystemRequest databaseInfo;
  User user;
  int userSessionExpirationMinsWeb;

  LoginResponse({
    required this.databaseInfo,
    required this.user,
    required this.userSessionExpirationMinsWeb,
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory LoginResponse.fromJson(Map<String, dynamic> json) => _$LoginResponseFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$LoginResponseToJson(this);
}
