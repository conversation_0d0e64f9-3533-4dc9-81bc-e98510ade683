import 'app_localizations.dart';

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get locationNotesTitle => 'Location Notes';

  @override
  String get punchStatus => 'Punch Status';

  @override
  String get address1 => 'Address 1';

  @override
  String get address2 => 'Address 2';

  @override
  String get addressInformation => 'Address Information';

  @override
  String get administrator => 'Administrator';

  @override
  String get administratorDescription => 'Controls everything in the system.';

  @override
  String get cancel => 'Cancel';

  @override
  String get city => 'City';

  @override
  String get configSettingUserSessionExpirationMinsMvc => 'User Session Expiration Admin Site (Mins)';

  @override
  String get configSettingUserSessionExpirationMinsMvcDescription => 'Amount of time in minutes before inactive user sessions will expire on the admin site.';

  @override
  String get clockIn => 'Punch In';

  @override
  String get clockOut => 'Punch Out';

  @override
  String get punch => 'Punch';

  @override
  String get inW => 'In';

  @override
  String get outW => 'Out';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get confirmPasswordNotMatchPassword => 'The Confirm Password does not match the entered password.';

  @override
  String get confirmPasswordRequired => 'The Confirm Password is required.';

  @override
  String get contact => 'Contact';

  @override
  String get contactTypeDescription => 'Default permission template for a contact.';

  @override
  String get contactInformation => 'Contact Information';

  @override
  String get country => 'Country';

  @override
  String get emailAddressAlreadyRegistered => 'This email address is already registered.';

  @override
  String get firstName => 'First Name';

  @override
  String get firstNameRequired => 'The First Name is required.';

  @override
  String get forcedOut => 'Forced Out';

  @override
  String get forgotPassword => 'Forgot Password';

  @override
  String get internalError => 'There was an internal server error. Please try again.';

  @override
  String get invalidRequest => 'The request sent was invalid.';

  @override
  String get lastName => 'Last Name';

  @override
  String get lastNameRequired => 'The Last Name is required.';

  @override
  String get loggedOut => 'Logged Out';

  @override
  String get sandbox => 'Sandbox';

  @override
  String get login => 'Login';

  @override
  String get logout => 'Logout';

  @override
  String get mainEmailAddress => 'Main Email Address';

  @override
  String get mainEmailAddressMustBeValidEmailFormat => 'The Main Email Address must be a valid email format.';

  @override
  String get mainEmailAddressRequired => 'The Main Email Address is required.';

  @override
  String get messages => 'Messages';

  @override
  String get networkUnavailable => 'Network Unavailable';

  @override
  String get ok => 'OK';

  @override
  String get punchedIn => 'Punched In';

  @override
  String get organizationId => 'Organization Identifier';

  @override
  String get organizationIdNotFound => 'The Organization Identifier was not found.';

  @override
  String get organizationIdRequired => 'The Organization Identifier is required.';

  @override
  String get organizationName => 'Organization Name';

  @override
  String get organizationNameRequired => 'The Organization Name is required.';

  @override
  String get password => 'Password';

  @override
  String get passwordNotConformRules => 'The Password does not conform to the password rules.';

  @override
  String get passwordRequired => 'The Password is required.';

  @override
  String get phone => 'Phone';

  @override
  String get register => 'Register';

  @override
  String get registerOrganization => 'Register Organization';

  @override
  String get schedule => 'Schedule';

  @override
  String get sessionForcedOutReason => 'Another session has been started from IP address , if you feel this is in error, please sign in again and change your password.';

  @override
  String get state => 'State';

  @override
  String get sysop => 'Sysop';

  @override
  String get sysopDescription => 'Controls everything in the system, and can see extended developer details.';

  @override
  String get timedOut => 'Timed Out';

  @override
  String get punchCard => 'Punch Card';

  @override
  String get timeClock => 'Time Clock';

  @override
  String get username => 'Username';

  @override
  String get usernameOrPasswordIncorrect => 'The user name or password is incorrect.';

  @override
  String get usernameRequired => 'The Username is required.';

  @override
  String get userMarkedInactive => 'This user is marked as inactive.';

  @override
  String get punchedThisWeek => 'Total Time Punched';

  @override
  String get punchedToday => 'Punched Today';

  @override
  String get zip => 'Zip';

  @override
  String get deviceIdRequired => 'The Device Identifier is required.';

  @override
  String get invalidHash => 'The hash value sent in was invalid.';

  @override
  String get noResultsFound => 'There were no results found.';

  @override
  String get pleaseWait => 'Please Wait';

  @override
  String get sessionNotFound => 'The session was not found.';

  @override
  String get rotatedOut => 'Rotated Out';

  @override
  String get sessionForcedOut => 'This session has been forced out.';

  @override
  String get sessionLoggedOut => 'This session has been logged out of.';

  @override
  String get sessionTimedOut => 'This session has timed out.';

  @override
  String get userNotFoundOrInactive => 'This user was not found or is inactive.';

  @override
  String get sessionRotatedOut => 'The session was rotated out.';

  @override
  String get configSettingUserSessionExpirationMinsApi => 'User Session Expiration Handheld Devices (Mins)';

  @override
  String get configSettingUserSessionExpirationMinsApiDescription => 'Amount of time in minutes before inactive user sessions will expire on the handheld devices.';

  @override
  String get home => 'Home';

  @override
  String get locations => 'Locations';

  @override
  String get allLocations => 'All Locations';

  @override
  String get changeLocation => 'Change Location';

  @override
  String get action => 'Action';

  @override
  String get activate => 'Activate';

  @override
  String get activateAreYouSure => 'Are you sure you want to activate this record?';

  @override
  String get activateQuestion => 'Activate?';

  @override
  String get active => 'Active';

  @override
  String get activeType => 'Active Type';

  @override
  String get address => 'Address';

  @override
  String get all => 'All';

  @override
  String get deactivate => 'Deactivate';

  @override
  String get deactivateAreYouSure => 'Are you sure you want to deactivate this record?';

  @override
  String get deactivateQuestion => 'Deactivate?';

  @override
  String get edit => 'Edit';

  @override
  String get inactive => 'Inactive';

  @override
  String get locationName => 'Location Name';

  @override
  String get locationNotFound => 'This location was not found.';

  @override
  String get no => 'No';

  @override
  String get noPermission => 'You do not have permission to access this feature.';

  @override
  String get search => 'Search';

  @override
  String get successExclamation => 'Success!';

  @override
  String get view => 'View';

  @override
  String get yes => 'Yes';

  @override
  String get abbreviation => 'Abbreviation';

  @override
  String get emailAddress => 'Email Address';

  @override
  String get location => 'Location';

  @override
  String get addLocation => 'Add Location';

  @override
  String get locationNameRequired => 'The Location Name is required.';

  @override
  String get submit => 'Submit';

  @override
  String get viewLocations => 'View Locations';

  @override
  String get abbreviationAlreadyExists => 'This abbreviation already exists.';

  @override
  String get abbreviationRequired => 'The Abbreviation is required.';

  @override
  String get editLocation => 'Edit Location';

  @override
  String get userpermissionCanEditLocations => 'Can Edit Locations';

  @override
  String get userpermissionCanEditLocationsDescription => 'Allows user to edit location details.';

  @override
  String get userpermissionCanViewLocations => 'Can View Locations';

  @override
  String get userpermissionCanViewLocationsDescription => 'Allows user to view location details.';

  @override
  String get usernameCannotContainSpaces => 'The Username cannot contain spaces.';

  @override
  String get chat => 'Chat';

  @override
  String get chatDescription => 'Chat messages';

  @override
  String get email => 'Email';

  @override
  String get emailDescription => 'Email messages';

  @override
  String get actions => 'Actions';

  @override
  String get addEmployee => 'Add Employee';

  @override
  String get employees => 'Employees';

  @override
  String get employeeName => 'Employee Name';

  @override
  String get employeeType => 'Employee Type';

  @override
  String get userpermissionCanEditEmployees => 'Can Edit Employees';

  @override
  String get userpermissionCanEditEmployeesDescription => 'Allows user to edit employee details.';

  @override
  String get userpermissionCanViewEmployees => 'Can View Employees';

  @override
  String get userpermissionCanViewEmployeesDescription => 'Allows user to view employee details.';

  @override
  String get viewEmployees => 'View Employees';

  @override
  String get editEmployee => 'Edit Employee';

  @override
  String get employee => 'Employee';

  @override
  String get employeeId => 'Employee ID';

  @override
  String get employeeNotFound => 'This employee was not found.';

  @override
  String get employeeTypeRequired => 'The Employee Type is required.';

  @override
  String get usernameAlreadyRegistered => 'This username is already registered.';

  @override
  String get userpermissionCanEditContacts => 'Can Edit Contacts';

  @override
  String get userpermissionCanEditContactsDescription => 'Allows user to edit contact details.';

  @override
  String get userpermissionCanViewContacts => 'Can View Contacts';

  @override
  String get userpermissionCanViewContactsDescription => 'Allows user to view contact details.';

  @override
  String get addContact => 'Add Contact';

  @override
  String get contacts => 'Contacts';

  @override
  String get contactName => 'Contact Name';

  @override
  String get viewContacts => 'View Contacts';

  @override
  String get contactId => 'Contact ID';

  @override
  String get contactLocationRequired => 'The location is required for this contact.';

  @override
  String get contactNotFound => 'This contact was not found.';

  @override
  String get editContact => 'Edit Contact';

  @override
  String get chooseLocation => 'Choose Location';

  @override
  String get select => 'Select';

  @override
  String get employeeTypeDescription => 'Default permission template for an employee.';

  @override
  String get contactType => 'Contact Type';

  @override
  String get contactTypeRequired => 'The Contact Type is required.';

  @override
  String get chooseContact => 'Choose Contact';

  @override
  String get addContactToLocation => 'Add Contact To Location';

  @override
  String get addEmployeeToLocation => 'Add Employee To Location';

  @override
  String get contactAlreadyInLocation => 'This contact is already in this location.';

  @override
  String get areYouSureQuestion => 'Are You Sure?';

  @override
  String get areYouSureRemoveContactFromLocation => 'Are you sure you want to remove this contact from this location?';

  @override
  String get contactNotFoundInLocation => 'This contact was not found in this location.';

  @override
  String get removeContactFromLocation => 'Remove Contact From Location';

  @override
  String get notes => 'Manager Notes';

  @override
  String get employeeTypeActions => 'Employee Type Actions';

  @override
  String get userpermissionCanViewEmployeeTypes => 'Can View Employee Types';

  @override
  String get userpermissionCanViewEmployeeTypesDescription => 'Allows user to view employee type details.';

  @override
  String get viewEmployeeTypes => 'View Employee Types';

  @override
  String get employeeCount => 'Employee Count';

  @override
  String get employeeTypes => 'Employee Types';

  @override
  String get employeeTypeName => 'Employee Type Name';

  @override
  String get permissionCount => 'Permission Count';

  @override
  String get addEmployeeType => 'Add Employee Type';

  @override
  String get userpermissionCanEditEmployeeTypes => 'Can Edit Employee Types';

  @override
  String get userpermissionCanEditEmployeeTypesDescription => 'Allows user to edit employee type details.';

  @override
  String get employeeTypeNotFound => 'This employee type was not found.';

  @override
  String get editEmployeeType => 'Edit Employee Type';

  @override
  String get permissions => 'Permissions';

  @override
  String get description => 'Description';

  @override
  String get permission => 'Permission';

  @override
  String get notEditable => 'This record cannot be edited.';

  @override
  String get employeeTypeNameRequired => 'The Employee Type Name is required.';

  @override
  String get copyFromEmployeeType => 'Copy From Existing Employee Type';

  @override
  String get addPermissionToEmployeeType => 'Add Permission To Employee Type';

  @override
  String get areYouSureRemovePermissionFromEmployeeType => 'Are you sure you want to remove this permission from this employee type?';

  @override
  String get choosePermission => 'Choose Permission';

  @override
  String get permissionAlreadyInEmployeeType => 'This permission is already in this employee type.';

  @override
  String get permissionNotFound => 'The Permission was not found.';

  @override
  String get permissionNotFoundInEmployeeType => 'This permission was not found in this employee type.';

  @override
  String get remove => 'Remove';

  @override
  String get userpermissionCanPunchIn => 'Can Punch In';

  @override
  String get userpermissionCanPunchInDescription => 'Allows user punch in or out.';

  @override
  String get userpermissionCanEditPunchCards => 'Can Edit Punch Cards';

  @override
  String get userpermissionCanEditPunchCardsDescription => 'Allows user to edit other user\'s punch card details.';

  @override
  String get userpermissionCanViewPunchCards => 'Can View PunchCards';

  @override
  String get userpermissionCanViewPunchCardsDescription => 'Allows user to view other user\'s punch card details.';

  @override
  String get punchcardNotFound => 'This punch card was not found.';

  @override
  String get geoFenceWarning => 'WARNING: You exited the location area without Punching Out.';

  @override
  String get geoFenceEntered => 'Geo Fence Entered';

  @override
  String get geoFenceEnteredDescription => 'User entered geo fence area.';

  @override
  String get geoFenceExited => 'Geo Fence Exited';

  @override
  String get geoFenceExitedDescription => 'User exited geo fence area.';

  @override
  String get geoLocationDisabled => 'Geo Location Disabled';

  @override
  String get geoLocationDisabledDescription => 'Device geo location disabled.';

  @override
  String get geoLocationEnabled => 'Geo Location Enabled';

  @override
  String get geoLocationEnabledDescription => 'Device geo location enabled.';

  @override
  String get geoLocationUpdate => 'Geo Location Update';

  @override
  String get geoLocationUpdateDescription => 'Update to user geo location.';

  @override
  String get oldPasswordRequired => 'The Old Password is required.';

  @override
  String get oldPasswordIncorrect => 'The old password is incorrect.';

  @override
  String get updatePassword => 'Update Password';

  @override
  String get newPassword => 'New Password';

  @override
  String get oldPassword => 'Old Password';

  @override
  String get contactTypes => 'Contact Types';

  @override
  String get contactTypeActions => 'Contact Type Actions';

  @override
  String get contactTypeName => 'Contact Type Name';

  @override
  String get contactTypeNameRequired => 'The Contact Type Name is required.';

  @override
  String get contactTypeNotFound => 'This contact type was not found.';

  @override
  String get userpermissionCanEditContactTypes => 'Can Edit Contact Types';

  @override
  String get userpermissionCanEditContactTypesDescription => 'Allows user to edit contact type details.';

  @override
  String get userpermissionCanViewContactTypes => 'Can View Contact Types';

  @override
  String get userpermissionCanViewContactTypesDescription => 'Allows user to view contact type details.';

  @override
  String get permissionAlreadyInContactType => 'This permission is already in this contact type.';

  @override
  String get permissionNotFoundInContactType => 'This permission was not found in this contact type.';

  @override
  String get addContactType => 'Add Contact Type';

  @override
  String get viewContactTypes => 'View Contact Types';

  @override
  String get copyFromContactType => 'Copy From Contact Type';

  @override
  String get contactCount => 'Contact Count';

  @override
  String get addPermissionToContactType => 'Add Permission To Contact Type';

  @override
  String get areYouSureRemovePermissionFromContactType => 'Are you sure you want to remove this permission from this contact type?';

  @override
  String get editContactType => 'Edit Contact Type';

  @override
  String get daily => 'Daily';

  @override
  String get day => 'Day';

  @override
  String get firstOccurrence => 'First Occurrence';

  @override
  String get fourthOccurrence => 'Fourth Occurrence';

  @override
  String get friday => 'Friday';

  @override
  String get hour => 'Hour';

  @override
  String get lastOccurrence => 'Last Occurrence';

  @override
  String get minute => 'Minute';

  @override
  String get monday => 'Monday';

  @override
  String get month => 'Month';

  @override
  String get saturday => 'Saturday';

  @override
  String get second => 'Second';

  @override
  String get secondOccurrence => 'Second Occurrence';

  @override
  String get sunday => 'Sunday';

  @override
  String get thirdOccurrence => 'Third Occurrence';

  @override
  String get thursday => 'Thursday';

  @override
  String get tuesday => 'Tuesday';

  @override
  String get wednesday => 'Wednesday';

  @override
  String get week => 'Week';

  @override
  String get weekday => 'Weekday';

  @override
  String get weekend => 'Weekend';

  @override
  String get addSchedule => 'Add Schedule';

  @override
  String get employeeSchedule => 'Employee Schedule';

  @override
  String get locationSchedule => 'Location Schedule';

  @override
  String get schedules => 'Schedules';

  @override
  String get userpermissionCanEditSchedules => 'Can Edit Schedules';

  @override
  String get userpermissionCanViewSchedules => 'Can View Schedules';

  @override
  String get viewSchedules => 'View Schedules';

  @override
  String get duration => 'Duration';

  @override
  String get every => 'Every';

  @override
  String get frequency => 'Frequency';

  @override
  String get nextScheduled => 'Next Scheduled';

  @override
  String get on => 'On';

  @override
  String get startDate => 'Start Date';

  @override
  String get startTime => 'Start Time';

  @override
  String get viewSchedule => 'View Schedule';

  @override
  String get repeatEvery => 'Repeat every';

  @override
  String get showPlannedSchedule => 'Show Planned Schedule';

  @override
  String get scheduleNotFound => 'This schedule was not found.';

  @override
  String get timeZone => 'Time Zone';

  @override
  String get addRepeatingSchedule => 'Add Repeating Schedule';

  @override
  String get repeatingSchedules => 'Repeating Schedules';

  @override
  String get viewRepeatingSchedule => 'View Repeating Schedule';

  @override
  String get viewRepeatingSchedules => 'View Repeating Schedules';

  @override
  String get editSchedule => 'Edit Schedule';

  @override
  String get endLocationTimeZone => 'End (Location time zone)';

  @override
  String get from => 'From';

  @override
  String get invalidDateRange => 'Invalid Date Range';

  @override
  String get invalidDuration => 'Invalid Duration';

  @override
  String get invalidRecurringRange => 'Invalid Recurring Range';

  @override
  String get invalidTimeOfDay => 'Invalid Time Of Day';

  @override
  String get originalLocationTimeZone => 'Original (Location time zone)';

  @override
  String get scheduledLocalTimeZone => 'Scheduled (Current time zone)';

  @override
  String get scheduledLocationTimeZone => 'Scheduled (Location time zone)';

  @override
  String get selectAtLeastOneDayOfTheWeek => 'Select at least one day of the week.';

  @override
  String get startLocationTimeZone => 'Start (Location time zone)';

  @override
  String get startTimeLocationTimeZone => 'Start Time (Location time zone)';

  @override
  String get to => 'To';

  @override
  String get schedulegeneratorInvalidRecurringDailyfreqOccurseveryfreqId => 'This schedule has an invalid Recurring_DailyFreq_OccursEveryFreq_ID.';

  @override
  String get schedulegeneratorInvalidRecurringFrequencyId => 'This schedule has an invalid Recurring_Frequency_ID.';

  @override
  String get schedulegeneratorIsNotRecurringNoOnetimeOccurson => 'This schedule is marked as not recurring and did not have a OneTime_OccursOn value.';

  @override
  String get schedulegeneratorIsRecurringNoRecurringDailyfreqOccursonce => 'This schedule is marked as recurring and did not have a Recurring_DailyFreq_OccursOnce value.';

  @override
  String get schedulegeneratorNotRecurringDailyfreqOccursonceNoRecurringDailyfreqStartOrEnd => 'This schedule is marked as not recurring daily once and did not have a Recurring_DailyFreq_StartingOn or Recurring_DailyFreq_EndingOn value.';

  @override
  String get schedulegeneratorRecurringFrequencyDayNoRecurringFrequencyEverynfreq => 'This schedule is marked as recurring daily and did not have a Recurring_Frequency_EveryNFreq value.';

  @override
  String get schedulegeneratorRecurringFrequencyMonthNoRecurringFrequencyMonthlyThedayoftheweekId => 'This schedule is marked as recurring monthly and did not have a Recurring_Frequency_Monthly_TheDayOfTheWeek_ID value.';

  @override
  String get schedulegeneratorRecurringFrequencyMonthNoRecurringFrequencyMonthlyTheweekId => 'This schedule is marked as recurring monthly and did not have a Recurring_Frequency_Monthly_TheWeek_ID value.';

  @override
  String get userpermissionCanSendChatMessage => 'Can Send Chat Messages';

  @override
  String get userpermissionCanSendChatMessageDescription => 'Allows user to send chat messages.';

  @override
  String get userpermissionCanViewChatMessage => 'Can View Chat Messages';

  @override
  String get userpermissionCanViewChatMessageDescription => 'Allows user to view chats with other users.';

  @override
  String get message => 'Message';

  @override
  String get recentChatMessages => 'Recent Chat Messages';

  @override
  String get send => 'Send';

  @override
  String get sendChatMessageTo => 'Send chat message to';

  @override
  String get punchCards => 'Punch Cards';

  @override
  String get viewPunchCards => 'View Punch Cards';

  @override
  String get clockedInBy => 'Punched In By';

  @override
  String get clockedOutBy => 'Punched Out By';

  @override
  String get editPunchCard => 'Edit Punch Card';

  @override
  String get punchCardId => 'Punch Card ID';

  @override
  String get punchCardNotFound => 'This punch card was not found.';

  @override
  String get clockOutBeforeClockedIn => 'Punch out must be after punch in.';

  @override
  String get clockedIn => 'Punched In';

  @override
  String get christmasHoliday => 'Christmas Holiday';

  @override
  String get fourthOfJulyHoliday => 'Fourth Of July Holiday';

  @override
  String get newYearsHoliday => 'New Years Holiday';

  @override
  String get thanksgivingHoliday => 'Thanksgiving Holiday';

  @override
  String get userpermissionCanEditNotes => 'Can Edit Notes';

  @override
  String get userpermissionCanViewNotes => 'Can View Notes';

  @override
  String get noteRequired => 'Text is required in a note.';

  @override
  String get addNote => 'Add Note';

  @override
  String get editNote => 'Edit Note';

  @override
  String get noteNotFound => 'This note was not found.';

  @override
  String get changedBy => 'Changed By';

  @override
  String get clear => 'Clear';

  @override
  String get chooseEmployee => 'Choose Employee';

  @override
  String get employeeOrLocationRequired => 'Either an employee or location must be selected.';

  @override
  String get userpermissionCanEditChecklists => 'Can Edit Checklists';

  @override
  String get userpermissionCanEditChecklistsDescription => 'Allows user to edit checklists.';

  @override
  String get userpermissionCanViewChecklists => 'Can View Checklists';

  @override
  String get userpermissionCanViewChecklistsDescription => 'Allows user to view checklists.';

  @override
  String get addChecklist => 'Add Checklist';

  @override
  String get checklist => 'Checklist';

  @override
  String get checklists => 'Checklists';

  @override
  String get addChecklistItem => 'Add Item';

  @override
  String get checklistNotFound => 'This checklist was not found.';

  @override
  String get editChecklist => 'Edit Checklist';

  @override
  String get editChecklistItem => 'Edit Item';

  @override
  String get items => 'Items';

  @override
  String get close => 'Close';

  @override
  String get endLocalTimeZone => 'End (Local time zone)';

  @override
  String get startLocalTimeZone => 'Start (Local time zone)';

  @override
  String get createdBy => 'Created By';

  @override
  String get note => 'Note';

  @override
  String get error => 'Error';

  @override
  String get calendar => 'Calendar';

  @override
  String get alerts => 'Alerts';

  @override
  String get allPunchCards => 'All Punch Cards';

  @override
  String get currentPayPeriod => 'Current Pay Period';

  @override
  String get customTimePeriod => 'Custom Time Period';

  @override
  String get hours => 'Hours';

  @override
  String get recentPunchCards => 'Recent Punch Cards';

  @override
  String get addPunchCard => 'Add Punch Card';

  @override
  String get timePeriod => 'Time Period';

  @override
  String get endDateRequired => 'The end date is required.';

  @override
  String get employeeRequired => 'The employee is required.';

  @override
  String get startDateRequired => 'The start date is required.';

  @override
  String get startDateCannotBeAfterEndDate => 'The start date cannot be after the end date.';

  @override
  String get locationRequired => 'The location is required.';

  @override
  String get selectLocationLoadSchedules => 'Select a location to load the schedules.';

  @override
  String get takeNote => 'Take Note';

  @override
  String get endTime => 'End Time';

  @override
  String get endTimeLocationTimeZone => 'End Time (Location time zone)';

  @override
  String get recurring => 'Recurring';

  @override
  String get endsBy => 'Ends By';

  @override
  String get invalidEndDate => 'Invalid End Date';

  @override
  String get noEndDate => 'No End Date';

  @override
  String get nextLocation => 'Next Location';

  @override
  String get travelTime => 'Travel Time';

  @override
  String get localTime => 'Local Time';

  @override
  String get scheduledTime => 'Scheduled Time';

  @override
  String get scheduleDetailsFor => 'Schedule Details For';

  @override
  String get days => 'Days';

  @override
  String get weeks => 'Weeks';

  @override
  String get months => 'Months';

  @override
  String get dayOfMonth => 'Day Of Month';

  @override
  String get onSpecificDay => 'On Specific Day';

  @override
  String get onSpecificWeek => 'On Specific Week';

  @override
  String get editRepeatingSchedule => 'Edit Repeating Schedule';

  @override
  String get april => 'April';

  @override
  String get august => 'August';

  @override
  String get december => 'December';

  @override
  String get february => 'February';

  @override
  String get january => 'January';

  @override
  String get july => 'July';

  @override
  String get june => 'June';

  @override
  String get march => 'March';

  @override
  String get may => 'May';

  @override
  String get november => 'November';

  @override
  String get october => 'October';

  @override
  String get september => 'September';

  @override
  String get onSpecificMonth => 'On Specific Month';

  @override
  String get endTimeRequired => 'The End Time is required.';

  @override
  String get noDescription => 'No Description';

  @override
  String get scheduletemplateInvalidRecurringFrequencyId => 'This schedule has an invalid Day, Week, Month selection.';

  @override
  String get scheduletemplateRecurringFrequencyDayNoRecurringFrequencyEverynfreq => 'This schedule is marked as recurring daily and did not have a repeat value.';

  @override
  String get scheduletemplateRecurringFrequencyMonthNoRecurringFrequencyEverynfreq => 'This schedule is marked as recurring monthly and did not have a repeat value.';

  @override
  String get scheduletemplateRecurringFrequencyWeekNoRecurringFrequencyEverynfreq => 'This schedule is marked as recurring weekly and did not have a repeat value.';

  @override
  String get scheduletemplateRecurringFrequencyWeekNoWeekdaySelected => 'This schedule is marked as recurring weekly and no weekdays were selected.';

  @override
  String get startTimeCannotBeAfterEndTime => 'The start time cannot be after the end time.';

  @override
  String get startTimeRequired => 'The Start Time is required.';

  @override
  String get scheduletemplateRecurringFrequencyInvalidMonthDay => 'This schedule is marked as recurring monthly on a specific month day and the day is not valid.';

  @override
  String get scheduletemplateRecurringFrequencyNoMonthlyOnMonth => 'This schedule is marked as recurring monthly on a specific month and did not have a month value.';

  @override
  String get scheduletemplateRecurringFrequencyNoMonthlyWeek => 'This schedule is marked as recurring monthly on a specific month week and did not have a weekly ocurrance value.';

  @override
  String get scheduletemplateRecurringFrequencyNoMonthlyWeekday => 'This schedule is marked as recurring monthly on a specific month week and did not have a week day value.';

  @override
  String get scheduletemplateRecurringFrequencyNoMonthDay => 'This schedule is marked as recurring monthly on a specific month day and did not have a day value.';

  @override
  String get areYouSureEditSubscriptionTemplateWillRemoveFutureSchedules => 'Are you sure you want to edit this repeating schedule? This will remove any future existing schedules that are already created.';

  @override
  String get addNewSchedule => 'Add New Schedule';

  @override
  String get editThisSchedule => 'Edit This Schedule';

  @override
  String get viewThisSchedule => 'View This Schedule';

  @override
  String get of0 => 'Of';

  @override
  String get onThe => 'On The';

  @override
  String get nd => 'nd\n    As in the 2nd of the month.';

  @override
  String get rd => 'rd\n    As in the 3rd of the month.';

  @override
  String get st => 'st\n    As in the 1st of the month.';

  @override
  String get th => 'th\n    As in the 4th of the month.';

  @override
  String get year => 'Year';

  @override
  String get years => 'Years';

  @override
  String get in0 => 'In';

  @override
  String get admin => 'Admin';

  @override
  String get adminDescription => 'Administrative';

  @override
  String get locationDescription => 'Unscheduled Job';

  @override
  String get scheduleDescription => 'Scheduled Job';

  @override
  String get travelTimeDescription => 'Travel Time';

  @override
  String get invalidJobType => 'Invalid Job Type';

  @override
  String get jobType => 'Job Type';

  @override
  String get userpermissionCanViewJobTypes => 'Can View Job Types';

  @override
  String get userpermissionCanViewJobTypeDescription => 'Allows user to view job types.';

  @override
  String get addJobType => 'Add Job Type';

  @override
  String get editJobType => 'Edit Job Type';

  @override
  String get jobTypes => 'Job Types';

  @override
  String get jobTypeActions => 'Job Type Actions';

  @override
  String get jobTypeNotFound => 'This job type was not found.';

  @override
  String get userpermissionCanEditJobTypes => 'Can Edit Job Types';

  @override
  String get userpermissionCanEditJobTypesDescription => 'Allows user to edit job type details.';

  @override
  String get viewJobTypes => 'View Job Types';

  @override
  String get scheduletemplateCouldNotBeCreated => 'A new schedule template could not be created. Please try again.';

  @override
  String get userpermissionCanViewAlerts => 'Can View Alerts';

  @override
  String get userpermissionCanViewReports => 'Can View Reports';

  @override
  String get userpermissionCanViewReportsDescription => 'Allows user to view reports.';

  @override
  String get reportUserSessions => 'User Sessions';

  @override
  String get reportUserSessionsDescription => 'Shows the login/out times and forced out reasons for user sessions.';

  @override
  String get administration => 'Administration';

  @override
  String get reports => 'Reports';

  @override
  String get reportName => 'Report Name';

  @override
  String get run => 'Run';

  @override
  String get reportNotFound => 'This report was not found.';

  @override
  String get configSettingAlertClockInLimitMinMvc => 'Alert Punch In Limit (Mins)';

  @override
  String get configSettingAlertClockInLimitMinMvcDescription => 'Amount of time in minutes after a schedule starts when an alert is generated if employee fails to punch in.';

  @override
  String get report => 'Report';

  @override
  String get setReportParameters => 'Set Report Parameters';

  @override
  String get fillInOptionsClickRunReport => 'Fill in the options, then click Run Report.';

  @override
  String get adobeAcrobat => 'Adobe Acrobat';

  @override
  String get commaSeparatedValues => 'Comma Separated Values';

  @override
  String get downloadReport => 'Download Report';

  @override
  String get microsoftExcel => 'Microsoft Excel';

  @override
  String get microsoftWord => 'Microsoft Word';

  @override
  String get reportFormat => 'Report Format';

  @override
  String get runReport => 'Run Report';

  @override
  String get clickHereDownloadPdf => 'Click here to download the PDF';

  @override
  String get clickHereInstallAdobeReader => 'or click here to install Adobe Reader.';

  @override
  String get noPdfSupportMessage => 'It appears you don\'t have Adobe Reader or PDF support in this web browser.';

  @override
  String get goFullscreen => 'Go Fullscreen';

  @override
  String get fillInAboveOptionsClickRunReport => 'Fill in the options above, then click Run Report.';

  @override
  String get reportNotReadyToRun => 'Your report is not ready to run.';

  @override
  String get canAddSchedulesDescription => 'Allows user to add schedules.';

  @override
  String get canEditNotesDescription => 'Allows user to edit notes.';

  @override
  String get canEditSchedulesDescription => 'Allows user to edit schedules.';

  @override
  String get canViewAlertsDescription => 'Allows user to view alerts.';

  @override
  String get canViewJobTypesDescription => 'Allows user to view job types.';

  @override
  String get canViewSchedulesDescription => 'Allows user to view schedules.';

  @override
  String get userpermissionCanViewNotesDescription => 'Allows user to view notes.';

  @override
  String get subordinates => 'Subordinates';

  @override
  String get supervisors => 'Supervisors';

  @override
  String get configSettingDatabaseBackupFolder => 'Database Backup Folder';

  @override
  String get configSettingDatabaseBackupFolderDescription => 'Location where the SQL database backups are stored.';

  @override
  String get cleaning => 'Cleaning';

  @override
  String get cleaningDescription => 'Cleaning related jobs';

  @override
  String get notListed => 'Not Listed';

  @override
  String get notListedDescription => 'Unlisted jobs';

  @override
  String get scheduled => 'Scheduled';

  @override
  String get scheduledDescription => 'Scheduled jobs';

  @override
  String get status => 'Status';

  @override
  String get unscheduled => 'Unscheduled';

  @override
  String get unscheduledDescription => 'Unscheduled jobs';

  @override
  String get mustCompleteChallenge => 'You must complete the challenge!';

  @override
  String get lastMonth => 'Last Month';

  @override
  String get lastUpdated => 'Last Updated';

  @override
  String get lastWeek => 'Last Week';

  @override
  String get thisMonth => 'This Month';

  @override
  String get thisWeek => 'This Week';

  @override
  String get today => 'Today';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get noLocation => 'No Location';

  @override
  String get clock => 'Clock';

  @override
  String get punchedOut => 'Punched Out';

  @override
  String get punchInAt => 'Punch In at:';

  @override
  String get date => 'Date';

  @override
  String get profile => 'Profile';

  @override
  String get punches => 'Punches';

  @override
  String get calendarDetail => 'Calendar Detail';

  @override
  String get punchInOut => 'Punch In/Out';

  @override
  String get punchesDetail => 'Punches Detail';

  @override
  String get confirmPunchOut => 'Are you sure you want to punch out?';

  @override
  String get writeNow => 'Write Now...';

  @override
  String get configSettingRegistrationEmailExpirationMinutes => 'Registration Email Expiration (Mins)';

  @override
  String get configSettingRegistrationEmailExpirationMinutesDescription => 'Amount of time in minutes before an unresponded registration email will expire.';

  @override
  String get registrationConfirmation => 'Registration Confirmation';

  @override
  String get confirm => 'Confirm';

  @override
  String get registrationKeyRequired => 'The Registration Key is required.';

  @override
  String get registrationKey => 'Registration Key';

  @override
  String get registrationRequestExpired => 'This registration request has expired and will need to be resent.';

  @override
  String get registrationRequestNotFound => 'This registration request could not be found. You will need to resubmit your registration request.';

  @override
  String get resendRegistrationRequest => 'Resend Registration Request';

  @override
  String get viewPunchCardsByDay => 'Punches By Day';

  @override
  String get configSettingRegistrationConfirmationUrl => 'Registration Confirmation URL';

  @override
  String get configSettingRegistrationConfirmationUrlDescription => 'The URL for confirming a new registration of an organization.';

  @override
  String get configSettingRegistrationUrl => 'Registration URL';

  @override
  String get configSettingRegistrationUrlDescription => 'The URL for registering a new organization.';

  @override
  String get configSettingRegistrationEmail => 'Registration Email';

  @override
  String get configSettingRegistrationEmailDescription => 'The email template that gets sent when registering an organization.';

  @override
  String get configSettingAdministrationUrl => 'Administration Site URL';

  @override
  String get configSettingAdministrationUrlDescription => 'The URL for the administration site.';

  @override
  String get configSettingAndroidMobileDeviceUrl => 'Android Mobile App URL';

  @override
  String get configSettingAndroidMobileDeviceUrlDescription => 'The URL for the android mobile application.';

  @override
  String get configSettingFirebaseMessagingConfiguration => 'Firebase Messaging Configuration';

  @override
  String get configSettingFirebaseMessagingConfigurationDescription => 'The configuration information for connecting to he Firebase service.';

  @override
  String get configSettingRegistrationThankyouEmail => 'Registration Thank You Email';

  @override
  String get configSettingRegistrationThankyouEmailDescription => 'The email template that gets sent after you uccessfully register an organization.';

  @override
  String get configSettingSmtpConfiguration => 'SMTP Configuration';

  @override
  String get configSettingSmtpConfigurationDescription => 'The configuration information for sending emails via SMTP.';

  @override
  String get created => 'Created';

  @override
  String get deleted => 'Deleted';

  @override
  String get earlyPunchIn => 'Early Punch In';

  @override
  String get earlyPunchInDescription => 'Employee punched in early.';

  @override
  String get earlyPunchOut => 'Early Punch Out';

  @override
  String get earlyPunchOutDescription => 'Employee punched out early.';

  @override
  String get latePunchIn => 'Late Punch In';

  @override
  String get latePunchInDescription => 'Employee punched in late.';

  @override
  String get latePunchOut => 'Late Punch Out';

  @override
  String get latePunchOutDescription => 'Employee punched out late.';

  @override
  String get noPunchIn => 'No Punch In';

  @override
  String get noPunchInDescription => 'Employee did not punch in.';

  @override
  String get outsideGeofence => 'Breached Geofence';

  @override
  String get outsideGeofenceDescription => 'Employee is clocked in to a location, but is outside the location\'s geofence.';

  @override
  String get pushNotification => 'Push Notification';

  @override
  String get queued => 'Queued';

  @override
  String get sent => 'Sent';

  @override
  String get silentNotification => 'Silent Notification';

  @override
  String get selectAValue => 'Select a value';

  @override
  String get userpermissionCanViewEmployeePayRates => 'Can View Employee Pay Rates';

  @override
  String get userpermissionCanViewEmployeePayRatesDescription => 'Allows user to view employee pay rates.';

  @override
  String get payRate => 'Pay Rate';

  @override
  String get userpermissionCanEditEmployeePayRates => 'Can Edit Employee Pay Rates';

  @override
  String get userpermissionCanEditEmployeePayRatesDescription => 'Allows user to edit employee pay rates.';

  @override
  String get payRateMustBeGreaterThanZero => 'The Pay Rate must be greater than zero.';

  @override
  String get hourly => 'Hourly';

  @override
  String get weekly => 'Weekly';

  @override
  String get yearly => 'Yearly';

  @override
  String get configSettingFirstDayOfWeek => 'First Day Of The Week';

  @override
  String get configSettingFirstDayOfWeekDescription => 'The first day of the week for your organization.';

  @override
  String get configSettingOvertimeHours => 'Overtime Hours';

  @override
  String get configSettingOvertimeHoursDescription => 'The amount of hours before ovetime pay is applied.';

  @override
  String get timeZoneRequired => 'The Time Zone is required.';

  @override
  String get reportEmployeesHoursWorked => 'Employees Hours Worked';

  @override
  String get reportEmployeesHoursWorkedDescription => 'Shows the hours worked for employees over a given week.';

  @override
  String get reportLocationsHoursWorked => 'Locations Hours Worked';

  @override
  String get reportLocationsHoursWorkedDescription => 'Shows the hours worked at locations over a given week.';

  @override
  String get languageEnUs => 'English (United States)';

  @override
  String get languageEsUs => 'Spanish (United States)';

  @override
  String get language => 'Language';

  @override
  String get languageRequired => 'The Language is required.';

  @override
  String get userpermissionCanEditPermissions => 'Can Edit Permissions';

  @override
  String get userpermissionCanEditPermissionsDescription => 'Allows user to edit employee and contact permissions.';

  @override
  String get userpermissionCanEditReports => 'Can Edit Reports';

  @override
  String get userpermissionCanEditReportsDescription => 'Allows user to activate or deactivate reports.';

  @override
  String get addPhoto => 'Add Photo';

  @override
  String get addTemplate => 'Add Template';

  @override
  String get agenda => 'Agenda';

  @override
  String get busy => 'Busy';

  @override
  String get createNewInspection => 'Create New Inspection';

  @override
  String get editInspection => 'Edit Inspection';

  @override
  String get editInspectionItem => 'Edit Inspection Item';

  @override
  String get inspections => 'Inspections';

  @override
  String get inspectionTemplates => 'Inspection Templates';

  @override
  String get addInspectionTemplates => 'Add Inspection Templates';

  @override
  String get editInspectionTemplates => 'Edit Inspection Templates';

  @override
  String get newChat => 'New Chat';

  @override
  String get newInspection => 'New Inspection';

  @override
  String get viewInspections => 'View Inspections';

  @override
  String get addInspections => 'Add Inspections';

  @override
  String get editInspections => 'Edit Inspections';

  @override
  String get viewInspectionTemplates => 'View Inspection Templates';

  @override
  String get noPunchCardsThisWeek => 'No Punch Cards This Week';

  @override
  String get save => 'Save';

  @override
  String get saveConfirmation => 'Are you sure you want to save?';

  @override
  String get sync => 'Sync';

  @override
  String get takePhoto => 'Take Photo';

  @override
  String get userpermissionCanEditInspections => 'Can Edit Inspections';

  @override
  String get userpermissionCanEditInspectionsDescription => 'Allows user to create and edit inspections and inspection templates.';

  @override
  String get userpermissionCanViewInspections => 'Can View Inspections';

  @override
  String get userpermissionCanViewInspectionsDescription => 'Allows user to view inspections and inspection templates.';

  @override
  String get forgotLogin => 'Forgot Login or Password';

  @override
  String get emailRequired => 'The Email Address is required.';

  @override
  String get invalidEmail => 'The Email Address must be a valid email format.';

  @override
  String get count => 'Count';

  @override
  String get name => 'Name';

  @override
  String get payrollId => 'Payroll Id';

  @override
  String get downloadPayroll => 'Download Payroll';

  @override
  String get hidePassword => 'Hide Password';

  @override
  String get showPassword => 'Show Password';

  @override
  String get liveStatus => 'Live Status';

  @override
  String get accountInformation => 'Account Information';

  @override
  String get personalInformation => 'Personal Information';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get additionalAddressInformation => 'Additional Address Information';

  @override
  String get phoneRequired => 'Phone required.';

  @override
  String get invalidPhone => 'Invalid phone number.';

  @override
  String get address1Required => 'Address 1 is required.';

  @override
  String get invalidAddress => 'Invalid address.';

  @override
  String get cityRequired => 'City is required.';

  @override
  String get stateRequired => 'State is required.';

  @override
  String get zipRequired => 'Zip code is required.';
}
