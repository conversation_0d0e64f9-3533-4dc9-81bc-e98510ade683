import 'package:flutter/foundation.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/widgets.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../base_data.dart';
import '../../misc/json_conversion.dart';

part 'alert.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
@HiveType(typeId: 1)
class Alert extends BaseData {
  static const earlyPunchInId = 'ed3d03ed-edee-409a-8db8-030a183934e0';
  static const latePunchInId = 'c6f68f26-d161-41b5-a6b8-9dda683bf904';
  static const earlyPunchOutId = '22c5f8a2-c660-411f-aa91-12a7df664c1f';
  static const latePunchOutId = '0a9cb952-fdb5-4ca1-ad2f-fccfb13037af';
  static const outsideGeofenceId = '2d8ea258-9199-4375-81fc-61414128abd1';
  static const reconnectedGeofenceId = '883ebcc9-81b2-4769-acfa-7f3a0e9beeb1';
  static const noPunchInId = '3e668a44-6995-4dbf-bb20-593d7e2fea57';
  static const punchOutDuringBreach = '61548bb1-762a-49bc-931c-7406591fbb02';
  static const activeBreach = '3b728af4-d3c6-47d2-820c-2b20adbf3fb2';
  static const appTerminationDetected = '80bf39b9-1161-4ce0-bf51-2fe8f5db75ef';

  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  @JsonKey(fromJson: idFromJson)
  String alertTypeId;

  @HiveField(102)
  @JsonKey(fromJson: nullableIdFromJson)
  String? punchCardId;

  @HiveField(103)
  @JsonKey(fromJson: nullableIdFromJson)
  String? scheduleId;

  @HiveField(104)
  @JsonKey(fromJson: nullableIdFromJson)
  String? locationId;

  @HiveField(105)
  @JsonKey(toJson: dateTimeToJson, fromJson: dateTimeFromJson)
  DateTime alertOn;

  @HiveField(106)
  @JsonKey(fromJson: nullableIdFromJson)
  String? geoLocationId;

  @HiveField(107)
  @JsonKey(fromJson: idFromJson)
  String userId;

  Alert({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.alertTypeId,
    this.punchCardId,
    this.scheduleId,
    this.locationId,
    required this.alertOn,
    this.geoLocationId,
    required this.userId,
  });

  String name(BuildContext context) {
    switch (alertTypeId) {
      case earlyPunchInId:
        return AppLocalizations.of(context)!.earlyPunchIn;
      case latePunchInId:
        return AppLocalizations.of(context)!.latePunchIn;
      case earlyPunchOutId:
        return AppLocalizations.of(context)!.earlyPunchOut;
      case latePunchOutId:
        return AppLocalizations.of(context)!.latePunchOut;
      case outsideGeofenceId:
        return AppLocalizations.of(context)!.outsideGeofence;
      case reconnectedGeofenceId:
        return 'Re-Entered Geofence';        
      case noPunchInId:
        return AppLocalizations.of(context)!.noPunchIn;
      case punchOutDuringBreach:
        return 'Punched Out';        
      case activeBreach:
        return 'Active Breach';
      case appTerminationDetected:
        return 'App Termination Detected';              
      default:
        return '';
    }
  }

  factory Alert.fromJson(Map<String, dynamic> json) {
    try {
      return _$AlertFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }
  Map<String, dynamic> toJson() => _$AlertToJson(this);
}
