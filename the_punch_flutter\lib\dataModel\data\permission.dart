import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../misc/json_conversion.dart';

import '../base_data.dart';

part 'permission.g.dart';

@HiveType(typeId: 16)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class Permission extends BaseData {
  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  String key;

  @HiveField(102)
  @Json<PERSON>ey(name: 'Key_Translated')
  String name;

  @HiveField(103)
  @<PERSON>son<PERSON>ey(name: 'Description_Translated')
  String description;

  Permission({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.key,
    required this.description,
    required this.name,
  });

  factory Permission.fromJson(Map<String, dynamic> json) {
    try {
      return _$PermissionFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }
  Map<String, dynamic> toJson() => _$PermissionToJson(this);

  static blank() {}
}
