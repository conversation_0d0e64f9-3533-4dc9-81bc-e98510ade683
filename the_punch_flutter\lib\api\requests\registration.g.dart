// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'registration.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AddressInfo _$AddressInfoFromJson(Map<String, dynamic> json) => AddressInfo(
      address1: json['Address1'] as String,
      address2: json['Address2'] as String,
      city: json['City'] as String,
      state: json['State'] as String,
      zip: json['Zip'] as String,
    );

Map<String, dynamic> _$AddressInfoToJson(AddressInfo instance) =>
    <String, dynamic>{
      'Address1': instance.address1,
      'Address2': instance.address2,
      'City': instance.city,
      'State': instance.state,
      'Zip': instance.zip,
    };

Registration _$RegistrationFromJson(Map<String, dynamic> json) => Registration(
      emailAddress: json['EmailAddress'] as String,
      organizationName: json['OrganizationName'] as String,
      username: json['Username'] as String,
      firstName: json['FirstName'] as String,
      lastName: json['LastName'] as String,
      phone: json['Phone'] as String,
      addressInfo:
          AddressInfo.fromJson(json['AddressInfo'] as Map<String, dynamic>),
      timeZone: json['TimeZone'] as String,
      languageKey: json['LanguageKey'] as String,
      password: json['Password'] as String,
      confirmPassword: json['ConfirmPassword'] as String,
    );

Map<String, dynamic> _$RegistrationToJson(Registration instance) =>
    <String, dynamic>{
      'EmailAddress': instance.emailAddress,
      'OrganizationName': instance.organizationName,
      'Username': instance.username,
      'FirstName': instance.firstName,
      'LastName': instance.lastName,
      'Phone': instance.phone,
      'AddressInfo': instance.addressInfo,
      'TimeZone': instance.timeZone,
      'LanguageKey': instance.languageKey,
      'Password': instance.password,
      'ConfirmPassword': instance.confirmPassword,
    };

RegistrationRequest _$RegistrationRequestFromJson(Map<String, dynamic> json) =>
    RegistrationRequest(
      registration:
          Registration.fromJson(json['Registration'] as Map<String, dynamic>),
      captchaResponse: json['CaptchaResponse'] as String,
    );

Map<String, dynamic> _$RegistrationRequestToJson(
        RegistrationRequest instance) =>
    <String, dynamic>{
      'Registration': instance.registration,
      'CaptchaResponse': instance.captchaResponse,
    };

CompleteRegistrationRequest _$CompleteRegistrationRequestFromJson(
        Map<String, dynamic> json) =>
    CompleteRegistrationRequest(
      registrationKey: json['RegistrationKey'] as String,
      captchaResponse: json['CaptchaResponse'] as String,
      timeZone: json['TimeZone'] as String,
    );

Map<String, dynamic> _$CompleteRegistrationRequestToJson(
        CompleteRegistrationRequest instance) =>
    <String, dynamic>{
      'RegistrationKey': instance.registrationKey,
      'CaptchaResponse': instance.captchaResponse,
      'TimeZone': instance.timeZone,
    };

CompleteRegistrationResponse _$CompleteRegistrationResponseFromJson(
        Map<String, dynamic> json) =>
    CompleteRegistrationResponse(
      organizationId: json['organizationID'] as String? ?? '',
      username: json['Username'] as String? ?? '',
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$CompleteRegistrationResponseToJson(
    CompleteRegistrationResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['organizationID'] = instance.organizationId;
  val['Username'] = instance.username;
  return val;
}
