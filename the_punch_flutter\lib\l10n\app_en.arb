{"locationNotesTitle": "Location Notes", "punchStatus": "Punch Status", "address1": "Address 1", "address2": "Address 2", "addressInformation": "Address Information", "administrator": "Administrator", "administratorDescription": "Controls everything in the system.", "cancel": "Cancel", "city": "City", "configSettingUserSessionExpirationMinsMvc": "User Session Expiration Admin Site (Mins)", "configSettingUserSessionExpirationMinsMvcDescription": "Amount of time in minutes before inactive user sessions will expire on the admin site.", "clockIn": "Punch In", "clockOut": "Punch Out", "punch": "Punch", "inW": "In", "outW": "Out", "confirmPassword": "Confirm Password", "confirmPasswordNotMatchPassword": "The Confirm Password does not match the entered password.", "confirmPasswordRequired": "The Confirm Password is required.", "contact": "Contact", "contactTypeDescription": "Default permission template for a contact.", "contactInformation": "Contact Information", "country": "Country", "emailAddressAlreadyRegistered": "This email address is already registered.", "firstName": "First Name", "firstNameRequired": "The First Name is required.", "forcedOut": "Forced Out", "forgotPassword": "Forgot Password", "internalError": "There was an internal server error. Please try again.", "invalidRequest": "The request sent was invalid.", "lastName": "Last Name", "lastNameRequired": "The Last Name is required.", "loggedOut": "Logged Out", "sandbox": "Sandbox", "login": "<PERSON><PERSON>", "logout": "Logout", "mainEmailAddress": "Main Email Address", "mainEmailAddressMustBeValidEmailFormat": "The Main Email Address must be a valid email format.", "mainEmailAddressRequired": "The Main Email Address is required.", "messages": "Messages", "networkUnavailable": "Network Unavailable", "ok": "OK", "punchedIn": "Punched In", "organizationId": "Organization Identifier", "organizationIdNotFound": "The Organization Identifier was not found.", "organizationIdRequired": "The Organization Identifier is required.", "organizationName": "Organization Name", "organizationNameRequired": "The Organization Name is required.", "password": "Password", "passwordNotConformRules": "The Password does not conform to the password rules.", "passwordRequired": "The Password is required.", "phone": "Phone", "register": "Register", "registerOrganization": "Register Organization", "schedule": "Schedule", "sessionForcedOutReason": "Another session has been started from IP address , if you feel this is in error, please sign in again and change your password.", "state": "State", "sysop": "<PERSON><PERSON><PERSON>", "sysopDescription": "Controls everything in the system, and can see extended developer details.", "timedOut": "Timed Out", "punchCard": "Punch Card", "timeClock": "Time Clock", "username": "Username", "usernameOrPasswordIncorrect": "The user name or password is incorrect.", "usernameRequired": "The Username is required.", "userMarkedInactive": "This user is marked as inactive.", "punchedThisWeek": "Total Time Punched", "punchedToday": "Punched Today", "zip": "Zip", "deviceIdRequired": "The Device Identifier is required.", "invalidHash": "The hash value sent in was invalid.", "noResultsFound": "There were no results found.", "pleaseWait": "Please Wait", "sessionNotFound": "The session was not found.", "rotatedOut": "Rotated Out", "sessionForcedOut": "This session has been forced out.", "sessionLoggedOut": "This session has been logged out of.", "sessionTimedOut": "This session has timed out.", "userNotFoundOrInactive": "This user was not found or is inactive.", "sessionRotatedOut": "The session was rotated out.", "configSettingUserSessionExpirationMinsApi": "User Session Expiration Handheld Devices (Mins)", "configSettingUserSessionExpirationMinsApiDescription": "Amount of time in minutes before inactive user sessions will expire on the handheld devices.", "home": "Home", "locations": "Locations", "allLocations": "All Locations", "changeLocation": "Change Location", "action": "Action", "activate": "Activate", "activateAreYouSure": "Are you sure you want to activate this record?", "activateQuestion": "Activate?", "active": "Active", "activeType": "Active Type", "address": "Address", "all": "All", "deactivate": "Deactivate", "deactivateAreYouSure": "Are you sure you want to deactivate this record?", "deactivateQuestion": "Deactivate?", "edit": "Edit", "inactive": "Inactive", "locationName": "Location Name", "locationNotFound": "This location was not found.", "no": "No", "noPermission": "You do not have permission to access this feature.", "search": "Search", "successExclamation": "Success!", "view": "View", "yes": "Yes", "abbreviation": "Abbreviation", "emailAddress": "Email Address", "location": "Location", "addLocation": "Add Location", "locationNameRequired": "The Location Name is required.", "submit": "Submit", "viewLocations": "View Locations", "abbreviationAlreadyExists": "This abbreviation already exists.", "abbreviationRequired": "The Abbreviation is required.", "editLocation": "Edit Location", "userpermissionCanEditLocations": "Can Edit Locations", "userpermissionCanEditLocationsDescription": "Allows user to edit location details.", "userpermissionCanViewLocations": "Can View Locations", "userpermissionCanViewLocationsDescription": "Allows user to view location details.", "usernameCannotContainSpaces": "The Username cannot contain spaces.", "chat": "Cha<PERSON>", "chatDescription": "Chat messages", "email": "Email", "emailDescription": "Email messages", "actions": "Actions", "addEmployee": "Add Employee", "employees": "Employees", "employeeName": "Employee Name", "employeeType": "Employee Type", "userpermissionCanEditEmployees": "Can Edit Employees", "userpermissionCanEditEmployeesDescription": "Allows user to edit employee details.", "userpermissionCanViewEmployees": "Can View Employees", "userpermissionCanViewEmployeesDescription": "Allows user to view employee details.", "viewEmployees": "View Employees", "editEmployee": "Edit Employee", "employee": "Employee", "employeeId": "Employee ID", "employeeNotFound": "This employee was not found.", "employeeTypeRequired": "The Employee Type is required.", "usernameAlreadyRegistered": "This username is already registered.", "userpermissionCanEditContacts": "Can Edit Contacts", "userpermissionCanEditContactsDescription": "Allows user to edit contact details.", "userpermissionCanViewContacts": "Can View Contacts", "userpermissionCanViewContactsDescription": "Allows user to view contact details.", "addContact": "Add Contact", "contacts": "Contacts", "contactName": "Contact Name", "viewContacts": "View Contacts", "contactId": "Contact ID", "contactLocationRequired": "The location is required for this contact.", "contactNotFound": "This contact was not found.", "editContact": "Edit Contact", "chooseLocation": "Choose Location", "select": "Select", "employeeTypeDescription": "Default permission template for an employee.", "contactType": "Contact Type", "contactTypeRequired": "The Contact Type is required.", "chooseContact": "<PERSON>ose <PERSON>", "addContactToLocation": "Add Contact To Location", "addEmployeeToLocation": "Add Employee To Location", "contactAlreadyInLocation": "This contact is already in this location.", "areYouSureQuestion": "Are You Sure?", "areYouSureRemoveContactFromLocation": "Are you sure you want to remove this contact from this location?", "contactNotFoundInLocation": "This contact was not found in this location.", "removeContactFromLocation": "Remove Contact From Location", "notes": "Manager Notes", "employeeTypeActions": "Employee Type Actions", "userpermissionCanViewEmployeeTypes": "Can View Employee Types", "userpermissionCanViewEmployeeTypesDescription": "Allows user to view employee type details.", "viewEmployeeTypes": "View Employee Types", "employeeCount": "Employee Count", "employeeTypes": "Employee Types", "employeeTypeName": "Employee Type Name", "permissionCount": "Permission Count", "addEmployeeType": "Add Employee Type", "userpermissionCanEditEmployeeTypes": "Can Edit Employee Types", "userpermissionCanEditEmployeeTypesDescription": "Allows user to edit employee type details.", "employeeTypeNotFound": "This employee type was not found.", "editEmployeeType": "Edit Employee Type", "permissions": "Permissions", "description": "Description", "permission": "Permission", "notEditable": "This record cannot be edited.", "employeeTypeNameRequired": "The Employee Type Name is required.", "copyFromEmployeeType": "Copy From Existing Employee Type", "addPermissionToEmployeeType": "Add Permission To Employee Type", "areYouSureRemovePermissionFromEmployeeType": "Are you sure you want to remove this permission from this employee type?", "choosePermission": "Choose Permission", "permissionAlreadyInEmployeeType": "This permission is already in this employee type.", "permissionNotFound": "The Permission was not found.", "permissionNotFoundInEmployeeType": "This permission was not found in this employee type.", "remove": "Remove", "userpermissionCanPunchIn": "Can Punch In", "userpermissionCanPunchInDescription": "Allows user punch in or out.", "userpermissionCanEditPunchCards": "Can Edit Punch Cards", "userpermissionCanEditPunchCardsDescription": "Allows user to edit other user's punch card details.", "userpermissionCanViewPunchCards": "Can View PunchCards", "userpermissionCanViewPunchCardsDescription": "Allows user to view other user's punch card details.", "punchcardNotFound": "This punch card was not found.", "geoFenceWarning": "WARNING: You exited the location area without Punching Out.", "geoFenceEntered": "Geo Fence Entered", "geoFenceEnteredDescription": "User entered geo fence area.", "geoFenceExited": "Geo Fence Exited", "geoFenceExitedDescription": "User exited geo fence area.", "geoLocationDisabled": "Geo Location Disabled", "geoLocationDisabledDescription": "Device geo location disabled.", "geoLocationEnabled": "Geo Location Enabled", "geoLocationEnabledDescription": "Device geo location enabled.", "geoLocationUpdate": "Geo Location Update", "geoLocationUpdateDescription": "Update to user geo location.", "oldPasswordRequired": "The Old Password is required.", "oldPasswordIncorrect": "The old password is incorrect.", "updatePassword": "Update Password", "newPassword": "New Password", "oldPassword": "Old Password", "contactTypes": "Contact Types", "contactTypeActions": "Contact Type Actions", "contactTypeName": "Contact Type Name", "contactTypeNameRequired": "The Contact Type Name is required.", "contactTypeNotFound": "This contact type was not found.", "userpermissionCanEditContactTypes": "Can Edit Contact Types", "userpermissionCanEditContactTypesDescription": "Allows user to edit contact type details.", "userpermissionCanViewContactTypes": "Can View Contact Types", "userpermissionCanViewContactTypesDescription": "Allows user to view contact type details.", "permissionAlreadyInContactType": "This permission is already in this contact type.", "permissionNotFoundInContactType": "This permission was not found in this contact type.", "addContactType": "Add Contact Type", "viewContactTypes": "View Contact Types", "copyFromContactType": "Copy From Contact Type", "contactCount": "Contact Count", "addPermissionToContactType": "Add Permission To Contact Type", "areYouSureRemovePermissionFromContactType": "Are you sure you want to remove this permission from this contact type?", "editContactType": "Edit Contact Type", "daily": "Daily", "day": "Day", "firstOccurrence": "First Occurrence", "fourthOccurrence": "Fourth Occurrence", "friday": "Friday", "hour": "Hour", "lastOccurrence": "Last Occurrence", "minute": "Minute", "monday": "Monday", "month": "Month", "saturday": "Saturday", "second": "Second", "secondOccurrence": "Second Occurrence", "sunday": "Sunday", "thirdOccurrence": "Third Occurrence", "thursday": "Thursday", "tuesday": "Tuesday", "wednesday": "Wednesday", "week": "Week", "weekday": "Weekday", "weekend": "Weekend", "addSchedule": "Add Schedule", "employeeSchedule": "Employee Schedule", "locationSchedule": "Location Schedule", "schedules": "Schedules", "userpermissionCanEditSchedules": "Can Edit Schedules", "userpermissionCanViewSchedules": "Can View Schedules", "viewSchedules": "View Schedules", "duration": "Duration", "every": "Every", "frequency": "Frequency", "nextScheduled": "Next Scheduled", "on": "On", "startDate": "Start Date", "startTime": "Start Time", "viewSchedule": "View Schedule", "repeatEvery": "Repeat every", "showPlannedSchedule": "Show Planned Schedule", "scheduleNotFound": "This schedule was not found.", "timeZone": "Time Zone", "addRepeatingSchedule": "Add Repeating Schedule", "repeatingSchedules": "Repeating Schedules", "viewRepeatingSchedule": "View Repeating Schedule", "viewRepeatingSchedules": "View Repeating Schedules", "editSchedule": "Edit Schedule", "endLocationTimeZone": "End (Location time zone)", "from": "From", "invalidDateRange": "Invalid Date Range", "invalidDuration": "Invalid Duration", "invalidRecurringRange": "Invalid Recurring Range", "invalidTimeOfDay": "Invalid Time Of Day", "originalLocationTimeZone": "Original (Location time zone)", "scheduledLocalTimeZone": "Scheduled (Current time zone)", "scheduledLocationTimeZone": "Scheduled (Location time zone)", "selectAtLeastOneDayOfTheWeek": "Select at least one day of the week.", "startLocationTimeZone": "Start (Location time zone)", "startTimeLocationTimeZone": "Start Time (Location time zone)", "to": "To", "schedulegeneratorInvalidRecurringDailyfreqOccurseveryfreqId": "This schedule has an invalid Recurring_DailyFreq_OccursEveryFreq_ID.", "schedulegeneratorInvalidRecurringFrequencyId": "This schedule has an invalid Recurring_Frequency_ID.", "schedulegeneratorIsNotRecurringNoOnetimeOccurson": "This schedule is marked as not recurring and did not have a OneTime_OccursOn value.", "schedulegeneratorIsRecurringNoRecurringDailyfreqOccursonce": "This schedule is marked as recurring and did not have a Recurring_DailyFreq_OccursOnce value.", "schedulegeneratorNotRecurringDailyfreqOccursonceNoRecurringDailyfreqStartOrEnd": "This schedule is marked as not recurring daily once and did not have a Recurring_DailyFreq_StartingOn or Recurring_DailyFreq_EndingOn value.", "schedulegeneratorRecurringFrequencyDayNoRecurringFrequencyEverynfreq": "This schedule is marked as recurring daily and did not have a Recurring_Frequency_EveryNFreq value.", "schedulegeneratorRecurringFrequencyMonthNoRecurringFrequencyMonthlyThedayoftheweekId": "This schedule is marked as recurring monthly and did not have a Recurring_Frequency_Monthly_TheDayOfTheWeek_ID value.", "schedulegeneratorRecurringFrequencyMonthNoRecurringFrequencyMonthlyTheweekId": "This schedule is marked as recurring monthly and did not have a Recurring_Frequency_Monthly_TheWeek_ID value.", "userpermissionCanSendChatMessage": "Can Send Chat Messages", "userpermissionCanSendChatMessageDescription": "Allows user to send chat messages.", "userpermissionCanViewChatMessage": "Can View Chat Messages", "userpermissionCanViewChatMessageDescription": "Allows user to view chats with other users.", "message": "Message", "recentChatMessages": "Recent Chat Messages", "send": "Send", "sendChatMessageTo": "Send chat message to", "punchCards": "Punch Cards", "viewPunchCards": "View Punch Cards", "clockedInBy": "Punched In By", "clockedOutBy": "Punched Out By", "editPunchCard": "Edit Punch Card", "punchCardId": "Punch Card ID", "punchCardNotFound": "This punch card was not found.", "clockOutBeforeClockedIn": "Punch out must be after punch in.", "clockedIn": "Punched In", "christmasHoliday": "Christmas Holiday", "fourthOfJulyHoliday": "Fourth Of July Holiday", "newYearsHoliday": "New Years Holiday", "thanksgivingHoliday": "Thanksgiving Holiday", "userpermissionCanEditNotes": "Can Edit Notes", "userpermissionCanViewNotes": "Can View Notes", "noteRequired": "Text is required in a note.", "addNote": "Add Note", "editNote": "Edit Note", "noteNotFound": "This note was not found.", "changedBy": "Changed By", "clear": "Clear", "chooseEmployee": "<PERSON><PERSON>loyee", "employeeOrLocationRequired": "Either an employee or location must be selected.", "userpermissionCanEditChecklists": "Can Edit Checklists", "userpermissionCanEditChecklistsDescription": "Allows user to edit checklists.", "userpermissionCanViewChecklists": "Can View Checklists", "userpermissionCanViewChecklistsDescription": "Allows user to view checklists.", "addChecklist": "Add Checklist", "checklist": "Checklist", "checklists": "Checklists", "addChecklistItem": "Add Item", "checklistNotFound": "This checklist was not found.", "editChecklist": "Edit Checklist", "editChecklistItem": "<PERSON>em", "items": "Items", "close": "Close", "endLocalTimeZone": "End (Local time zone)", "startLocalTimeZone": "Start (Local time zone)", "createdBy": "Created By", "note": "Note", "error": "Error", "calendar": "Calendar", "alerts": "<PERSON><PERSON><PERSON>", "allPunchCards": "All Punch Cards", "currentPayPeriod": "Current Pay Period", "customTimePeriod": "Custom Time Period", "hours": "Hours", "recentPunchCards": "Recent Punch Cards", "addPunchCard": "Add Punch Card", "timePeriod": "Time Period", "endDateRequired": "The end date is required.", "employeeRequired": "The employee is required.", "startDateRequired": "The start date is required.", "startDateCannotBeAfterEndDate": "The start date cannot be after the end date.", "locationRequired": "The location is required.", "selectLocationLoadSchedules": "Select a location to load the schedules.", "takeNote": "Take Note", "endTime": "End Time", "endTimeLocationTimeZone": "End Time (Location time zone)", "recurring": "Recurring", "endsBy": "Ends By", "invalidEndDate": "Invalid End Date", "noEndDate": "No End Date", "nextLocation": "Next Location", "travelTime": "Travel Time", "localTime": "Local Time", "scheduledTime": "Scheduled Time", "scheduleDetailsFor": "Schedule Details For", "days": "Days", "weeks": "Weeks", "months": "Months", "dayOfMonth": "Day Of Month", "onSpecificDay": "On Specific Day", "onSpecificWeek": "On Specific Week", "editRepeatingSchedule": "Edit Repeating Schedule", "april": "April", "august": "August", "december": "December", "february": "February", "january": "January", "july": "July", "june": "June", "march": "March", "may": "May", "november": "November", "october": "October", "september": "September", "onSpecificMonth": "On Specific Month", "endTimeRequired": "The End Time is required.", "noDescription": "No Description", "scheduletemplateInvalidRecurringFrequencyId": "This schedule has an invalid Day, Week, Month selection.", "scheduletemplateRecurringFrequencyDayNoRecurringFrequencyEverynfreq": "This schedule is marked as recurring daily and did not have a repeat value.", "scheduletemplateRecurringFrequencyMonthNoRecurringFrequencyEverynfreq": "This schedule is marked as recurring monthly and did not have a repeat value.", "scheduletemplateRecurringFrequencyWeekNoRecurringFrequencyEverynfreq": "This schedule is marked as recurring weekly and did not have a repeat value.", "scheduletemplateRecurringFrequencyWeekNoWeekdaySelected": "This schedule is marked as recurring weekly and no weekdays were selected.", "startTimeCannotBeAfterEndTime": "The start time cannot be after the end time.", "startTimeRequired": "The Start Time is required.", "scheduletemplateRecurringFrequencyInvalidMonthDay": "This schedule is marked as recurring monthly on a specific month day and the day is not valid.", "scheduletemplateRecurringFrequencyNoMonthlyOnMonth": "This schedule is marked as recurring monthly on a specific month and did not have a month value.", "scheduletemplateRecurringFrequencyNoMonthlyWeek": "This schedule is marked as recurring monthly on a specific month week and did not have a weekly ocurrance value.", "scheduletemplateRecurringFrequencyNoMonthlyWeekday": "This schedule is marked as recurring monthly on a specific month week and did not have a week day value.", "scheduletemplateRecurringFrequencyNoMonthDay": "This schedule is marked as recurring monthly on a specific month day and did not have a day value.", "areYouSureEditSubscriptionTemplateWillRemoveFutureSchedules": "Are you sure you want to edit this repeating schedule? This will remove any future existing schedules that are already created.", "addNewSchedule": "Add New Schedule", "editThisSchedule": "Edit This Schedule", "viewThisSchedule": "View This Schedule", "of0": "Of", "onThe": "On The", "nd": "nd\n    As in the 2nd of the month.", "rd": "rd\n    As in the 3rd of the month.", "st": "st\n    As in the 1st of the month.", "th": "th\n    As in the 4th of the month.", "year": "Year", "years": "Years", "in0": "In", "admin": "Admin", "adminDescription": "Administrative", "locationDescription": "Unscheduled Job", "scheduleDescription": "Scheduled Job", "travelTimeDescription": "Travel Time", "invalidJobType": "Invalid Job Type", "jobType": "Job Type", "userpermissionCanViewJobTypes": "Can View Job Types", "userpermissionCanViewJobTypeDescription": "Allows user to view job types.", "addJobType": "Add Job Type", "editJobType": "Edit Job Type", "jobTypes": "Job Types", "jobTypeActions": "Job Type Actions", "jobTypeNotFound": "This job type was not found.", "userpermissionCanEditJobTypes": "Can Edit Job Types", "userpermissionCanEditJobTypesDescription": "Allows user to edit job type details.", "viewJobTypes": "View Job Types", "scheduletemplateCouldNotBeCreated": "A new schedule template could not be created. Please try again.", "userpermissionCanViewAlerts": "Can View Alerts", "userpermissionCanViewReports": "Can View Reports", "userpermissionCanViewReportsDescription": "Allows user to view reports.", "reportUserSessions": "User Sessions", "reportUserSessionsDescription": "Shows the login/out times and forced out reasons for user sessions.", "administration": "Administration", "reports": "Reports", "reportName": "Report Name", "run": "Run", "reportNotFound": "This report was not found.", "configSettingAlertClockInLimitMinMvc": "<PERSON><PERSON> Limit (Mins)", "configSettingAlertClockInLimitMinMvcDescription": "Amount of time in minutes after a schedule starts when an alert is generated if employee fails to punch in.", "report": "Report", "setReportParameters": "Set Report Parameters", "fillInOptionsClickRunReport": "Fill in the options, then click Run Report.", "adobeAcrobat": "Adobe Acrobat", "commaSeparatedValues": "Comma Separated Values", "downloadReport": "Download Report", "microsoftExcel": "Microsoft Excel", "microsoftWord": "Microsoft Word", "reportFormat": "Report Format", "runReport": "Run Report", "clickHereDownloadPdf": "Click here to download the PDF", "clickHereInstallAdobeReader": "or click here to install Adobe Reader.", "noPdfSupportMessage": "It appears you don't have Adobe Reader or PDF support in this web browser.", "goFullscreen": "Go Fullscreen", "fillInAboveOptionsClickRunReport": "Fill in the options above, then click Run Report.", "reportNotReadyToRun": "Your report is not ready to run.", "canAddSchedulesDescription": "Allows user to add schedules.", "canEditNotesDescription": "Allows user to edit notes.", "canEditSchedulesDescription": "Allows user to edit schedules.", "canViewAlertsDescription": "Allows user to view alerts.", "canViewJobTypesDescription": "Allows user to view job types.", "canViewSchedulesDescription": "Allows user to view schedules.", "userpermissionCanViewNotesDescription": "Allows user to view notes.", "subordinates": "Subordinates", "supervisors": "Supervisors", "configSettingDatabaseBackupFolder": "Database Backup Folder", "configSettingDatabaseBackupFolderDescription": "Location where the SQL database backups are stored.", "cleaning": "Cleaning", "cleaningDescription": "Cleaning related jobs", "notListed": "Not Listed", "notListedDescription": "Unlisted jobs", "scheduled": "Scheduled", "scheduledDescription": "Scheduled jobs", "status": "Status", "unscheduled": "Unscheduled", "unscheduledDescription": "Unscheduled jobs", "mustCompleteChallenge": "You must complete the challenge!", "lastMonth": "Last Month", "lastUpdated": "Last Updated", "lastWeek": "Last Week", "thisMonth": "This Month", "thisWeek": "This Week", "today": "Today", "yesterday": "Yesterday", "noLocation": "No Location", "clock": "Clock", "punchedOut": "Punched Out", "punchInAt": "Punch In at:", "date": "Date", "profile": "Profile", "punches": "Punches", "calendarDetail": "Calendar Detail", "punchInOut": "Punch In/Out", "punchesDetail": "<PERSON><PERSON>", "confirmPunchOut": "Are you sure you want to punch out?", "writeNow": "Write Now...", "configSettingRegistrationEmailExpirationMinutes": "Registration Email Expiration (Mins)", "configSettingRegistrationEmailExpirationMinutesDescription": "Amount of time in minutes before an unresponded registration email will expire.", "registrationConfirmation": "Registration Confirmation", "confirm": "Confirm", "registrationKeyRequired": "The Registration Key is required.", "registrationKey": "Registration Key", "registrationRequestExpired": "This registration request has expired and will need to be resent.", "registrationRequestNotFound": "This registration request could not be found. You will need to resubmit your registration request.", "resendRegistrationRequest": "Resend Registration Request", "viewPunchCardsByDay": "Punches By Day", "configSettingRegistrationConfirmationUrl": "Registration Confirmation URL", "configSettingRegistrationConfirmationUrlDescription": "The URL for confirming a new registration of an organization.", "configSettingRegistrationUrl": "Registration URL", "configSettingRegistrationUrlDescription": "The URL for registering a new organization.", "configSettingRegistrationEmail": "Registration Email", "configSettingRegistrationEmailDescription": "The email template that gets sent when registering an organization.", "configSettingAdministrationUrl": "Administration Site URL", "configSettingAdministrationUrlDescription": "The URL for the administration site.", "configSettingAndroidMobileDeviceUrl": "Android Mobile App URL", "configSettingAndroidMobileDeviceUrlDescription": "The URL for the android mobile application.", "configSettingFirebaseMessagingConfiguration": "Firebase Messaging Configuration", "configSettingFirebaseMessagingConfigurationDescription": "The configuration information for connecting to he Firebase service.", "configSettingRegistrationThankyouEmail": "Registration Thank You Email", "configSettingRegistrationThankyouEmailDescription": "The email template that gets sent after you uccessfully register an organization.", "configSettingSmtpConfiguration": "SMTP Configuration", "configSettingSmtpConfigurationDescription": "The configuration information for sending emails via SMTP.", "created": "Created", "deleted": "Deleted", "earlyPunchIn": "Early Punch In", "earlyPunchInDescription": "Employee punched in early.", "earlyPunchOut": "Early Punch Out", "earlyPunchOutDescription": "Employee punched out early.", "latePunchIn": "Late Punch In", "latePunchInDescription": "Employee punched in late.", "latePunchOut": "Late Punch Out", "latePunchOutDescription": "Employee punched out late.", "noPunchIn": "No Punch In", "noPunchInDescription": "Employee did not punch in.", "outsideGeofence": "Breached Geofence", "outsideGeofenceDescription": "Employee is clocked in to a location, but is outside the location's geofence.", "pushNotification": "Push Notification", "queued": "Queued", "sent": "<PERSON><PERSON>", "silentNotification": "Silent Notification", "selectAValue": "Select a value", "userpermissionCanViewEmployeePayRates": "Can View Employee Pay Rates", "userpermissionCanViewEmployeePayRatesDescription": "Allows user to view employee pay rates.", "payRate": "Pay Rate", "userpermissionCanEditEmployeePayRates": "Can Edit Employee Pay Rates", "userpermissionCanEditEmployeePayRatesDescription": "Allows user to edit employee pay rates.", "payRateMustBeGreaterThanZero": "The Pay Rate must be greater than zero.", "hourly": "Hourly", "weekly": "Weekly", "yearly": "Yearly", "configSettingFirstDayOfWeek": "First Day Of The Week", "configSettingFirstDayOfWeekDescription": "The first day of the week for your organization.", "configSettingOvertimeHours": "Overtime Hours", "configSettingOvertimeHoursDescription": "The amount of hours before ovetime pay is applied.", "timeZoneRequired": "The Time Zone is required.", "reportEmployeesHoursWorked": "Employees Hours Worked", "reportEmployeesHoursWorkedDescription": "Shows the hours worked for employees over a given week.", "reportLocationsHoursWorked": "Locations Hours Worked", "reportLocationsHoursWorkedDescription": "Shows the hours worked at locations over a given week.", "languageEnUs": "English (United States)", "languageEsUs": "Spanish (United States)", "language": "Language", "languageRequired": "The Language is required.", "userpermissionCanEditPermissions": "Can Edit Permissions", "userpermissionCanEditPermissionsDescription": "Allows user to edit employee and contact permissions.", "userpermissionCanEditReports": "Can Edit Reports", "userpermissionCanEditReportsDescription": "Allows user to activate or deactivate reports.", "addPhoto": "Add Photo", "addTemplate": "Add Template", "agenda": "Agenda", "busy": "Busy", "createNewInspection": "Create New Inspection", "editInspection": "Edit Inspection", "editInspectionItem": "Edit Inspection Item", "inspections": "Inspections", "inspectionTemplates": "Inspection Templates", "addInspectionTemplates": "Add Inspection Templates", "editInspectionTemplates": "Edit Inspection Templates", "newChat": "New Chat", "newInspection": "New Inspection", "viewInspections": "View Inspections", "addInspections": "Add Inspections", "editInspections": "Edit Inspections", "viewInspectionTemplates": "View Inspection Templates", "noPunchCardsThisWeek": "No Punch Cards This Week", "save": "Save", "saveConfirmation": "Are you sure you want to save?", "sync": "Sync", "takePhoto": "Take Photo", "userpermissionCanEditInspections": "Can Edit Inspections", "userpermissionCanEditInspectionsDescription": "Allows user to create and edit inspections and inspection templates.", "userpermissionCanViewInspections": "Can View Inspections", "userpermissionCanViewInspectionsDescription": "Allows user to view inspections and inspection templates.", "forgotLogin": "Forgot Login or Password", "emailRequired": "The Email Address is required.", "invalidEmail": "The Email Address must be a valid email format.", "count": "Count", "name": "Name", "payrollId": "Payroll Id", "downloadPayroll": "Download Payroll", "hidePassword": "Hide Password", "showPassword": "Show Password", "liveStatus": "Live Status", "accountInformation": "Account Information", "personalInformation": "Personal Information", "selectLanguage": "Select Language", "additionalAddressInformation": "Additional Address Information", "phoneRequired": "Phone required.", "invalidPhone": "Invalid phone number.", "address1Required": "Address 1 is required.", "invalidAddress": "Invalid address.", "cityRequired": "City is required.", "stateRequired": "State is required.", "zipRequired": "Zip code is required."}