// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'punch_cards_history.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PunchCardsHistory _$PunchCardsHistoryFromJson(Map<String, dynamic> json) =>
    PunchCardsHistory(
      id: idFromJson(json['Id']),
      punchCardId: idFromJson(json['PunchCardId']),
      userId: nullableIdFromJson(json['UserId']),
      locationId: nullableIdFromJson(json['LocationId']),
      jobTypeId: nullableIdFromJson(json['JobTypeId']),
      clockedIn: nullableDateTimeFromJson(json['ClockedIn']),
      clockedOut: nullableDateTimeFromJson(json['ClockedOut']),
      duration: (json['Duration'] as num?)?.toDouble(),
      changedByUserId: idFromJson(json['ChangedByUserId']),
      changeDate: dateTimeFromJson(json['ChangeDate']),
      isActive: json['IsActive'] as bool? ?? true,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
    );

Map<String, dynamic> _$PunchCardsHistoryToJson(PunchCardsHistory instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['PunchCardId'] = instance.punchCardId;
  writeNotNull('UserId', instance.userId);
  writeNotNull('LocationId', instance.locationId);
  writeNotNull('JobTypeId', instance.jobTypeId);
  writeNotNull('ClockedIn', nullableDateTimeToJson(instance.clockedIn));
  writeNotNull('ClockedOut', nullableDateTimeToJson(instance.clockedOut));
  writeNotNull('Duration', instance.duration);
  val['ChangedByUserId'] = instance.changedByUserId;
  val['ChangeDate'] = dateTimeToJson(instance.changeDate);
  return val;
}
