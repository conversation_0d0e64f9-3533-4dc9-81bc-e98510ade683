import 'package:flutter/material.dart';

class ColorHelper {
  static Color lightThemeWhite() => const Color.fromARGB(255, 240, 240, 240);

  static Color thePunchRed() => const Color.fromARGB(255, 203, 37, 37);

  static Color thePunchBlue() => const Color.fromRGBO(793,174,230, 1);

  static Color thePunchLightBlue() => const Color.fromRGBO(225, 241, 253, 1);

  static Color thePunchGray() => const Color.fromRGBO(102, 102, 102, 1);

  static Color thePunchLightGray() => const Color.fromRGBO(153, 153, 153, 1);

  static Color thePunchGrayBlue() => const Color.fromRGBO(70, 166, 244, .16);

  static Color thePunchDesktopGray() => const Color.fromRGBO(245, 245, 245, 1);

  static Color thePunchDesktopLightGray() =>
      const Color.fromRGBO(233, 233, 233, 1);

  static Color thePunchGreen() => const Color.fromRGBO(228, 249, 232, 1);

  static Color thePunchLightGreen() => const Color.fromRGBO(65, 188, 82, 1);

  static Color thePunchBorderGreen() => const Color.fromRGBO(182, 239, 194, 1);

  static Color thePunchLightRed() => const Color.fromRGBO(251, 231, 231, 1);

  static Color thePunchAccentRed() => const Color.fromRGBO(231, 105, 105, 1);

  static Color thePunchBorderRed() => const Color.fromRGBO(245, 191, 191, 1);
    static Color thePunchPink() => const  Color.fromRGBO(228,76,91,1);
  static Color thePunchLightPink() => const  Color.fromRGBO(250,226,230,1);
  static Color thePunchLightYellow() => const  Color.fromRGBO(246,246,218,1);
  static Color thePunchMustard() => const  Color.fromRGBO(203,203,23,1);
  static Color thePunchLighterGray() => Color.fromRGBO( 209, 209, 209, .4);
  static Color thePunchBlueGray() => Color.fromRGBO(9, 31, 48, 1);
  static Color thePunchDarkBlue() => Color.fromRGBO(17,31,47, 1);
  static Color thePunchAdminForm() => Color.fromRGBO(194,218,236, 1);
  static Color thePunchAdminButtonBlue() => Color.fromRGBO(75,162,231, 1);

}
