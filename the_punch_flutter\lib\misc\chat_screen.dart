import 'package:flutter/material.dart';

class ChatApp extends StatelessWidget {
  const ChatApp({super.key});

  @override
  Widget build(BuildContext context) => const MaterialApp(
      debugShowCheckedModeBanner: false,
      home: ChatScreen(),
    );
}

class ChatScreen extends StatelessWidget {
  const ChatScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final List<Message> messages = [
      Message(text: 'Lorem ipsum dolor sit amet, consectetur', time: '15m', isSent: false),
      Message(text: 'Donec quis pretium nibh. Aenean vitae lectus iaculis, tristique tellus quis, auctor massa.', time: '15m', isSent: true),
      Message(text: 'Amet', time: '15m', isSent: false),
      Message(text: 'Lorem ipsum dolor sit amet, consectetur', time: '15m', isSent: false),
      Message(
        text: 'In iaculis orci dolor, ut luctus turpis vehicula sit amet. Donec quis pretium nibh. Aenean vitae lectus iaculis, tristique tellus quis, auctor massa.',
        time: '15m',
        isSent: true,
      ),
      Message(text: 'Lorem ipsum dolor sit amet, consectetur', time: '15m', isSent: false),
    ];

return Scaffold(
  body: Container(
    decoration: const BoxDecoration(
      // gradient: LinearGradient(
      //   begin: Alignment.topCenter,
      //   end: Alignment.bottomCenter,
      //   colors: [
      //     Color(0xFFF2F2F2), // Light gray at the top
      //     Colors.white,       // White at the bottom
      //   ],
      // ),
    ),
    child: Column(
      children: [
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(12),
            itemCount: messages.length,
            itemBuilder: (context, index) {
              final message = messages[index];
              return ChatBubble(message: message);
            },
          ),
        ),
        const ChatInputField(),
      ],
    ),
  ),
);

  }
}

class Message {
  final String text;
  final String time;
  final bool isSent;

  Message({required this.text, required this.time, required this.isSent});
}

class ChatBubble extends StatelessWidget {
  final Message message;

  const ChatBubble({super.key, required this.message});

  @override
  Widget build(BuildContext context) => Align(
  alignment: message.isSent ? Alignment.centerRight : Alignment.centerLeft,
  child: Column(
    crossAxisAlignment:
        message.isSent ? CrossAxisAlignment.end : CrossAxisAlignment.start,
    children: [
      Row(
        mainAxisAlignment:
            message.isSent ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!message.isSent)
            const CircleAvatar(
              backgroundImage: NetworkImage("https://i.pravatar.cc/150?img=3"),
              radius: 16,
            ),
          if (!message.isSent) const SizedBox(width: 8),
          if (message.isSent) const SizedBox(width: 40),
          if (message.isSent) Text(
          message.time,
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
        ),
          if (message.isSent) const SizedBox(width: 8),
          Flexible(
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 6),
              padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 10),
              decoration: BoxDecoration(
                color: !message.isSent ? Colors.blue[100] : Colors.grey[300],
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                message.text,
                style: const TextStyle(fontSize: 14, color: Colors.black87),
              ),
            ),
          ),
          if (!message.isSent) 
          const SizedBox(width: 8),
          if (!message.isSent) 
          Text(
          message.time,
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
        )
        ],
      ),

    ],
  ),
);

}

class ChatInputField extends StatelessWidget {
  const ChatInputField({super.key});

  @override
  Widget build(BuildContext context) => Container(
  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
  decoration: BoxDecoration(
    color: Colors.grey[200],
    borderRadius: BorderRadius.circular(30),
    border: Border.all(color: Colors.grey[300]!),
  ),
  child: Row(
    children: [
      const Expanded(
        child: TextField(
          decoration: InputDecoration(
            hintText: 'Type a message...',
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
          ),
        ),
      ),
      const SizedBox(width: 8),
      CircleAvatar(
        backgroundColor: Colors.blue,
        radius: 22,
        child: IconButton(
          icon: const Icon(Icons.send, color: Colors.white),
          onPressed: () {},
        ),
      ),
    ],
  ),
);

}
