import 'dart:async';

import 'package:flutter/material.dart';

class TickingDuration extends StatefulWidget {
  final DateTime clockedInTime;
  final ThemeData theme;

  const TickingDuration({
    required this.clockedInTime,
    required this.theme,
  });

  @override
  _TickingDurationState createState() => _TickingDurationState();
}

class _TickingDurationState extends State<TickingDuration> {
  late Duration _elapsed;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _elapsed = DateTime.now().difference(widget.clockedInTime);
    _startTimer();
  }

  @override
  void didUpdateWidget(covariant TickingDuration oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.clockedInTime != widget.clockedInTime) {
      _resetTimer();
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _elapsed = DateTime.now().difference(widget.clockedInTime);
        });
      }
    });
  }

  void _resetTimer() {
    _timer?.cancel();
    setState(() {
      _elapsed = Duration.zero;
    });
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final hours = _elapsed.inHours.toString().padLeft(2, '0');
    final minutes = (_elapsed.inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (_elapsed.inSeconds % 60).toString().padLeft(2, '0');

    return Text('');
  }
}
