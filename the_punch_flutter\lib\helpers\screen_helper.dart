import 'package:flutter/material.dart';

class ScreenHelper {
  static double minTabletWidth = 550;

  static bool isTablet(BuildContext context) =>
      minTabletWidth <= screenWidth(context);

  static double screenHeight(BuildContext context) =>
      MediaQuery.of(context).size.height;

  static double screenWidth(BuildContext context) =>
      MediaQuery.of(context).size.width;

  static double screenWidthPercentage(
          BuildContext context, double percentage) =>
      MediaQuery.of(context).size.width * (percentage / 100);

  static double screenHeightPercentage(
          BuildContext context, double percentage) =>
      MediaQuery.of(context).size.height * (percentage / 100);

  static double screenStatusBarHeight(BuildContext context) =>
      MediaQuery.of(context).padding.top;
}
