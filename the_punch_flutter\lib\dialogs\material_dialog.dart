import 'package:flutter/material.dart';
import '../misc/app_localization.dart';

class MaterialDialog extends StatelessWidget {
  const MaterialDialog({super.key, this.title, this.subTitle, this.constraints, this.expandChild = true, required this.child});

  final String? title;
  final String? subTitle;
  final Widget child;
  final BoxConstraints? constraints;
  final bool expandChild;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline6 = theme.textTheme.titleLarge;
    final body1 = theme.textTheme.bodyLarge;

    return LayoutBuilder(builder: (context, constraints) => Center(
        child: Padding(
          padding: constraints.maxWidth > 1000 ? const EdgeInsets.all(32) : const EdgeInsets.all(8),
          child: ConstrainedBox(
            constraints: this.constraints ?? BoxConstraints(maxWidth: 1300, maxHeight: constraints.maxHeight),
            child: Material(
              color: Colors.transparent,
              child: Card(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (title != null) Text(title!, style: headline6),
                    if (subTitle != null) Text(subTitle!, style: body1),
                    if (expandChild) Expanded(child: child) else child,
                    Align(
                      alignment: Alignment.centerRight,
                      child: Padding(
                        padding: const EdgeInsets.all(8),
                        child: TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: Text(AppLocalization.of(context).close),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ));
  }
}
