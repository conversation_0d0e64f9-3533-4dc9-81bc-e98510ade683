import 'package:flutter/material.dart';

class TableComponent extends StatelessWidget {
  final List<Map<String, dynamic>> tableData;

  TableComponent({
    Key? key,
    required this.tableData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32),
        border: Border.all(
          color: const Color(0xFFEBF6FF),
          width: 1.5,
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns: _buildColumns(),
          rows: _buildRows(),
          headingRowHeight: 56,
          dataRowHeight: 52,
          columnSpacing: 12,
          horizontalMargin: 24,
        ),
      ),
    );

  List<DataColumn> _buildColumns() => [
      DataColumn(label: _buildHeaderCell('Name')),
      DataColumn(label: _buildHeaderCell('Total Alerts')),
      DataColumn(label: _buildHeaderCell('Schedule Alerts')),
      DataColumn(label: _buildHeaderCell('Geofence Alerts')),
      DataColumn(label: _buildHeaderCell('Total Breached Time')),
      DataColumn(label: _buildHeaderCell('Action')),
    ];

  Widget _buildHeaderCell(String text) => Text(
      text,
      style: const TextStyle(
        fontFamily: 'Poppins',
        fontSize: 11,
        fontWeight: FontWeight.w700,
        color: Color(0xFF091F30),
      ),
    );

  List<DataRow> _buildRows() => tableData.map((data) => _buildRow(data)).toList();

  DataRow _buildRow(Map<String, dynamic> data) => DataRow(
      color: MaterialStateProperty.resolveWith<Color?>((Set<MaterialState> states) {
        if (data['isHighlighted']) {
          return const Color(0xFFDAF9E1);
        }
        return null;
      }),
      cells: [
        _buildCell(data['name']),
        _buildCell(data['totalAlerts']),
        _buildCell(data['scheduleAlerts']),
        _buildCell(data['geofenceAlerts']),
        _buildBreachedTimeCell(data['totalBreachedTime']),
        _buildActionCell(),
      ],
    );

  DataCell _buildCell(String text) => DataCell(
      Text(
        text,
        style: const TextStyle(
          fontFamily: 'Poppins',
          fontSize: 11,
          fontWeight: FontWeight.w500,
          color: Color(0xFF091F30),
        ),
      ),
    );

  DataCell _buildBreachedTimeCell(String text) => DataCell(
      Text(
        text,
        style: const TextStyle(
          fontFamily: 'Poppins',
          fontSize: 11,
          fontWeight: FontWeight.w500,
          color: Color(0xFFC35B5B),
        ),
      ),
    );

  DataCell _buildActionCell() => DataCell(
      ElevatedButton(
        onPressed: () {},
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF4BA2E7),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(100),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        ),
        child: const Text(
          'View Alerts',
          style: TextStyle(
            fontFamily: 'Poppins',
            fontSize: 12,
            fontWeight: FontWeight.w700,
            letterSpacing: -0.12,
            color: Colors.white,
          ),
        ),
      ),
    );
}

