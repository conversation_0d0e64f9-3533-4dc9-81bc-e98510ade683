import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../../dataModel/data/inspection_template.dart';
import '../../../dataModel/data/location.dart';
import '../../../dataModel/data_model.dart';
import '../../../dialogs/delete_dialog.dart';
import '../../../helpers/color_helper.dart';
import '../../../helpers/screen_helper.dart';
import '../../../misc/app_localization.dart';
import '../../../misc/my_platform.dart';
import '../../view_model_mixin.dart';
import '../../../widgets/padded_card.dart';
import 'package:collection/collection.dart';

import '../my_scaffold.dart';

class InspectionTemplatesPage extends StatelessWidget {
  late final viewModel = _ViewModel();
  final _scaffoldKey = GlobalKey<ScaffoldState>();

  InspectionTemplatesPage()
      : super(
          key: const ValueKey('InspectionTemplatesPage'),
        );

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider.value(
        value: viewModel,
        builder: (context, child) => MyScaffold(
          key: _scaffoldKey,
          title: AppLocalizations.of(context)!.inspectionTemplates,
          body: _Body(),
        ),
      );
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline5 =
        theme.textTheme.titleSmall?.copyWith(color: ColorHelper.thePunchGray());
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Stack(
          children: [
            
            Padding(
              padding: const EdgeInsets.only(top: 90),
              child: Flex(
                direction: Axis.horizontal,
                children: [
                  Flexible(child: _LocationList()),
                  Flexible(
                    flex: 3,
                    child: Column(
                      children: [
                        if (MyPlatform.isHandheld)
                          Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text('Choose a template:  ', style: headline5),
                              _InspectionTemplateDropDown(),
                            ],
                          ),
                        if (MyPlatform.isDesktop)
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text('Choose a template:  ', style: headline5),
                              _InspectionTemplateDropDown(),
                            ],
                          ),
                        Flexible(
                          child: _InspectionTemplate(),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _LocationList extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final subtitle1 = theme.textTheme.titleMedium
        ?.copyWith(color: theme.colorScheme.secondary);
    final bodyText1 = theme.textTheme.bodyLarge;
    final focusColor = theme.focusColor;

    return Consumer<_ViewModel>(
        builder: (context, viewModel, child) => ListView(
              children: [
                Center(
                    child: Text(AppLocalization.of(context).locations,
                        style: bodyText1)),
                Padding(
                  padding: const EdgeInsets.only(right: 16),
                  child: InkWell(
                    onTap: () => viewModel.locationId = '',
                    child: Container(
                        color: viewModel.locationId.isEmpty ? focusColor : null,
                        child: Padding(
                          padding: const EdgeInsets.all(8),
                          child: Center(
                              child: Text('Common To All Locations',
                                  style: subtitle1,
                                  textAlign: TextAlign.center)),
                        )),
                  ),
                ),
                for (final location in viewModel.locations) ...[
                  const Padding(
                      padding: EdgeInsets.only(left: 8, right: 24),
                      child: Divider()),
                  Padding(
                    padding: const EdgeInsets.only(right: 16),
                    child: InkWell(
                      onTap: () => viewModel.locationId = location.id,
                      child: Container(
                          color: viewModel.locationId == location.id
                              ? focusColor
                              : null,
                          child: Padding(
                            padding: const EdgeInsets.all(8),
                            child: Center(
                                child: Text(location.name,
                                    style: subtitle1,
                                    textAlign: TextAlign.center)),
                          )),
                    ),
                  ),
                ]
              ],
            ));
  }
}

class _InspectionTemplate extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Stack(
        children: [
          _InspectionTemplateAreas(),
          const Align(
            alignment: Alignment.bottomRight,
            child: _InspectionTemplateButtons(),
          )
        ],
      );
}

class _InspectionTemplateDropDown extends StatefulWidget {
  @override
  _InspectionTemplateDropDownState createState() =>
      _InspectionTemplateDropDownState();
}

class _InspectionTemplateDropDownState
    extends State<_InspectionTemplateDropDown> {
  String dropdownValue = '';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline5 = theme.textTheme.bodySmall;

    return Consumer<_ViewModel>(builder: (context, viewModel, child) {
      final inspectionTemplates = viewModel.templates;
      if (inspectionTemplates.isEmpty) return Container();
      if (dropdownValue.isEmpty ||
          !inspectionTemplates.map((e) => e.id).contains(dropdownValue)) {
        dropdownValue = inspectionTemplates.first.id;
      }
      return DropdownButton<String>(
        value: dropdownValue,
        onChanged: (String? newValue) {
          setState(() {
            dropdownValue = newValue!;
            viewModel.templateId = newValue;
          });
        },
        items: inspectionTemplates
            .map((e) => DropdownMenuItem<String>(
                  value: e.id,
                  child: Text(e.name, style: headline5),
                ))
            .toList(),
      );
    });
  }
}

class _InspectionTemplateAreas extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final headline6 = Theme.of(context).textTheme.titleLarge;

    return Consumer<_ViewModel>(builder: (context, viewModel, _) {
      if (viewModel.template == null) {
        if (viewModel.location == null) {
          return Center(
              child: Text(
            'No templates common to all locations.\nTap "Add Template" to create a new template.',
            textAlign: TextAlign.center,
            style: headline6,
          ));
        } else {
          return Center(
              child: Text(
            'No templates for ${viewModel.location!.name}.\nTap "Add Template" to create a new template.',
            textAlign: TextAlign.center,
            style: headline6,
          ));
        }
      }
      final areas = viewModel.areas;
      return ListView(
        shrinkWrap: true,
        children: [
          for (final area in areas)
            PaddedCard(
              elevation: 0,
              color: ColorHelper.thePunchDesktopLightGray(),
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  children: [
                    SizedBox(
                      width: double.maxFinite,
                      child: Text(
                        'Area: ${area.name}',
                        style: headline6,
                      ),
                    ),
                    for (final item in viewModel.getItemsByArea(area.id))
                      SizedBox(
                        width: double.maxFinite,
                        child: Text(
                          item.name,
                          style: headline6?.copyWith(
                              fontSize: ScreenHelper.screenHeightPercentage(
                                  context, 2),
                              fontStyle: FontStyle.italic),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          Container(height: 64),
        ],
      );
    });
  }
}

class _InspectionTemplateButtons extends StatelessWidget {
  const _InspectionTemplateButtons();

  @override
  Widget build(BuildContext context) =>
      Consumer<_ViewModel>(builder: (context, viewModel, _) {
        final hasTemplate = viewModel.template != null;
        // final templateName = viewModel.template?.name ?? '';

        return Wrap(
          alignment: WrapAlignment.end,
          children: [
            Padding(
              padding: const EdgeInsets.all(8),
              child: OutlinedButton.icon(
                style: OutlinedButton.styleFrom(
                    side: BorderSide(
                  color: ColorHelper.thePunchRed(),
                )),
                onPressed: !hasTemplate
                    ? null
                    : () async => context.pushNamed('/inspectionTemplates/edit',
                            queryParameters: {
                              'id': viewModel.templateId,
                              'locationId': viewModel.locationId
                            }),
                icon: const Icon(Icons.edit),
                label: const Text('Edit Inspection Template'),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8),
              child: OutlinedButton.icon(
                style: OutlinedButton.styleFrom(
                    side: BorderSide(
                  color: ColorHelper.thePunchRed(),
                )),
                onPressed: !hasTemplate
                    ? null
                    : () async => await showDialog(
                        context: context,
                        builder: (context) => DeleteDialog(
                                // message: 'Are you sure you want to delete the template $templateName?',
                                delete: () async {
                              await viewModel.delete();
                            })),
                icon: const Icon(Icons.delete),
                label: const Text('Delete Template'),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8),
              child: ElevatedButton.icon(
                onPressed: () async => context.pushNamed('/inspectionTemplates/edit',
                    queryParameters: {
                      'locationId': viewModel.locationId,
                      'anything': '1'
                    }),
                icon: const Icon(Icons.add),
                label: const Text('Add Template'),
              ),
            ),
          ],
        );
      });
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  _ViewModel() {
    addListenables([
      DataModel().inspectionTemplateModel,
      DataModel().inspectionTemplateAreaModel,
      DataModel().inspectionTemplateItemModel,
      DataModel().locationModel,
    ]);
    unawaited(refresh());
  }

  List<Location> locations = [];
  List<InspectionTemplate> _templates = [];
  List<InspectionTemplateArea> _areas = [];
  List<InspectionTemplateItem> _items = [];

  String _locationId = '';
  String get locationId => _locationId;
  set locationId(String value) {
    if (_locationId != value) {
      _locationId = value;
      unawaited(refresh());
    }
  }

  Location? get location => _locationId.isNotEmpty
      ? locations.firstWhere((e) => e.id == _locationId)
      : null;

  String _templateId = '';
  String get templateId => _templateId;
  set templateId(String value) {
    if (_templateId != value) {
      _templateId = value;
      unawaited(refresh());
    }
  }

  InspectionTemplate? get template =>
      _templates.firstWhereOrNull((e) => e.id == _templateId);
  Iterable<InspectionTemplate> get templates => _templates;
  Iterable<InspectionTemplateArea> get areas => _areas;
  Iterable<InspectionTemplateItem> getItemsByArea(String id) =>
      _items.where((e) => e.inspectionTemplateAreaId == id);

  @override
  Future<void> refresh() async {
    locations = (await DataModel().locationModel.active).toList();
    if (_locationId.isNotEmpty && !locations.any((e) => e.id == _locationId)) {
      _locationId = '';
    }
    _templates =
        (await DataModel().inspectionTemplateModel.getByLocationId(_locationId))
            .toList();
    if (!_templates.map((e) => e.id).contains(_templateId)) _templateId = '';
    if (_templateId.isEmpty && _templates.isNotEmpty) {
      _templateId = templates.first.id;
    }
    if (_templateId.isEmpty) {
      _areas = [];
      _items = [];
    } else {
      _areas = (await DataModel()
              .inspectionTemplateAreaModel
              .getActiveByInspectionTemplateId(_templateId))
          .toList();
      _items = (await DataModel()
              .inspectionTemplateItemModel
              .getActiveByInspectionTemplateId(_templateId))
          .toList();
      _areas.sort((a, b) => a.order.compareTo(b.order));
      _items.sort((a, b) => a.order.compareTo(b.order));
    }
    notifyListeners();
  }

  Future<void> delete() async {
    final template = this.template;
    if (template == null) return;
    final areas = _areas;
    final items = _items;

    template.isActive = false;
    for (final e in areas) {
      e.isActive = false;
    }
    for (final e in items) {
      e.isActive = false;
    }

    _templates.removeWhere((e) => e.id == template.id);
    templateId = _templates.firstOrNull?.id ?? '';

    await DataModel().inspectionTemplateModel.saveDirty([template]);
    await (DataModel().inspectionTemplateAreaModel.saveDirty(areas));
    await (DataModel().inspectionTemplateItemModel.saveDirty(items));
  }
}
