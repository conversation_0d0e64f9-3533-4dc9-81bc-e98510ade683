import 'dart:async';
import 'dart:math';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'firebase_options.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:timezone/data/latest_10y.dart' as tz;
import 'package:url_strategy/url_strategy.dart';
import 'misc/my_platform.dart';
import 'services/notification_service.dart';
import 'state/app_state.dart';
import 'state/fcm_model.dart';
import 'state/punch_state.dart';
import 'the_punch_app.dart';
import 'services/location_background_service.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:package_info_plus/package_info_plus.dart';

// Import the router to get the navigator key
import 'navigator_keys.dart'; // Import navigator_keys

// Import your ApiModel and DataModel
import 'api/api_model.dart';
import 'dataModel/data_model.dart';
// Import LoginState
import 'state/login_state.dart';

// A global/static session ID that gets regenerated on each cold start:
String? currentAppSessionId;

// Helper to create a random string for the session ID:
String _generateRandomSessionId([int length = 12]) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  final rand = Random.secure();
  return List.generate(length, (_) => chars[rand.nextInt(chars.length)]).join();
}

final RouteObserver<PageRoute> routeObserver = RouteObserver<PageRoute>();

// Define the AppTerminated variable
bool appTerminated = false;

// FlutterLocalNotificationsPlugin instance
final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

// Background message handler (only works on mobile; won't affect web)
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Initialize Firebase (needed in the background isolate)
  await Firebase.initializeApp();

  // Check if the message is for '/chat'
  if (message.data['targetPage'] == '/chat') {
    await _fetchAndUpdateMessages();
  }
}

Future<void> checkIfTerminatedWhilePunchedIn() async {
  final prefs = await SharedPreferences.getInstance();
  final punchCardSession = prefs.getString('punchCardSessionId');
  final currentSession = prefs.getString('currentAppSessionId');

  final bool userWasPunchedIn = prefs.getBool('isPunchedIn') ?? false;
  if (!userWasPunchedIn) return;

  if (punchCardSession != currentSession) {
    final punchCardId = prefs.getString('punchCardId') ?? '';
    final userId = LoginState.userId;

    try {
      final success = await ApiModel().createAppTerminationAlert(
        punchCardId: punchCardId,
        userId: userId,
        alertOn: DateTime.now().toUtc(), // optional
      );
      if (success) {
        print('AppTermination alert successfully created on server!');
      }
    } catch (e) {
      print('Failed to create appTermincationDetected alert: $e');
    }
  }
}


Future<void> main() async {
  tz.initializeTimeZones();
  WidgetsFlutterBinding.ensureInitialized();

  // Lock orientation to portrait only
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown, // Allows upside-down portrait mode (optional)
  ]);

  // Generate a new session ID on every true cold start
  currentAppSessionId = _generateRandomSessionId();

  final prefs = await SharedPreferences.getInstance();
  prefs.setString('currentAppSessionId', currentAppSessionId!);

  final packageInfo = await PackageInfo.fromPlatform();
  final localVersion = packageInfo.version; // e.g. "1.1.0"

  AppState.appVersion = localVersion;

  await NotificationService.initialize();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  if (MyPlatform.isAndroid || MyPlatform.isIOS) {
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;
    await FCMModel.initialize();
  }

  setPathUrlStrategy();

  final appState = await AppState.initialize();

  if (MyPlatform.isAndroid || MyPlatform.isIOS) {
    await initializeService();
  }

  await LoginState.instance;

  RemoteMessage? initialMessage;
  if (MyPlatform.isAndroid || MyPlatform.isIOS) {
    final FirebaseMessaging messaging = FirebaseMessaging.instance;
    await messaging.requestPermission();
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
    initialMessage = await messaging.getInitialMessage();
  }

  runApp(ThePunchApp(appState: appState));

  WidgetsBinding.instance.addPostFrameCallback((_) async {
    await checkIfTerminatedWhilePunchedIn();
  });  

  if (MyPlatform.isAndroid || MyPlatform.isIOS) {
    if (initialMessage != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _handleNotificationClick(initialMessage!.data['targetPage']);
      });

      if (initialMessage.data['targetPage'] == '/chat') {
        await _fetchAndUpdateMessages();
      }
    }

    FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
      RemoteNotification? notification = message.notification;
      if (notification != null) {
        await NotificationService.showNotification(
          title: notification.title ?? '',
          body: notification.body ?? '',
          payload: message.data['targetPage'],
        );
      }

      if (message.data['targetPage'] == '/chat') {
        await _fetchAndUpdateMessages();
      }
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) async {
      NotificationService.handleNotificationClick(message.data['targetPage']);

      if (message.data['targetPage'] == '/chat') {
        await _fetchAndUpdateMessages();
      }
    });
  }
}


// Notification click handler
Future<void> _handleNotificationClick(String? targetPage) async {
  if (targetPage != null) {
    final context = rootNavigatorKey.currentContext;
    if (context != null) {
      GoRouter.of(context).go(targetPage);
    } else {
      print('Error: rootNavigatorKey.currentContext is null');
    }
  }
}

// Function to fetch and update messages
Future<void> _fetchAndUpdateMessages() async {
  try {
    String? employeeId = LoginState.userId;
    if (employeeId.isEmpty) {
      // If LoginState.userId is empty, try to get it from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      employeeId = prefs.getString('userId') ?? '';
    }

    if (employeeId.isNotEmpty) {
      final fetchResponse = await ApiModel().fetchMessages(employeeId);
      await DataModel().messageModel.updateMessages(fetchResponse.messages);
    } else {
      print('Employee ID is null or empty. Cannot fetch messages.');
    }
  } catch (e) {
    print('Error fetching and updating messages: $e');
  }
}
