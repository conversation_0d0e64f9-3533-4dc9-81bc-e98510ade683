// ignore_for_file: use_build_context_synchronously

import 'dart:async';
// Removed: import 'dart:js'; // This was causing overshadowing of 'context'.

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../../../api/api_model.dart';
import '../../../dataModel/data/location.dart';
import '../../../dataModel/data/schedule_template.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data_model.dart';
import '../../../dialogs/busy_dialog.dart';
import '../../../dialogs/error_dialog.dart';
import '../../../dialogs/web/expanded_employees_dialog.dart';
import '../../../dialogs/web/expanded_locations_dialog.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/app_localization.dart';
import '../../../misc/logging.dart';
import '../../view_model_mixin.dart';
import '../../../widgets/decorated_text_field.dart';
import '../../../misc/extensions.dart';
import '../../../widgets/number_picker.dart';
import '../../../misc/json_conversion.dart';
import '../home/<USER>';
import '../my_scaffold.dart';

class EditRepeatingSchedulePage extends StatelessWidget {
  final String scheduleTemplateId;
  final DateTime? startDate;

  EditRepeatingSchedulePage(Map<String, String> queryParms, {super.key})
      : scheduleTemplateId = queryParms['id'] ?? '',
        startDate = nullableDateTimeFromJson(queryParms['startDate']);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<_ViewModel>(
      create: (_) => _ViewModel(scheduleTemplateId, startDate),
      child: MyScaffold(
        title: AppLocalizations.of(context)!.editRepeatingSchedule,
        body: const _Body(),
      ),
    );
  }
}

class _Body extends StatelessWidget {
  const _Body({super.key});

  @override
  Widget build(BuildContext context) {
    // Check if the viewModel found no template
    return Consumer<_ViewModel>(builder: (context, viewModel, child) {
      if (viewModel.didFindNoTemplate) {
        // Defer navigation until after the current frame
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (context.mounted) {
            context.replace('/schedules');
          }
        });
      }

      return Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1300),
          child: Column(
            children: [
              const _BodyHeader(),
              const _Row1(),
              const _Row2(),
              const _Row3(),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 32),
                child: Divider(),
              ),
              const _Row4(),
              // Additional rows depending on recurring frequency
              Builder(builder: (_) {
                if (viewModel.scheduleTemplate == null) return Container();
                if (viewModel.scheduleTemplate!.recurringFrequencyId == 2) {
                  return const _WeekRow();
                }
                if (viewModel.scheduleTemplate!.recurringFrequencyId == 3) {
                  return const _MonthRow();
                }
                return Container();
              }),
            ],
          ),
        ),
      );
    });
  }
}

class _BodyHeader extends StatefulWidget {
  const _BodyHeader({super.key});

  @override
  State<_BodyHeader> createState() => _BodyHeaderState();
}

class _BodyHeaderState extends State<_BodyHeader> {
  @override
  Widget build(BuildContext context) {
    final boldBodyText2 = Theme.of(context)
        .textTheme
        .bodyMedium
        ?.copyWith(fontWeight: FontWeight.w600);

    return IntrinsicHeight(
      child: Consumer<_ViewModel>(builder: (context, viewModel, child) {
        final text = viewModel.scheduleTemplate
                ?.displayName(context, viewModel.location) ??
            '';
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          child: Stack(
            children: [
              Align(
                alignment: Alignment.centerLeft,
                child: Column(
                  children: [Text(text, style: boldBodyText2)],
                ),
              ),
              Align(
                alignment: Alignment.centerRight,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                      ),
                      onPressed: viewModel.scheduleTemplateId.isEmpty
                          ? null
                          : () async {
                              // Show confirmation & then do the delete
                              await _showDeleteConfirmationDialog(context, viewModel);
                            },
                      child: const Text('Delete'),
                    ),
                    SizedBox(width: 12),
                    ElevatedButton(
                      onPressed: !viewModel.canSave
                          ? null
                          : () async {
                              await _showSaveDialog(context, viewModel);
                            },
                      child: Text(AppLocalizations.of(context)!.submit),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Future<void> _showSaveDialog(BuildContext context, _ViewModel viewModel) {
    return showDialog(
      context: context,
      builder: (_) => BusyDialog(
        future: () async {
          try {
            await viewModel.save();
            if (!mounted) return;
            Navigator.of(context).pop(); // Close BusyDialog
          } on ApiException catch (e, stack) {
            await logApiException(e, stack);
            if (!mounted) return;
            await showDialog(
              context: context,
              builder: (_) => ErrorDialog(errorCode: e.errorCode),
            );
          } on Exception catch (e, stack) {
            await logException(e, stack);
            if (!mounted) return;
            await showDialog(
              context: context,
              builder: (_) => const ErrorDialog(),
            );
          }
        },
      ),
    );
  }

  Future<void> _showDeleteConfirmationDialog(
    BuildContext context,
    _ViewModel viewModel,
  ) {
    return showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: Text(AppLocalizations.of(context)!.confirm),
        content: const Text('Delete Repeating Schedule'),
        actions: [
          TextButton(
            child: Text(AppLocalizations.of(context)!.cancel),
            onPressed: () => Navigator.of(context).pop(),
          ),
          TextButton(
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
            onPressed: () async {
              Navigator.of(context).pop(); // Close confirmation dialog

              // Show BusyDialog while deleting
              await showDialog(
                context: context,
                builder: (dialogContext) => BusyDialog(
                  future: () async {
                    try {
                      await viewModel.deleteSchedule();
                    } on ApiException catch (e, stack) {
                      await logApiException(e, stack);
                      if (!dialogContext.mounted) return;
                      await showDialog(
                        context: dialogContext,
                        builder: (_) => ErrorDialog(errorCode: e.errorCode),
                      );
                    } on Exception catch (e, stack) {
                      await logException(e, stack);
                      if (!dialogContext.mounted) return;
                      await showDialog(
                        context: dialogContext,
                        builder: (_) => const ErrorDialog(),
                      );
                    }
                  },
                ),
              );

              // After BusyDialog, show "Repeating Schedule Removed!"
              if (!mounted) return;
              await showSuccessDialog(context);
            },
          ),
        ],
      ),
    );
  }
}

Future<void> showSuccessDialog(BuildContext rootContext) {
  return showDialog(
    context: rootContext,
    builder: (dialogContext) {
      return AlertDialog(
        title: const Text('Repeating Schedule Removed!'),
        content: const Text('The repeating schedule was successfully removed.'),
        actions: [
          TextButton(
            onPressed: () {
              // 1. Pop THIS dialog
              if (Navigator.canPop(dialogContext)) {
                Navigator.of(dialogContext).pop(); 
              }

              // 2. Optionally, do more navigation from rootContext if it’s still mounted
              //    (like going to /home, or popping the page)
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (rootContext.mounted) {
                  Navigator.of(rootContext).pop(); 
                  // or context.replace('/home'), or whatever
                }
              });
            },
            child: const Text('OK'),
          )
        ],
      );
    },
  );
}

class _Row1 extends StatelessWidget {
  const _Row1({super.key});

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          final location = viewModel.location;
          final employee = viewModel.employee;
          if (viewModel.scheduleTemplate == null) return Container();

          return Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                flex: 2,
                child: InkWell(
                  onTap: () => unawaited(
                    showDialog(
                      context: context,
                      builder: (_) => ExpandedEmployeesDialog(
                        onSelection: (employeeId) => viewModel.setEmployee(employeeId),
                      ),
                    ),
                  ),
                  child: DecoratedText(
                    padding: const EdgeInsets.all(8),
                    filled: true,
                    text: employee?.name ?? '',
                    labelText: AppLocalizations.of(context)!.employee,
                    validator: () => employee != null
                        ? null
                        : AppLocalization.of(context).employeeRequired,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: InkWell(
                  onTap: () => unawaited(
                    showDialog(
                      context: context,
                      builder: (_) => ExpandedLocationsDialog(
                        onSelection: (locationId) => viewModel.setLocation(locationId),
                      ),
                    ),
                  ),
                  child: DecoratedText(
                    padding: const EdgeInsets.all(8),
                    filled: true,
                    text: location?.name ?? '',
                    labelText: AppLocalizations.of(context)!.location,
                    validator: () => location != null
                        ? null
                        : AppLocalization.of(context).locationRequired,
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class _Row2 extends StatelessWidget {
  const _Row2({super.key});

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);
    return IntrinsicHeight(
      child: Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          if (viewModel.scheduleTemplate == null) return Container();

          return Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                flex: 8,
                child: InkWell(
                  onTap: () async {
                    final pickedDate = await showDatePicker(
                      context: context,
                      builder: (_, child) => Theme(
                        data: ThemeData.light().copyWith(
                          colorScheme: ColorScheme.light(
                            primary: ColorHelper.thePunchRed(),
                          ),
                        ),
                        child: child!,
                      ),
                      initialDate: viewModel.scheduleTemplate!.startDateLocal,
                      firstDate: DateTime.utc(DateTime.now().year - 20),
                      lastDate: DateTime.utc(DateTime.now().year + 20),
                    );
                    if (pickedDate != null) {
                      viewModel.setStartDate(pickedDate);
                    }
                  },
                  child: DecoratedText(
                    padding: const EdgeInsets.all(8),
                    filled: true,
                    text: viewModel.scheduleTemplate!.startDateLocal
                        .toFormattedDateWithYearNoLocal(locale),
                    labelText: AppLocalizations.of(context)!.startDate,
                  ),
                ),
              ),
              Expanded(
                flex: 8,
                child: InkWell(
                  onTap: () async {
                    final pickedDate = await showDatePicker(
                      context: context,
                      builder: (_, child) => Theme(
                        data: ThemeData.light().copyWith(
                          colorScheme: ColorScheme.light(
                            primary: ColorHelper.thePunchRed(),
                          ),
                        ),
                        child: child!,
                      ),
                      initialDate: viewModel.scheduleTemplate!.endDateLocal ??
                          DateTime.now().dateOnly,
                      firstDate: DateTime.utc(DateTime.now().year - 20),
                      lastDate: DateTime.utc(DateTime.now().year + 20),
                    );
                    if (pickedDate != null) {
                      viewModel.setEndDate(pickedDate);
                    }
                  },
                  child: DecoratedText(
                    padding: const EdgeInsets.all(8),
                    filled: true,
                    text: viewModel.scheduleTemplate!.endDateLocal
                            ?.toFormattedDateWithYearNoLocal(locale) ??
                        '',
                    labelText: AppLocalizations.of(context)!.endsBy,
                    suffixIcon: IconButton(
                      onPressed: viewModel.scheduleTemplate!.endDateLocal == null
                          ? null
                          : () => viewModel.clearEndDate(),
                      icon: const Icon(Icons.clear),
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class _Row3 extends StatelessWidget {
  const _Row3({super.key});

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          if (viewModel.scheduleTemplate == null) return Container();

          var startTimeLabel = AppLocalizations.of(context)!.startTime;
          if (viewModel.location?.timeZone != null) {
            startTimeLabel += ' (${viewModel.location?.timeZone})';
          }
          var endTimeLabel = AppLocalizations.of(context)!.endTime;
          if (viewModel.location?.timeZone != null) {
            endTimeLabel += ' (${viewModel.location?.timeZone})';
          }

          return Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                flex: 10,
                child: InkWell(
                  onTap: () async {
                    final pickedTime = await showTimePicker(
                      context: context,
                      initialTime:
                          viewModel.scheduleTemplate!.startDateLocal.timeOfDay,
                    );
                    if (pickedTime != null) {
                      viewModel.setStartTime(pickedTime.duration);
                    }
                  },
                  child: DecoratedText(
                    padding: const EdgeInsets.all(8),
                    filled: true,
                    text: viewModel.scheduleTemplate!.startDateLocal.timeOfDay
                        .toFormatted(context),
                    labelText: startTimeLabel,
                  ),
                ),
              ),
              Expanded(
                flex: 10,
                child: InkWell(
                  onTap: () async {
                    final pickedTime = await showTimePicker(
                      context: context,
                      initialTime: viewModel.endTimeLocal.timeOfDay,
                    );
                    if (pickedTime != null) {
                      viewModel.setEndTime(pickedTime.duration);
                    }
                  },
                  child: DecoratedText(
                    padding: const EdgeInsets.all(8),
                    filled: true,
                    text: viewModel.endTimeLocal.timeOfDay.toFormatted(context),
                    labelText: endTimeLabel,
                  ),
                ),
              ),
              Expanded(
                flex: 8,
                child: InkWell(
                  onTap: () async {
                    final duration = viewModel.scheduleTemplate!.duration.ceilMinutes;
                    final pickedTime = await showTimePicker(
                      helpText: AppLocalizations.of(context)!.duration,
                      context: context,
                      initialTime: duration.timeOfDay,
                      builder: (_, child) => MediaQuery(
                        data: MediaQuery.of(context).copyWith(
                          alwaysUse24HourFormat: true,
                        ),
                        child: child!,
                      ),
                    );
                    if (pickedTime != null) {
                      viewModel.setDuration(pickedTime.duration);
                    }
                  },
                  child: DecoratedText(
                    padding: const EdgeInsets.all(8),
                    filled: true,
                    text: viewModel.scheduleTemplate!.duration.toFormatted,
                    labelText: AppLocalizations.of(context)!.duration,
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class _Row4 extends StatelessWidget {
  const _Row4({super.key});

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          if (viewModel.scheduleTemplate == null) return Container();

          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                AppLocalization.of(context).repeatEvery,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              NumberPicker(
                value: viewModel.scheduleTemplate!.recurringFrequencyEveryNFreq,
                minValue: 1,
                onChanged: (value) {
                  viewModel.setFrequency(value);
                },
              ),
              DropdownButton<int>(
                onChanged: (value) {
                  if (value == null) return;
                  viewModel.setRecurringFrequencyId(value);
                },
                value: viewModel.scheduleTemplate!.recurringFrequencyId,
                items: [
                  DropdownMenuItem(
                    value: 1,
                    child: Text(
                      viewModel.scheduleTemplate!.recurringFrequencyEveryNFreq == 1
                          ? AppLocalization.of(context).day
                          : AppLocalization.of(context).days,
                    ),
                  ),
                  DropdownMenuItem(
                    value: 2,
                    child: Text(
                      viewModel.scheduleTemplate!.recurringFrequencyEveryNFreq == 1
                          ? AppLocalization.of(context).week
                          : AppLocalization.of(context).weeks,
                    ),
                  ),
                  DropdownMenuItem(
                    value: 3,
                    child: Text(
                      viewModel.scheduleTemplate!.recurringFrequencyEveryNFreq == 1
                          ? AppLocalization.of(context).month
                          : AppLocalization.of(context).months,
                    ),
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }
}

class _WeekRow extends StatelessWidget {
  const _WeekRow({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        if (viewModel.scheduleTemplate == null) return Container();
        return Padding(
          padding: const EdgeInsets.all(8),
          child: Container(
            constraints: const BoxConstraints(maxWidth: 780),
            child: DecoratedContainer(
              validator: () => viewModel.validateWeekday
                  ? null
                  : 'At least one day of the week required.',
              child: Center(
                child: Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    FilterChip(
                      label: Text(AppLocalization.of(context).monday),
                      selected:
                          viewModel.scheduleTemplate!.recurringFrequencyWeeklyOnMonday,
                      onSelected: viewModel.setMonday,
                    ),
                    FilterChip(
                      label: Text(AppLocalization.of(context).tuesday),
                      selected:
                          viewModel.scheduleTemplate!.recurringFrequencyWeeklyOnTuesday,
                      onSelected: viewModel.setTuesday,
                    ),
                    FilterChip(
                      label: Text(AppLocalization.of(context).wednesday),
                      selected:
                          viewModel.scheduleTemplate!.recurringFrequencyWeeklyOnWednesday,
                      onSelected: viewModel.setWednesday,
                    ),
                    FilterChip(
                      label: Text(AppLocalization.of(context).thursday),
                      selected:
                          viewModel.scheduleTemplate!.recurringFrequencyWeeklyOnThursday,
                      onSelected: viewModel.setThursday,
                    ),
                    FilterChip(
                      label: Text(AppLocalization.of(context).friday),
                      selected:
                          viewModel.scheduleTemplate!.recurringFrequencyWeeklyOnFriday,
                      onSelected: viewModel.setFriday,
                    ),
                    FilterChip(
                      label: Text(AppLocalization.of(context).saturday),
                      selected:
                          viewModel.scheduleTemplate!.recurringFrequencyWeeklyOnSaturday,
                      onSelected: viewModel.setSaturday,
                    ),
                    FilterChip(
                      label: Text(AppLocalization.of(context).sunday),
                      selected:
                          viewModel.scheduleTemplate!.recurringFrequencyWeeklyOnSunday,
                      onSelected: viewModel.setSunday,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class _MonthRow extends StatelessWidget {
  const _MonthRow({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        if (viewModel.scheduleTemplate == null) return Container();

        return Column(
          children: [
            _buildSelection(context, viewModel),
            if (viewModel.onSpecificMonth) _buildSpecificMonth(context, viewModel),
            if (viewModel.onSpecificWeek) _buildSpecificWeek(context, viewModel),
            if (viewModel.onSpecificDay) _buildSpecificDay(context, viewModel),
          ],
        );
      },
    );
  }

  Widget _buildSelection(BuildContext context, _ViewModel viewModel) {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: [
          FilterChip(
            label: Text(AppLocalization.of(context).onSpecificMonth),
            selected: viewModel.onSpecificMonth,
            onSelected: (_) => viewModel.setOnSpecificMonth(),
          ),
          FilterChip(
            label: Text(AppLocalization.of(context).onSpecificWeek),
            selected: viewModel.onSpecificWeek,
            onSelected: (_) => viewModel.setOnSpecificWeek(),
          ),
          FilterChip(
            label: Text(AppLocalization.of(context).onSpecificDay),
            selected: viewModel.onSpecificDay,
            onSelected: (_) => viewModel.setOnSpecificDay(),
          ),
        ],
      ),
    );
  }

  Widget _buildSpecificMonth(BuildContext context, _ViewModel viewModel) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(8),
                child: Text(
                  '${AppLocalization.of(context).month}:',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
              DropdownButton<int>(
                onChanged: (value) {
                  if (value == null) return;
                  viewModel.setRecurringFrequencyMonthlyOnMonth(value);
                },
                value: viewModel.scheduleTemplate!.recurringFrequencyMonthlyOnMonth,
                items: [
                  DropdownMenuItem(
                    value: 1,
                    child: Text(AppLocalization.of(context).january),
                  ),
                  DropdownMenuItem(
                    value: 2,
                    child: Text(AppLocalization.of(context).february),
                  ),
                  DropdownMenuItem(
                    value: 3,
                    child: Text(AppLocalization.of(context).march),
                  ),
                  DropdownMenuItem(
                    value: 4,
                    child: Text(AppLocalization.of(context).april),
                  ),
                  DropdownMenuItem(
                    value: 5,
                    child: Text(AppLocalization.of(context).may),
                  ),
                  DropdownMenuItem(
                    value: 6,
                    child: Text(AppLocalization.of(context).june),
                  ),
                  DropdownMenuItem(
                    value: 7,
                    child: Text(AppLocalization.of(context).july),
                  ),
                  DropdownMenuItem(
                    value: 8,
                    child: Text(AppLocalization.of(context).august),
                  ),
                  DropdownMenuItem(
                    value: 9,
                    child: Text(AppLocalization.of(context).september),
                  ),
                  DropdownMenuItem(
                    value: 10,
                    child: Text(AppLocalization.of(context).october),
                  ),
                  DropdownMenuItem(
                    value: 11,
                    child: Text(AppLocalization.of(context).november),
                  ),
                  DropdownMenuItem(
                    value: 12,
                    child: Text(AppLocalization.of(context).december),
                  ),
                ],
              )
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(8),
                child: Text(
                  '${AppLocalization.of(context).dayOfMonth}:',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
              DropdownButton<int>(
                onChanged: (value) {
                  if (value == null) return;
                  viewModel.setRecurringFrequencyMonthlyOnDay(value);
                },
                value: viewModel.scheduleTemplate!.recurringFrequencyMonthlyOnDay,
                items: [
                  for (var i = 1; i <= viewModel.daysInMonthlyOnMonth; i++)
                    DropdownMenuItem(
                      value: i,
                      child: Text(i.toOrdinal(context)),
                    ),
                ],
              )
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSpecificWeek(BuildContext context, _ViewModel viewModel) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(8),
                child: Text(
                  '${AppLocalization.of(context).week}:',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
              DropdownButton<int>(
                onChanged: (value) {
                  if (value == null) return;
                  viewModel.setRecurringFrequencyMonthlyTheWeekId(value);
                },
                value: viewModel.scheduleTemplate!.recurringFrequencyMonthlyTheWeekId,
                items: [
                  DropdownMenuItem(
                    value: 4,
                    child: Text(AppLocalization.of(context).firstOccurrence),
                  ),
                  DropdownMenuItem(
                    value: 5,
                    child: Text(AppLocalization.of(context).secondOccurrence),
                  ),
                  DropdownMenuItem(
                    value: 6,
                    child: Text(AppLocalization.of(context).thirdOccurrence),
                  ),
                  DropdownMenuItem(
                    value: 7,
                    child: Text(AppLocalization.of(context).fourthOccurrence),
                  ),
                  DropdownMenuItem(
                    value: 8,
                    child: Text(AppLocalization.of(context).lastOccurrence),
                  ),
                ],
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(8),
                child: Text(
                  '${AppLocalization.of(context).day}:',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
              DropdownButton<int>(
                onChanged: (value) {
                  if (value == null) return;
                  viewModel.setRecurringFrequencyMonthlyTheDayOfTheWeekId(value);
                },
                value: viewModel
                    .scheduleTemplate!.recurringFrequencyMonthlyTheDayOfTheWeekId,
                items: [
                  DropdownMenuItem(
                    value: 11,
                    child: Text(AppLocalization.of(context).monday),
                  ),
                  DropdownMenuItem(
                    value: 12,
                    child: Text(AppLocalization.of(context).tuesday),
                  ),
                  DropdownMenuItem(
                    value: 13,
                    child: Text(AppLocalization.of(context).wednesday),
                  ),
                  DropdownMenuItem(
                    value: 14,
                    child: Text(AppLocalization.of(context).thursday),
                  ),
                  DropdownMenuItem(
                    value: 15,
                    child: Text(AppLocalization.of(context).friday),
                  ),
                  DropdownMenuItem(
                    value: 9,
                    child: Text(AppLocalization.of(context).saturday),
                  ),
                  DropdownMenuItem(
                    value: 10,
                    child: Text(AppLocalization.of(context).sunday),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSpecificDay(BuildContext context, _ViewModel viewModel) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.all(8),
              child: Text(
                '${AppLocalization.of(context).dayOfMonth}:',
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ),
            DropdownButton<int>(
              onChanged: (value) {
                if (value == null) return;
                viewModel.setRecurringFrequencyMonthlyOnDay(value);
              },
              value: viewModel.scheduleTemplate!.recurringFrequencyMonthlyOnDay,
              items: [
                for (var i = 1; i <= 31; i++)
                  DropdownMenuItem(
                    value: i,
                    child: Text(i.toOrdinal(context)),
                  ),
              ],
            ),
          ],
        ),
        if (viewModel.scheduleTemplate!.recurringFrequencyMonthlyOnDay > 28)
          Text(
            'On months with less than 29 days, the schedule is set to the last day of the month.',
            style: Theme.of(context).textTheme.bodySmall,
          ),
      ],
    );
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String scheduleTemplateId;
  final DateTime? startDate;

  /// Flag to indicate that no template was found.
  bool didFindNoTemplate = false;

  Location? location;
  User? employee;
  ScheduleTemplate? scheduleTemplate;

  _ViewModel(this.scheduleTemplateId, this.startDate) {
    addListenables([
      DataModel().locationModel,
      DataModel().userModel,
      DataModel().scheduleTemplateModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    if (scheduleTemplateId.isEmpty) {
      scheduleTemplate = ScheduleTemplate.create();
    } else {
      scheduleTemplate =
          await DataModel().scheduleTemplateModel.getById(scheduleTemplateId);

      if (scheduleTemplate == null) {
        // Not found in DB => navigate away
        didFindNoTemplate = true;
        notifyListeners();
        return;
      }

      employee = await DataModel().userModel.getById(scheduleTemplate!.userId);
      location = await DataModel().locationModel.getById(scheduleTemplate!.locationId);

      // If startDate was passed in, override the date portion
      if (startDate != null) {
        final oldTime = scheduleTemplate!.startDateLocal;
        scheduleTemplate!.startDateLocal = DateTime.utc(
          startDate!.year,
          startDate!.month,
          startDate!.day,
          oldTime.hour,
          oldTime.minute,
        );
      }
    }

    notifyListeners();
  }

  Future<void> deleteSchedule() async {
    await ApiModel().deleteScheduleTemplate(scheduleTemplate!.id);
    await DataModel().scheduleTemplateModel.remove([scheduleTemplateId]);
    await DataModel().scheduleModel.deleteAllFutureSchedulesWithTemplateId(
      scheduleTemplateId,
    );
  }

  Future<void> save() {
    if (scheduleTemplateId.isNotEmpty) {
      // Editing an existing schedule
      return ApiModel().editScheduleTemplate(scheduleTemplate!);
    } else {
      // Creating a new schedule
      return ApiModel().addScheduleTemplate(scheduleTemplate!);
    }
  }

  /// Whether the user can press 'submit'
  bool get canSave {
    if (scheduleTemplate == null) return false;
    if (!scheduleTemplate!.isDirty) return false;
    if (scheduleTemplate!.userId.isEmpty) return false;
    if (scheduleTemplate!.locationId.isEmpty) return false;
    if (!validateWeekday) return false;
    return true;
  }

  // Time & Duration properties
  Duration get endTimeLocal => scheduleTemplate!.startDateLocal
      .add(scheduleTemplate!.duration)
      .timeOfDay
      .duration;

  bool get onSpecificMonth =>
      scheduleTemplate?.recurringFrequencyMonthlyOccursOnSpecificMonth ?? false;
  bool get onSpecificWeek =>
      !(scheduleTemplate?.recurringFrequencyMonthlyOccursOnSpecificMonth ?? false) &&
      !(scheduleTemplate?.recurringFrequencyMonthlyOccursOnSpecificDay ?? false);
  bool get onSpecificDay =>
      scheduleTemplate?.recurringFrequencyMonthlyOccursOnSpecificDay ?? false;

  bool get validateWeekday {
    // If not weekly (2), skip
    if (scheduleTemplate?.recurringFrequencyId != 2) return true;
    return scheduleTemplate!.recurringFrequencyWeeklyOnMonday ||
        scheduleTemplate!.recurringFrequencyWeeklyOnTuesday ||
        scheduleTemplate!.recurringFrequencyWeeklyOnWednesday ||
        scheduleTemplate!.recurringFrequencyWeeklyOnThursday ||
        scheduleTemplate!.recurringFrequencyWeeklyOnFriday ||
        scheduleTemplate!.recurringFrequencyWeeklyOnSaturday ||
        scheduleTemplate!.recurringFrequencyWeeklyOnSunday;
  }

  Future<void> setEmployee(String id) async {
    scheduleTemplate!.userId = id;
    employee = await DataModel().userModel.getById(id);
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  Future<void> setLocation(String id) async {
    scheduleTemplate!.locationId = id;
    location = await DataModel().locationModel.getById(id);
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setStartDate(DateTime value) {
    final oldTime = scheduleTemplate!.startDateLocal;
    scheduleTemplate!.startDateLocal =
        DateTime.utc(value.year, value.month, value.day, oldTime.hour, oldTime.minute);
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setEndDate(DateTime value) {
    final oldTime = scheduleTemplate!.startDateLocal;
    scheduleTemplate!.endDateLocal =
        DateTime.utc(value.year, value.month, value.day, oldTime.hour, oldTime.minute);
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void clearEndDate() {
    scheduleTemplate!.endDateLocal = null;
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setStartTime(Duration value) {
    final date = scheduleTemplate!.startDateLocal;
    scheduleTemplate!.startDateLocal = DateTime.utc(
      date.year,
      date.month,
      date.day,
      value.inHours,
      value.inMinutes % 60,
    );
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setEndTime(Duration value) {
    var duration = value - scheduleTemplate!.startDateLocal.timeOfDay.duration;
    if (duration < Duration.zero) {
      duration += const Duration(hours: 24);
    }
    if (duration > const Duration(hours: 24)) {
      duration -= const Duration(hours: 24);
    }
    scheduleTemplate!.duration = duration;
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setDuration(Duration value) {
    scheduleTemplate!.duration = value;
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setFrequency(int value) {
    scheduleTemplate!.recurringFrequencyEveryNFreq = value;
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setRecurringFrequencyId(int value) {
    scheduleTemplate!.recurringFrequencyId = value;
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setMonday(bool value) {
    scheduleTemplate!.recurringFrequencyWeeklyOnMonday = value;
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setTuesday(bool value) {
    scheduleTemplate!.recurringFrequencyWeeklyOnTuesday = value;
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setWednesday(bool value) {
    scheduleTemplate!.recurringFrequencyWeeklyOnWednesday = value;
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setThursday(bool value) {
    scheduleTemplate!.recurringFrequencyWeeklyOnThursday = value;
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setFriday(bool value) {
    scheduleTemplate!.recurringFrequencyWeeklyOnFriday = value;
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setSaturday(bool value) {
    scheduleTemplate!.recurringFrequencyWeeklyOnSaturday = value;
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setSunday(bool value) {
    scheduleTemplate!.recurringFrequencyWeeklyOnSunday = value;
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setOnSpecificMonth() {
    // If day is out of range for the new month, clamp
    if (scheduleTemplate!.recurringFrequencyMonthlyOnDay > daysInMonthlyOnMonth) {
      scheduleTemplate!.recurringFrequencyMonthlyOnDay = daysInMonthlyOnMonth;
    }
    scheduleTemplate!.recurringFrequencyMonthlyOccursOnSpecificMonth = true;
    scheduleTemplate!.recurringFrequencyMonthlyOccursOnSpecificDay = false;
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setOnSpecificWeek() {
    scheduleTemplate!.recurringFrequencyMonthlyOccursOnSpecificMonth = false;
    scheduleTemplate!.recurringFrequencyMonthlyOccursOnSpecificDay = false;
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setOnSpecificDay() {
    scheduleTemplate!.recurringFrequencyMonthlyOccursOnSpecificMonth = false;
    scheduleTemplate!.recurringFrequencyMonthlyOccursOnSpecificDay = true;
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setRecurringFrequencyMonthlyTheWeekId(int value) {
    scheduleTemplate!.recurringFrequencyMonthlyTheWeekId = value;
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setRecurringFrequencyMonthlyTheDayOfTheWeekId(int value) {
    scheduleTemplate!.recurringFrequencyMonthlyTheDayOfTheWeekId = value;
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  void setRecurringFrequencyMonthlyOnMonth(int value) {
    scheduleTemplate!.recurringFrequencyMonthlyOnMonth = value;
    if (scheduleTemplate!.recurringFrequencyMonthlyOnDay > daysInMonthlyOnMonth) {
      scheduleTemplate!.recurringFrequencyMonthlyOnDay = daysInMonthlyOnMonth;
    }
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }

  int get daysInMonthlyOnMonth {
    if (scheduleTemplate!.recurringFrequencyMonthlyOnMonth == 2) return 28;
    if (scheduleTemplate!.recurringFrequencyMonthlyOnMonth % 2 == 0) return 30;
    return 31;
  }

  void setRecurringFrequencyMonthlyOnDay(int value) {
    scheduleTemplate!.recurringFrequencyMonthlyOnDay = value;
    scheduleTemplate!.isDirty = true;
    notifyListeners();
  }
}
