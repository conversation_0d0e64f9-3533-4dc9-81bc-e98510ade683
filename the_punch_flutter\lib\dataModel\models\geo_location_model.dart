import 'package:collection/collection.dart';
import 'package:uuid/uuid.dart';
import '../../api/api_model.dart';
import '../../misc/extensions.dart';
import '../base_data.dart';
import '../data/geo_location.dart';
import '../hive_db.dart';

class GeoLocationModel extends BaseDataModel<GeoLocation> {
  @override
  Future<Iterable<GeoLocation>> get all async =>
      (await HiveDb.database).geoLocations.values;

  @override
  Future<void> save(Iterable<GeoLocation> t) async {
    await (await HiveDb.database)
        .geoLocations
        .putAll({for (final e in t) e.id: e});

    // Mark as not dirty
    for (var geoLocation in t) {
      geoLocation.isDirty = false;
    }
    notifyListeners();
  }

  // -------------------------------------------------------------------
  // EXAMPLE: createAndSaveGeoLocation, used for Punch In/Out, etc.
  // -------------------------------------------------------------------
  Future<void> createAndSaveGeoLocation({
    required String punchCardId,
    required DateTime recordedOn,
    required double latitude,
    required double longitude,
    required double distanceFromLocation,
    required String geoLocationTypeId,
  }) async {
    // 1) Create the new GeoLocation object
    final geoLocation = GeoLocation.create()
      ..id = const Uuid().v4() // or however you want to generate an ID
      ..punchCardId = punchCardId
      ..recordedOn = recordedOn
      ..latitude = latitude
      ..longitude = longitude
      ..distanceFromLocation = distanceFromLocation
      ..geoLocationTypeId = geoLocationTypeId
      ..isDirty = true; // Mark as dirty for local DB, if you want.

    // 2) Immediately send to your API if you wish
    //    (This depends on your design—some people queue them first, then send.)
    await ApiModel().sendGeoFencePing(geoLocation);

    // 3) Save locally in Hive so you have it offline.
    //    (If your API call returns an updated ID or timestamp, incorporate that.)
    await save([geoLocation]);
  }

  // If you wish to keep your `saveGeoFencePing` method for geofence events:
  Future<void> saveGeoFencePing(
    String punchCardId,
    double latitude,
    double longitude,
    double distance,
    String geoFenceActionId,
  ) async {
    var newGeoLocation = GeoLocation.create()
      ..geoLocationTypeId = geoFenceActionId
      ..punchCardId = punchCardId
      ..distanceFromLocation = distance
      ..latitude = latitude
      ..longitude = longitude;

    await ApiModel().sendGeoFencePing(newGeoLocation);
  }

  // -------------------------------------------------------------------
  // Other existing methods
  // -------------------------------------------------------------------
  Future<Iterable<GeoLocation>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }

  Future<Iterable<GeoLocation>> lastByPunchCardIds(
      Iterable<String> punchCardIds) async {
    final geoLocations = (await all)
        .where((e) => punchCardIds.contains(e.punchCardId))
        .where((e) => e.latitude != null && e.longitude != null);

    final geoLocationGroups = geoLocations.groupListsBy((e) => e.punchCardId);
    return geoLocationGroups.values
        .map((l) => l.reduce((v, e) => v.recordedOn < e.recordedOn ? e : v));
  }

  Future<Iterable<GeoLocation>> getByPunchCardId(String punchCardId) async =>
      (await all)
          .where((e) => e.punchCardId == punchCardId)
          .where((e) => e.latitude != null && e.longitude != null);
}
