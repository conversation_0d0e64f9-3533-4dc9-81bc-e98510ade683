import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class ListingComponent extends StatelessWidget {
  final String groupName;
  final String message;
  final DateTime timestamp;
  final int notificationCount;
  final VoidCallback? onTap;

  const ListingComponent({
    Key? key,
    this.groupName = "Group Chat Name",
    this.message = "Lorem ipsum dolor sit amet consectet...",
    required this.timestamp,
    this.notificationCount = 3,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.fromLTRB(20, 16, 12, 20),
        decoration: BoxDecoration(
          color: const Color(0x0F091F30),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        groupName,
                        style: const TextStyle(
                          fontFamily: 'Poppins',
                          fontSize: 16,
                          fontWeight: FontWeight.w700,
                          color: Color(0xFF091F30),
                          height: 1,
                        ),
                      ),
                      const SizedBox(width: 6),
                      Container(
                        padding: const EdgeInsets.fromLTRB(6, 4, 6, 3),
                        decoration: BoxDecoration(
                          color: const Color(0x29091F30),
                          borderRadius: BorderRadius.circular(200),
                        ),
                        child: Text(
                          notificationCount.toString(),
                          style: const TextStyle(
                            fontFamily: 'Poppins',
                            fontSize: 9,
                            fontWeight: FontWeight.w600,
                            color: Color(0x66091F30),
                            height: 1.1,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  Text(
                    message,
                    style: const TextStyle(
                      fontFamily: 'Poppins',
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF091F30),
                      height: 0.83,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    DateFormat('MMM d h:mma').format(timestamp ?? DateTime.now()),
                    style: const TextStyle(
                      fontFamily: 'Poppins',
                      fontSize: 9,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF969FA5),
                      height: 1.1,
                    ),
                  ),
                ],
              ),
            ),
            Image.network(
              'https://dashboard.codeparrot.ai/api/image/Z_aSgkcWlgHicifT/keyboard.png',
              width: 23.63,
              height: 24,
            ),
          ],
        ),
      ),
    );
}
