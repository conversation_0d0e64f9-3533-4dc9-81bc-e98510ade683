import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../misc/json_conversion.dart';
import 'system.dart';

part 'fetch_travel_data_request.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class GetTravelDataRequest extends SystemRequest {
  final String originLocationId;
  final String destinationLocationId;

  GetTravelDataRequest({
    required this.originLocationId,
    required this.destinationLocationId,
    required String serverIP,
    required String databaseName,
    required String sessionId,
  }) : super(serverIP: serverIP, databaseName: databaseName, sessionId: sessionId);

  static Future<GetTravelDataRequest> create({
    required String originLocationId,
    required String destinationLocationId,
  }) async {
    final systemRequest = await SystemRequest.create();
    return GetTravelDataRequest(
      originLocationId: originLocationId,
      destinationLocationId: destinationLocationId,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory GetTravelDataRequest.fromJson(Map<String, dynamic> json) =>
      _$GetTravelDataRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$GetTravelDataRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class GetTravelDataResponse extends SystemResponse {
  @JsonKey(name: 'Miles')
  final String? miles;

  @JsonKey(name: 'PolylineCoordinates')
  final String? polylineCoordinates;

  GetTravelDataResponse({
    this.miles,
    this.polylineCoordinates,
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory GetTravelDataResponse.fromJson(Map<String, dynamic> json) =>
      _$GetTravelDataResponseFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$GetTravelDataResponseToJson(this);
}
