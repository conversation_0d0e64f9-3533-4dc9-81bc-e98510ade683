// ignore_for_file: use_build_context_synchronously

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:the_punch_flutter/state/app_state.dart';

import '../../api/api_model.dart';
import '../../api/sync_model.dart';
import '../../dialogs/busy_dialog.dart';
import '../../dialogs/error_dialog.dart';
import '../../dialogs/login_tracker_dialog.dart';
import '../../dialogs/organizations_dialog.dart';
import '../../helpers/color_helper.dart';
import '../../helpers/screen_helper.dart';
import '../../misc/app_localization.dart';
import '../../misc/logging.dart';
import '../../misc/my_platform.dart';
import '../../state/fcm_model.dart';
import '../../state/login_state.dart';
import '../web/my_scaffold.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) => PopScope(
        canPop: false, // 🚀 This fully disables back navigation
        onPopInvoked: (bool didPop) {
          if (didPop) return; // If pop is already handled, do nothing.
        },
        child: GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: MyScaffold(
            enableBottomBar: false,
            showLoggedOutDrawer: true,
            showDesktopHeader: false,
            showDrawer: false,
            hideBar: true,
            title: AppLocalizations.of(context)!.login,
            body: Center(
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: _Body(),
              ),
            ),
          ),
        ),
      );
}

class _Body extends StatefulWidget {
  @override
  State<_Body> createState() => _BodyState();
}

class _BodyState extends State<_Body> {
  // Track when login completes & data loading completes
  final ValueNotifier<bool> _loginComplete = ValueNotifier<bool>(false);
  final ValueNotifier<bool> _dataLoadComplete = ValueNotifier<bool>(false);

  final _formKey = GlobalKey<FormState>();

  bool _obscureText = true;
  bool _enableLogin = false;

  final _scrollController = ScrollController();
  final _organizationIdController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();

  _BodyState() {
    _organizationIdController.addListener(validateFields);
    _usernameController.addListener(validateFields);
    _passwordController.addListener(validateFields);
  }

  void validateFields() {
    final enableLogin = _organizationIdController.text.isNotEmpty &&
        _usernameController.text.isNotEmpty &&
        _passwordController.text.isNotEmpty;
    if (_enableLogin != enableLogin) {
      setState(() => _enableLogin = enableLogin);
    }
  }

  @override
  void initState() {
    super.initState();
    unawaited(_prefillFields());
    final logoutErrorCode = ApiModel().logoutErrorCode.value ?? '';
    if (logoutErrorCode.isNotEmpty) {
      Future.delayed(const Duration(seconds: 1), () async {
        await showDialog(
          context: context,
          builder: (context) => ErrorDialog(errorCode: logoutErrorCode),
        );
        // indicate the errorCode is handled
        ApiModel().logoutErrorCode.value = null;
      });
    }
  }

  final _usernameFocus = FocusNode();
  final _passwordFocus = FocusNode();

  Future<void> _prefillFields() async {
    final prefs = await SharedPreferences.getInstance();
    _organizationIdController.text = prefs.getString('organizationId') ??
        (kReleaseMode ? '' : '4leafcleaners_1');
    _usernameController.text =
        prefs.getString('username') ?? (kReleaseMode ? '' : 'admin1');
    _passwordController.text = (kReleaseMode ? '' : 'pw123');
  }

  @override
  Widget build(BuildContext context) {
    // Determine screen size for responsiveness
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Container(
      constraints: const BoxConstraints(minWidth: 200, maxWidth: 550),
      child: SingleChildScrollView(
        controller: _scrollController,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Responsive Logo
            Container(
              margin: const EdgeInsets.only(bottom: 20),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: isSmallScreen ? 150 : 200,
                  maxHeight: isSmallScreen ? 150 : 200,
                ),
                child: Image.asset('images/the_punch_logo_small.png'),
              ),
            ),
            // Form Fields
            FocusScope(
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    _createOrganizationIdField(context, isSmallScreen),
                    SizedBox(
                      height: ScreenHelper.screenHeightPercentage(context, 2),
                    ),
                    _createUsernameField(context, isSmallScreen),
                    SizedBox(
                      height: ScreenHelper.screenHeightPercentage(context, 2),
                    ),
                    _createPasswordField(context, isSmallScreen),
                  ],
                ),
              ),
            ),
            // Login Button
            FocusScope(child: _createLoginButton(context, isSmallScreen)),
            // Additional Links
            FocusScope(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (MyPlatform.isWeb || kDebugMode)
                      TextButton(
                        onPressed: () => context.go('/register'),
                        child: Text(
                          AppLocalization.of(context).registerOrganization,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: ColorHelper.thePunchRed(),
                              ),
                        ),
                      ),
                    TextButton(
                      onPressed: () => context.go('/forgotLogin'),
                      child: Text(
                        AppLocalization.of(context).forgotLogin,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: ColorHelper.thePunchRed(),
                            ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // About Section
            Container(
              margin: const EdgeInsets.only(top: 30),
              child: _About(),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> scrollToLoginButton() async {
    await Future.delayed(const Duration(milliseconds: 500));
    if (_scrollController.position.extentAfter > 0) {
      await _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 500),
        curve: Curves.ease,
      );
    }
  }

  TextFormField _createOrganizationIdField(
      BuildContext context, bool isSmallScreen) {
    return TextFormField(
      key: const ValueKey('OrganizationIdField'),
      autofocus: true,
      controller: _organizationIdController,
      onTap: scrollToLoginButton,
      onFieldSubmitted: (value) => _usernameFocus.requestFocus(),
      keyboardType: TextInputType.text,
      textInputAction: TextInputAction.next,
      autovalidateMode: AutovalidateMode.always,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: isSmallScreen ? 16 : 18,
          ),
      decoration: InputDecoration(
        labelText: AppLocalizations.of(context)!.organizationId,
        labelStyle: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontSize: isSmallScreen ? 16 : 18,
            ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: const BorderSide(
            color: Colors.blue,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: BorderSide(
            color: ColorHelper.thePunchBlue(),
            width: 2,
          ),
        ),
        filled: false,
        suffixIcon: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              key: const ValueKey('SearchOrganizationButton'),
              icon: const Icon(Icons.search),
              onPressed: () => unawaited(searchOrganizationId(context)),
              tooltip: AppLocalizations.of(context)!.search,
            ),
            IconButton(
              icon: const Icon(Icons.clear),
              onPressed: clearOrganizationIdPressed,
              tooltip: AppLocalizations.of(context)!.clear,
            ),
          ],
        ),
      ),
    );
  }

  TextFormField _createUsernameField(
      BuildContext context, bool isSmallScreen) {
    return TextFormField(
      key: const ValueKey('UsernameField'),
      focusNode: _usernameFocus,
      controller: _usernameController,
      onTap: scrollToLoginButton,
      onFieldSubmitted: (value) => _passwordFocus.requestFocus(),
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      autofillHints: const [AutofillHints.username],
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: isSmallScreen ? 16 : 18,
          ),
      decoration: InputDecoration(
        labelText: AppLocalizations.of(context)!.username,
        labelStyle: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontSize: isSmallScreen ? 16 : 18,
            ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: const BorderSide(
            color: Colors.blue,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: BorderSide(
            color: ColorHelper.thePunchBlue(),
            width: 2,
          ),
        ),
        filled: false,
        suffixIcon: IconButton(
          icon: const Icon(Icons.clear),
          onPressed: clearUsernamePressed,
          tooltip: AppLocalizations.of(context)!.clear,
        ),
      ),
    );
  }

  TextFormField _createPasswordField(
      BuildContext context, bool isSmallScreen) {
    return TextFormField(
      key: const ValueKey('PasswordField'),
      focusNode: _passwordFocus,
      controller: _passwordController,
      onTap: scrollToLoginButton,
      onFieldSubmitted: (value) =>
          _enableLogin ? unawaited(loginPressed()) : null,
      keyboardType: TextInputType.visiblePassword,
      textInputAction: TextInputAction.done,
      autofillHints: const [AutofillHints.password],
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontSize: isSmallScreen ? 16 : 18,
          ),
      decoration: InputDecoration(
        labelText: AppLocalizations.of(context)!.password,
        labelStyle: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontSize: isSmallScreen ? 16 : 18,
            ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: const BorderSide(
            color: Colors.blue,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(24),
          borderSide: BorderSide(
            color: ColorHelper.thePunchBlue(),
            width: 2,
          ),
        ),
        filled: false,
        suffixIcon: IconButton(
          icon: Icon(_obscureText ? Icons.visibility : Icons.visibility_off),
          onPressed: showPasswordPressed,
          tooltip: _obscureText
              ? AppLocalizations.of(context)!.showPassword
              : AppLocalizations.of(context)!.hidePassword,
        ),
      ),
      obscureText: _obscureText,
    );
  }

  Widget _createLoginButton(BuildContext context, bool isSmallScreen) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          key: const ValueKey('LoginButton'),
          onPressed: _enableLogin ? () async => await loginPressed() : null,
          icon: const Icon(Icons.login, color: Colors.white),
          label: Text(
            AppLocalizations.of(context)!.login,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w700,
              fontSize: 16,
            ),
          ),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24),
            ),
            backgroundColor: _enableLogin
                ? ColorHelper.thePunchRed()
                : ColorHelper.thePunchRed().withOpacity(0.6),
          ),
        ),
      ),
    );
  }

  Future<void> searchOrganizationId(BuildContext context) async {
    return showDialog(
      context: context,
      builder: (BuildContext context) => OrganizationsDialog(
        organizationSelected: (organizationId) {
          setState(() {
            _organizationIdController.text = organizationId;
            _usernameFocus.requestFocus();
          });
        },
      ),
    );
  }

  void clearOrganizationIdPressed() {
    _organizationIdController.clear();
  }

  void clearUsernamePressed() {
    _usernameController.clear();
  }

  void showPasswordPressed() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  Future<void> loginPressed() async {
    try {
      // Show the loading dialog, now wrapped in WillPopScope
      await showLoginTrackerDialog(context);
    } on ApiException catch (e, stack) {
      await logApiException(e, stack);
      if (!context.mounted) return;
      await showErrorDialog(context, e.errorCode);
    } on Exception catch (e, stack) {
      await logException(e, stack);
      if (!context.mounted) return;
      await showErrorDialog(context, null);
    }
  }

  /// Show the login dialog, disallowing dismissal (back button or otherwise)
  /// for at least 30 seconds.
  Future<void> showLoginTrackerDialog(BuildContext context) async {
    final startTime = DateTime.now();

    // barrierDismissible=false blocks tapping outside; WillPopScope will block back button
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) {
        return WillPopScope(
          // If the user tries to pop via back button, only allow it after 30 seconds:
          onWillPop: () async {
            final elapsed = DateTime.now().difference(startTime).inSeconds;
            return elapsed >= 30;
          },
          child: LoginTrackerDialog(
            subTitle: 'Logging in...',
            future: () async {
              // Run your login process
              await loginProcess();

              // If login finishes in under 30 seconds, force-wait the difference
              final elapsed = DateTime.now().difference(startTime).inSeconds;
              if (elapsed < 30) {
                await Future.delayed(Duration(seconds: 30 - elapsed));
              }
            },
            loginComplete: _loginComplete,
            dataLoadComplete: _dataLoadComplete,
          ),
        );
      },
    );
  }

  /// Actual login method
  Future<void> loginProcess() async {
    final organizationId = _organizationIdController.text;
    final username = _usernameController.text;
    final password = _passwordController.text;

    // --- VERSION CHECK START ------------------------------------
    final serverMinVersion = await ApiModel().getMinimumVersion();
    debugPrint('Server reported MINIMUM version: $serverMinVersion');
    debugPrint('Local app version: ${AppState.appVersion}');

    if (serverMinVersion == null) {
      // If server didn't return anything, forcibly log user out & show update dialog
      debugPrint('No server version returned. Forcing logout...');
      final loginState = await LoginState.instance;
      await loginState.logout(forcedOut: true);

      if (context.mounted) {
        debugPrint('Showing "Update Required" dialog due to null version.');
        await showDialog(
          barrierDismissible: true,
          context: context,
          builder: (_) => AlertDialog(
            title: const Text('Update Required'),
            content: const Text('Could not retrieve the required app version.'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
      return; // Stop here
    } else {
      // Check if local version is at least the server's minimum
      final isCompatible = _isAppVersionCompatible(AppState.appVersion!, serverMinVersion);
      if (!isCompatible) {
        debugPrint('Local app version is BELOW min required. Forcing logout...');
        final loginState = await LoginState.instance;
        await loginState.logout(forcedOut: true);

        if (context.mounted) {
          debugPrint('Showing "Update Required" dialog due to version mismatch.');
          await showDialog(
            barrierDismissible: true,
            context: context,
            builder: (_) => AlertDialog(
              title: const Text('Update Required'),
              content: Text(
                'This app version (${AppState.appVersion}) is not supported.\n'
                'Minimum required: $serverMinVersion\n\nPlease update your application.',
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('OK'),
                ),
              ],
            ),
          );
        }
        return; // Stop here
      }
    }
    // --- VERSION CHECK END --------------------------------------

    // If we get here, version is fine => proceed to login
    var deviceId = '';
    if (FCMModel.token != null) {
      deviceId = FCMModel.token!;
    } else {
      if (MyPlatform.isAndroid) deviceId = 'Android';
      if (MyPlatform.isIOS) deviceId = 'iOS';
      if (MyPlatform.isWeb) deviceId = 'Web';
      if (MyPlatform.isWindows) deviceId = 'Windows';
      if (MyPlatform.isMacOS) deviceId = 'macOS';
    }

    final loginResponse =
        await ApiModel().login(organizationId, username, password, deviceId);

    final orgs = await ApiModel().getOrganizations(organizationId);
    if (orgs.isNotEmpty) {
      final orgName = orgs.first.name;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('organizationName', orgName);
    }

    final user = loginResponse.user;
    final systemRequest = loginResponse.databaseInfo;
    final loginState = await LoginState.instance;

    await loginState.login(
      user,
      organizationId,
      systemRequest,
      loginResponse.userSessionExpirationMinsWeb,
    );

    // Mark login as complete for the tracker
    _loginComplete.value = true;

    // Synchronously load data
    await loadDataProcess();
  }

  Future<void> loadDataProcess() async {
    await SyncModel().syncSynchronous();

    // Mark data load as complete
    _dataLoadComplete.value = true;

    // Simulate a short wait for UX
    await Future.delayed(const Duration(milliseconds: 900));

    // Navigate to home
    if (MyPlatform.isHandheld) {
      GoRouter.of(context).go('/clock');
    } else {
      GoRouter.of(context).go('/');
    }
  }

  Future<void> showErrorDialog(BuildContext context, String? errorCode) async {
    await showDialog(
      context: context,
      builder: (context) => ErrorDialog(errorCode: errorCode),
    );
  }

  /// Compare local (installed) version to the server's minimum required version.
  /// Returns `true` if local >= minimum (i.e., is compatible).
  bool _isAppVersionCompatible(String localVersion, String minRequiredVersion) {
    // Simple approach:
    //  1) Remove trailing plus if present (e.g. "1.1.2+")
    //  2) Parse major.minor.patch
    final cleanedMin = minRequiredVersion.replaceAll('+', '');
    final cleanedLocal = localVersion.replaceAll('+', '');

    final minParts = cleanedMin.split('.');
    final localParts = cleanedLocal.split('.');

    // If parse fails or incomplete, treat as not compatible
    if (minParts.length < 2 || localParts.length < 2) {
      return false;
    }

    final minMajor = int.tryParse(minParts[0]) ?? 0;
    final minMinor = int.tryParse(minParts[1]) ?? 0;
    final minPatch = int.tryParse(minParts.length > 2 ? minParts[2] : '0') ?? 0;

    final localMajor = int.tryParse(localParts[0]) ?? 0;
    final localMinor = int.tryParse(localParts[1]) ?? 0;
    final localPatch = int.tryParse(localParts.length > 2 ? localParts[2] : '0') ?? 0;

    // Compare major
    if (localMajor > minMajor) return true;
    if (localMajor < minMajor) return false;

    // If major is equal, compare minor
    if (localMinor > minMinor) return true;
    if (localMinor < minMinor) return false;

    // If major & minor are equal, compare patch
    return localPatch >= minPatch;
  }
}

class _About extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Material(
        color: Colors.transparent,
        child: FutureBuilder<PackageInfo>(
          future: Future(PackageInfo.fromPlatform),
          builder: (context, snapshot) => DecoratedBox(
            decoration: const BoxDecoration(
              color: Color.fromARGB(0, 255, 255, 255),
            ),
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Center(
                child: MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () {
                      showAboutDialog(
                        context: context,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Image.asset('images/the_punch_logo_small.png',
                                  scale: 5),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    'The Punch',
                                    style:
                                        Theme.of(context).textTheme.titleLarge,
                                  ),
                                  SizedBox(
                                    height: ScreenHelper.screenHeightPercentage(
                                        context, 1),
                                  ),
                                  Text(
                                    snapshot.data?.version ?? '',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium,
                                  ),
                                  SizedBox(
                                    height: ScreenHelper.screenHeightPercentage(
                                        context, 1),
                                  ),
                                  SizedBox(
                                    width: ScreenHelper.screenWidthPercentage(
                                        context, 40),
                                    child: Text(
                                      '\u{a9} 2023 Morris Consulting Service',
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleMedium,
                                      overflow: TextOverflow.visible,
                                      textAlign: TextAlign.end,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          )
                        ],
                      );
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.info,
                          color: ColorHelper.thePunchBlue(),
                        ),
                        const SizedBox(width: 10),
                        Text(
                          'About The Punch',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: ColorHelper.thePunchBlue(),
                                fontWeight: FontWeight.w800,
                                fontSize: 16,
                              ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      );
}
