import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:the_punch_flutter/misc/json_conversion.dart';
import 'system.dart';

part 'fetch_travel_time_request.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class FetchTravelTimeRequest extends SystemRequest {
  @JsonKey(name: 'OriginLocationId')
  final String originLocationId;

  @<PERSON><PERSON><PERSON>ey(name: 'DestinationLocationId')
  final String destinationLocationId;

  FetchTravelTimeRequest({
    required this.originLocationId,
    required this.destinationLocationId,
    required super.serverIP,
    required super.databaseName,
    required super.sessionId,
  });

  static Future<FetchTravelTimeRequest> create({
    required String originLocationId,
    required String destinationLocationId,
  }) async {
    final systemRequest = await SystemRequest.create();
    return FetchTravelTimeRequest(
      originLocationId: originLocationId,
      destinationLocationId: destinationLocationId,
      serverIP: systemRequest.serverIP ?? '',
      databaseName: systemRequest.databaseName ?? '',
      sessionId: systemRequest.sessionId ?? '',
    );
  }

  factory FetchTravelTimeRequest.fromJson(Map<String, dynamic> json) {
    try {
      return _$FetchTravelTimeRequestFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }

  @override
  Map<String, dynamic> toJson() => _$FetchTravelTimeRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class FetchTravelTimeResponse extends SystemResponse {
  @JsonKey(fromJson: _durationFromJson, toJson: _durationToJson)
  final Duration? duration;

  @JsonKey(defaultValue: '')
  final String message;

  FetchTravelTimeResponse(
    String errorCode,
    DateTime? serverTime, // Changed from DateTime to DateTime?
    {
      required this.duration,
      required this.message,
    }
  ) : super(errorCode, serverTime);

  factory FetchTravelTimeResponse.fromJson(Map<String, dynamic> json) =>
    FetchTravelTimeResponse(
      json['ErrorCode'] as String? ?? '',
      nullableDateTimeFromJson(json['ServerTime']), // This returns DateTime?
      duration: json['Duration'] != null ? _durationFromJson(json['Duration']) : null,
      message: json['Message'] as String? ?? '',
    );

  @override
  Map<String, dynamic> toJson() => _$FetchTravelTimeResponseToJson(this);

  static Duration _durationFromJson(String duration) {
    final parts = duration.split(':');
    if (parts.length == 3) {
      final hours = int.parse(parts[0]);
      final minutes = int.parse(parts[1]);
      final seconds = int.parse(parts[2]);
      return Duration(hours: hours, minutes: minutes, seconds: seconds);
    }
    return Duration.zero;
  }

  static String _durationToJson(Duration? duration) {
    if (duration == null) return '';
    final hours = duration.inHours.toString().padLeft(2, '0');
    final minutes = (duration.inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (duration.inSeconds % 60).toString().padLeft(2, '0');
    return '$hours:$minutes:$seconds';
  }
}