import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../api/api_model.dart';
import '../../dataModel/data/group_member.dart';
import '../../dataModel/data/message.dart';
import '../../dataModel/data/user.dart';
import '../../dataModel/data_model.dart';
import '../../helpers/color_helper.dart';
import '../../helpers/screen_helper.dart';
import '../../misc/chat_screen.dart';
import '../view_model_mixin.dart';
import '../../misc/extensions.dart';
import '../../state/login_state.dart';
import '../web/my_scaffold.dart';
import 'employee_widgets/create_chat_button.dart';

class ChatPage extends StatefulWidget {
  ChatPage({super.key});

  @override
  _ChatPageState createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  late final ViewModel viewModel;
  late final GoRouterDelegate _routerDelegate;

  @override
  void initState() {
    super.initState();
    viewModel = ViewModel();

    viewModel.refresh();

    // Store a reference to the router delegate
    _routerDelegate = GoRouter.of(context).routerDelegate;

    // Add a listener to detect when the page is resumed
    _routerDelegate.addListener(_routeListener);
  }

  // Separate method for the route listener
  void _routeListener() {
    // Check if widget is still mounted before accessing context
    if (!mounted) return;

    if (_routerDelegate.currentConfiguration.fullPath == '/chat') {
      viewModel.refresh();
    }
  }

  @override
  void dispose() {
    // Remove the listener when the widget is disposed
    _routerDelegate.removeListener(_routeListener);
    viewModel.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) =>
      ChangeNotifierProvider<ViewModel>.value(
        value: viewModel,
        child: MyScaffold(
          title: AppLocalizations.of(context)!.chat,
          body: Center(
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: _Body(viewModel: viewModel),
            ),
          ),
        ),
      );
}

class _Body extends StatelessWidget {
  final ViewModel viewModel;
  _Body({required this.viewModel, super.key});

  @override
  Widget build(BuildContext context) =>
      ChangeNotifierProvider<ViewModel>.value(
          value: viewModel,
          child: Column(
            children: [
              // Chat list section (expandable)
              const SizedBox(
                height: 8,
              ),
              const Center(
                  child: Text(
                "Chats",
                style:  TextStyle(
                  fontSize: 30,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              )),
              const SizedBox(
                height: 4,
              ),
              Expanded(
                child: Center(
                  child: ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 500),
                    child: Consumer<ViewModel>(
                      builder: (context, viewModel, _) {
                        if (viewModel.isLoading) {
                          return const Center(
                            child: CircularProgressIndicator(color: Colors.red),
                          );
                        }

                        if (viewModel.errorMessage != null) {
                          return Center(child: Text(viewModel.errorMessage!));
                        }

                        if (viewModel.chatCardsData.isEmpty) {
                          return const Center(child: Text('No Chats.'));
                        }

                        return RefreshIndicator(
                          color: Colors.red,
                          backgroundColor: Colors.grey.shade200,
                          onRefresh: viewModel.refresh,
                          child: ListView.builder(
                            padding: const EdgeInsets.only(
                                bottom: 80), // space for button
                            itemCount: viewModel.chatCardsData.length + 1,
                            itemBuilder: (context, index) {
                              if (index == viewModel.chatCardsData.length) {
                                return const SizedBox(height: 64);
                              }
                              final chatCardData =
                                  viewModel.chatCardsData.elementAt(index);
                              return _Tile(chatCardData);
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),

              // Create chat button pinned to bottom
              Padding(
                padding: const EdgeInsets.only(
                  bottom: 16, // Space above bottom app bar
                  left: 16,
                  right: 16,
                  top: 8,
                ),
                child: SizedBox(
                  width: double.infinity,
                  child: CreateChatButton(
                    onPressed: () async => {
                      await context.pushNamed('/chat/create',
                          extra: viewModel),
                    },
                  ),
                ),
              ),
            ],
          ));
}

class _Tile extends StatelessWidget {
  final _TileData data;

  const _Tile(this.data);

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: () async {
        final result = await context.pushNamed(
          '/chat/details',
          queryParameters: {'id': data.groupId},
        );

        if (result == 'refresh') {
          context.read<ViewModel>().refresh();
        }
      },
      child: Card(
        elevation: 0,
        color: const Color.fromARGB(255, 240, 240, 240),
        margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Left side: Texts
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    // Group Chat Name + Count
                    Row(
                      children: [
                        Text(
                          data.title,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w700,
                            fontSize: 16,
                            color: Colors.black,
                          ),
                        ),
                        if (data.memberCount != 2)
                          Container(
                            margin: const EdgeInsets.only(left: 6),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: const Color.fromARGB(255, 177, 177, 177),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '${data.memberCount}',
                              style: theme.textTheme.labelSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _truncateWithEllipsis(35, data.lastMessageBody),
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontSize: 13,
                        color: Colors.black87,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      data.lastMessageSentOn.toFormattedDateTime(locale),
                      style: theme.textTheme.labelSmall?.copyWith(
                        fontSize: 12,
                        color: Colors.black45,
                      ),
                    ),
                  ],
                ),
              ),
              // Right side: Chevron
              const Column(
                children: [
                  Icon(
                    Icons.chevron_right,
                    color: Colors.black54,
                    size: 24,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _truncateWithEllipsis(int cutoff, String text) {
    return (text.length <= cutoff) ? text : '${text.substring(0, cutoff)}...';
  }
}

/// New structure to represent the chat card data for groups or direct chats.
class _TileData {
  final String groupId;
  final String title; // The group or the other user's name
  final String lastMessageBody;
  final DateTime lastMessageSentOn;
  final bool isNew; // Whether there's an unread message
  final int memberCount; // <-- NEW field to show total group members

  _TileData({
    required this.groupId,
    required this.title,
    required this.lastMessageBody,
    required this.lastMessageSentOn,
    required this.isNew,
    required this.memberCount,
  });
}

class ViewModel extends ChangeNotifier with ViewModelMixin {
  bool _isRefreshing = false; // Flag to prevent recursive refreshes
  late String employeeId;
  Iterable<_TileData> chatCardsData = [];
  bool isLoading = false;
  String? errorMessage;

  // Constructor
  ViewModel() {
    unawaited(refresh());
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// Fetch and build the UI model for chat-cards
  Future<void> refresh() async {
    if (_isRefreshing) {
      print('Refresh already in progress. Skipping...');
      return;
    }
    _isRefreshing = true;
    isLoading = true;
    errorMessage = null;
    notifyListeners();

    bool apiFetchFailed = false;

    try {
      employeeId = LoginState.userId;

      // Step 1: Attempt to Fetch Messages, Groups, and GroupMembers from API
      try {
        print('Fetching data from API for employeeId: $employeeId');
        final fetchResponse = await ApiModel().fetchMessages(employeeId);

        // --- Step 2: OVERWRITE local DB with fetched data ---
        await DataModel().messageGroupModel.clearAll();
        await DataModel().groupMemberModel.clearAll();

        await DataModel().messageModel.updateMessages(fetchResponse.messages);
        await DataModel()
            .messageGroupModel
            .updateMessageGroups(fetchResponse.messageGroups ?? []);
        await DataModel()
            .groupMemberModel
            .updateGroupMembers(fetchResponse.groupMembers ?? []);

        print('Successfully fetched and updated messages/groups from API.');
      } catch (apiError) {
        print('API fetch failed: $apiError');
        apiFetchFailed = true;
      }

      // Step 3: Load from local DB
      final messages = await DataModel().messageModel.all ?? [];
      final groups = await DataModel().messageGroupModel.all ?? [];
      final groupMembers = await DataModel().groupMemberModel.all ?? [];
      final users = await DataModel().userModel.all ?? [];

      // Identify groupIds in which the current user is a member
      final userGroupIds = groupMembers
          .where((gm) => gm.userId == employeeId)
          .map((gm) => gm.groupId)
          .toSet();

      if (groups.isEmpty || userGroupIds.isEmpty) {
        errorMessage = 'No Chats.';
      } else {
        final Map<String, User> userMap = {
          for (final u in users) u.id: u,
        };

        final List<_TileData> tiles = [];

        for (final grp in groups) {
          if (!userGroupIds.contains(grp.id)) {
            continue; // Skip if user is not part of this group
          }
          // Gather messages for this group
          final groupMsgs = messages
              .where((m) => m.toGroupId == grp.id)
              .toList()
            ..sort((a, b) => b.sentOn.compareTo(a.sentOn));
          final lastMsg = groupMsgs.isNotEmpty ? groupMsgs.first : null;

          // Collect group members
          final members =
              groupMembers.where((gm) => gm.groupId == grp.id).toList();

          // Determine group name (for direct chat: use the other user's name)
          String displayTitle;
          if (members.length == 2) {
            final otherMember = members.firstWhere(
              (m) => m.userId != employeeId,
              orElse: () => members.first,
            );
            displayTitle = userMap[otherMember.userId]?.name ?? grp.name;
          } else {
            displayTitle = grp.name;
          }

          // Build preview text
          String lastMsgBody = lastMsg?.body ?? 'No Messages';
          if (_isUuid(lastMsgBody)) {
            lastMsgBody = '*Image Preview*';
          }

          final bool isNew = lastMsg != null &&
              lastMsg.fromUserId != employeeId &&
              lastMsg.openedOn == null;

          tiles.add(
            _TileData(
              groupId: grp.id,
              title: displayTitle,
              lastMessageBody: lastMsgBody,
              lastMessageSentOn:
                  lastMsg?.sentOn ?? (grp.createdOn ?? DateTime.now()),
              isNew: isNew,
              memberCount: members.length,
            ),
          );
        }

        tiles
            .sort((a, b) => b.lastMessageSentOn.compareTo(a.lastMessageSentOn));
        chatCardsData = tiles;

        if (tiles.isEmpty) {
          errorMessage = 'No Chats.';
        }
      }

      if (apiFetchFailed && chatCardsData.isNotEmpty) {
        errorMessage = errorMessage == null
            ? 'Failed to fetch latest data. Showing cached.'
            : '${errorMessage} Additionally, failed to fetch latest data.';
      }
    } catch (e, st) {
      errorMessage = 'An unexpected error occurred.';
      print('Unexpected error: $e\n$st');
    } finally {
      isLoading = false;
      _isRefreshing = false;
      notifyListeners();
    }
  }

  /// Simple check for standard UUID format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
  bool _isUuid(String text) {
    final regex = RegExp(
      r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$',
      caseSensitive: false,
    );
    return regex.hasMatch(text);
  }
}
