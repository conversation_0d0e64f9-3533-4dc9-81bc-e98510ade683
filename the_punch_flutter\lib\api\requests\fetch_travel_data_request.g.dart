// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fetch_travel_data_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetTravelDataRequest _$GetTravelDataRequestFromJson(
        Map<String, dynamic> json) =>
    GetTravelDataRequest(
      originLocationId: json['OriginLocationId'] as String,
      destinationLocationId: json['DestinationLocationId'] as String,
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$GetTravelDataRequestToJson(
        GetTravelDataRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'OriginLocationId': instance.originLocationId,
      'DestinationLocationId': instance.destinationLocationId,
    };

GetTravelDataResponse _$GetTravelDataResponseFromJson(
        Map<String, dynamic> json) =>
    GetTravelDataResponse(
      miles: json['Miles'] as String?,
      polylineCoordinates: json['PolylineCoordinates'] as String?,
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$GetTravelDataResponseToJson(
    GetTravelDataResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  writeNotNull('Miles', instance.miles);
  writeNotNull('PolylineCoordinates', instance.polylineCoordinates);
  return val;
}
