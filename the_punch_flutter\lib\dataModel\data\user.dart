import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import '../base_data.dart';
import '../../misc/json_conversion.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import 'package:uuid/uuid.dart';

import 'user_type.dart';

part 'user.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
@HiveType(typeId: 3)
class User extends BaseData {
  static const primaryUserId = '4a6e0e08-4601-46ae-94ca-2317734b80a2';

  @HiveField(100)
  @override
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(101)
  String? username;

  @HiveField(102)
  String firstName;

  @HiveField(103)
  String lastName;

  @HiveField(104)
  String? phone;

  @HiveField(105)
  String? emailAddress;

  @HiveField(106)
  @JsonKey(fromJson: idFromJson)
  String userTypeId;

  @HiveField(107)
  bool isContact;

  // --------------------------------------------
  // Changed from single `managedById` => multiple
  // --------------------------------------------
  @HiveField(112)
  String? managedByIds; 
  // e.g. "GUID1,GUID2,GUID3"

  String? password;
  String? confirmPassword;

  @HiveField(108)
  double? payRate;

  @HiveField(109)
  @JsonKey(name: 'PayRate_Frequency')
  int payRateFrequency;

  @HiveField(110)
  String languageKey;

  @HiveField(111)
  @JsonKey(name: 'PayrollID')
  String? payrollId;

  User({
    super.isDirty,
    super.isActive,
    required super.createdOn,
    super.createdByUserId,
    super.lastChangedOn,
    super.lastChangedByUserId,
    required this.id,
    required this.username,
    this.phone,
    this.emailAddress,
    required this.firstName,
    required this.lastName,
    required this.userTypeId,
    required this.isContact,
    this.password,
    this.confirmPassword,
    this.payRate,
    required this.payRateFrequency,
    required this.languageKey,
    this.payrollId,
    this.managedByIds,  // <-- updated constructor param
  });

  String get name => '$firstName $lastName';

  // -------------------
  // JSON Serialization
  // -------------------
  factory User.fromJson(Map<String, dynamic> json) {
    try {
      return _$UserFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() => _$UserToJson(this);

  // -----------
  // Factories
  // -----------
  factory User.empty() => User(
        id: '',
        username: '',
        firstName: '',
        lastName: '',
        userTypeId: '',
        createdOn: DateTime(1970),
        createdByUserId: '',
        isContact: false,
        payRateFrequency: PayRateFrequency.hourly.index,
        languageKey: 'en-US',
        payrollId: '',
      );

  factory User.from(User o) => User(
        isDirty: o.isDirty,
        isActive: o.isActive,
        createdOn: o.createdOn,
        createdByUserId: o.createdByUserId,
        lastChangedOn: o.lastChangedOn,
        lastChangedByUserId: o.lastChangedByUserId,
        id: o.id,
        username: o.username,
        firstName: o.firstName,
        lastName: o.lastName,
        userTypeId: o.userTypeId,
        phone: o.phone,
        emailAddress: o.emailAddress,
        isContact: o.isContact,
        payRate: o.payRate,
        payRateFrequency: o.payRateFrequency,
        languageKey: o.languageKey,
        payrollId: o.payrollId,
        managedByIds: o.managedByIds, // copy multiple managers
      );

  factory User.create() => User(
        id: const Uuid().v4(),
        createdOn: ServerTimeState().utcTime,
        createdByUserId: LoginState.userId,
        userTypeId: '',
        lastName: '',
        firstName: '',
        username: '',
        isContact: false,
        payRateFrequency: PayRateFrequency.hourly.index,
        languageKey: 'en-US',
      );

  factory User.createEmployee() => User.create()
    ..isContact = false
    ..userTypeId = UserType.employeeId;

  factory User.createContact() => User.create()
    ..isContact = true
    ..userTypeId = UserType.contactId;
}

enum PayRateFrequency { hourly, weekly, yearly }
