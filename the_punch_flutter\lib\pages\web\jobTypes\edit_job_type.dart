import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../dataModel/data/job_type.dart';
import '../../../dataModel/data_model.dart';
import '../../../misc/app_localization.dart';
import '../../view_model_mixin.dart';
import '../../../widgets/active_toggle.dart';
import '../../../widgets/decorated_text_field.dart';
import '../my_scaffold.dart';

class EditJobTypePage extends StatelessWidget {
  final String jobTypeId;
  final String anything;

  EditJobTypePage(Map<String, String> queryParams, {super.key})
      : jobTypeId = queryParams['id'] ?? '',
        anything = queryParams['anything'] ?? '';

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
        create: (context) => _ViewModel(jobTypeId),
        child: MyScaffold(
          title: anything == '1'
              ? AppLocalization.of(context).addJobType
              : AppLocalization.of(context).editJobType,
          body: const _Body(),
        ),
      );
}

class _Body extends StatelessWidget {
  const _Body({super.key});

  @override
  Widget build(BuildContext context) => Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 800),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const _BodyHeader(),
                  const SizedBox(height: 16),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.2),
                          spreadRadius: 2,
                          blurRadius: 6,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    padding: const EdgeInsets.all(16),
                    child: const FocusScope(child: _Row1()),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
}

class _BodyHeader extends StatelessWidget {
  const _BodyHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final headline6 = Theme.of(context)
        .textTheme
        .titleLarge
        ?.copyWith(fontWeight: FontWeight.bold);
    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final jobType = viewModel.jobType;
        if (jobType == null) return Container();
        final title = jobType.name.isEmpty
            ? AppLocalization.of(context).jobType
            : jobType.name;
        return Stack(
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Text(title, style: headline6),
              ),
            ),
            Align(
              alignment: Alignment.centerRight,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Moved the ActiveSwitch here
                  ActiveSwitch(
                    value: jobType.isActive,
                    onChanged: (value) => viewModel.setActive(value),
                    enableDialog: false,
                  ),
                  const SizedBox(width: 10),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blueAccent,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10)),
                    ),
                    onPressed: () async {
                      Navigator.of(context).pop();
                      await viewModel.save();
                    },
                    child: Text(AppLocalization.of(context).save),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}

class _Row1 extends StatelessWidget {
  const _Row1({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;
    final textSize = isMobile ? 12.0 : 16.0;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center, 
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 25),
          Consumer<_ViewModel>(
            builder: (context, viewModel, child) {
              final jobType = viewModel.jobType;
              if (jobType == null) return Container();

              return Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Wrap(
                    alignment: WrapAlignment.center,
                    spacing: 16,
                    runSpacing: 16,
                    children: [
                      SizedBox(
                        width: isMobile ? double.infinity : 300,
                        child: DecoratedTextField(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          initialValue: jobType.name,
                          labelText: AppLocalization.of(context).jobType,
                          textStyle: TextStyle(fontSize: textSize),
                          validator: (value) =>
                              value!.isEmpty ? 'Job type name required.' : null,
                          onChanged: (value) => viewModel.setName(value),
                        ),
                      ),
                      SizedBox(
                        width: isMobile ? double.infinity : 300,
                        child: DecoratedTextField(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          initialValue: jobType.description ?? '',
                          labelText: AppLocalization.of(context).description,
                          textStyle: TextStyle(fontSize: textSize),
                          onChanged: (value) => viewModel.setDescription(value),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // Removed the toggle here since it's now in _BodyHeader
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String jobTypeId;
  JobType? jobType;

  _ViewModel(this.jobTypeId) {
    addListenables([
      DataModel().jobTypeModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    if (jobTypeId.isEmpty) {
      final jobTypeCount = (await DataModel().jobTypeModel.all).length;
      jobType = JobType.create()..order = jobTypeCount + 1;
    } else {
      jobType = await DataModel().jobTypeModel.getById(jobTypeId);
    }
    notifyListeners();
  }

  void setName(String value) {
    if (jobType == null) return;
    jobType!.name = value;
    notifyListeners();
  }

  void setDescription(String value) {
    if (jobType == null) return;
    jobType!.description = value;
    notifyListeners();
  }

  void setActive(bool value) {
    if (jobType == null) return;
    jobType!.isActive = value;
    notifyListeners();
  }

  Future<void> save() async {
    if (jobType == null) return;
    await DataModel().jobTypeModel.saveDirty([jobType!]);
  }
}