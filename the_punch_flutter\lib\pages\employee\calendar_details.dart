import 'dart:async';
import 'dart:io' show Platform; // For Android/iOS checks
import 'package:community_material_icon/community_material_icon.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart'; // For launching native navigation
import 'package:maps_launcher/maps_launcher.dart'; // For pinned map launch

import '../../dataModel/data/location.dart';
import '../../dataModel/data/note.dart';
import '../../dataModel/data/punch_card.dart';
import '../../dataModel/data/schedule.dart';
import '../../dataModel/data_model.dart';
import '../../helpers/screen_helper.dart';
import '../view_model_mixin.dart';
import '../../state/punch_state.dart';
import '../../widgets/date_pill.dart';
import '../../widgets/icon_tile.dart';
import '../../misc/extensions.dart';
import '../web/my_scaffold.dart';
import 'package:intl/intl.dart'; // For date formatting
import '../../helpers/color_helper.dart';

class CalendarDetailsPage extends StatelessWidget {
  final String scheduleId;

  CalendarDetailsPage(Map<String, String> queryParms, {super.key})
      : scheduleId = queryParms['id'] ?? '';

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider(
        create: (context) => _ViewModel(scheduleId),
        builder: (context, child) => MyScaffold(
          showBackButton: true,
          // 1) Change top title from “Calendar Detail” to “Scheduled”
          title: "Scheduled",
          body: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _Header(),
              Expanded(
                child: _Body(),
              ),
            ],
          ),
        ),
      );
}

class _Header extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Match the styling in punches_details_page.dart if desired
    final primaryTextStyle = theme.textTheme.titleLarge?.copyWith(
      color: ColorHelper.thePunchGray(),
    );
    final calendarTitleStyle = theme.textTheme.bodyMedium?.copyWith(
      fontWeight: FontWeight.bold,
      color: ColorHelper.thePunchGray(),
    );

    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        if (viewModel.schedule == null) {
          return Container();
        }

        final schedule = viewModel.schedule!;
        final dateString = DateFormat.yMMMd(
          Localizations.localeOf(context).toString(),
        ).format(schedule.startDateUtc);

        final location = viewModel.location!;
        final locationName = location.name;
        final scheduleStart = schedule.startDateUtc;
        final scheduleEnd = schedule.endDateUtc;
        final scheduleDuration = scheduleEnd.difference(scheduleStart);
        final headline6 = theme.textTheme.titleLarge;
        // 2) We removed the old “Calendar Detail” label row here
        //    and only show date/duration info
        return IntrinsicHeight(
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 16.0), // Adjust as needed
                child: Align(
                  child: Text(
                    locationName,
                    style: headline6,
                  ),
                ),
              ),        
              // Row with "Date" on the left and "Duration" on the right
              Padding(
                padding: const EdgeInsets.all(15),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // Date
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context)!.date,
                            style: calendarTitleStyle,
                          ),
                          Text(
                            dateString,
                            style: primaryTextStyle,
                          ),
                        ],
                      ),
                    ),
                    // Duration
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            AppLocalizations.of(context)!.duration,
                            style: calendarTitleStyle,
                          ),
                          _DurationDisplay(
                            duration: scheduleDuration,
                            mainStyle: primaryTextStyle!,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const Divider(),
            ],
          ),
        );
      },
    );
  }
}

/// Same approach used in punches_details_page.dart to display hh/mm/ss
class _DurationDisplay extends StatelessWidget {
  final Duration duration;
  final TextStyle mainStyle;

  const _DurationDisplay({
    Key? key,
    required this.duration,
    required this.mainStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final int totalSeconds = duration.inSeconds;
    final int hours = totalSeconds ~/ 3600;
    final int remainder = totalSeconds % 3600;
    final int minutes = remainder ~/ 60;
    final int seconds = remainder % 60;

    return Text(
      '${hours}h ${minutes}m ${seconds}s',
      style: mainStyle,
    );
  }
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);
    final theme = Theme.of(context);
    final headline6 = theme.textTheme.titleLarge;
    final body1 = theme.textTheme.titleLarge?.copyWith(
      fontSize: ScreenHelper.screenHeightPercentage(context, 1.7),
    );

    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        if (viewModel.schedule == null) return Container();

        final schedule = viewModel.schedule!;
        final location = viewModel.location!;
        final punchCard = viewModel.punchCard;
        final notes = viewModel.notes;

        // For schedule display
        final scheduleStart = schedule.startDateUtc;
        final scheduleEnd = schedule.endDateUtc;
        final scheduleDuration =
            scheduleEnd.difference(scheduleStart).ceilMinutes;

        // Location details
        final locationName = location.name;
        final address1 = location.address1;
        final address2 = location.address2;
        final address3 = location.address3;

        // Punch details
        final punchedIn = punchCard?.clockedIn;
        final punchedOut = punchCard?.clockedOut;
        final punchDuration = (punchedIn != null && punchedOut != null)
            ? punchedOut.difference(punchedIn).ceilMinutes
            : null;

        final buffer = StringBuffer();
        if (punchedIn != null) {
          buffer.write('${punchedIn.toFormattedTime(locale)} - ');
        }
        if (punchedOut != null) {
          buffer.write(punchedOut.toFormattedTime(locale));
        }

        final punchInOutString = (buffer.isNotEmpty) ? buffer.toString() : '';
        final punchDurationString =
            (punchDuration != null) ? punchDuration.toFormatted : '';

        // 3) Instead of “Scheduled” for no punch card, we show location name
        //    We do that in `viewModel.getPunchStatusText`
        final String punchedInString = viewModel.getPunchStatusText(context);

        return Center(
          child: Container(
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 250, maxWidth: 500),
            child: Consumer<PunchState>(
              builder: (context, punchStateModel, child) => Stack(
                fit: StackFit.expand,
                children: [
                  // Main content
                  Container(
                    margin: const EdgeInsets.only(bottom: 30),
                    child: ListView(
                      shrinkWrap: true,
                      children: [
                        // Align(
                        //   // 4) This line used to show “Scheduled”
                        //   //    Now it shows location name if no punch card
                        //   child: Text(locationName, style: headline6),
                        // ),
                        IconTile(
                          padding: const EdgeInsets.all(8),
                          leading: const Icon(Icons.dashboard),
                          title: Text(
                            AppLocalizations.of(context)!.schedule,
                            style: body1,
                          ),
                          body: Text(
                            '${scheduleStart.toFormattedTime(locale)} - '
                            '${scheduleEnd.toFormattedTime(locale)}',
                          ),
                          trailing: Text(scheduleDuration.toFormatted),
                        ),
                        IconTile(
                          padding: const EdgeInsets.all(8),
                          leading: const Icon(Icons.pin_drop),
                          title: Text(
                            AppLocalizations.of(context)!.location,
                            style: body1,
                          ),
                          body: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(locationName),
                              if (address1.isNotEmpty) Text(address1),
                              if (address2.isNotEmpty) Text(address2),
                              if (address3.isNotEmpty) Text(address3),
                            ],
                          ),
                          // 5) Remove the mini map button & replace with a single
                          //    button that says “Navigate”
                          trailing: TextButton.icon(
                            onPressed: () => _openNavigation(
                              latitude: location.latitude,
                              longitude: location.longitude,
                              fallbackAddress: '$address1 $address2 $address3',
                            ),
                            icon: Icon(
                              Icons.navigation,
                              color: ColorHelper.thePunchBlue(), // Keep your icon's color
                            ),
                            label: Text(
                              'Navigate',
                              style: TextStyle(color: ColorHelper.thePunchGray()), // Set the text to grey here
                            ),
                          ),
                        ),
                        // IconTile(
                        //   padding: const EdgeInsets.all(8),
                        //   leading: const Icon(Icons.timelapse),
                        //   title: Text(
                        //     AppLocalizations.of(context)!.punchInOut,
                        //     style: body1,
                        //   ),
                        //   body: Text(punchInOutString),
                        //   trailing: Text(punchDurationString),
                        // ),
                        IconTile(
                          padding: const EdgeInsets.all(8),
                          leading: const Icon(Icons.edit),
                          title: Text(
                            AppLocalizations.of(context)!.notes,
                            style: body1,
                          ),
                          body: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              for (final note in notes)
                                Padding(
                                  padding: const EdgeInsets.only(bottom: 4),
                                  child: Text(note.text),
                                )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // (Optional) Punch in/out buttons at the bottom, commented out
                  // if (PunchState.canPunchIn &&
                  //     punchStateModel.isPunchedIn &&
                  //     punchCard != null &&
                  //     punchStateModel.punchCard!.id == punchCard.id)
                  //   Align(
                  //     alignment: Alignment.bottomCenter,
                  //     child: ElevatedButton.icon(
                  //       onPressed: () => punchStateModel.punchOut(),
                  //       icon: const Icon(Icons.schedule),
                  //       label: Text(AppLocalizations.of(context)!.clockOut),
                  //     ),
                  //   ),
                  // if (PunchState.canPunchIn &&
                  //     !punchStateModel.isPunchedIn &&
                  //     punchCard == null)
                  //   Align(
                  //     alignment: Alignment.bottomCenter,
                  //     child: ElevatedButton.icon(
                  //       onPressed: () async {
                  //         var connectivityResult =
                  //             await (Connectivity().checkConnectivity());
                  //         if (connectivityResult == ConnectivityResult.none) {
                  //           // No internet connection
                  //           await showDialog(
                  //             context: context,
                  //             builder: (BuildContext context) => AlertDialog(
                  //               content: const Text(
                  //                   "No Internet Connection, Please Reconnect and Try Again"),
                  //               actions: [
                  //                 TextButton(
                  //                   child: const Text("OK"),
                  //                   onPressed: () {
                  //                     Navigator.of(context).pop();
                  //                   },
                  //                 ),
                  //               ],
                  //             ),
                  //           );
                  //         } else {
                  //           // Potential location proximity check
                  //           unawaited(showDialog(
                  //             context: context,
                  //             builder: (context) => WorkPickerDialog(
                  //               onLocationSelection: (isScheduled, id) {
                  //                 if (isScheduled) {
                  //                   Provider.of<PunchState>(context, listen: false)
                  //                       .schedulePunchIn(scheduleId: id);
                  //                 } else {
                  //                   Provider.of<PunchState>(context, listen: false)
                  //                       .locationPunchIn(locationId: id);
                  //                 }
                  //               },
                  //             ),
                  //           ));
                  //         }
                  //       },
                  //       icon: const Icon(CommunityMaterialIcons.clock_in),
                  //       label: Text(AppLocalizations.of(context)!.clockIn),
                  //     ),
                  //   ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Opens turn-by-turn navigation in Google Maps (Android),
  /// Apple Maps (iOS), or falls back to a universal web link.
  Future<void> _openNavigation({
    required double latitude,
    required double longitude,
    required String fallbackAddress,
  }) async {
    // If we have valid coordinates, try launching a native scheme first
    if (latitude != 0.0 && longitude != 0.0) {
      if (Platform.isAndroid) {
        final uri = Uri.parse('google.navigation:q=$latitude,$longitude');
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri);
          return;
        }
      } else if (Platform.isIOS) {
        final uri = Uri.parse('http://maps.apple.com/?daddr=$latitude,$longitude');
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri);
          return;
        }
      }
      // If neither scheme works or another platform, fallback to a universal web link
      final fallbackUri = Uri.parse(
        'https://www.google.com/maps/dir/?api=1&destination=$latitude,$longitude',
      );
      await launchUrl(fallbackUri, mode: LaunchMode.externalApplication);
    } else {
      // If no lat/long provided, fallback to textual address
      final fallbackUri = Uri.parse(
        'https://www.google.com/maps/dir/?api=1&destination=${Uri.encodeFull(fallbackAddress)}',
      );
      await launchUrl(fallbackUri, mode: LaunchMode.externalApplication);
    }
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String scheduleId;
  Schedule? schedule;
  Location? location;
  PunchCard? punchCard;
  Iterable<Note> notes = [];

  _ViewModel(this.scheduleId) {
    addListenables([
      DataModel().scheduleModel,
      DataModel().locationModel,
      DataModel().punchCardModel,
      DataModel().noteModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    schedule = await DataModel().scheduleModel.getById(scheduleId);
    notes = [];
    if (schedule?.locationId != null) {
      location = await DataModel().locationModel.getById(schedule!.locationId);
      notes = await DataModel().noteModel.getByLocationId(schedule!.locationId);
    }
    punchCard = await DataModel().punchCardModel.getByScheduleId(scheduleId);
    notifyListeners();
  }

  /// A convenient helper to display punch status text.
  /// Instead of “Scheduled” for no punchCard, we show the location name.
  String getPunchStatusText(BuildContext context) {
    // location may be null if data not loaded yet
    final locName = location?.name ?? "";
    if (punchCard == null) {
      // was “Scheduled”, now show location name
      return locName;
    } else if (punchCard?.clockedOut == null) {
      return AppLocalizations.of(context)!.punchedIn;
    } else {
      return AppLocalizations.of(context)!.punchedOut;
    }
  }
}
