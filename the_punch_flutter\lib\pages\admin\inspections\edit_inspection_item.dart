import 'dart:async';

import 'package:async/async.dart';
import 'package:camera/camera.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../api/assets_model.dart';
import '../../../dataModel/data_model.dart';
import '../../../dataModel/data/inspection.dart';
import '../../../dialogs/handheld/take_photo_dialog.dart';
import '../../../misc/my_platform.dart';
import '../../view_model_mixin.dart';
import '../../../state/server_time_state.dart';
import '../../../widgets/my_body.dart';
import '../../../widgets/photos_viewer.dart';
import '../../web/my_scaffold.dart';

class EditInspectionItemPage extends StatelessWidget {
  final String id;
  final String newInspection;

  EditInspectionItemPage(Map<String, String> queryParams, {super.key})
      : id = queryParams['id'] ?? '',
        newInspection = queryParams['newInspection'] ?? '';

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider(
        create: (context) => _ViewModel(id),
        builder: (context, child) => MyScaffold(
          title: newInspection == '1'
              ? AppLocalizations.of(context)!.createNewInspection
              : AppLocalizations.of(context)!.editInspection,
          body: MyBody(header: _Header(), body: _Body()),
        ),
      );
}

class _Header extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onPrimary = theme.colorScheme.onPrimary;
    final headline6 = theme.textTheme.titleLarge?.copyWith(color: onPrimary);

    return Consumer<_ViewModel>(
        builder: (context, data, child) => Center(
                child: Padding(
              padding: const EdgeInsets.all(8),
              child: Text(data.inspectionItem?.name ?? '', style: headline6),
            )));
  }
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Center(
          child: Flex(
        direction: Axis.vertical,
        children: [
          const Flexible(flex: 0, child: Padding(padding: EdgeInsets.all(8), child: _GradeButtons())),
          Flexible(flex: 0, child: Padding(padding: const EdgeInsets.all(8), child: _Note())),
          const Expanded(child: _Thumbnails()),
          const Flexible(flex: 0, child: _AddPhoto()),
        ],
      ));
}

class _GradeButtons extends StatelessWidget {
  const _GradeButtons();

  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(builder: (context, viewModel, child) {
        if (viewModel.inspectionItem == null) return Container();
        final inspectionItem = viewModel.inspectionItem!;

        final isSelected = [1, 2].map((i) => i == inspectionItem.grade).toList();

        return ToggleButtons(
          borderRadius: BorderRadius.circular(20),
          onPressed: (int index) async {
            for (var buttonIndex = 0; buttonIndex < isSelected.length; buttonIndex++) {
              if (buttonIndex == index) {
                isSelected[buttonIndex] = !isSelected[buttonIndex];
              } else {
                isSelected[buttonIndex] = false;
              }
            }
            final selectedButton = [isSelected.indexOf(true)].map((e) => e == -1 ? null : e + 1).first;
            await viewModel.setGrade(selectedButton);
          },
          isSelected: isSelected,
          children: const [
            Padding(
                padding: EdgeInsets.symmetric(horizontal: 8),
                child: Row(children: [
                  Icon(Icons.thumb_down, color: Colors.black),
                  Padding(
                    padding: EdgeInsets.only(left: 8),
                    child: Text('Bad'),
                  )
                ])),
            Padding(
                padding: EdgeInsets.symmetric(horizontal: 8),
                child: Row(children: [
                  Icon(Icons.thumb_up, color: Colors.black),
                  Padding(
                    padding: EdgeInsets.only(left: 8),
                    child: Text('Good'),
                  )
                ])),
          ],
        );
      });
}

class _Note extends StatefulWidget {
  @override
  _NoteState createState() => _NoteState();
}

class _NoteState extends State<_Note> {
  TextEditingController? _textController;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cardColor = theme.cardColor;

    return Consumer<_ViewModel>(builder: (context, viewModel, child) {
      if (viewModel.inspectionItem == null) return Container();
      if (_textController == null) {
        _textController = TextEditingController(text: viewModel.inspectionItem?.note ?? '');
        _textController!.addListener(() {
          unawaited(viewModel.setNote(_textController!.text));
        });
      }
      return TextField(
        decoration: InputDecoration(
          labelText: AppLocalizations.of(context)!.note,
          border: const OutlineInputBorder(),
          fillColor: cardColor,
          filled: true,
        ),
        keyboardType: TextInputType.multiline,
        minLines: 5,
        maxLines: 5,
        controller: _textController,
      );
    });
  }
}

class _Thumbnails extends StatelessWidget {
  const _Thumbnails();

  @override
  Widget build(BuildContext context) => Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: LayoutBuilder(
            builder: (context, layout) => Consumer<_ViewModel>(
                builder: (context, viewModel, _) => GridView.count(
                      crossAxisSpacing: 8,
                      mainAxisSpacing: 8,
                      crossAxisCount: layout.maxWidth ~/ 84,
                      children: [
                        ...viewModel.inspectionImages.map((e) => _ImageThumbnail(e.id)).toList(),
                      ],
                    ))),
      );
}

class _AddPhoto extends StatelessWidget {
  const _AddPhoto();

  @override
  Widget build(BuildContext context) => Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: FutureBuilder<bool>(
          initialData: false,
          future: Future(() async => await _cameraAvailable()),
          builder: (context, snapshot) => ElevatedButton.icon(
                onPressed: (snapshot.data!) ? () async => await _addPhotoTapped(context) : null,
                icon: const Icon(Icons.add_a_photo),
                label: Text(AppLocalizations.of(context)!.addPhoto),
              )));

  Future<bool> _cameraAvailable() async {
    if (!MyPlatform.isHandheld) return false;
    return (await availableCameras()).isNotEmpty;
  }

  Future<void> _addPhotoTapped(BuildContext context) async {
    final viewModel = context.read<_ViewModel>();

    await showDialog(
        context: context,
        builder: (context) => TakePhotoDialog(photoTaken: (id) async => await viewModel.addPhoto(id)));
  }
}

class _ImageThumbnail extends StatelessWidget {
  final String id;
  const _ImageThumbnail(this.id);

  @override
  Widget build(BuildContext context) {
    final viewModel = context.read<_ViewModel>();
    final ids = viewModel.inspectionImages.map((e) => e.id).toList();
    return FutureBuilder<Image?>(
      future: Future(() async => await AssetsModel().getImage(id)),
      builder: (context, snapshot) {
        final image = snapshot.data;
        if (!snapshot.hasData || image == null) return const Placeholder();
        return GestureDetector(
            onTap: () async => await showDialog(
                context: context,
                builder: (context) => PhotosViewer(
                      ids: ids,
                      initialPhoto: ids.indexOf(id),
                      deletePhoto: (id) async => await viewModel.setImageActive(id, false),
                    )),
            child: FittedBox(
              clipBehavior: Clip.hardEdge,
              fit: BoxFit.cover,
              child: image,
            ));
      },
    );
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String id;

  Inspection? inspection;
  InspectionItem? inspectionItem;
  List<InspectionImage> inspectionImages = [];

  _ViewModel(this.id) {
    addListenables([DataModel().inspectionItemModel, DataModel().inspectionImageModel]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    inspectionItem = await DataModel().inspectionItemModel.getById(id);
    inspectionImages = (await DataModel().inspectionImageModel.getActiveByInspectionItemId(id)).toList();
    inspectionImages.sort((a, b) => a.order.compareTo(b.order));
    inspection = await DataModel().inspectionModel.getById(inspectionItem!.inspectionId);
    notifyListeners();
  }

  Future<void> setGrade(int? grade) async {
    if (inspectionItem!.grade != grade) {
      inspectionItem!.grade = grade;
      inspectionItem!.lastChangedOn = ServerTimeState().utcTime;
      inspection!.lastChangedOn = ServerTimeState().utcTime;
      await DataModel().inspectionModel.saveDirty([inspection!]);
      await DataModel().inspectionItemModel.saveDirty([inspectionItem!]);
    }
    notifyListeners();
  }

  RestartableTimer? _timer;

  Future<void> setNote(String note) async {
    if (inspectionItem!.note != note) {
      inspectionItem!.note = note;
      inspectionItem!.lastChangedOn = ServerTimeState().utcTime;
      inspection!.lastChangedOn = ServerTimeState().utcTime;

      // wait for 5 seconds before saving so we don't create a ton of messages as the user types
      // if we're already waiting, reset the wait timer to 5 seconds
      if (_timer != null) {
        _timer?.reset();
        return;
      }
      _timer = RestartableTimer(const Duration(seconds: 5), () async {
        _timer = null;
        await DataModel().inspectionModel.saveDirty([inspection!]);
        await DataModel().inspectionItemModel.saveDirty([inspectionItem!]);
      });
    }
  }

  Future<void> addPhoto(String id) async {
    if (inspectionItem == null) return;
    final inspectionImage = InspectionImage.create()
      ..id = id
      ..inspectionId = inspectionItem!.inspectionId
      ..inspectionItemId = inspectionItem!.id
      ..order = inspectionImages.length + 1;
    inspectionItem!.lastChangedOn = ServerTimeState().utcTime;
    inspection!.lastChangedOn = ServerTimeState().utcTime;
    await DataModel().inspectionModel.saveDirty([inspection!]);
    await DataModel().inspectionImageModel.saveDirty([inspectionImage]);
    notifyListeners();
  }

  Future<void> setImageActive(String id, bool isActive) async {
    final inspectionImage = inspectionImages.firstWhere((e) => e.id == id);
    inspectionImage.isActive = isActive;
    await DataModel().inspectionImageModel.saveDirty([inspectionImage]);
    inspectionImages.remove(inspectionImage);
    notifyListeners();
  }
}
