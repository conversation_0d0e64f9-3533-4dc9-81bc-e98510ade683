import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import '../dataModel/data/inspection_template.dart';
import '../dataModel/data_model.dart';
import 'constrained_search_dialog.dart';
import '../misc/change_notification_builder.dart';
import '../pages/view_model_mixin.dart';
import '../widgets/padded_card.dart';

class InspectionTemplatesDialog extends StatefulWidget {
  final Function(String) templateSelected;
  final String locationId;

  const InspectionTemplatesDialog(
      {super.key, required this.templateSelected, required this.locationId});

  @override
  State<InspectionTemplatesDialog> createState() =>
      _InspectionTemplatesDialogState();
}

class _InspectionTemplatesDialogState extends State<InspectionTemplatesDialog> {
  final search = ValueNotifier<String>('');

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline6 = theme.textTheme.titleLarge;

    return ChangeNotifierBuilder<_ViewModel>(
      create: (context) => _ViewModel(locationId: widget.locationId),
      builder: (context, viewModel, child) {
        if (viewModel.inspectionTemplates.isEmpty) return Container();
        return ConstrainedSearchDialog(
          title:
              Text(AppLocalizations.of(context)!.inspections, style: headline6),
          builder: (search) {
            final visibleInspectionTemplates =
                viewModel.inspectionTemplates.where(
              (e) => _inspectionTemplateMatches(e, search),
            );
            return visibleInspectionTemplates.map(
              (e) => _InspectionTemplateTile(
                  inspectionTemplate: e,
                  templateSelected: widget.templateSelected),
            );
          },
        );
      },
    );
  }

  bool _inspectionTemplateMatches(
          InspectionTemplate inspectionTemplate, String search) =>
      search.isEmpty ||
      inspectionTemplate.name.toLowerCase().contains(search.toLowerCase());
}

class _InspectionTemplateTile extends StatelessWidget {
  const _InspectionTemplateTile(
      {required this.templateSelected, required this.inspectionTemplate});

  final InspectionTemplate inspectionTemplate;
  final Function(String) templateSelected;

  @override
  Widget build(BuildContext context) => GestureDetector(
        onTap: () => templateSelected(inspectionTemplate.id),
        child: PaddedCard(
          elevation: 1,
          color: Colors.white,
          child: Center(child: Text(inspectionTemplate.name)),
        ),
      );
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String locationId;
  List<InspectionTemplate> inspectionTemplates = [];

  _ViewModel({required this.locationId}) {
    addListenables([DataModel().inspectionTemplateModel]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    inspectionTemplates.addAll(
        await DataModel().inspectionTemplateModel.getByLocationId(null));
    inspectionTemplates.addAll(
        await DataModel().inspectionTemplateModel.getByLocationId(locationId));
    notifyListeners();
  }
}
