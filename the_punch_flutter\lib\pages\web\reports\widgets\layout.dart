import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../dataModel/data/alert.dart';
import 'InfoComponent.dart';
import 'AlertComponent.dart';
import 'ActionComponent.dart';

class Layout extends StatefulWidget {
  final List<Map<String, dynamic>>? alertsData;
  final String? userName;
  const Layout({super.key, this.alertsData, required this.userName});

  @override
  State<Layout> createState() => _LayoutState();
}

class _LayoutState extends State<Layout> {
  List<Map<String, dynamic>>? _alertsData;

  @override
  void initState() {
    super.initState();
    _alertsData = widget.alertsData;
  }

  @override
  void didUpdateWidget(Layout oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.alertsData != oldWidget.alertsData) {
      setState(() {
        _alertsData = widget.alertsData;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_alertsData != null && _alertsData!.isNotEmpty) {
      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: _buildAlertRows(),
        ),
      );
    }
    return const Text("Select a user to see alerts");
  }

  List<Widget> _buildAlertRows() {
    var rows = <Widget>[];
    rows.add(Text(
      widget.userName ?? 'Unknown Employee',
      style: Theme.of(context).textTheme.titleLarge,
    ));
    rows.add(const SizedBox(height: 16));

    final filteredAlertsData = <Map<String, dynamic>>[];
    for (final alertData in _alertsData!) {
      final alerts = List<Alert>.from(alertData['alerts']);
      final filteredAlerts = alerts.where((alert) =>
        !['80bf39b9-1161-4ce0-bf51-2fe8f5db75ef'].contains(alert.alertTypeId.toLowerCase())
      ).toList()
        ..sort((a, b) => a.alertOn.compareTo(b.alertOn));
      if (filteredAlerts.isNotEmpty) {
        final filteredAlertData = Map<String, dynamic>.from(alertData);
        filteredAlertData['alerts'] = filteredAlerts;
        filteredAlertsData.add(filteredAlertData);
      }
    }

    for (var i = 0; i < filteredAlertsData.length; i++) {
      final alertData = filteredAlertsData[i];
      final alerts = List<Alert>.from(alertData['alerts'])
        ..sort((a, b) => a.alertOn.compareTo(b.alertOn));
      if (i > 0) {
        rows.add(const SizedBox(height: 32));
      }
      rows.add(
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  InfoComponent(
                    id: alertData['punchCard'].id,
                    date: DateFormat('MMM dd').format(alertData['punchCard'].clockedIn.toLocal()),
                    alertCount: alerts.length,
                    breachedTime: alertData['breachedTime'],
                  ),
                  ActionComponent(punchCard: alertData['punchCard'])
                ],
              ),
              const SizedBox(height: 16),
              Column(
                children: _buildAlertComponents(alerts, alertData['breachedTime'], i),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      );
    }
    return List<Widget>.from(rows);
  }

  // Helper functions moved outside _buildAlertComponents for clarity
  bool _isScheduleAlert(Alert alert) {
    final id = alert.alertTypeId.toUpperCase();
    return id == 'ED3D03ED-EDEE-409A-8DB8-030A183934E0' ||
        id == 'C6F68F26-D161-41B5-A6B8-9DDA683BF904' ||
        id == '22C5F8A2-C660-411F-AA91-12A7DF664C1F' ||
        id == '0A9CB952-FDB5-4CA1-AD2F-FCCFB13037AF' ||
        id == '3E668A44-6995-4DBF-BB20-593D7E2FEA57';
  }

  bool _isGeofenceAlert(Alert alert) {
    final id = alert.alertTypeId.toUpperCase();
    return id == '2D8EA258-9199-4375-81FC-61414128ABD1' ||
        id == '883EBCC9-81B2-4769-ACFA-7F3A0E9BEEB1';
  }

  bool _isBreach(Alert alert) =>
      alert.alertTypeId.toUpperCase() == '2D8EA258-9199-4375-81FC-61414128ABD1';
  bool _isReEnter(Alert alert) =>
      alert.alertTypeId.toUpperCase() == '883EBCC9-81B2-4769-ACFA-7F3A0E9BEEB1';
  bool _noPunchIn(Alert alert) =>
      alert.alertTypeId.toUpperCase() == '3E668A44-6995-4DBF-BB20-593D7E2FEA57';

  List<Widget> _buildAlertComponents(List alertData, breachTime, int outerIndex) {
    final alertComponents = <Widget>[];
    for (var i = 0; i < alertData.length; i++) {
      final alert = alertData[i];
      if (_isBreach(alert)) {
        alertComponents.add(
          AlertComponent(
            alertNumber: 'ALERT ${i + 1}',
            alertTitle: _isBreach(alert)
                ? 'Breached Geofence'
                : _isGeofenceAlert(alert)
                    ? 'Re-Entered Geofence'
                    : _isScheduleAlert(alert)
                        ? "Schedule Alert"
                        : _isReEnter(alert)
                            ? "Reentry Alert"
                            : "FOO",
            time: DateFormat('h:mma').format(alert.alertOn.toLocal()),
            duration: alertData.length % 2 == 1
                ? breachTime
                : (i + 1 < alertData.length
                    ? formatDuration(alertData[i + 1].alertOn.toLocal().difference(alertData[i].alertOn.toLocal()))
                    : ''),
            isReEntry: _isBreach(alert),
            index: i,
            reEntryTime: alertData.length % 2 == 1
                ? DateFormat('h:mma').format(alertData[0].punchCard.clockedOut.toLocal())
                : (i + 1 < alertData.length
                    ? DateFormat('h:mma').format(alertData[i + 1].alertOn.toLocal())
                    : ''),
            alertLength: alertData.length,
          ),
        );
      }
      if (_noPunchIn(alert)) {
        alertComponents.add(
          AlertComponent(
            alertNumber: 'ALERT ${i + 1}',
            alertTitle: 'No Punch In',
            time: DateFormat('h:mma').format(alert.alertOn.toLocal()),
            duration: '0m 0s',
            isReEntry: false,
            index: i,
            reEntryTime: '',
            alertLength: alertData.length,
            showRight: false,
          ),
        );
      }
    }
    return alertComponents;
  }
}

String formatDuration(Duration duration) {
  int minutes = duration.inMinutes.remainder(60);
  int seconds = duration.inSeconds.remainder(60);
  return '${minutes}m ${seconds}s';
}