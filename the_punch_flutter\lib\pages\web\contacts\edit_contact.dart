import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../../dataModel/data/location_contact.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data_model.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../../helpers/color_helper.dart';
import '../../view_model_mixin.dart';
import '../../../widgets/active_toggle.dart';
import '../../../widgets/decorated_text_field.dart';
import '../../../misc/extensions.dart';
import '../home/<USER>';
import '../my_scaffold.dart';

class EditContactPage extends StatelessWidget {
  final String userId;
  final String locationId;
  final String anything;

  EditContactPage(Map<String, String> queryParms, {super.key})
      : userId = queryParms['id'] ?? '',
        locationId = queryParms['locationId'] ?? '',
        anything = queryParms['anything'] ?? '';

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
        create: (context) => _ViewModel(userId, locationId),
        child: MyScaffold(
          title: anything == '1'
              ? AppLocalizations.of(context)!.addContact
              : AppLocalizations.of(context)!.editContact,
          body: _Body(),
        ),
      );
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1300),
          child: ScrollConfiguration(
            behavior: CustomScrollBehavior(),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 16.0),
                child: FocusScope(
                  child: Column(
                    children: [
                      _BodyHeader(),
                      _Section(
                        title: "Personal Details",
                        child: _Row1(),
                      ),
                      const SizedBox(height: 25),
                      _Section(
                        title: "Contact Info",
                        child: _Row2(),
                      ),
                      const SizedBox(height: 75),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      );
}

class CustomScrollBehavior extends ScrollBehavior {
  @override
  Widget buildViewportChrome(
      BuildContext context, Widget child, AxisDirection axisDirection) {
    return child; // Makes the scrollbar invisible
  }
}

class _Section extends StatelessWidget {
  final String title;
  final Widget child;

  const _Section({required this.title, required this.child});

  // Helper method to get appropriate icon for each section
  IconData _getIconForSection(String title) {
    switch (title) {
      case 'Personal Details':
        return Icons.person_outline;
      case 'Contact Info':
        return Icons.contact_phone_outlined;
      default:
        return Icons.info_outline;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title with icon
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _getIconForSection(title),
                size: 20,
                color: Colors.black87,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                      fontSize: 18,
                      color: Colors.black87,
                    ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        // Section content
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[50], // Light gray background
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.all(16),
          child: isMobile
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [child],
                )
              : child,
        ),
      ],
    );
  }
}

class _BodyHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final headline6 = Theme.of(context)
        .textTheme
        .titleLarge
        ?.copyWith(fontWeight: FontWeight.bold, color: Colors.black87);
    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final contact = viewModel.contact;
        if (contact == null) return Container();
        final title = contact.name.isEmpty
            ? AppLocalizations.of(context)!.contact
            : contact.name;
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                 contact.name,
                style: headline6,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Cancel button:
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 30),
                        backgroundColor: ColorHelper.thePunchAdminButtonBlue(),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(15),
                        ),
                      ),
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        AppLocalizations.of(context)!.cancel,
                        style: Theme.of(context).textTheme.labelLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    // Save button and toggle
                    Row(
                      children: [
                        // Toggle Active
                        ActiveSwitch(
                          value: contact.isActive,
                          onChanged: (value) => viewModel.setActive(value),
                          enableDialog: false,
                        ),
                        const SizedBox(width: 16),
                        // Save button
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 30),
                            backgroundColor: Colors.red,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15),
                            ),
                          ),
                          onPressed: !viewModel.canSave
                              ? null
                              : () async {
                                  final navigator = Navigator.of(context);
                                  await viewModel.save();
                                  navigator.pop();
                                },
                          child: Text(
                            AppLocalizations.of(context)!.save,
                            style: Theme.of(context).textTheme.labelLarge?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}


class _Row1 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    final textSize = isMobile ? 12.0 : 16.0;

    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final contact = viewModel.contact;
        if (contact == null) return Container();

        return isMobile
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildFirstNameField(context, viewModel, textSize),
                  const SizedBox(height: 12),
                  _buildLastNameField(context, viewModel, textSize),
                ],
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    children: [
                      Expanded(
                          child: _buildFirstNameField(context, viewModel, textSize)),
                      const SizedBox(width: 12),
                      Expanded(
                          child: _buildLastNameField(context, viewModel, textSize)),
                    ],
                  ),
                ],
              );
      },
    );
  }

  Widget _buildFirstNameField(
    BuildContext context,
    _ViewModel viewModel,
    double textSize,
  ) =>
      DecoratedTextField(
        padding: const EdgeInsets.symmetric(vertical: 8),
        initialValue: viewModel.contact?.firstName,
        labelText: AppLocalizations.of(context)!.firstName,
        textStyle: TextStyle(fontSize: textSize),
        validator: (value) => value!.isEmpty
            ? AppLocalizations.of(context)!.firstNameRequired
            : null,
        autofocus: true,
        onChanged: viewModel.setFirstName,
      );

  Widget _buildLastNameField(
    BuildContext context,
    _ViewModel viewModel,
    double textSize,
  ) =>
      DecoratedTextField(
        padding: const EdgeInsets.symmetric(vertical: 8),
        initialValue: viewModel.contact?.lastName,
        labelText: AppLocalizations.of(context)!.lastName,
        textStyle: TextStyle(fontSize: textSize),
        validator: (value) => value!.isEmpty
            ? AppLocalizations.of(context)!.lastNameRequired
            : null,
        onChanged: viewModel.setLastName,
      );
}

class _Row2 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    final textSize = isMobile ? 12.0 : 16.0;

    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final contact = viewModel.contact;
        if (contact == null) return Container();

        return isMobile
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildPhoneField(context, viewModel, textSize),
                  const SizedBox(height: 12),
                  _buildEmailField(context, viewModel, textSize),
                ],
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    children: [
                      Expanded(
                          child: _buildPhoneField(context, viewModel, textSize)),
                      const SizedBox(width: 12),
                      Expanded(
                          child: _buildEmailField(context, viewModel, textSize)),
                    ],
                  ),
                ],
              );
      },
    );
  }

  Widget _buildPhoneField(
    BuildContext context,
    _ViewModel viewModel,
    double textSize,
  ) =>
      DecoratedTextField(
        padding: const EdgeInsets.symmetric(vertical: 8),
        initialValue: viewModel.contact?.phone ?? '',
        labelText: AppLocalizations.of(context)!.phone,
        textStyle: TextStyle(fontSize: textSize),
        onChanged: viewModel.setPhone,
        validator: (value) => value == null ||
                value.isEmpty ||
                value.validatePhone
            ? null
            : 'Invalid phone number.',
      );

  Widget _buildEmailField(
    BuildContext context,
    _ViewModel viewModel,
    double textSize,
  ) =>
      DecoratedTextField(
        padding: const EdgeInsets.symmetric(vertical: 8),
        initialValue: viewModel.contact?.emailAddress ?? '',
        labelText: AppLocalizations.of(context)!.emailAddress,
        textStyle: TextStyle(fontSize: textSize),
        onChanged: viewModel.setEmail,
        validator: (value) => value == null ||
                value.isEmpty ||
                value.validateEmail
            ? null
            : 'Invalid email address.',
      );
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String userId;
  final String locationId;
  User? contact;

  _ViewModel(this.userId, this.locationId) {
    addListenables([
      DataModel().userModel,
      DataModel().userTypeModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    if (userId.isEmpty) {
      contact = User.createContact();
    } else {
      contact = await DataModel().userModel.getById(userId);
      contact = User.from(contact!);
    }

    notifyListeners();
  }

  void setFirstName(String value) {
    if (contact == null) return;
    contact!.firstName = value;
    contact!.isDirty = true;
    notifyListeners();
  }

  void setLastName(String value) {
    if (contact == null) return;
    contact!.lastName = value;
    contact!.isDirty = true;
    notifyListeners();
  }

  void setEmail(String value) {
    if (contact == null) return;
    contact!.emailAddress = value;
    contact!.isDirty = true;
    notifyListeners();
  }

  void setPhone(String value) {
    if (contact == null) return;
    contact!.phone = value;
    contact!.isDirty = true;
    notifyListeners();
  }

  void setActive(bool value) {
    if (contact == null) return;
    contact!.isActive = value;
    contact!.isDirty = true;
    notifyListeners();
  }

  Future<void> save() async {
    if (contact == null) return;
    if (locationId.isNotEmpty) {
      final locationContact = LocationContact.create()
        ..userId = contact!.id
        ..locationId = locationId;
      await DataModel().locationContactModel.saveDirty([locationContact]);
    }
    if (contact!.isDirty) await DataModel().userModel.saveDirty([contact!]);
  }

  bool get canSave {
    if (contact!.firstName.isEmpty) return false;
    if (contact!.lastName.isEmpty) return false;
    if (contact!.phone != null &&
        contact!.phone!.isNotEmpty &&
        !contact!.phone!.validatePhone) return false;
    if (contact!.emailAddress != null &&
        contact!.emailAddress!.isNotEmpty &&
        !contact!.emailAddress!.validateEmail) {
      return false;
    }
    return true;
  }
}
