import 'package:json_annotation/json_annotation.dart';

part 'organization.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class Organization {
  @JsonKey(name: 'OrganizationID')
  final String organizationId;
  final String name;

  Organization({required this.organizationId, required this.name});

  factory Organization.fromJson(Map<String, dynamic> json) => _$OrganizationFromJson(json);
  Map<String, dynamic> toJson() => _$OrganizationToJson(this);
}
