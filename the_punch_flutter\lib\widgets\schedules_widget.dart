import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../dataModel/data/location.dart';
import '../dataModel/data/schedule.dart';
import '../dataModel/data/user.dart';
import '../dataModel/data_model.dart';
import '../misc/app_localization.dart';
import '../pages/view_model_mixin.dart';
import '../misc/extensions.dart';
import '../state/permissions_state.dart';
import 'active_toggle.dart';
import 'calendar/calendar_widget.dart';
import 'menu/popup_menu_label.dart';

class SchedulesWidget extends StatelessWidget {
  final Iterable<String>? employeeIds;
  final Iterable<String>? locationIds;
  final ActiveToggleState? active;
  final bool showEmployeeNames;
  final bool scrollable;
  final dynamic selectedDate;

  const SchedulesWidget(
      {super.key,
      this.employeeIds,
      this.locationIds,
      this.active,
      required this.showEmployeeNames,
      this.scrollable = false,
      this.selectedDate});

  @override
  Widget build(BuildContext context) {
    final viewModel = _ViewModel(
        employeeIds: employeeIds, locationIds: locationIds, active: active);
    return ChangeNotifierProvider.value(
      value: viewModel,
      child: Consumer<_ViewModel>(
        builder: (context, viewModel, _) => CalendarWidget(
          selectedDate: selectedDate,
          scrollable: scrollable,
          builder: (context, start, end) async => await _getCalendarEvents(
              context, start, end, showEmployeeNames, viewModel),
        ),
      ),
    );
  }

  Future<Iterable<CalendarEvent>> _getCalendarEvents(
      BuildContext context,
      DateTime start,
      DateTime end,
      bool showEmployeeNames,
      _ViewModel viewModel) async {
    final theme = Theme.of(context);
    final caption = theme.textTheme.bodySmall;
    final activeTextStyle = caption?.copyWith(color: Colors.white);
    final inactiveTextStyle = caption?.copyWith(color: Colors.black87);
    final tooltipStyle = theme.textTheme.bodyLarge?.copyWith(
        color:
            theme.brightness == Brightness.dark ? Colors.black : Colors.white);

    final schedules = (await viewModel.getSchedules(start, end)).toList();
    schedules.sort((a, b) => a.startDateUtc.compareTo(b.startDateUtc));
    return schedules.map((e) => CalendarEvent(
          start: e.startDateUtc,
          end: e.endDateUtc,
          text: _scheduleToString(e, showEmployeeNames, viewModel),
          detailedText: _scheduleToDetailString(e, viewModel),
          tooltip: _scheduleToTooltip(context, e, viewModel),
          tooltipTextStyle: tooltipStyle,
          color: e.isActive ? Colors.green : Colors.yellow,
          textStyle: e.isActive ? activeTextStyle : inactiveTextStyle,
          // onTap: () => AppState().pushState(PageState.viewSchedule(e.id)),
          popupOnSelected: (value) {
            switch (value) {
              case 1:
                GoRouter.of(context).pushNamed('/schedules/view',
                    queryParameters: {'id': e.id});
                break;
              case 2:
                GoRouter.of(context).pushNamed('/schedules/edit',
                    queryParameters: {'id': e.id});
                break;
              case 3:
                // final path = '/schedules/editRepeatingSchedule';
                // final id = e.scheduleTemplateId;
                // final startDate = e.startDateUtc.dateOnly;
                // final queryParameters = {'id': id, 'startDate': startDate.toIso8601String()};
                // final uri = Uri(path: path, queryParameters: queryParameters);
                // final appRouter = AppRouter.instance;
                // appRouter.pushUri(uri);
                context.pushNamed('/schedules/editRepeatingSchedule',
                    queryParameters: {
                      'id': e.scheduleTemplateId,
                      'startDate': e.startDateUtc.dateOnly.toIso8601String()
                    });
                break;
            }
          },
          popupItemBuilder: (context) => [
            PopupMenuLabel(
                child: Text(_scheduleToTooltip(context, e, viewModel))),
            PopupMenuItem<int>(
                value: 1,
                child: Text(AppLocalization.of(context).viewSchedule)),
            if (PermissionsState().editSchedules)
              PopupMenuItem<int>(
                  value: 2,
                  child: Text(AppLocalization.of(context).editThisSchedule)),
            if (PermissionsState().editSchedules &&
                e.startDateUtc >= DateTime.now().toUtc())
              PopupMenuItem<int>(
                  value: 3,
                  child:
                      Text(AppLocalization.of(context).editRepeatingSchedule)),
          ],
        ));
  }

  String _scheduleToTooltip(
      BuildContext context, Schedule schedule, _ViewModel viewModel) {
    final locale = Localizations.localeOf(context);

    final startDate = schedule.startDateUtc.toFormattedDate(locale);
    final startTime = schedule.startDateUtc.toFormattedTime(locale);
    final endTime = schedule.endDateUtc.toFormattedTime(locale);
    final employeeName = viewModel.employeeMap[schedule.userId]?.name ?? '';
    final locationName = viewModel.locationMap[schedule.locationId]?.name ?? '';
    return '$startDate\n$startTime - $endTime\n$employeeName\n$locationName';
  }

  String _scheduleToString(
      Schedule schedule, bool showEmployeeNames, _ViewModel viewModel) {
    if (showEmployeeNames) {
      return viewModel.employeeMap[schedule.userId]?.name ?? '';
    } else {
      return viewModel.locationMap[schedule.locationId]?.name ?? '';
    }
  }

  String _scheduleToDetailString(Schedule schedule, _ViewModel viewModel) =>
      '${viewModel.employeeMap[schedule.userId]?.name ?? ''}\n${viewModel.locationMap[schedule.locationId]?.name ?? ''}';
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final Iterable<String>? employeeIds;
  final Iterable<String>? locationIds;
  final ActiveToggleState? active;

  DateTime start = DateTime.now();
  DateTime end = DateTime.now();
  Map<String, User> employeeMap = {};
  Map<String, Location> locationMap = {};

  _ViewModel(
      {required this.employeeIds,
      required this.locationIds,
      required this.active}) {
    addListenables([
      DataModel().scheduleModel,
      DataModel().locationModel,
      DataModel().userModel,
    ]);
  }

  @override
  Future<void> refresh() async {
    notifyListeners();
  }

  Future<Iterable<Schedule>> getSchedules(DateTime start, DateTime end) async {
    var schedules = (await DataModel().scheduleModel.getBetween(start, end));

    if (employeeIds != null && employeeIds!.isNotEmpty) {
      schedules = schedules.where((e) => employeeIds!.contains(e.userId));
    }
    if (locationIds != null && locationIds!.isNotEmpty) {
      schedules = schedules.where((e) => locationIds!.contains(e.locationId));
    }
    switch (active ?? ActiveToggleState.active) {
      case ActiveToggleState.active:
        schedules = schedules.where((e) => e.isActive);
        break;
      case ActiveToggleState.inactive:
        schedules = schedules.where((e) => !e.isActive);
        break;
      case ActiveToggleState.all:
        break;
    }

    final employeeIds0 = schedules.map((e) => e.userId).toSet();
    employeeIds0.removeWhere((e) => employeeMap.keys.contains(e));
    final employees = (await DataModel().userModel.getByIds(employeeIds0));
    employeeMap.addAll({for (final e in employees) e.id: e});

    final locationIds0 = schedules.map((e) => e.locationId).toSet();
    locationIds0.removeWhere((e) => locationMap.keys.contains(e));
    final locations = (await DataModel().locationModel.getByIds(locationIds0));
    locationMap.addAll({for (final e in locations) e.id: e});

    return schedules;
  }
}
