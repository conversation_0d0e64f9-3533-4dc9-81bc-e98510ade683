import 'dart:async';
import 'package:community_material_icon/community_material_icon.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:the_punch_flutter/dialogs/end_task_dialog.dart';
import 'package:the_punch_flutter/dialogs/start_task_dialog.dart';
import 'package:the_punch_flutter/dialogs/view_punch_card_dialog.dart';
import 'package:the_punch_flutter/pages/employee/employee_widgets/punched_out_body.dart';
import 'package:the_punch_flutter/pages/web/punchCards/punched_in.dart';
import 'package:the_punch_flutter/widgets/duration_display.dart';
import 'package:the_punch_flutter/widgets/jobType.dart';
import 'package:the_punch_flutter/widgets/location_notes.dart';
import 'package:the_punch_flutter/widgets/location_tile.dart';
import 'package:the_punch_flutter/widgets/ticking_duration.dart';
import '../../../../dataModel/data/location_notes.dart';
import '../../api/api_model.dart';
import '../../dataModel/data/user.dart';
import '../../dataModel/data/user_type.dart';
import '../../dataModel/data_model.dart';
import '../../dataModel/data/job_type.dart';
import '../../dataModel/data/location.dart';
import '../../dataModel/data/punch_card.dart';
import '../../dataModel/data/schedule.dart';
import '../../dataModel/models/location_model.dart';
import '../../dataModel/models/location_notes_model.dart';
import '../../dataModel/models/punch_card_model.dart';
import '../../dataModel/models/user_model.dart';
import '../../dataModel/models/user_type_model.dart';
import '../../dialogs/constrained_search_dialog.dart';
import '../../dialogs/contacts_dialog.dart';
import '../../dialogs/notes_dialog.dart';
import '../../helpers/check_session.dart';
import '../../helpers/color_helper.dart';
import '../../helpers/screen_helper.dart';
import '../../helpers/text_style_helper.dart';
import '../../misc/calculate_distance.dart';
import '../../services/location_background_service.dart';
import '../../services/notification_service.dart';
import '../../state/location_state.dart';
import '../../widgets/badged_icon.dart';
import '../../widgets/padded_card.dart';
import '../view_model_mixin.dart';
import '../../state/login_state.dart';
import '../../state/permissions_state.dart';
import '../../state/server_time_state.dart';
import '../../widgets/animated_duration.dart';
import '../../widgets/date_pill.dart';
import '../../state/punch_state.dart';
import '../../misc/extensions.dart';
import '../web/my_scaffold.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../widgets/progress_bar/progress_bar.dart';
import '../../dataModel/models/punch_view_model.dart';
import 'package:material_symbols_icons/material_symbols_icons.dart';
import '../../utils/utils.dart';
import 'location_card.dart';
import 'start_task.dart';
class ClockMain extends StatefulWidget {
  const ClockMain({super.key});

  @override
  ClockMainState createState() => ClockMainState();
}

class ClockMainState extends State<ClockMain> {
  StreamSubscription<String?>? _geofenceSubscription;

  bool selectedisScheduled = false;
  String selectedlocationid = '';
  String? selectedScheduleId;
  bool locationSelected = false;

  @override
  void initState() {
    super.initState();

    // Listen to the StreamController's stream
    _geofenceSubscription =
        geoFenceStreamController.stream.listen(_handleGeoFenceBreach);

 
    //_printAllSharedPreferences();
  }

  void _handleGeoFenceBreach(String? geoFenceStatus) {
    // Print out the stream data for debugging
    print('GeoFence status stream emitted: $geoFenceStatus'); // Debugging

    ScaffoldMessenger.of(context).hideCurrentSnackBar();

    if (geoFenceStatus == 'true') {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                "WARNING: You exited the location area without Punching Out."),
            backgroundColor: Colors.red,
            duration: Duration(days: 365), // Effectively indefinite
          ),
        );
      }
    } else if (geoFenceStatus == 'false') {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text("Reconnected: You are back at the location."),
            backgroundColor: Colors.green,
          ),
        );
      }
    } else {
      ScaffoldMessenger.of(context).hideCurrentSnackBar();
    }
  }

  @override
  void dispose() {
    _geofenceSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => MultiProvider(
        providers: [
          ChangeNotifierProvider<PunchViewModel>(
            create: (context) => PunchViewModel()..initializeData(),
          ),
          ChangeNotifierProvider<PunchState>.value(
            value: PunchState(),
          ),
        ],
        child: Builder(
          builder: (context) => MyScaffold(
            showDrawer: false,
            showNotificationButton: true,
            title: AppLocalizations.of(context)!.clock,
            body: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const _Header(),
                Expanded(
                  child: _Body(onLocationSelected: _locationSelected),
                )
              ],
            ),
          ),
        ),
      );

  void _locationSelected(BuildContext context, bool isScheduled,
      String locationId, String? scheduleId) async {
    if (mounted) {
      setState(() {
        selectedisScheduled = isScheduled;
        selectedlocationid = locationId;
        selectedScheduleId = scheduleId;
        locationSelected = true;
      });
    }
    Navigator.pop(context);
  }
}

class _Header extends StatelessWidget {
  const _Header();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final titleLargeStyle = theme.textTheme.titleLarge?.copyWith();
    final punchCard = context.watch<PunchState>().punchCard;
    return Consumer<PunchState>(
      builder: (context, punchState, child) => Container(
          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
          width: double.infinity, // Ensure the container takes the full width
          child: Row(
            children: [
              Expanded(
                child: Column(
                  children: [
                    SizedBox(
                      height: ScreenHelper.screenHeightPercentage(context, 1),
                    ),
                    if (punchState.isPunchedIn && punchCard?.locationId!='fake-location')
                      const CombinedLocationCard(),
                if (punchState.isPunchedIn && punchCard?.locationId =='fake-location')
                      Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(
                              text: AppLocalizations.of(context)!.punchedIn,
                              style: titleLargeStyle?.copyWith(
                                fontSize: 45,
                                fontWeight: FontWeight.w900,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                      ),
                     if (punchState.isPunchedIn && punchCard?.locationId =='fake-location') 
                      DateExt(
                        date: DateTime.now(),
                      ),  
                    if (!punchState.isPunchedIn)
                      Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(
                              text: AppLocalizations.of(context)!.punchedOut,
                              style: titleLargeStyle?.copyWith(
                                fontSize: 45,
                                fontWeight: FontWeight.w900,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                      ),
                    SizedBox(
                      height: ScreenHelper.screenHeightPercentage(context, 2),
                    ),
                    if (!punchState.isPunchedIn)
                      DateExt(
                        date: DateTime.now(),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
    );
  }
}
class _Body extends StatelessWidget {
  final Function(BuildContext, bool, String, String?) onLocationSelected;

  const _Body({required this.onLocationSelected});

  @override
  Widget build(BuildContext context) => Consumer<PunchViewModel>(
        builder: (context, viewModel, child) =>
            FutureBuilder<ConnectivityResult>(
          future: Connectivity().checkConnectivity(),
          builder: (context, snapshot) {
            if (!snapshot.hasData) {
              return const Center(
                child: Text('Checking connectivity...'),
              );
            }

            if (snapshot.data == ConnectivityResult.none) {
              return Container(
                margin: const EdgeInsets.fromLTRB(0, 150, 0, 50),
                padding: const EdgeInsets.fromLTRB(50, 0, 50, 0),
                child: Column(
                  children: [
                    const Text(
                      'No internet connection, please make sure airplane mode is disabled & reconnect to the internet.',
                      style: TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 10),
                    TextButton(
                      onPressed: () {
                        context.read<PunchViewModel>().initializeData();
                      },
                      child: const Text(
                        'Retry',
                        style: TextStyle(
                          color: Colors.blue,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }

            if (viewModel.punchCard == null &&
                viewModel.prePageLoad == true &&
                viewModel.isLoading == false) {
              // Directly check permission status here
              if (viewModel.permissionStatus == null) {
                return Container(
                  margin: const EdgeInsets.fromLTRB(0, 50, 0, 50),
                  child: const Text(
                    'Loading Permissions...',
                    style: TextStyle(color: Colors.black),
                  ),
                );
              } else if (viewModel.permissionStatus !=
                  LocationPermission.always) {
                return Container(
                  margin: const EdgeInsets.fromLTRB(0, 150, 0, 50),
                  padding: const EdgeInsets.fromLTRB(50, 0, 50, 0),
                  child: Column(
                    children: [
                      const Text(
                        'Please set Location Permissions: "Always"',
                        style: TextStyle(color: Colors.black),
                      ),
                      const Text(
                        'in Settings > The Punch on your device.',
                        style: TextStyle(color: Colors.black),
                      ),
                      const SizedBox(height: 10),
                      TextButton(
                        onPressed: () {
                          context.read<PunchViewModel>().initializeData();
                        },
                        child: const Text(
                          'Refresh',
                          style: TextStyle(
                            color: Colors.blue,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              } else {
                return Container(
                  margin: const EdgeInsets.fromLTRB(0, 50, 0, 50),
                  child: const Text(
                    'Loading...',
                    style: TextStyle(color: Colors.black),
                  ),
                );
              }
            }
            if (viewModel.isLoading) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      viewModel.isPunchingIn
                          ? 'Punching In...'
                          : 'Punching Out...',
                      style: TextStyle(
                          fontSize: 18, color: ColorHelper.thePunchRed()),
                    ),
                    const SizedBox(height: 16),
                    const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
                    ),
                  ],
                ),
              );
            } else {
              // This is needed for if the user is punched in but turned off permissions.
              if (viewModel.permissionStatus != LocationPermission.always) {
                return Container(
                  margin: const EdgeInsets.fromLTRB(0, 150, 0, 50),
                  padding: const EdgeInsets.fromLTRB(50, 0, 50, 0),
                  child: Column(
                    children: [
                      const Text(
                        'Please set Location Permissions: "Always"',
                        style: TextStyle(color: Colors.black),
                      ),
                      const Text(
                        'in Settings > The Punch on your device.',
                        style: TextStyle(color: Colors.black),
                      ),
                      const SizedBox(height: 10),
                      TextButton(
                        onPressed: () async {
                          var permission =
                              await LocationState.checkAndRequestPermissions();
                          // Reload the data only if permission is still not 'always'
                          if (permission != LocationPermission.always) {
                            await context
                                .read<PunchViewModel>()
                                .initializeData();
                          }
                        },
                        child: const Text(
                          'Refresh',
                          style: TextStyle(
                            color: Colors.blue,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              } else {
                return Consumer<PunchState>(
                  builder: (context, punchState, child) => punchState
                          .isPunchedIn
                      ? const PunchedInBody()
                      : PunchedOutBody(onLocationSelected: onLocationSelected),
                );
              }
            }
          },
        ),
      );
}