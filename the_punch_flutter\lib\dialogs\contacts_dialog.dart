import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import '../dataModel/data/location.dart';
import '../dataModel/data/user.dart';
import '../dataModel/data_model.dart';
import 'constrained_dialog.dart';
import '../misc/change_notification_builder.dart';
import '../pages/view_model_mixin.dart';
import '../widgets/icon_tile.dart';
import '../widgets/padded_card.dart';
import 'package:url_launcher/url_launcher.dart' as url_launcher;

class ContactsDialog extends StatelessWidget {
  final String locationId;

  const ContactsDialog({super.key, required this.locationId});

  @override
  Widget build(BuildContext context) => ChangeNotifierBuilder<_ViewModel>(
        create: (context) => _ViewModel(locationId),
        builder: (context, viewModel, child) {
          if (viewModel.location == null) return Container();
          final location = viewModel.location!;
          final contacts = viewModel.contacts;

          return ConstrainedDialog(
            title: AppLocalizations.of(context)!.contacts,
            subTitle: location.name,
            child: Flexible(
              child: ListView(
                shrinkWrap: true,
                children: [
                  for (final contact in contacts) _Tile(contact: contact),
                ],
              ),
            ),
          );
        },
      );
}

class _Tile extends StatelessWidget {
  const _Tile({required this.contact});

  final User contact;

  @override
  Widget build(BuildContext context) => PaddedCard(
        elevation: 0,
        color: Colors.white,
        child: Column(
          children: [
            Text('${contact.firstName} ${contact.lastName}'),
            const Divider(indent: 20, endIndent: 20),
            IconTile(
              leading: const Icon(Icons.phone_android),
              title: Text(AppLocalizations.of(context)!.phone),
              body: Text(contact.phone ?? ''),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed: () => unawaited(
                      url_launcher.launchUrl(
                        Uri(scheme: 'tel', path: '+${contact.phone}'),
                      ),
                    ),
                    icon: const Icon(Icons.phone),
                  ),
                  IconButton(
                    onPressed: () => unawaited(
                      url_launcher.launchUrl(
                        Uri(scheme: 'sms', path: contact.phone),
                      ),
                    ),
                    icon: const Icon(Icons.sms),
                  ),
                ],
              ),
            ),
            if (contact.emailAddress != null)
              IconTile(
                leading: const Icon(Icons.email),
                title: Text(AppLocalizations.of(context)!.email),
                body: Text(contact.emailAddress!),
                trailing: IconButton(
                  onPressed: () => unawaited(
                    url_launcher.launchUrl(
                      Uri(scheme: 'mailto', path: contact.emailAddress),
                    ),
                  ),
                  icon: const Icon(Icons.email),
                ),
                // trailingWidget: IconButton(onPressed: () =>UrlLauncher.launch('mailto:${contact.emailAddress}?subject=News&body=New%20plugin'), icon: Icon(Icons.email)),
              )
          ],
        ),
      );
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String locationId;
  Iterable<User> contacts = [];
  Location? location;

  _ViewModel(this.locationId) {
    addListenables([
      DataModel().locationModel,
      DataModel().userModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    contacts = await DataModel().userModel.getContactsByLocationId(locationId);
    location = await DataModel().locationModel.getById(locationId);
    notifyListeners();
  }
}
