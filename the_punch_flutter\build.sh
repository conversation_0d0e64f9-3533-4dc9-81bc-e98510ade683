#/bin/bash

# note: this script only works on the mac (for now); windows can't create the ios target and windows doesn't have zip

VERSION=`grep version pubspec.yaml | sed 's/.* //' | sed 's/+.*//'`

# clean
flutter upgrade
flutter clean
flutter pub get

# build
flutter build ipa --release
flutter build appbundle --release
flutter build apk --release
flutter build web --release


# deploy to zip file
cd build
rm -rf deploy
mkdir deploy

cd web
zip -r ../web.zip .
cd ..

cp web.zip deploy/ThePunchFlutter-$VERSION.zip
cp -R ios/archive/Runner.xcarchive deploy/ThePunchFlutter-$VERSION.xcarchive
cp app/outputs/apk/release/app-release.apk deploy/ThePunchFlutter-$VERSION.apk
cp app/outputs/bundle/release/app-release.aab deploy/ThePunchFlutter-$VERSION.aab

cd ..
