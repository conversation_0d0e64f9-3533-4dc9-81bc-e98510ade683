import 'dart:async';

import 'package:flutter/material.dart';
import '../dialogs/material_dialog.dart';

class MyBottomAppBar extends StatelessWidget {
  final Iterable<MyBottomAppBarItem> items;

  const MyBottomAppBar(this.items, {super.key});

  @override
  Widget build(BuildContext context) => BottomAppBar(
          child: Wrap(
        alignment: WrapAlignment.spaceAround,
        runSpacing: 8,
        spacing: 8,
        children: items
            .map((e) => TextButton.icon(
                onPressed: e.dialog == null
                    ? null
                    : () => unawaited(showDialog(context: context, builder: (_) => MaterialDialog(child: e.dialog!))),
                icon: e.icon ?? const Icon(null),
                label: Text(e.label ?? '')))
            .toList(),
      ));
}

class MyBottomAppBarItem {
  Icon? icon;
  String? label;
  Widget? dialog;

  MyBottomAppBarItem({this.icon, this.label, this.dialog});
}
