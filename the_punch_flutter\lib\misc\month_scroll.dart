import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../helpers/color_helper.dart';

class MonthScroller extends StatefulWidget {
  // final Map<DateTime, int>? pipsByDate;
  // final DateTime initialDate;
  // final ValueNotifier<DateTime> selectedDate;

  // const MonthScroller({super.key, this.pipsByDate, required this.initialDate, required this.selectedDate});

  @override
  _MonthScrollerState createState() => _MonthScrollerState();
}

class _MonthScrollerState extends State<MonthScroller> {
  final List<DateTime> _dates =
      List.generate(12, (index) => DateTime(DateTime.now().year, index + 1));
  int _selectedIndex = DateTime.now().month - 1; // Initially select the current month
  final ScrollController _scrollController = ScrollController();
  final Map<int, GlobalKey> _itemKeys = {};
  final Map<int, double> _itemPositions = {}; // Store item positions dynamically

  @override
  void initState() {
    super.initState();
    for (int i = 0; i < _dates.length; i++) {
      _itemKeys[i] = GlobalKey();
    }
    _scrollController.addListener(_updateItemPositions);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_updateItemPositions);
    _scrollController.dispose();
    super.dispose();
  }

  void _updateItemPositions() {
    if (!mounted) return;
    setState(() {
      for (int index in _itemKeys.keys) {
        final key = _itemKeys[index];
        if (key == null) continue;

        final renderBox = key.currentContext?.findRenderObject() as RenderBox?;
        if (renderBox != null) {
          final position = renderBox.localToGlobal(Offset.zero);
          _itemPositions[index] = position.dx;
        }
      }
    });
  }

  bool _isPartiallyOffscreen(int index) {
    if (!_itemPositions.containsKey(index)) return false;

    double dx = _itemPositions[index]!;
    double screenWidth = MediaQuery.of(context).size.width;

    return dx < 0 || dx > screenWidth - 50; // 50 is the assumed item width
  }

  @override
  Widget build(BuildContext context) => Column(
        children: [
          Text(
            DateFormat.MMMM().format(DateTime.now())+' '+ DateFormat.y().format(DateTime.now()), // Displays current year
            style:  TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: ColorHelper.thePunchBlueGray()),
          ),
          const SizedBox(height: 10),
          Container(
            height: 90,
            child: ListView.builder(
              controller: _scrollController,
              scrollDirection: Axis.horizontal,
              itemCount: _dates.length,
              itemBuilder: (context, index) {
                bool isSelected = index == _selectedIndex;
                bool isOffscreen = _isPartiallyOffscreen(index);

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedIndex = index;
                    });
                  },
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Text(
                      //   DateFormat.MMM().format(_dates[index]), // Month abbreviation (Jan, Feb, etc.)
                      //   style: TextStyle(
                      //     fontSize: 12,
                      //     fontWeight: FontWeight.bold,
                      //     color: isSelected ? ColorHelper.thePunchBlueGray() : Colors.grey,
                      //   ),
                      // ),
                      // const SizedBox(height: 5),
                      Container(
                        key: _itemKeys[index], // Assign key to track position
                        width: isSelected ? 65 : 55,
                        height: isSelected ? 65 : 55,
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        decoration: BoxDecoration(
                          color: isSelected ? ColorHelper.thePunchBlueGray() : Colors.white,
                          borderRadius: BorderRadius.circular(35),
                          border: Border.all(
                            color: isSelected
                                ? ColorHelper.thePunchBlueGray()
                                : ColorHelper.thePunchLighterGray(),
                          ),
                        ),
                        alignment: Alignment.center,
                        child: Text(
                          DateFormat.MMM().format(_dates[index]), // Day of the month (e.g., "1", "2")
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: isSelected ? Colors.white : isOffscreen ? Colors.black54 : ColorHelper.thePunchBlueGray(),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      );
}
