import 'package:flutter/material.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import '../helpers/screen_helper.dart';
import '../helpers/text_style_helper.dart';
import '../widgets/search_text_field.dart';

class ConstrainedSearchDialog extends StatelessWidget {
  final Widget? title;
  final Iterable<Widget> Function(String search) builder;
  final String? hintText;
  final bool autofocus;

  const ConstrainedSearchDialog(
      {super.key,
      required this.builder,
      this.hintText,
      this.title,
      this.autofocus = false});

  @override
  Widget build(BuildContext context) {
    final search = ValueNotifier<String>('');

    return Dialog(
      backgroundColor: Theme.of(context).colorScheme.primary,
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: ScreenHelper.screenHeightPercentage(context, 2),
          horizontal: ScreenHelper.screenWidthPercentage(context, 2),
        ),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: ScreenHelper.screenWidthPercentage(context, 80),
          ),
          child: Flex(
            direction: Axis.vertical,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (title != null)
                Flexible(
                  flex: 0,
                  child: Padding(
                    padding: EdgeInsets.only(
                      bottom: ScreenHelper.screenHeightPercentage(context, 1.8),
                    ),
                    child: title,
                  ),
                ),
              Flexible(
                flex: 0,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: SearchTextField(
                    autofocus: autofocus,
                    hintText: hintText,
                    notifier: search,
                  ),
                ),
              ),
              Flexible(
                child: ValueListenableBuilder<String>(
                  valueListenable: search,
                  builder: (context, search, child) {
                    final children = builder(search);
                    return ListView(
                      shrinkWrap: true,
                      children: children.toList(),
                    );
                  },
                ),
              ),
              Align(
                alignment: Alignment.centerRight,
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      'Cancel',
                      style: TextStyleHelper.redBodyText(context),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ConstrainedSearchDialog2 extends StatelessWidget {
  final Widget? title;
  final Widget Function(BuildContext context, int index, String search) builder;
  final int initialPosition;
  final int itemCount;
  final String? hintText;
  final bool autofocus;

  const ConstrainedSearchDialog2({
    super.key,
    required this.builder,
    required this.itemCount,
    this.hintText,
    this.title,
    this.autofocus = false,
    this.initialPosition = 0,
  });

  @override
  Widget build(BuildContext context) {
    final search = ValueNotifier<String>('');

    return Dialog(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 500),
        child: Flex(
          direction: Axis.vertical,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (title != null) Flexible(flex: 0, child: title!),
            Flexible(
                flex: 0,
                child: SearchTextField(
                  autofocus: autofocus,
                  hintText: hintText,
                  notifier: search,
                )),
            Flexible(
              child: ValueListenableBuilder<String>(
                  valueListenable: search,
                  builder: (context, search, child) =>
                      ScrollablePositionedList.builder(
                        itemScrollController: ItemScrollController()
                          ..jumpTo(index: initialPosition),
                        itemCount: itemCount,
                        itemBuilder: (context, index) =>
                            builder(context, index, search),
                      )),
            ),
            Align(
              alignment: Alignment.centerRight,
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
