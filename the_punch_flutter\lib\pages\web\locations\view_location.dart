// ignore_for_file: prefer_expression_function_bodies

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../../dataModel/data/location.dart';
import '../../../dataModel/data/location_notes.dart';
import '../../../dataModel/data_model.dart';
import '../../../dialogs/material_dialog.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/app_localization.dart';
import '../../view_model_mixin.dart';
import '../my_scaffold.dart';
import 'location_mixin.dart';
import '../../../misc/extensions.dart';
import '../../../state/permissions_state.dart';
import '../../../widgets/alerts_widget.dart';
import '../../../widgets/contacts_widget.dart';
import '../../../widgets/decorated_text_field.dart';
import '../../../widgets/map_widget.dart';
import '../../../widgets/notes_widget.dart';
import '../../../widgets/schedules_widget.dart';

class ViewLocationPage extends StatelessWidget {
  final String locationId;

  ViewLocationPage(Map<String, String> queryParams, {super.key})
      : locationId = queryParams['id'] ?? '';

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
        create: (context) => _ViewModel(locationId),
        child: MyScaffold(
          title: AppLocalization.of(context).location,
          body: _Body(),
        ),
      );
}

class CustomScrollBehavior extends ScrollBehavior {
  @override
  Widget buildViewportChrome(
      BuildContext context, Widget child, AxisDirection axisDirection) {
    return child; // Keeps default viewport chrome
  }
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1300),
          child: ScrollConfiguration(
            behavior: CustomScrollBehavior(),
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 12.0),
              child: Column(
                children: [
                  _BodyHeader(),
                  const SizedBox(height: 50),
                  _Section(
                    title: 'Location Details',
                    child: _ResponsiveRow(children: [_Row1()]),
                  ),
                  const SizedBox(height: 25),
                  _Section(
                    title: 'Contact Info',
                    child: _ResponsiveRow(children: [_Row2()]),
                  ),
                  const SizedBox(height: 25),
                  _Section(
                    title: 'Address',
                    child: _ResponsiveRow(children: [_Row3()]),
                  ),
                  const SizedBox(height: 25),
                  _Section(
                    title: 'City, State, Zip',
                    child: _ResponsiveRow(children: [_Row4()]),
                  ),
                  const SizedBox(height: 25),
                  _Section(
                    title: 'Time Zone',
                    child: _ResponsiveRow(children: [_Row5()]),
                  ),
                  const SizedBox(height: 25),
                  _Section(
                    title: 'Location Notes',
                    child: _ResponsiveRow(children: [_Row6()]),
                  ),
                  const SizedBox(height: 5),
                ],
              ),
            ),
          ),
        ),
      );
}

class _Section extends StatelessWidget {
  final String title;
  final Widget child;

  const _Section({required this.title, required this.child});

  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    return Container(
      decoration: BoxDecoration(
        color: const Color.fromARGB(255, 243, 243, 243),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 2,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w700,
                  fontSize: 18,
                  color: ColorHelper.thePunchRed(),
                ),
          ),
          const SizedBox(height: 16),
          isMobile
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [child],
                )
              : child,
        ],
      ),
    );
  }
}

class _ResponsiveRow extends StatelessWidget {
  final List<Widget> children;

  const _ResponsiveRow({required this.children, super.key});

  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    if (isMobile) {
      // On mobile, stack children vertically
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: children
            .map((child) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: child,
                ))
            .toList(),
      );
    } else {
      // On larger screens, place children in a row
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children
            .map((child) => Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: child,
                  ),
                ))
            .toList(),
      );
    }
  }
}

class _BodyHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    final headline6 = Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
        );

    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final location = viewModel.location;
        if (location == null) return Container();

        return Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment:
                isMobile ? CrossAxisAlignment.center : CrossAxisAlignment.stretch,
            children: [
              // Title Section
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Text(
                  location.name,
                  style: headline6,
                  textAlign: isMobile ? TextAlign.center : TextAlign.left,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              // Buttons Section
              Align(
                alignment: isMobile ? Alignment.center : Alignment.centerRight,
                child: _HeaderButtons(viewModel: viewModel),
              ),
            ],
          ),
        );
      },
    );
  }
}

class _HeaderButtons extends StatelessWidget {
  final _ViewModel viewModel;

  const _HeaderButtons({required this.viewModel, super.key});

  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
      child: Wrap(
        alignment: isMobile ? WrapAlignment.center : WrapAlignment.end,
        spacing: 12.0,
        runSpacing: 12.0,
        children: [
          _buildButton(
            context: context,
            icon: Icons.contacts,
            label: AppLocalization.of(context).contacts,
            onPressed: () =>
                _showDialog(context, ContactsWidget(locationId: viewModel.locationId)),
          ),
          _buildButton(
            context: context,
            icon: Icons.edit,
            label: AppLocalization.of(context).notes,
            onPressed: () =>
                _showDialog(context, NotesWidget(locationId: viewModel.locationId)),
          ),
          _buildButton(
            context: context,
            icon: Icons.map,
            label: 'Map',
            onPressed: () => _showDialog(context, MapWidget(locationId: viewModel.locationId)),
          ),
          _buildButton(
            context: context,
            icon: Icons.notifications,
            label: AppLocalization.of(context).alerts,
            onPressed: () =>
                _showDialog(context, AlertsWidget(locationId: viewModel.locationId)),
          ),
          _buildButton(
            context: context,
            icon: Icons.dashboard,
            label: AppLocalization.of(context).schedules,
            onPressed: () => _showDialog(
              context,
              SchedulesWidget(locationIds: [viewModel.locationId], showEmployeeNames: true),
            ),
          ),
          if (PermissionsState().editLocations)
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              onPressed: () async => context.pushNamed(
                '/locations/edit',
                queryParameters: {'id': viewModel.location?.id ?? ''},
              ),
              child: Text(AppLocalization.of(context).editLocation),
            ),
        ],
      ),
    );
  }

  Widget _buildButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 20),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        minimumSize: const Size(120, 40),
      ),
    );
  }

  void _showDialog(BuildContext context, Widget child) {
    showDialog(
      context: context,
      builder: (context) => MaterialDialog(child: child),
    );
  }
}

// Simplified the _RowN classes to just return their fields without IntrinsicHeight or Flex.
// They now just return a Column or Row as needed. The _ResponsiveRow handles responsiveness.

class _Row1 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final location = viewModel.location;
        if (location == null) return Container();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            DecoratedText(
              padding: const EdgeInsets.all(8),
              text: location.name,
              labelText: AppLocalization.of(context).locationName,
            ),
          ],
        );
      },
    );
  }
}

class _Row2 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final location = viewModel.location;
        if (location == null) return Container();

        // On larger screens, this will be placed in a row by _ResponsiveRow.
        // Here we just return a column of DecoratedText fields.
        // _ResponsiveRow will turn them into a row if not mobile.
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            DecoratedText(
              padding: const EdgeInsets.all(8),
              text: location.phone,
              labelText: AppLocalization.of(context).phone,
            ),
            DecoratedText(
              padding: const EdgeInsets.all(8),
              text: location.emailAddress,
              labelText: AppLocalization.of(context).emailAddress,
            ),
            DecoratedText(
              padding: const EdgeInsets.all(8),
              text: location.isActive.toActive(context),
              labelText: AppLocalization.of(context).activate,
            ),
          ],
        );
      },
    );
  }
}

class _Row3 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final location = viewModel.location;
        if (location == null) return Container();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            DecoratedText(
              padding: const EdgeInsets.all(8),
              text: location.address1,
              labelText: AppLocalization.of(context).address1,
            ),
            DecoratedText(
              padding: const EdgeInsets.all(8),
              text: location.address2,
              labelText: AppLocalization.of(context).address2,
            ),
          ],
        );
      },
    );
  }
}

class _Row4 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final location = viewModel.location;
        if (location == null) return Container();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            DecoratedText(
              padding: const EdgeInsets.all(8),
              text: location.city,
              labelText: AppLocalization.of(context).city,
            ),
            DecoratedText(
              padding: const EdgeInsets.all(8),
              text: location.state,
              labelText: AppLocalization.of(context).state,
            ),
            DecoratedText(
              padding: const EdgeInsets.all(8),
              text: location.zip,
              labelText: AppLocalization.of(context).zip,
            ),
          ],
        );
      },
    );
  }
}

class _Row5 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final location = viewModel.location;
        if (location == null) return Container();

        return DecoratedText(
          padding: const EdgeInsets.all(8),
          text: location.timeZone,
          labelText: AppLocalization.of(context).timeZone,
        );
      },
    );
  }
}

class _Row6 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        if (viewModel.locationNotes.isEmpty) {
          return Container(
            padding: const EdgeInsets.all(8),
            child: const Text('No Location Notes'),
          );
        }

        final locationNotesText = viewModel.locationNotes
            .map((note) => '• ${note.note}')
            .join('\n');

        return DecoratedText(
          padding: const EdgeInsets.all(8),
          text: locationNotesText,
          labelText: AppLocalization.of(context).locationNotesTitle,
          filled: false,
        );
      },
    );
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin, LocationMixin {
  final String locationId;
  Location? location;
  List<LocationNote> locationNotes = [];

  _ViewModel(this.locationId) {
    addListenables([
      DataModel().locationModel,
      DataModel().locationNoteModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    location = await DataModel().locationModel.getById(locationId);
    if (location != null) {
      locationNotes =
          (await DataModel().locationNoteModel.getByLocationIds([locationId]))
              .where((note) => note.isActive)
              .toList();
    }
    notifyListeners();
  }
}
