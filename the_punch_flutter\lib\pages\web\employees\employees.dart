import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data/user_type.dart';
import '../../../dataModel/data_model.dart';
import '../../../helpers/color_helper.dart';
import '../../../helpers/screen_helper.dart';
import '../../../misc/app_localization.dart';
import '../../view_model_mixin.dart';
import '../my_scaffold.dart';
import 'employee_mixin.dart';
import '../../../state/permissions_state.dart';
import '../../../widgets/active_toggle.dart';
import '../../../widgets/filter_chips.dart';
import '../../../widgets/search_text_field.dart';
import '../../../widgets/tables_global.dart';

class EmployeesPage extends StatelessWidget {
  const EmployeesPage({super.key});

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
        create: (context) => _ViewModel(),
        child: MyScaffold(
          title: AppLocalization.of(context).employees,
          body: _Body(),
        ),
      );
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 1300),
        child:  Column(
            children: [
              _TableHeader(),
              const SizedBox(height: 10), // Space between header and table
              Flexible(
                child: _EmployeesTable(), // Updated with the new CustomTable
              ),
            ],
          ),
        );}

class _TableHeader extends StatefulWidget {
  @override
  _TableHeaderState createState() => _TableHeaderState();
}

class _TableHeaderState extends State<_TableHeader> {
  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, constraints) {
          final isMobile =
              constraints.maxWidth < 768; // Check if it's a mobile viewport

          return Consumer<_ViewModel>(
            builder: (context, viewModel, child) => Card(
              color: const Color.fromARGB(0, 255, 255, 255),
              elevation: 0,
              child: Padding(
                  padding:
                      const EdgeInsets.all(8), // Ensure padding on all sides
                  child: isMobile
                      ? Column(
                          // Stack inputs vertically for mobile
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            _buildSearchField(viewModel),
                            const SizedBox(height: 10),
                            _buildFilterChips(viewModel),
                            const SizedBox(height: 10),
                            _buildActiveToggle(viewModel),
                            const SizedBox(height: 10),
                            if (PermissionsState().editEmployees)
                              _buildAddEmployeeButton(context),
                          ],
                        )
                      : Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment
                                  .start, // Ensures left alignment
                              children: [
                                Flexible(child: _buildSearchField(viewModel)),
                                const SizedBox(width: 20),
                                _buildFilterChips(viewModel),
                                const SizedBox(width: 20),
                                Padding(
                                  padding: const EdgeInsets.all(8),
                                  child: _buildActiveToggle(viewModel),
                                ),
                                if (PermissionsState().editEmployees)
                                  Expanded(
                                      child: Align(
                                    alignment: Alignment.centerRight,
                                    child: Padding(
                                      padding: const EdgeInsets.all(8),
                                      child: _buildAddEmployeeButton(context),
                                    ),
                                  ))
                              ],
                            ),
                          ],
                        )),
            ),
          );
        },
      );

  // Search field widget with a max width of 300px
  Widget _buildSearchField(_ViewModel viewModel) => ConstrainedBox(
        constraints:
            const BoxConstraints(maxWidth: 300), // Max width for search input
        child: SearchTextField(
            notifier: viewModel.searchNotifier,
            borderRadius: BorderRadius.all(Radius.circular(10))),
      );

  // Filter chips widget
  Widget _buildFilterChips(_ViewModel viewModel) => FilterChips(
        viewModel.employeeTypesNotifier,
        constraints: const BoxConstraints(
          minHeight: 44,
          minWidth: 300,
          maxWidth: 300,
        ),
        hint: 'Filter by employee type',
        borderRadius: BorderRadius.circular(10),
      );

  // Active toggle widget
  Widget _buildActiveToggle(_ViewModel viewModel) =>
      ActiveToggle(viewModel.activeNotifier);

  // Add employee button widget
  Widget _buildAddEmployeeButton(BuildContext context) => ElevatedButton.icon(
        onPressed: () => context.go('/employees/edit'),
        style: ButtonStyle(
            backgroundColor: MaterialStateProperty.all<Color>(
                ColorHelper.thePunchAdminButtonBlue())),
        icon: const Icon(Icons.add),
        label: Text(AppLocalization.of(context).addEmployee),
      );
}

class _EmployeesTable extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          // Apply filtering and sorting to get the list of visible employees
          Iterable<User> employees = viewModel.employees;

          // Filter active/inactive employees
          switch (viewModel.activeNotifier.value) {
            case ActiveToggleState.active:
              employees = employees.where((e) => e.isActive);
              break;
            case ActiveToggleState.inactive:
              employees = employees.where((e) => !e.isActive);
              break;
            default:
              break;
          }

          // Filter by employee type
          if (viewModel.employeeTypesNotifier.value.values.any((e) => e)) {
            final filterUserTypeIds = viewModel
                .employeeTypesNotifier.value.entries
                .where((e) => e.value)
                .map((e) => e.key.id)
                .toSet();
            employees = employees
                .where((e) => filterUserTypeIds.contains(e.userTypeId));
          }

          // Filter by search input and apply sorting logic
          final search = viewModel.searchNotifier.value;
          if (search.isNotEmpty) {
            employees = employees.where((e) {
              if (e.name.toLowerCase().contains(search.toLowerCase()))
                return true;
              if (e.phone != null && e.phone!.contains(search)) return true;
              if (e.emailAddress != null &&
                  e.emailAddress!.toLowerCase().contains(search)) return true;
              return false;
            });

            // Sort by relevance based on the search input
            employees = employees.toList()
              ..sort((a, b) => _sortByRelevance(a, b, search));
          }

          final visibleEmployees = employees.toList();

          // Build rows for the table without assigning keys to DataRow
          final rows = visibleEmployees
              .map((employee) => DataRow(
                    cells: [
                      DataCell(Text(
                        employee.name,
                        style: TextStyle(fontWeight: FontWeight.bold),
                      )),
                      DataCell(
                        InkWell(
                          onTap: () {
                            if (employee.phone != null &&
                                employee.phone!.isNotEmpty) {
                              // Launch phone call
                              final uri = Uri.parse('tel:${employee.phone}');
                              launchUrl(uri);
                            }
                          },
                          child: Text(
                            formatPhoneNumber(employee.phone) ?? '',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: ColorHelper.thePunchAdminButtonBlue(),
                              decoration:  null,
                              fontSize: 14
                            ),
                          ),
                        ),
                      ),
                      DataCell(
                        InkWell(
                          onTap: () {
                            if (employee.emailAddress != null &&
                                employee.emailAddress!.isNotEmpty) {
                              // Launch email
                              final uri =
                                  Uri.parse('mailto:${employee.emailAddress}');
                              launchUrl(uri);
                            }
                          },
                          child: Text(
                            employee.emailAddress ?? '',
                            style: TextStyle(
                              color: ColorHelper.thePunchAdminButtonBlue(),
                              decoration: null,
                            ),
                          ),
                        ),
                      ),
                      DataCell(Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                            viewModel.userTypeMap[employee.userTypeId]?.name ??
                                ''),
                      )),
                      DataCell(employee.isActive
                          ? Icon(
                              Symbols.where_to_vote,
                              color: Colors.green,
                            )
                          : Icon(
                              Icons.close_outlined,
                              color: Colors.red,
                            )),
                      DataCell(
                        ElevatedButton(
                          style: ButtonStyle(
                            backgroundColor:MaterialStateProperty.all<Color>( Colors.white),
                            foregroundColor: MaterialStateProperty.all<Color>(
                                ColorHelper.thePunchAdminButtonBlue()),
                            side: MaterialStateProperty.all<BorderSide>(
                                BorderSide(
                                    color:
                                        ColorHelper.thePunchAdminButtonBlue())),
                            shape: MaterialStateProperty.all<
                                RoundedRectangleBorder>(
                              RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                          ),
                          onPressed: () async => context.pushNamed(
                            '/employees/view',
                            queryParameters: {'id': employee.id},
                          ),
                          child: Text(
                            'View',
                            style: TextStyle(
                                color: ColorHelper.thePunchAdminButtonBlue(),
                              fontSize: 12,)
                          ),
                        ),
                      ),
                    ],
                  ))
              .toList();

          return //Padding(
              //padding: const EdgeInsets.symmetric(horizontal: 0),
              //child:
              CustomTable(
            columns: _getColumns(context),
            rows: rows,
            originalData: visibleEmployees,
            onRowClick: (DataRow row) async {
              // Determine the index of the clicked row
              final index = rows.indexOf(row);
              if (index >= 0 && index < visibleEmployees.length) {
                final employeeId = visibleEmployees[index].id;

                print(employeeId);
                // Navigate to employee view with the employee ID
                await context.pushNamed(
                  '/employees/view',
                  queryParameters: {'id': employeeId},
                );
              }
            },
            headerHeight: 60,
            rowHeight: 40,
            mobileTableTitle: "Employees",

            stickyHeader: true,
            headerFontSize: 14, // Custom header font size
            cellFontSize: 13,
            autoHeight: false,
          )
              //)
              ;
        },
      );

  // Define columns for CustomTable
  List<DataColumn> _getColumns(BuildContext context) => [
        DataColumn(label: Text(AppLocalization.of(context).employeeName)),
        DataColumn(label: Text(AppLocalization.of(context).phone)),
        DataColumn(label: Text(AppLocalization.of(context).emailAddress)),
        DataColumn(label: Text(AppLocalization.of(context).employeeType)),
        DataColumn(label: Text("Active")),
        const DataColumn(label: Text('Actions')),
      ];

  // Sorting by relevance function based on the search input
  int _sortByRelevance(User a, User b, String search) {
    final relevanceA = _calculateRelevance(a, search);
    final relevanceB = _calculateRelevance(b, search);
    return relevanceB
        .compareTo(relevanceA); // Sort in descending order of relevance
  }

  // Calculate relevance based on search matching
  int _calculateRelevance(User employee, String search) {
    var relevance = 0;

    // Increase relevance score based on match position and field type
    if (employee.name.toLowerCase().contains(search.toLowerCase())) {
      relevance += 3;
    }
    if (employee.phone != null && employee.phone!.contains(search)) {
      relevance += 2;
    }
    if (employee.emailAddress != null &&
        employee.emailAddress!.toLowerCase().contains(search)) relevance += 1;

    return relevance;
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin, EmployeeMixin {
  var employees = <User>[];
  var userTypes = <UserType>[];
  var userTypeMap = <String, UserType>{};
  var activeNotifier = ValueNotifier(ActiveToggleState.active);
  var searchNotifier = ValueNotifier('');
  var employeeTypesNotifier = FilterChipsNotifier(<UserType, bool>{});

  _ViewModel() {
    addListenables([DataModel().userModel]);
    unawaited(refresh());

    // Add listeners to refresh table when active state or search changes
    activeNotifier.addListener(notifyListeners);
    searchNotifier.addListener(notifyListeners); // Add this line
    employeeTypesNotifier.addListener(notifyListeners); // Add this line
  }

  @override
  Future<void> refresh() async {
    employees = (await DataModel().userModel.allEmployees).toList();
    final userTypeIds = employees.map((e) => e.userTypeId).toSet();
    userTypes =
        (await DataModel().userTypeModel.getByIds(userTypeIds)).toList();
    userTypeMap = {for (final e in userTypes) e.id: e};
    employeeTypesNotifier.value = {for (final e in userTypes) e: false};
    notifyListeners();
  }
}

String formatPhoneNumber(String? phone) {
  if (phone == null || phone.isEmpty) return '';

  // Remove all non-digit characters
  final digits = phone.replaceAll(RegExp(r'[^\d]'), '');

  // Handle different length phone numbers
  if (digits.length == 10) {
    return '(${digits.substring(0, 3)}) ${digits.substring(3, 6)}-${digits.substring(6)}';
  } else if (digits.length == 11 && digits[0] == '1') {
    return '(${digits.substring(1, 4)}) ${digits.substring(4, 7)}-${digits.substring(7)}';
  }

  // Return unformatted if not standard format
  return phone;
}
