import 'dart:async';
// import 'dart:html' as html;
import 'package:universal_html/html.dart' as html;

import '../misc/my_platform.dart';
import 'login_state.dart';

class SessionTimeout {
  var _lastEvent = DateTime.now().toUtc();
  // ignore: unused_field
  Timer? _timer;
  var sessionTimeout = const Duration(minutes: 720);

  static SessionTimeout? _singleton;
  factory SessionTimeout() {
    _singleton ??= SessionTimeout._();
    return _singleton!;
  }
  SessionTimeout._() {
    if (MyPlatform.isWeb) {
      // html.window.onUnload.listen((event) async {
      //   final loginState = await LoginState.instance;
      //   if (loginState.isLoggedIn) await loginState.logout();
      // });
      html.window.onInput.listen((event) {
        _lastEvent = DateTime.now().toUtc();
      });
      html.window.onClick.listen((event) {
        _lastEvent = DateTime.now().toUtc();
      });
      _timer = Timer.periodic(const Duration(minutes: 1), _checkSessionTimeout);
    }
    unawaited(_initialize());
  }

  Future<void> _initialize() async {}

  Future<void> _checkSessionTimeout(Timer timer) async {
    if (DateTime.now().toUtc().difference(_lastEvent) >= sessionTimeout) {
      _lastEvent = DateTime.now().toUtc();
      final loginState = await LoginState.instance;
      if (LoginState.isLoggedIn) await loginState.logout();
    }
  }
}
