import 'dart:async';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../misc/extensions.dart';
import 'calendar_widget.dart';

class CalendarListWidget extends StatelessWidget {
  final Future<Iterable<CalendarEvent>> Function(BuildContext context, DateTime start, DateTime end) builder;
  final bool scrollable;
  final Widget suffix;

  const CalendarListWidget({super.key, required this.builder, required this.suffix, this.scrollable = true, required double height});

  @override
  Widget build(BuildContext context) => Column(
        children: [
          _Header(suffix: suffix),
          Flexible(child: _Body(builder: builder, scrollable: scrollable)),
        ],
      );
}

class _Header extends StatelessWidget {
  final Widget suffix;

  const _Header({required this.suffix});

  @override
  Widget build(BuildContext context) => Consumer<CalendarViewModel>(
      builder: (context, viewModel, _) => Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CalendarHeaderPrefix(
                previousPage: () => viewModel.setDate(viewModel.date.addDays(-1)),
                nextPage: () => viewModel.setDate(viewModel.date.addDays(1)),
                today: () => viewModel.setDate(DateTime.now().dateOnly),
              ),
              DropdownButton<DateTime>(
                onChanged: (value) {
                  if (value == null || viewModel.date == value) return;
                  viewModel.setDate(value);
                },
                value: viewModel.date,
                items: [
                  for (var i = -5; i <= 5; i++)
                    DropdownMenuItem<DateTime>(
                        value: viewModel.date.addDays(i),
                        child: Text(formattedInterval(context, viewModel.date.addDays(i)))),
                ],
              ),
              suffix,
            ],
          ));

  String formattedInterval(BuildContext context, DateTime week) {
    final locale = Localizations.localeOf(context);

    return '${week.toFormattedDate(locale)}  - ${week.addDays(6).toFormattedDate(locale)}';
  }
}

class _Body extends StatefulWidget {
  final Future<Iterable<CalendarEvent>> Function(BuildContext context, DateTime start, DateTime end) builder;
  final bool scrollable;

  const _Body({required this.builder, this.scrollable = true});

  @override
  _BodyState createState() => _BodyState();
}

class _BodyState extends State<_Body> {
  static const _initialPage = 1073741824;
  final _controller = PageController(initialPage: _initialPage);
  final DateTime _initialDay = DateTime.now().dateOnly;

  @override
  void initState() {
    super.initState();
    _controller.addListener(() {
      if (_controller.page != null && _controller.page!.toInt() == _controller.page) {
        final offset = _controller.page!.toInt() - _initialPage;
        final day = _initialDay.addDays(offset);
        final viewModel = context.read<CalendarViewModel>();
        if (viewModel.date != day) viewModel.setDate(day);
      }
    });
  }

  @override
  Widget build(BuildContext context) => Consumer<CalendarViewModel>(builder: (context, viewModel, _) {
        if (_controller.hasClients) {
          final offset = (viewModel.date.difference(_initialDay).inHours / 24).round();
          final page = _initialPage + offset;
          if (page != _controller.page) {
            if ((page - _controller.page!).abs() > 2) {
              _controller.jumpToPage(page);
            } else {
              unawaited(
                  _controller.animateToPage(page, duration: const Duration(milliseconds: 500), curve: Curves.ease));
            }
          }
        }
        return PageView.builder(
            physics: widget.scrollable ? null : const NeverScrollableScrollPhysics(),
            controller: _controller,
            itemBuilder: (context, index) {
              final offset = index - _initialPage;
              final week = _initialDay.addDays(offset);
              return _Page(week: week, builder: widget.builder);
            });
      });
}

class _Page extends StatelessWidget {
  final DateTime week;
  final Future<Iterable<CalendarEvent>> Function(BuildContext context, DateTime start, DateTime end) builder;
  final _controller = ScrollController(initialScrollOffset: (CalendarViewModel.pageHeight / 48) * 18);

  _Page({required this.week, required this.builder});

  @override
  Widget build(BuildContext context) => Flex(direction: Axis.vertical, children: [
        _DatesRow(week),
        Expanded(
            child: SingleChildScrollView(
          controller: _controller,
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxHeight: CalendarViewModel.pageHeight),
            child: Stack(
              children: [
                _HoursColumn(),
                _HoizontalDividers(),
                _VerticalDividers(),
                _Events(week, builder),
              ],
            ),
          ),
        )),
      ]);

  List<Widget> buildColumnTitles() {
    final columns = <Widget>[];
    for (var i = 0; i < 7; i++) {
      final date = DateUtils.addDaysToDate(week, i);
      columns.add(Text(
        '${DateFormat(DateFormat.ABBR_WEEKDAY).format(date)} ${DateFormat(DateFormat.ABBR_MONTH_DAY).format(date)}',
      ));
    }
    return columns;
  }
}

class _DatesRow extends StatelessWidget {
  final DateTime week;

  const _DatesRow(this.week);

  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, constraints) {
          final dates = <String>[];
          for (var i = 0; i < 7; i++) {
            final date = DateUtils.addDaysToDate(week, i);
            dates.add(
                '${DateFormat(DateFormat.ABBR_WEEKDAY).format(date)} ${DateFormat(DateFormat.ABBR_MONTH_DAY).format(date)}');
          }

          const timesWidth = 75;
          final width = constraints.maxWidth - timesWidth;
          final columnWidth = width / dates.length;

          return ConstrainedBox(
            constraints: const BoxConstraints(maxHeight: 30),
            child: Stack(
              children: [
                for (var i = 0; i < dates.length; i++)
                  Positioned(
                    top: 8,
                    left: columnWidth * i + timesWidth,
                    child: SizedBox(width: columnWidth, child: Center(child: Text(dates[i]))),
                  ),
                Positioned(
                  left: 8,
                  right: 8,
                  bottom: 0,
                  child: Divider(
                    height: 1,
                    thickness: 1,
                    indent: 0,
                    endIndent: 0,
                    color: Theme.of(context).dividerColor.withAlpha(31),
                  ),
                )
              ],
            ),
          );
        },
      );
}

class _HoursColumn extends StatelessWidget {
  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, constraints) {
          final times = <String>[];
          for (var i = 0; i < 48; i++) {
            final hour = i ~/ 2;
            final minute = (i % 2) * 30;
            final date = DateTime.now().setTimeOfDay(TimeOfDay(hour: hour, minute: minute));
            times.add(DateFormat(DateFormat.HOUR_MINUTE).format(date));
          }

          final height = constraints.maxHeight - (constraints.maxHeight / times.length) / 2;

          return Stack(
            children: [
              for (var i = 0; i < times.length; i++)
                Positioned(
                  top: height / times.length * i + (height / times.length) / 2,
                  left: 8,
                  child: Text(times[i]),
                ),
            ],
          );
        },
      );
}

class _HoizontalDividers extends StatelessWidget {
  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, constraints) {
          const rows = 48;

          final height = constraints.maxHeight - (constraints.maxHeight / rows) / 2;

          return Stack(
            children: [
              for (var i = 2; i < rows; i += 2)
                Positioned(
                  top: height / rows * i + (height / rows) / 4,
                  left: 8,
                  right: 8,
                  child: Divider(
                    height: 1,
                    thickness: 1,
                    indent: 0,
                    endIndent: 0,
                    color: Theme.of(context).dividerColor.withAlpha(31),
                  ),
                ),
              for (var i = 1; i < rows; i += 2)
                Positioned(
                  top: height / rows * i + (height / rows) / 4,
                  left: 8,
                  right: 8,
                  child: Divider(
                    height: 1,
                    thickness: 1,
                    indent: 0,
                    endIndent: 0,
                    color: Theme.of(context).dividerColor.withAlpha(10),
                  ),
                ),
            ],
          );
        },
      );
}

class _VerticalDividers extends StatelessWidget {
  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, constraints) {
          const columns = 7;

          const timesWidth = 75;
          final width = constraints.maxWidth - timesWidth;

          return Stack(
            children: [
              for (var i = 0; i < columns; i += 1)
                Positioned(
                  top: 8,
                  bottom: 8,
                  left: (width / columns) * i + timesWidth,
                  child: const VerticalDivider(width: 1, thickness: 1, indent: 0, endIndent: 0),
                ),
            ],
          );
        },
      );
}

class _Events extends StatelessWidget {
  final Future<Iterable<CalendarEvent>> Function(BuildContext context, DateTime start, DateTime end) builder;
  final DateTime week;

  const _Events(this.week, this.builder);

  @override
  Widget build(BuildContext context) {
    final end = DateUtils.addDaysToDate(week, 7);
    return FutureBuilder<Iterable<CalendarEvent>>(
      future: Future(() async => await builder(context, week, end)),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return Container();
        final events = snapshot.data!.toList();
        if (events.isEmpty) return Container();
        return LayoutBuilder(
          builder: (context, constraints) {
            const columns = 7;
            const timesWidth = 75;
            final width = constraints.maxWidth - timesWidth;
            final columnWidth = width / columns;
            final height = constraints.maxHeight;

            final splitEvents = <CalendarEvent>[];
            for (final event in events) {
              if (event.start.toLocal().timeOfDay.duration > event.end.toLocal().timeOfDay.duration) {
                final splitEvent = CalendarEvent.from(event);
                event.splitEnd = splitEvent.splitStart = event.end.toLocal().dateOnly;
                splitEvents.add(splitEvent);
              }
            }
            events.addAll(splitEvents);

            // group events by date then group events that overlapping times
            final eventGroups = [for (var i = 0; i < 7; i++) <List<CalendarEvent>>[]];
            for (final event in events) {
              var added = false;
              final day = event.splitStart.toLocal().weekday - 1;
              for (final eventGroup in eventGroups[day]) {
                if (eventGroup.any((e) => e.splitStart <= event.splitEnd && e.splitEnd > event.splitStart)) {
                  eventGroup.add(event);
                  added = true;
                  break;
                }
              }
              if (!added) eventGroups[day].add([event]);
            }

            // display each date with overlapping getting 1/2 column width (or 1/3 if there's 3 overlapping, etc.)
            return Stack(
              children: [
                for (var i = 0; i < columns; i += 1)
                  for (final eventGroup in eventGroups[i])
                    for (var j = 0; j < eventGroup.length; j++)
                      Positioned(
                        top: eventGroup[j].splitStart.toLocal().timeOfDay.duration.inMinutes / (60 * 24) * height,
                        bottom: height -
                            eventGroup[j]
                                    .splitEnd
                                    .toLocal()
                                    .add(const Duration(minutes: -1))
                                    .timeOfDay
                                    .duration
                                    .inMinutes /
                                (60 * 24) *
                                height,
                        left: columnWidth * i + timesWidth + columnWidth * (j / eventGroup.length),
                        right: width -
                            columnWidth * (i + 1) +
                            columnWidth * ((eventGroup.length - j - 1) / eventGroup.length),
                        child: _EventTile(eventGroup[j]),
                      ),
              ],
            );
          },
        );
      },
    );
  }
}

class _EventTile extends StatelessWidget {
  final CalendarEvent event;

  const _EventTile(this.event);

  @override
  Widget build(BuildContext context) {
    final locale = Localizations.localeOf(context);

    final start = event.start.toFormattedTime(locale);
    final end = event.end.toFormattedTime(locale);

    Widget child = Container(
        color: event.color,
        child: Padding(
          padding: const EdgeInsets.all(2),
          child: SizedBox(
            height: 60, // Set a fixed height or adjust as needed
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                FittedBox(fit: BoxFit.scaleDown, child: Text('$start - $end', style: event.textStyle)),
                Expanded(
                    child: SingleChildScrollView(
                        physics: const NeverScrollableScrollPhysics(),
                        child: Text(event.detailedText ?? event.text, style: event.textStyle))),
              ],
            ),
          ),
        ));
    if (event.onTap != null) {
      child = InkWell(
        onTap: event.onTap,
        child: child,
      );
    }
    if (event.tooltip != null) {
      child = Tooltip(message: event.tooltip, textStyle: event.tooltipTextStyle, child: child);
    }
    return child;
  }
}
