import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:geolocator/geolocator.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../api/api_model.dart';
import '../api/sync_model.dart';
import '../dataModel/data/geo_location.dart';
import '../dataModel/data/job_type.dart';
import '../dataModel/data/location.dart';
import '../dataModel/data/travel_pings.dart';
import '../dataModel/data/user_type.dart';
import '../dataModel/data_model.dart';
import '../dataModel/data/punch_card.dart';
import '../dataModel/hive_db.dart';
import '../dataModel/models/punch_card_model.dart';
import '../dataModel/models/punch_view_model.dart';
import '../dataModel/models/schedule_model.dart';
import '../dataModel/models/travel_ping_model.dart';
import '../dataModel/models/location_model.dart';
import '../dataModel/models/user_model.dart';
import '../misc/my_platform.dart';
import '../services/location_background_service.dart';
import 'location_ping_state.dart';
import 'login_state.dart';
import 'server_time_state.dart';
import '../misc/extensions.dart';
import 'location_state.dart';
import 'service_state.dart';
import 'package:flutter_background_geolocation/flutter_background_geolocation.dart' as bg;

class PunchState extends ChangeNotifier {
  PunchCard? _punchCard;
  static const qualifiedDrivetimeRange = Duration(hours: 4);
  static const double qualifiedTravelDistance = 1609.34; // 1 mile in meters

  bool isTaskActive = false;
  String? currentTask; 
  String? currentTaskLocationName;

  String? jobTypeName;

  static PunchState? _singleton;
  factory PunchState() {
    _singleton ??= PunchState._();
    return _singleton!;
  }

  PunchState._() {
    DataModel().punchCardModel.addListener(() async {
      final employeeId = LoginState.userId;
      print('Employee ID: $employeeId');
      final punchCard =
          await DataModel().punchCardModel.getPunchedIn(employeeId);
      print('Current Punch Card: $punchCard');
      if (_punchCard?.id != punchCard?.id) {
        this.punchCard = punchCard;
      }
    });
    unawaited(initialize());
  }

  Future<void> initialize() async {
    PunchCard? punchCard;
    LocationPingState();
    if (LoginState.isLoggedIn) {
      await HiveDb.database;
      print('Hive database initialized');

      final employeeId = LoginState.userId;
      punchCard = await DataModel().punchCardModel.getPunchedIn(employeeId);
      this.punchCard = punchCard;
      print('Punch Card from DB: $punchCard');

      // --------------------------
      // RESTORE SHARED PREFERENCES
      // --------------------------
      final prefs = await SharedPreferences.getInstance();

      final punchCardId = prefs.getString('punchCardId') ?? '';
      print('Restored punchCardId: $punchCardId');

      // 1) Restore isTaskActive
      isTaskActive = prefs.getBool('isTaskActive') ?? false;         // <-- NEW

      // 2) Restore currentTask and currentTaskLocationName
      currentTask = prefs.getString('currentTask');                  // <-- NEW
      currentTaskLocationName = prefs.getString('currentTaskLocationName'); 
                                                                      // <-- NEW
      // If you also want to restore the "Manager Tracking" status from punchCard,
      // you could do that here as well.

      // If we DO have an active punch card, load the jobTypeId
      if (punchCard != null) {
        final jobTypeId = punchCard.jobTypeId;
        jobTypeName = await DataModel().jobTypeModel.getJobTypeName(jobTypeId.toUpperCase());

        setJobTypeName(jobTypeName!);
            } else {
        jobTypeName = null;
      }

      if (punchCard != null && punchCard.clockedOut == null) {
        print('MyAppDebug User is actively punched in');

        if (punchCard.jobTypeId == JobType.administrativeId) {
          print('Updating current location for administrative job');
          await LocationState.updateCurrentLocation();
          final currentPosition = LocationState().currentPosition;
          print('Current Position: $currentPosition');
          if (currentPosition != null) {
            final userTypeId =
                await UserModel().getCurrentUserTypeId(employeeId);
            final isManager =
                UserType.managerId == userTypeId?.toUpperCase() ||
                    UserType.administratorId == userTypeId?.toUpperCase();
            await LocationPingState().saveUserId(LoginState.userId);
            await LocationPingState().savePunchCardId(punchCard.id);
            if (isManager) {
              await DataModel().travelPingModel.createAndSavePing(
                currentPosition,
                LoginState.userId,
                punchCard.id,
                currentPosition.latitude.toString(),
                currentPosition.longitude.toString(),
                null,
                '',
                ServerTimeState().utcTime,
              );
              await LocationPingState().saveLastPingLocation(
                currentPosition.latitude.toString(),
                currentPosition.longitude.toString(),
              );
            } else {
              final currentLocationId = punchCard.locationId;
              Location? locationData =
                  await LocationModel().getLocationById(currentLocationId!);
              if (locationData != null) {
                final latitude = locationData.latitude.toString();
                final longitude = locationData.longitude.toString();
                Map<String, dynamic> geofenceData = {
                  'radius': locationData.geoFenceRadius
                };
                print(geofenceData);
                await LocationPingState().saveLocation(latitude, longitude);
                await LocationPingState().saveLocationGeofence(geofenceData);
              }
            }
          }
        }
      }

      final loginState = await LoginState.instance;
      loginState.addListener(() {
        if (!LoginState.isLoggedIn) {
          _punchCard = null;
          print('User logged out, punch card set to null');
        }
      });
    } else {
      print('NOT LOGGED IN ANYMORE');
    }
  }

  bool get isPunchedIn => punchCard != null;
  PunchCard? get punchCard =>
      (_punchCard != null && _punchCard!.clockedOut == null)
          ? _punchCard
          : null;
  set punchCard(PunchCard? newValue) {
    _punchCard = newValue;
    unawaited(_updatePunchPrefs()); 
    unawaited(_startDurationStream());
    notifyListeners();
  }

  // This method can be async
  Future<void> _updatePunchPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    if (_punchCard != null && _punchCard!.clockedOut == null) {
      await prefs.setBool('isPunchedIn', true);
      await prefs.setString('punchCardId', _punchCard!.id);
    } else {
      await prefs.setBool('isPunchedIn', false);
      await prefs.remove('punchCardId');
    }
  }
  Duration _totalDuration = Duration.zero;
  List<PunchCard> _relatedPunchCards = [];

  DateTime? get punchedIn => (punchCard != null) ? punchCard!.clockedIn : null;
  Duration? get duration => (punchedIn != null)
      ? ServerTimeState().utcTime.difference(punchedIn!)
      : null;

  final StreamController<String> _streamController =
      StreamController<String>.broadcast();

  Future<void> _startDurationStream() async {
    final punchedIn0 = punchedIn;
    _totalDuration = _calculateTotalDuration();
    print("TOTAL PUNCHIN" + punchedIn0.toString());
    print("TOTAL DURATION " + _totalDuration.toString());
    if (punchedIn0 == null) {
      _streamController.add('');
    } else {
      while (true) {
        if (punchedIn == null) {
          _streamController.add('');
          break;
        } else {
          _streamController.add(
            ServerTimeState().utcTime.difference(punchedIn0).toFormattedWithSeconds,
          );
        }
        await Future.delayed(const Duration(seconds: 1));
      }
    }
  }
  Duration _calculateTotalDuration() {
    Duration total = Duration.zero;
    for (var card in _relatedPunchCards) {
      if (card.clockedIn != null && card.clockedOut != null) {
        total += card.clockedOut!.difference(card.clockedIn!);
      } else if (card.clockedIn != null && card.clockedOut == null) {
        // If the punch card is active, add the duration till now
        total += DateTime.now().difference(card.clockedIn!);
      }
    }
    return total;
  }

  Future<List<PunchCard>> _fetchRelatedPunchCards() async {
    final prefs = await SharedPreferences.getInstance();
    final linkId = prefs.getString('LinkId') ?? '';

    // Fetch punch cards from the view model or API
    final punchCardModel = PunchCardModel();
    final allPunchCards =
        (await punchCardModel.getAllByLinkId(linkId)).toList();

    // Sort the punch cards by ClockedIn date (descending order)
    allPunchCards.sort((a, b) => b.clockedIn.compareTo(a.clockedIn));

    return allPunchCards;
  }
  Stream<String> get durationStream => _streamController.stream;

  // CHANGED: set isTaskActive using a helper so we can store in SharedPreferences
  Future<void> setTaskActive(bool active) async {                        // <-- NEW
    isTaskActive = active;
    final prefs = await SharedPreferences.getInstance();
    prefs.setBool('isTaskActive', active);
    notifyListeners();
  }

  // CHANGED: set currentTask using a helper
  Future<void> setCurrentTask(String? task) async {                      // <-- NEW
    currentTask = task;
    final prefs = await SharedPreferences.getInstance();
    if (task == null) {
      prefs.remove('currentTask');
    } else {
      prefs.setString('currentTask', task);
    }
    notifyListeners();
  }

  // CHANGED: set currentTaskLocationName using a helper
  Future<void> setCurrentTaskLocationName(String? locationName) async {  // <-- NEW
    currentTaskLocationName = locationName;
    final prefs = await SharedPreferences.getInstance();
    if (locationName == null) {
      prefs.remove('currentTaskLocationName');
    } else {
      prefs.setString('currentTaskLocationName', locationName);
    }
    notifyListeners();
  }

  Future<bool> locationPunchIn({
    required String locationId,
    required String jobTypeId,
  }) async =>
      await _punchIn(
        locationId: locationId,
        jobTypeId: jobTypeId,
        isManagerTask: false,
      );

  Future<bool> schedulePunchIn({
    required String scheduleId,
    required String locationId,
  }) async =>
      await _punchIn(
        locationId: locationId,
        scheduleId: scheduleId,
        jobTypeId: JobType.scheduledId,
        isManagerTask: false,
      );
      
  void setJobTypeName(String name) {
    jobTypeName = name;
    notifyListeners();
  }

  Future<bool> _punchIn({
    String? locationId,
    String? scheduleId,
    required String jobTypeId,
    required bool isManagerTask,
  }) async {
    try {
      final employeeId = LoginState.userId;

      // ---------------------------------------------------------
      final existingActivePunchCard =
          await DataModel().punchCardModel.getPunchedIn(employeeId);
      if (existingActivePunchCard != null) {
        print('User already has an active punch card. Aborting punch-in.');
        // You could show a message to the user or throw an exception here:
        // throw Exception('There is already an active punch card!');
        return false;
      }
      // ---------------------------------------------------------

      final userTypeId = await UserModel().getCurrentUserTypeId(employeeId);
      final isManager = UserType.managerId.toUpperCase() == userTypeId?.toUpperCase() || UserType.administratorId.toUpperCase() == userTypeId?.toUpperCase();

      // Fetch the job type name
      final jobTypeName = await DataModel().jobTypeModel.getJobTypeName(jobTypeId.toUpperCase());
      setJobTypeName(jobTypeName);
      notifyListeners();

      print(jobTypeId);

      // Changes tracking type based on JobType
      var managerTracking = false;
      if (jobTypeId != JobType.scheduledId &&
          jobTypeId != JobType.unscheduledId &&
          jobTypeId != JobType.travelTimeId) {
        managerTracking = true;
      } else {
        managerTracking = false;
      }

      print('MyAppDebug:User Type ID: $userTypeId');
      print('MyAppDebug:Is Manager: $isManager');
      print('MyAppDebug:Manager Tracking: $managerTracking');

      // Check for SharedPreference of LinkId
      final prefs = await SharedPreferences.getInstance();
      var linkId = prefs.getString('LinkId');
      if (linkId == null) {
        linkId = const Uuid().v4();
        print(linkId);
        await prefs.setString('LinkId', linkId);
        print('New LinkId created and saved: $linkId');
      } else {
        print('Existing LinkId found: $linkId');
      }

      //await LocationState.updateCurrentLocation();
      final currentPosition = LocationState().currentPosition;
      print('MyAppDebug:Current Position for punch in: $currentPosition');

      await setIsLocationUpdatesRunning(true);

      if (managerTracking) {
        print('MyAppDebug:MANAGER PUNCHED IN!');

        PunchCard punchCard;

        if (jobTypeId == JobType.trackingId) {
          punchCard = PunchCard.create()
            ..userId = LoginState.userId
            ..locationId = locationId
            ..clockedIn = ServerTimeState().utcTime
            ..jobTypeId = jobTypeId
            ..punchCardLinkId = prefs.getString('LinkId'); // Add linkId
          this.punchCard = punchCard;
        } else {
          punchCard = PunchCard.create()
            ..userId = LoginState.userId
            ..clockedIn = ServerTimeState().utcTime
            ..jobTypeId = jobTypeId
            ..locationId = locationId
            ..scheduleId = scheduleId
            ..punchCardLinkId = prefs.getString('LinkId'); // Add linkId
          this.punchCard = punchCard;
        }


        try {
          await DataModel().punchCardModel.savePunchCard(punchCard);
          print('manager punch card saved: $punchCard');
        } catch (e) {
          print('Warning: manager punch card failed. $e');
          // maybe show a toast/snackbar but don’t crash
        }

        //await Future.delayed(const Duration(seconds: 1));

        print('Current Position: $currentPosition');
        print('Punch Card ID: ${punchCard.id}');

        if (currentPosition != null) {
          print('MyAppDebug:CREATING TRAVEL PING!');

          await LocationPingState().saveUserId(LoginState.userId);
          await LocationPingState().savePunchCardId(punchCard.id);
          await LocationPingState().saveLastPingLocation(
            currentPosition.latitude.toString(),
            currentPosition.longitude.toString(),
          );

          await LocationPingState().saveIsManager(true);

          await DataModel().travelPingModel.createAndSavePing(
            currentPosition,
            LoginState.userId,
            punchCard.id,
            currentPosition.latitude.toString(),
            currentPosition.longitude.toString(),
            null,
            '',
            ServerTimeState().utcTime,
          );

          print('MyAppDebug:TRAVEL PING SAVED!');
          if(!isManagerTask){
            await bg.BackgroundGeolocation.start();
          }
          // if (!isManagerTask) {
          //   if (MyPlatform.isAndroid) {
          //     locationUpdate();
          //   }
          // }
        } else {
          print('Error: No location found for travel ping');
        }
      } else {
        if (locationId != null) {
          Location? locationData = await LocationModel().getLocationById(locationId);
          if (locationData != null) {
            final latitude = locationData.latitude.toString();
            final longitude = locationData.longitude.toString();

            Map<String, dynamic> geofenceData = {
              'radius': locationData.geoFenceRadius,
            };

            await LocationPingState().saveIsManager(false);
            await LocationPingState().saveLocation(latitude, longitude);
            await LocationPingState().saveLocationGeofence(geofenceData);
            print('Geofence and location data saved.');
          } else {
            print('No location data found for the specified locationId.');
          }
        } else {
          print('No locationId provided.');
        }

        final lastPunchCard = await DataModel()
            .punchCardModel
            .getLastCompletedJobByEmployeeId(employeeId);
        print('Last Punch Card: $lastPunchCard');

        // New code to handle scheduleId if locationId is not available
        String? lastLocationId = lastPunchCard?.locationId;
        if (lastLocationId == null && lastPunchCard?.scheduleId != null) {
          final schedule =
              await ScheduleModel().getScheduleById(lastPunchCard!.scheduleId!);
          lastLocationId = schedule?.locationId;
        }

        if (lastLocationId != null && lastPunchCard?.clockedOut != null) {
          final currentPunchInTime = ServerTimeState().utcTime;
          final lastPunchOutTime = lastPunchCard!.clockedOut!;
          final timeDifference =
              currentPunchInTime.difference(lastPunchOutTime);

          print('Time Difference: $timeDifference');
          print('Qualified Drivetime Range: $qualifiedDrivetimeRange');

          String? destinationLocationId = locationId;
          if (destinationLocationId == null && scheduleId != null) {
            final schedule = await ScheduleModel().getScheduleById(scheduleId);
            destinationLocationId = schedule?.locationId;
          }

          if (timeDifference <= qualifiedDrivetimeRange) {
            final travelTimeResponse = await ApiModel().fetchTravelTime(
              originLocationId: lastLocationId,
              destinationLocationId: destinationLocationId!,
            );

            print('Response: ${travelTimeResponse.message}');
            print('Response: ${travelTimeResponse.duration}');
            print('Response: $lastLocationId');
            print('Response: $lastPunchCard');

            final travelTime = travelTimeResponse.duration ?? Duration.zero;

            // Calculate the distance between the last location and the new location
            final lastLocation =
                await LocationModel().getLocationById(lastLocationId);
            final newLocation =
                await LocationModel().getLocationById(destinationLocationId);

            if (lastLocation != null && newLocation != null) {
              final distance = Geolocator.distanceBetween(
                lastLocation.latitude,
                lastLocation.longitude,
                newLocation.latitude,
                newLocation.longitude,
              );

              print('Distance between last and new location: $distance meters');

              // Check if the distance is greater than 1 mile (1609.34 meters)
              if (distance > qualifiedTravelDistance) {
                final travelTimePunchCard = PunchCard.create()
                  ..userId = employeeId
                  ..locationId = destinationLocationId
                  ..previousLocationId = lastLocationId
                  ..clockedIn = ServerTimeState().utcTime.subtract(travelTime)
                  ..clockedOut = ServerTimeState().utcTime
                  ..jobTypeId = JobType.travelTimeId
                  // OLD: ..punchCardLinkId = linkId;  // Re-used linkId from SharedPreferences
                  ..punchCardLinkId = const Uuid().v4();  // <-- NEW: Unique linkId for each travel punch
                          
                try {
                  await DataModel().punchCardModel.savePunchCard(travelTimePunchCard);
                  print('Travel time punch card saved: $travelTimePunchCard');
                } catch (e) {
                  print('Warning: travel time punch card failed. $e');
                  // maybe show a toast/snackbar but don't crash
                }
              } else {
                print('Locations are < 1 mile apart, no travel time punch card created.');
              }
            }
          }
        }

        final punchCard = PunchCard.create()
          ..userId = LoginState.userId
          ..clockedIn = ServerTimeState().utcTime
          ..locationId = locationId
          ..scheduleId = scheduleId
          ..jobTypeId = jobTypeId
          ..punchCardLinkId = linkId; // Add linkId

        try {
          await DataModel().punchCardModel.savePunchCard(punchCard);
          print('MyAppDebug:Regular punch card saved: $punchCard');
        } catch (e) {
          print('Warning: regular punch card failed. $e');
          // maybe show a toast/snackbar but don’t crash
        }        

        await LocationState.updateCurrentLocation();
            final currentPosition = LocationState().currentPosition;

            // 2) Save a "Punch In" geolocation if you have coordinates
            if (currentPosition != null) {
              await DataModel().geoLocationModel.createAndSaveGeoLocation(
                punchCardId: punchCard.id,
                recordedOn: ServerTimeState().utcTime,
                latitude: currentPosition.latitude,
                longitude: currentPosition.longitude,
                distanceFromLocation: 0.0, // or calculate actual distance if needed
                geoLocationTypeId: GeoLocation.geoLocationPunchInId
              );
            }

            // Update in-memory reference
            this.punchCard = punchCard;

        
        //await Future.delayed(const Duration(seconds: 1));

        await LocationPingState().saveUserId(LoginState.userId);
        await LocationPingState().savePunchCardId(punchCard.id);

        if(!isManagerTask){
          await bg.BackgroundGeolocation.start();
        }
        // if (!isManagerTask) {
        //   if (MyPlatform.isAndroid) {
        //     locationUpdate();
        //   }
        // }
        print('MyAppDebug:Starting Tracking Service');
      }

      return true;
    } catch (e) {
      print('Punch in failed: $e');
      return false;
    }
  }

  Future<void> punchOut(bool isManagerTask) async {
    final punchCard0 = punchCard;
    if (punchCard0 == null) return;

    punchCard = null;

     // 1) Get current location
  await LocationState.updateCurrentLocation();
  final currentPosition = LocationState().currentPosition;

  // 2) Record a "Punch Out" geolocation
  if (currentPosition != null) {
    try {
      await DataModel().geoLocationModel.createAndSaveGeoLocation(
        punchCardId: punchCard0.id,
        recordedOn: ServerTimeState().utcTime,
        latitude: currentPosition.latitude,
        longitude: currentPosition.longitude,
        distanceFromLocation: 0.0, 
        geoLocationTypeId: GeoLocation.geoLocationPunchOutId,
      );
    } catch (e) {
      print('Warning: could not save Punch Out geolocation: $e');
    }
  }

    if(!isManagerTask){
      isTaskActive = false;
      currentTask = null;
      currentTaskLocationName = null;

      await setIsLocationUpdatesRunning(false);

      // Also remove from SharedPreferences if not manager task
      final prefs = await SharedPreferences.getInstance();
      prefs.remove('isTaskActive');                 
      prefs.remove('currentTask');                  
      prefs.remove('currentTaskLocationName');      
    }

    print('MyAppDebug:Punching out: $punchCard0');

    if (punchCard0.jobTypeId != JobType.scheduledId &&
        punchCard0.jobTypeId != JobType.unscheduledId &&
        punchCard0.jobTypeId != JobType.travelTimeId) {
      await _sendLastTravelPing(punchCard0);
    }

    PunchViewModel().clearLocationNotes();
    setJobTypeName('Unknown');

    punchCard0.clockedOut = ServerTimeState().utcTime;
    try {
      await DataModel().punchCardModel.savePunchCard(punchCard0);
    } catch (e) {
      print('Warning: remote punch-out failed. $e');
      // maybe show a toast/snackbar but don’t crash
    }
    print('MyAppDebug:Punch card clocked out: $punchCard0');

    await ApiModel().sendStashedGeoFencePings();
    print('All stashed geofences sent.');

    // If it’s not manager task, remove LinkId so the next new punch set uses a fresh link
    if (!isManagerTask) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('LinkId');
      print('SharedPref LinkId deleted.');
    } else {
      print('isManagerTask is true, SharedPref LinkId not deleted.');
    }

    // Stop location updates first
    if (!isManagerTask) {
      await stopLocationUpdates();
    }

    await resetGeoFenceBreached();

    // Then stop the background geolocation service
    if (!isManagerTask) {
      await bg.BackgroundGeolocation.stop();
    }
    print('MyAppDebug: BackgroundGeolocation service stopped.');
    return;
  }

  Future<void> _sendLastTravelPing(PunchCard punchCard) async {
    await LocationState.updateCurrentLocation();
    final currentPosition = LocationState().currentPosition;
    print('Current Position for last travel ping: $currentPosition');

    if (currentPosition != null) {
      await LocationPingState().saveUserId(LoginState.userId);
      await LocationPingState().savePunchCardId(punchCard.id);
      await LocationPingState().saveLastPingLocation(
        currentPosition.latitude.toString(),
        currentPosition.longitude.toString(),
      );
      await DataModel().travelPingModel.createAndSavePing(
        currentPosition,
        LoginState.userId,
        punchCard.id,
        currentPosition.latitude.toString(),
        currentPosition.longitude.toString(),
        null,
        'stationary',
        ServerTimeState().utcTime,
      );
      print('sending final ping');
      print('Last travel ping sent before punching out.');
    } else {
      print('Failed to send last travel ping: No current position available.');
    }
  }

  // ------------------------------------------------------------------------------
  //  MANAGER TASKS
  // ------------------------------------------------------------------------------

  Future<void> managerTaskPunchIn({
    String? locationId,
    String? scheduleId,
    required String jobTypeId,
  }) async {
    // First, punch out whatever was previously in progress (but pass true for isManagerTask).
    await punchOut(true);
    print('Punched out current punch card.');

    await Future.delayed(const Duration(seconds: 1));

    // Mark task as active in preferences
    await setTaskActive(true);  // now stored in SharedPreferences

    // Also store the name of the task & location, so we can restore if the user closes the app
    // We fetch jobTypeName from the data model:
    final jobTypeName = await DataModel().jobTypeModel
        .getJobTypeName(jobTypeId.toUpperCase());
    await setCurrentTask(jobTypeName);              // <-- NEW

    if (locationId != null) {
      final location = await LocationModel().getById(locationId);
      await setCurrentTaskLocationName(location?.name);   
    } else {
      await setCurrentTaskLocationName(null);
    }

    if (scheduleId == null && locationId != null) {
      await _punchIn(locationId: locationId, jobTypeId: jobTypeId, isManagerTask: true);
    } else if (locationId == null) {
      await _punchIn(jobTypeId: jobTypeId, isManagerTask: true);
    } else {
      await _punchIn(locationId: locationId, jobTypeId: jobTypeId, scheduleId: scheduleId, isManagerTask: true);
    }
  }

  Future<void> managerTaskPunchOut() async {
    try {
      await punchOut(true);
      await Future.delayed(const Duration(seconds: 1));
      PunchViewModel().clearLocationNotes();
      isTaskActive = false;
      currentTask = null;
      currentTaskLocationName = null;
      
      // Also remove from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      prefs.remove('isTaskActive');                  
      prefs.remove('currentTask');                  
      prefs.remove('currentTaskLocationName');

      notifyListeners();

      // For a manager, you might jump right back into a "tracking" job type
      // automatically, so let's do that:
      await _punchIn(jobTypeId: JobType.trackingId, isManagerTask: true);
    } catch (e) {
      print('Error during managerTaskPunchOut: $e');
    }
  }

  static bool get canPunchIn => MyPlatform.isHandheld;

  String? get currentPunchCardId {
    return _punchCard?.id;
  }

  Future<String?> getJobTypeId() async {
    return punchCard?.jobTypeId;
  }



}
