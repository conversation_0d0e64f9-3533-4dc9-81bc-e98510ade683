import 'package:flutter/material.dart';
import 'package:material_symbols_icons/symbols.dart';

class AttachmentButton extends StatelessWidget {
  const AttachmentButton({super.key});

  @override
  Widget build(BuildContext context) => Stack(
      alignment: Alignment.center,
      children: [
        // Circular background
        CircleAvatar(
          radius: 30,
          backgroundColor: Colors.grey.shade200,
          child: const  Icon(
            Symbols.attach_file_add,
            color: Colors.black87,
            size: 20,
          ),
        ),
        // Plus icon overlay
        // const Positioned(
        //   bottom: 4,
        //   right: 4,
        //   child: CircleAvatar(
        //     radius: 8,
        //     backgroundColor: Colors.white,
        //     child: Icon(
        //       Icons.add,
        //       size: 12,
        //       color: Colors.black,
        //     ),
        //   ),
        // ),
      ],
    );
}
