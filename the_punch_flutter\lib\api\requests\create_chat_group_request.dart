import 'package:json_annotation/json_annotation.dart';
import '../../dataModel/data/message_group.dart'; // <-- Adjust import to your actual model
import '../../misc/json_conversion.dart';
import 'system.dart';

part 'create_chat_group_request.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class CreateChatGroupRequest extends SystemRequest {
  final String groupId;
  final String name;
  final String? createdByUserId; // <-- Make it nullable
  final List<String> memberUserIds;

  CreateChatGroupRequest({
    required this.groupId,
    required this.name,
    required this.createdByUserId,
    required this.memberUserIds,
    required super.serverIP,
    required super.databaseName,
    required super.sessionId,
  });

  static Future<CreateChatGroupRequest> create(
    MessageGroup group,
    List<String> userIds,
  ) async {
    final systemReq = await SystemRequest.create();
    return CreateChatGroupRequest(
      groupId: group.id,
      name: group.name,
      // If group.createdByUserId is nullable, just assign it directly:
      createdByUserId: group.createdByUserId,
      memberUserIds: userIds,
      serverIP: systemReq.serverIP,
      databaseName: systemReq.databaseName,
      sessionId: systemReq.sessionId,
    );
  }

  factory CreateChatGroupRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateChatGroupRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() {
    final data = _$CreateChatGroupRequestToJson(this);
    data.removeWhere((key, value) => value == null);
    return data;
  }
}


@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class CreateChatGroupResponse {
  final String errorCode;
  final String errorMessage;

  bool get isError => errorCode != "0";

  CreateChatGroupResponse({
    required this.errorCode,
    required this.errorMessage,
  });

  factory CreateChatGroupResponse.fromJson(Map<String, dynamic> json) {
    return CreateChatGroupResponse(
      errorCode: json['ErrorCode'] as String? ?? '',
      errorMessage: json['ErrorMessage'] as String? ?? '',
    );
  }
}

