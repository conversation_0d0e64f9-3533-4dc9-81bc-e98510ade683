import 'package:flutter/material.dart';

class HeaderComponent extends StatelessWidget {
  final String title;
  final VoidCallback? onBack;

  const HeaderComponent({
    Key? key,
    this.title = '<PERSON>',
    this.onBack,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          InkWell(
            onTap: onBack ?? () => Navigator.pop(context),
            child: Icon(
              Icons.arrow_back,
              size: 40,
              color: Color(0xFF091F30),
            ),
          ),
          const SizedBox(width: 24),
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontFamily: 'Poppins',
                fontSize: 40,
                fontWeight: FontWeight.w700,
                letterSpacing: -0.4,
                height: 1.2, // 48px line height / 40px font size
                color: Color(0xFF091F30),
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}

