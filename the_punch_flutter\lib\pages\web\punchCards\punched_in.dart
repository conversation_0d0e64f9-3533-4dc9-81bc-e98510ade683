import 'dart:async';
import 'package:community_material_icon/community_material_icon.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:the_punch_flutter/pages/employee/employee_widgets/punched_out_body.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../../api/sync_model.dart';
import '../../../dataModel/data/geo_location.dart';
import '../../../dataModel/data/job_type.dart';
import '../../../dataModel/data/location.dart';
import '../../../dataModel/data/punch_card.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data/user_type.dart';
import '../../../dataModel/data_model.dart';
import '../../../dataModel/models/punch_view_model.dart';
import '../../../dataModel/models/user_model.dart';
import '../../../dialogs/view_punch_card_dialog.dart';
import '../../../helpers/check_session.dart';
import '../../../helpers/color_helper.dart';
import '../../../helpers/screen_helper.dart';
import '../../../misc/app_localization.dart';
import '../../../state/login_state.dart';
import '../../../state/punch_state.dart';
import '../../../widgets/buttons/attachment_button.dart';
import '../../../widgets/duration_display.dart';
import '../../../widgets/jobType.dart';
import '../../../widgets/location_notes.dart';
import '../../../widgets/progress_bar/progress_bar.dart';
import '../../employee/employee_widgets/punched_in_body.dart';
import '../../view_model_mixin.dart';
import '../../../misc/extensions.dart';
import 'package:collection/collection.dart';
import 'package:uuid/uuid.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../my_scaffold.dart';

class PunchedInPage extends StatelessWidget {
  const PunchedInPage({super.key});

  @override
  Widget build(BuildContext context) => MyScaffold(
        title: AppLocalization.of(context).punchCard,
        body: const PunchedInBody(),
      );
}

class PunchedInBody extends StatefulWidget {
  const PunchedInBody({super.key});

  @override
  State<PunchedInBody> createState() => _PunchedInBodyState2();
}

class _PunchedInBodyState extends State<PunchedInBody> {
  Timer? _timer;

  @override
  Widget build(BuildContext context) => Center(
      child: VisibilityDetector(
          key: const Key('PunchedInPage'),
          onVisibilityChanged: (visibilityInfo) {
            if (visibilityInfo.visibleFraction > 0.1) {
              _timer ??= Timer.periodic(const Duration(minutes: 1), (_) async => await SyncModel().sync());
            } else {
              _timer?.cancel();
              _timer == null;
            }
          },
          child: _Map()));
}

class _Map extends StatefulWidget {
  @override
  __MapState createState() => __MapState();
}

class __MapState extends State<_Map> {
  final Set<Marker> _markers = {};
  final _viewModel = _ViewModel();

  @override
  void initState() {
    super.initState();
    _viewModel.addListener(() {
      setState(() {
        _markers.clear();
        _markers.addAll(buildMarkers(_viewModel));
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_viewModel.organizationLocation == null) return Container();
    return GoogleMap(
        markers: _markers,
        initialCameraPosition: CameraPosition(
          target: LatLng(_viewModel.organizationLocation!.latitude, _viewModel.organizationLocation!.longitude),
          zoom: 11,
        ));
  }

  Set<Marker> buildMarkers(_ViewModel viewModel) {
    final locale = Localizations.localeOf(context);

    return viewModel.geoLocations.map((geoLocation) {
      final punchCard = viewModel.punchCardMap[geoLocation.punchCardId]!;
      final employee = viewModel.employeeMap[punchCard.userId]!;
      final location = viewModel.locationMap[punchCard.id];
      final clockedIn = punchCard.clockedIn.toFormattedDateTime(locale);
      // final lastUpdated = DateTime.now().toUtc().difference(geoLocation.recordedOn).toAgo;
      final lastUpdated = geoLocation.recordedOn.toFormattedTime(locale);
      final title = '${employee.name}\n'
          '${AppLocalization.of(context).clockedIn}: $clockedIn\n'
          "${AppLocalization.of(context).location}: ${location?.name ?? ''}";
      final snippet = '${AppLocalization.of(context).lastUpdated}: $lastUpdated';

      return Marker(
        markerId: MarkerId(const Uuid().v4()),
        position: LatLng(geoLocation.latitude!, geoLocation.longitude!),
        infoWindow: InfoWindow(
          title: title,
          snippet: snippet,
          onTap: () async => context.pushNamed('/punchCards/view', queryParameters: {'id': punchCard.id}),
        ),
      );
    }).toSet();
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  Location? organizationLocation;
  Iterable<GeoLocation> geoLocations = [];
  Map<String, PunchCard> punchCardMap = {};
  Map<String, Location> locationMap = {};
  Map<String, User> employeeMap = {};

  _ViewModel() {
    addListenables([
      DataModel().punchCardModel,
      DataModel().geoLocationModel,
      DataModel().userModel,
      DataModel().locationModel,
      DataModel().scheduleModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    organizationLocation = await DataModel().locationModel.getById(Location.primaryLocationId);

    var punchCards = await DataModel().punchCardModel.getAllPunchedIn();
    final scheduleIds = punchCards.map((e) => e.scheduleId).whereNotNull().toSet();
    final schedules = await DataModel().scheduleModel.getByIds(scheduleIds);
    final scheduleMap = {for (final e in schedules) e.id: e};

    punchCards = punchCards.where((e) => (e.locationId ?? scheduleMap[e.scheduleId]) != null);
    punchCardMap = {for (final e in punchCards) e.id: e};

    final employeeIds = punchCards.map((e) => e.userId).toSet();
    final employees = await DataModel().userModel.getByIds(employeeIds);
    employeeMap = {for (final e in employees) e.id: e};

    final locationIds = punchCards.map((e) => e.locationId).followedBy(schedules.map((e) => e.locationId)).whereNotNull().toSet();
    final locations = (await DataModel().locationModel.getByIds(locationIds));
    locationMap = {
      for (final punchCard in punchCards)
        punchCard.id: locations.firstWhere((e) => e.id == (punchCard.locationId ?? scheduleMap[punchCard.scheduleId]?.locationId))
    };

    final punchCardIds = punchCards.map((e) => e.id);
    geoLocations = await DataModel().geoLocationModel.lastByPunchCardIds(punchCardIds);

    notifyListeners();
  }
}


class _PunchedInBodyState2 extends State<PunchedInBody> {
  Future<bool>? _isEmployeeFuture;

  @override
  void initState() {
    super.initState();
    _isEmployeeFuture = _checkIfEmployee();
  }
  Future<void> showPunchOutDialog(
    BuildContext context,
    ValueChanged<bool> onConfirm,
  ) async =>
      showDialog(
        context: context,
        builder: (BuildContext context) => AlertDialog(
          title: Text(
            '${AppLocalizations.of(context)!.punchStatus}:',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: ColorHelper.thePunchRed(),
                ),
          ),
          content: Text(
            AppLocalizations.of(context)!.confirmPunchOut,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontSize: ScreenHelper.screenHeightPercentage(context, 2),
                ),
          ),
          actions: [
            OutlinedButton.icon(
              style: OutlinedButton.styleFrom(
                side: BorderSide(
                  color: ColorHelper.thePunchRed(),
                ),
              ),
              icon: const Icon(Icons.cancel),
              label: const Text('No'),
              onPressed: () {
                onConfirm(false);
                Navigator.pop(context);
              },
            ),
            ElevatedButton.icon(
              icon: const Icon(
                CommunityMaterialIcons.clock_out,
                color: Colors.white,
              ),
              label: Text(
                AppLocalizations.of(context)!.clockOut,
                style: const TextStyle(color: Colors.white, fontWeight: FontWeight.w700),
              ),
              onPressed: () {
                onConfirm(true);
                Navigator.pop(context);
              },
            ),
          ],
        ),
      );

  Future<bool> _checkIfEmployee() async {
    final userId = LoginState.userId;
    final userTypeId = await UserModel().getCurrentUserTypeId(userId);
    // If the user's userTypeId equals the employee userTypeId, we consider them an employee
    return UserType.employeeId.toUpperCase() == userTypeId?.toUpperCase();
  }

  @override
  Widget build(BuildContext context) {
    final punchCard = context.watch<PunchState>().punchCard;

    return FutureBuilder<bool>(
      future: _isEmployeeFuture,
      builder: (context, snapshot) {
        final isEmployee = snapshot.data ?? false;
        
        return SingleChildScrollView(
          child: Column(
             children: [
              Container(
                padding: EdgeInsets.all(
                  ScreenHelper.screenHeightPercentage(context, 2),
                ),
              ),
              Column(children: [
               
                 ProgressBar(
                  duration: Duration(minutes: 0, seconds:0) ,
                  durationWidget: DurationWidget(),
                  locationId:punchCard?.locationId?? '', 
                  jobTypeWidget:  JobTypeW(jobTypeId: punchCard!.jobTypeId?? '')),
                 SizedBox(height:  ScreenHelper.screenHeightPercentage(context, 4)),
                 SizedBox(
                 //   width: 300.0,  // Example width
                    height: 200.0, // Example height
                    child:                  Stack(
                  children: [
                    Positioned(
                      top: 10,
                      left:ScreenHelper.screenWidthPercentage(context, 50) - 65,
                      child: Container(
                          width: 130,
                          height: 130,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: ColorHelper.thePunchLightRed(),
                          ),
                          child: 
                          // Center(
                          //   child: 
                          //     ElevatedButton(
                          //     onPressed: () => {},
                          //     style: ElevatedButton.styleFrom(
                          //       shape: const CircleBorder(
                          //       // side: BorderSide(color: Colors.red),
                          //       ),
                          //       backgroundColor: Colors.transparent,
                          //       elevation: 0,
                          //       padding: const EdgeInsets.all(0),
                          //     ),
                          //     child: 
                              ElevatedButton(
                              onPressed: () async {
                                  // 3A. Check session first
                                  final sessionOk = await checkSession(context);
                                  if (!sessionOk) {
                                    // Session was forced out, bail out
                                    return;
                                  }

                                  var connectivityResult = await (Connectivity().checkConnectivity());
                                  if (connectivityResult == ConnectivityResult.none) {
                                    await showDialog(
                                      context: context,
                                      builder: (BuildContext context) => AlertDialog(
                                        content: const Text(
                                            "No Internet Connection, Please Reconnect and Try Again"),
                                        actions: [
                                          TextButton(
                                            child: const Text("OK"),
                                            onPressed: () {
                                              Navigator.of(context).pop();
                                            },
                                          ),
                                        ],
                                      ),
                                    );
                                  } else {
                                    bool punchOutConfirmed = false;
                                    await showPunchOutDialog(context, (value) => punchOutConfirmed = value);
                                    if (punchOutConfirmed) {
                                      ScaffoldMessenger.of(context).hideCurrentSnackBar();
                                      await Provider.of<PunchViewModel>(context, listen: false).handlePunchOut();
                                      punchOutConfirmed = false;
                                    }
                                  }
                                },

                                style: ElevatedButton.styleFrom(
                                  shape: const CircleBorder(),
                                padding: const EdgeInsets.all(40),
                                backgroundColor: ColorHelper.thePunchLightPink(),
                                
                                
                                ),
                                child: Text(
                                  '${AppLocalizations.of(context)!.punch}\n${AppLocalizations.of(context)!.outW}',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(color: ColorHelper.thePunchRed(), fontSize: 16, fontWeight: FontWeight.bold)
                                // style: Theme.of(context).textTheme.bodyLarge,
                              
                                ),
                              ),
                            ),
                    //   ),
                    // )
                    ),
                    // Positioned(
                    //   top: 10,
                    //   right:ScreenHelper.screenWidthPercentage(context, 0) + 15,
                    //   child: Container(
                    //       width: 120,
                    //       height: 80,

                    //       child: Center(
                    //         child:  LocationNotesButton(
                    //       locationId: punchCard?.locationId?? '',
                    //     ) 
                    //          ),
                    //   ),
                    // ),
                    if(!isEmployee)
                    Positioned(
                      top: 105,
                      left:ScreenHelper.screenWidthPercentage(context, 0)+ 25 ,
                      child: !isEmployee ? const ViewPunchCardButton() : const SizedBox(),

                    )  ,
                    if(!isEmployee)
                    Positioned(
                      top: 95,
                      right:ScreenHelper.screenWidthPercentage(context, 0) + 25,
                      child: Container(
                          width: 105,
                          height: 105,
                          decoration: BoxDecoration(
                            color:PunchState().isTaskActive ? ColorHelper.thePunchLightBlue(): ColorHelper.thePunchBlue(),
                            shape: BoxShape.circle,
                            
                          ),
                          child: !isEmployee ? Consumer<PunchState>(
                          builder: (context, punchState, child) {
                            if (punchState.isTaskActive) {
                              return EndTaskButton(punchState: punchState);
                            } else {
                              return StartTaskButton();
                            }
                          },
                          ): Expanded(child: SizedBox()),
                      ),
                    ),                  
                    //Location Notes Button
                    Positioned(
                      top: 20,
                      right:ScreenHelper.screenWidthPercentage(context, 0) + 50,
                      child:  Center(
                            child:  LocationNotesButton(
                          locationId: punchCard?.locationId?? '',
                        ) 
                             
                      ),
                    ),
                    //Attachment Button
                    Positioned(
                      top: 20,
                      left:ScreenHelper.screenWidthPercentage(context, 0)+ 35 ,
                      child: const AttachmentButton(), 

                    )  ,
                 
                  ]
                ),
              
                  )
                ]),

            ],
          ),
        );
      },
    );
  }
}