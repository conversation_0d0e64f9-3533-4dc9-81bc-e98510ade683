{"locationNotesTitle": "Notas de Ubicación", "punchStatus": "Estado de Fichaje", "address1": "Dirección 1", "address2": "Dirección 2", "addressInformation": "Información de la Dirección", "administrator": "Administrador", "administratorDescription": "Controla todo en el sistema.", "cancel": "<PERSON><PERSON><PERSON>", "city": "Ciudad", "configSettingUserSessionExpirationMinsMvc": "Expiración de Sesión del Usuario en el Sitio Administrativo (Minutos)", "configSettingUserSessionExpirationMinsMvcDescription": "Cantidad de tiempo en minutos antes de que las sesiones inactivas de los usuarios expiren en el sitio administrativo.", "clockIn": "<PERSON><PERSON><PERSON>", "clockOut": "<PERSON><PERSON><PERSON>", "punch": "<PERSON><PERSON><PERSON>", "inW": "Entrada", "outW": "Salida", "confirmPassword": "Con<PERSON><PERSON><PERSON>", "confirmPasswordNotMatchPassword": "La contraseña confirmada no coincide con la contraseña ingresada.", "confirmPasswordRequired": "Se requiere confirmar la contraseña.", "contact": "Contacto", "contactTypeDescription": "Plantilla de permisos predeterminada para un contacto.", "contactInformation": "Información de Contacto", "country": "<PERSON><PERSON>", "emailAddressAlreadyRegistered": "Esta dirección de correo electrónico ya está registrada.", "firstName": "Nombre", "firstNameRequired": "El nombre es requerido.", "forcedOut": "Expulsado", "forgotPassword": "<PERSON><PERSON><PERSON><PERSON> la Contraseña", "internalError": "Hubo un error interno. Por favor, intente de nuevo.", "invalidRequest": "La solicitud enviada es inválida.", "lastName": "Apellido", "lastNameRequired": "El apellido es requerido.", "loggedOut": "<PERSON><PERSON><PERSON>", "sandbox": "Entorno de Pruebas", "login": "<PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "mainEmailAddress": "<PERSON><PERSON><PERSON>", "mainEmailAddressMustBeValidEmailFormat": "El correo electrónico principal debe tener un formato válido.", "mainEmailAddressRequired": "El correo electrónico principal es requerido.", "messages": "<PERSON><PERSON><PERSON><PERSON>", "networkUnavailable": "Red no Disponible", "ok": "Aceptar", "punchedIn": "Fichado <PERSON>", "organizationId": "Identificador de Organización", "organizationIdNotFound": "El identificador de organización no fue encontrado.", "organizationIdRequired": "El identificador de organización es requerido.", "organizationName": "Nombre de la Organización", "organizationNameRequired": "El nombre de la organización es requerido.", "password": "Contraseña", "passwordNotConformRules": "La contraseña no cumple con las reglas.", "passwordRequired": "La contraseña es requerida.", "phone": "Teléfono", "register": "Registrarse", "registerOrganization": "Registrar Organización", "schedule": "<PERSON><PERSON><PERSON>", "sessionForcedOutReason": "Otra sesión ha comenzado desde la dirección IP, si cree que es un error, inicie sesión nuevamente y cambie su contraseña.", "state": "Estado", "sysop": "Operador del Sistema", "sysopDescription": "Controla todo en el sistema y puede ver detalles extendidos del desarrollador.", "timedOut": "Tiempo Expirado", "punchCard": "Tarjeta de Fichaje", "timeClock": "<PERSON><PERSON><PERSON>", "username": "Nombre de Usuario", "usernameOrPasswordIncorrect": "El nombre de usuario o la contraseña es incorrecto.", "usernameRequired": "El nombre de usuario es requerido.", "userMarkedInactive": "Este usuario está marcado como inactivo.", "punchedThisWeek": "Tiempo Total Fichado", "punchedToday": "<PERSON><PERSON><PERSON>", "zip": "Código Postal", "deviceIdRequired": "El identificador del dispositivo es requerido.", "invalidHash": "El valor de hash enviado es inválido.", "noResultsFound": "No se encontraron resultados.", "pleaseWait": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "sessionNotFound": "La sesión no fue encontrada.", "rotatedOut": "Rotado Fuera", "sessionForcedOut": "Esta sesión ha sido forzada a salir.", "sessionLoggedOut": "Esta sesión ha cerrado sesión.", "sessionTimedOut": "Esta sesión ha expirado.", "userNotFoundOrInactive": "Este usuario no fue encontrado o está inactivo.", "sessionRotatedOut": "La sesión fue rotada.", "configSettingUserSessionExpirationMinsApi": "Expiración de Sesión del Usuario en Dispositivos Móviles (Minutos)", "configSettingUserSessionExpirationMinsApiDescription": "Cantidad de tiempo en minutos antes de que las sesiones inactivas de los usuarios expiren en los dispositivos móviles.", "home": "<PERSON><PERSON>o", "locations": "Ubicaciones", "allLocations": "Todas las Ubicaciones", "changeLocation": "Cambiar Ubicación", "action": "Acción", "activate": "Activar", "activateAreYouSure": "¿Está seguro de que desea activar este registro?", "activateQuestion": "¿Activar?", "active": "Activo", "activeType": "Tipo Activo", "address": "Dirección", "all": "Todos", "deactivate": "Desactivar", "deactivateAreYouSure": "¿Está seguro de que desea desactivar este registro?", "deactivateQuestion": "¿Desactivar?", "edit": "<PERSON><PERSON>", "inactive": "Inactivo", "locationName": "Nombre de Ubicación", "locationNotFound": "Esta ubicación no fue encontrada.", "no": "No", "noPermission": "No tiene permiso para acceder a esta función.", "search": "Buscar", "successExclamation": "¡Éxito!", "view": "<PERSON>er", "yes": "Sí", "abbreviation": "Abreviatura", "emailAddress": "Correo Electrónico", "location": "Ubicación", "addLocation": "Agregar Ubicación", "locationNameRequired": "El nombre de la ubicación es requerido.", "submit": "Enviar", "viewLocations": "Ver Ubicaciones", "abbreviationAlreadyExists": "Esta abreviatura ya existe.", "abbreviationRequired": "La abreviatura es requerida.", "editLocation": "Editar Ubicación", "userpermissionCanEditLocations": "Puede Editar Ubicaciones", "userpermissionCanEditLocationsDescription": "Permite al usuario editar los detalles de la ubicación.", "userpermissionCanViewLocations": "Puede Ver Ubicaciones", "userpermissionCanViewLocationsDescription": "Permite al usuario ver los detalles de la ubicación.", "usernameCannotContainSpaces": "El nombre de usuario no puede contener espacios.", "chat": "Cha<PERSON>", "chatDescription": "<PERSON><PERSON><PERSON><PERSON>", "email": "Correo Electrónico", "emailDescription": "Mensajes de Correo Electrónico", "actions": "Acciones", "addEmployee": "Ag<PERSON>gar <PERSON>", "employees": "Empleados", "employeeName": "Nombre del Empleado", "employeeType": "Tipo de Empleado", "userpermissionCanEditEmployees": "<PERSON><PERSON><PERSON>", "userpermissionCanEditEmployeesDescription": "Permite al usuario editar los detalles del empleado.", "userpermissionCanViewEmployees": "<PERSON><PERSON><PERSON>pleado<PERSON>", "userpermissionCanViewEmployeesDescription": "Permite al usuario ver los detalles del empleado.", "viewEmployees": "Ver Empleados", "editEmployee": "<PERSON><PERSON>", "employee": "Empleado", "employeeId": "ID de Empleado", "employeeNotFound": "Este empleado no fue encontrado.", "employeeTypeRequired": "El tipo de empleado es requerido.", "usernameAlreadyRegistered": "Este nombre de usuario ya está registrado.", "userpermissionCanEditContacts": "<PERSON><PERSON><PERSON>", "userpermissionCanEditContactsDescription": "Permite al usuario editar los detalles del contacto.", "userpermissionCanViewContacts": "<PERSON><PERSON><PERSON>", "userpermissionCanViewContactsDescription": "Permite al usuario ver los detalles del contacto.", "addContact": "<PERSON>g<PERSON><PERSON>", "contacts": "Contactos", "contactName": "Nombre del Contacto", "viewContacts": "<PERSON><PERSON> Contact<PERSON>", "contactId": "ID del Contacto", "contactLocationRequired": "Se requiere una ubicación para este contacto.", "contactNotFound": "Este contacto no fue encontrado.", "editContact": "<PERSON><PERSON>", "chooseLocation": "Elegir Ubicación", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "employeeTypeDescription": "Plantilla de permisos predeterminada para un empleado.", "contactType": "Tipo de Contacto", "contactTypeRequired": "El tipo de contacto es requerido.", "chooseContact": "Elegir <PERSON>", "addContactToLocation": "Agregar Contacto a la Ubicación", "addEmployeeToLocation": "Agregar Empleado a la Ubicación", "contactAlreadyInLocation": "Este contacto ya está en esta ubicación.", "areYouSureQuestion": "¿Está Seguro?", "areYouSureRemoveContactFromLocation": "¿Está seguro de que desea eliminar este contacto de esta ubicación?", "contactNotFoundInLocation": "Este contacto no fue encontrado en esta ubicación.", "removeContactFromLocation": "Eliminar Contacto de la Ubicación", "notes": "Notas del Gerente", "employeeTypeActions": "Acciones del Tipo de Empleado", "userpermissionCanViewEmployeeTypes": "Puede Ver Tipos de Empleados", "userpermissionCanViewEmployeeTypesDescription": "Permite al usuario ver los detalles del tipo de empleado.", "viewEmployeeTypes": "Ver Tipos de Empleados", "employeeCount": "Conteo de Empleados", "employeeTypes": "Tipos de Empleados", "employeeTypeName": "Nombre del Tipo de Empleado", "permissionCount": "Con<PERSON>o de Permisos", "addEmployeeType": "Agregar <PERSON>pleado", "userpermissionCanEditEmployeeTypes": "<PERSON>uede Editar Tipos de Empleados", "userpermissionCanEditEmployeeTypesDescription": "Permite al usuario editar los detalles del tipo de empleado.", "employeeTypeNotFound": "Este tipo de empleado no fue encontrado.", "editEmployeeType": "Editar T<PERSON> Empleado", "permissions": "<PERSON><PERSON><PERSON>", "description": "Descripción", "permission": "<PERSON><PERSON><PERSON>", "notEditable": "Este registro no puede ser editado.", "employeeTypeNameRequired": "El nombre del tipo de empleado es requerido.", "copyFromEmployeeType": "Copiar de Tipo de Empleado Existente", "addPermissionToEmployeeType": "Agregar <PERSON> al Tipo de Empleado", "areYouSureRemovePermissionFromEmployeeType": "¿Está seguro de que desea eliminar este permiso de este tipo de empleado?", "choosePermission": "<PERSON><PERSON><PERSON>", "permissionAlreadyInEmployeeType": "Este permiso ya está en este tipo de empleado.", "permissionNotFound": "El permiso no fue encontrado.", "permissionNotFoundInEmployeeType": "Este permiso no fue encontrado en este tipo de empleado.", "remove": "Eliminar", "userpermissionCanPunchIn": "<PERSON><PERSON><PERSON>", "userpermissionCanPunchInDescription": "Permite al usuario fichar entrada o salida.", "userpermissionCanEditPunchCards": "Puede Editar Tarjetas de Fichaje", "userpermissionCanEditPunchCardsDescription": "Permite al usuario editar los detalles de las tarjetas de fichaje de otros usuarios.", "userpermissionCanViewPunchCards": "Puede Ver Tarjetas de Fichaje", "userpermissionCanViewPunchCardsDescription": "Permite al usuario ver los detalles de las tarjetas de fichaje de otros usuarios.", "punchcardNotFound": "Esta tarjeta de fichaje no fue encontrada.", "geoFenceWarning": "ADVERTENCIA: Salió del área de la ubicación sin fichar salida.", "geoFenceEntered": "Área de Geo-cerca Ingresada", "geoFenceEnteredDescription": "El usuario ingresó al área de geo-cerca.", "geoFenceExited": "Área de Geo-cerca Salida", "geoFenceExitedDescription": "El usuario salió del área de geo-cerca.", "geoLocationDisabled": "Geolocalización Desactivada", "geoLocationDisabledDescription": "Geolocalización del dispositivo desactivada.", "geoLocationEnabled": "Geolocalización Activada", "geoLocationEnabledDescription": "Geolocalización del dispositivo activada.", "geoLocationUpdate": "Actualización de Geolocalización", "geoLocationUpdateDescription": "Actualización de la geolocalización del usuario.", "oldPasswordRequired": "La contraseña anterior es requerida.", "oldPasswordIncorrect": "La contraseña anterior es incorrecta.", "updatePassword": "Actualizar <PERSON>", "newPassword": "Nueva Contraseña", "oldPassword": "Contraseña Anterior", "contactTypes": "Tipos de Contacto", "contactTypeActions": "Acciones de Tipo de Contacto", "contactTypeName": "Nombre del Tipo de Contacto", "contactTypeNameRequired": "El nombre del tipo de contacto es requerido.", "contactTypeNotFound": "Este tipo de contacto no fue encontrado.", "userpermissionCanEditContactTypes": "<PERSON><PERSON>e Editar Tip<PERSON> de Contacto", "userpermissionCanEditContactTypesDescription": "Permite al usuario editar los detalles del tipo de contacto.", "userpermissionCanViewContactTypes": "Puede Ver Tipos de Contacto", "userpermissionCanViewContactTypesDescription": "Permite al usuario ver los detalles del tipo de contacto.", "permissionAlreadyInContactType": "Este permiso ya está en este tipo de contacto.", "permissionNotFoundInContactType": "Este permiso no fue encontrado en este tipo de contacto.", "addContactType": "Agregar <PERSON><PERSON>", "viewContactTypes": "Ver Tipos de Contacto", "copyFromContactType": "Copiar de Tipo de Contacto Existente", "contactCount": "Conteo de Contactos", "addPermissionToContactType": "Agregar <PERSON> al Tipo de Contacto", "areYouSureRemovePermissionFromContactType": "¿Está seguro de que desea eliminar este permiso de este tipo de contacto?", "editContactType": "<PERSON>ar <PERSON>", "daily": "Diario", "day": "Día", "firstOccurrence": "Primera Ocurrencia", "fourthOccurrence": "Cuarta Ocurrencia", "friday": "Viernes", "hour": "<PERSON><PERSON>", "lastOccurrence": "Última Ocurrencia", "minute": "Min<PERSON>", "monday": "<PERSON><PERSON>", "month": "<PERSON><PERSON>", "saturday": "Sábado", "second": "<PERSON><PERSON><PERSON>", "secondOccurrence": "Segunda Ocurrencia", "sunday": "Domingo", "thirdOccurrence": "Tercera Ocurrencia", "thursday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "week": "Se<PERSON>", "weekday": "Día de Semana", "weekend": "Fin de Semana", "addSchedule": "<PERSON><PERSON><PERSON><PERSON>", "employeeSchedule": "Horario del Empleado", "locationSchedule": "Horario de Ubicación", "schedules": "<PERSON><PERSON><PERSON>", "userpermissionCanEditSchedules": "<PERSON><PERSON><PERSON>", "userpermissionCanViewSchedules": "<PERSON><PERSON><PERSON> V<PERSON> Ho<PERSON>", "viewSchedules": "<PERSON><PERSON>", "duration": "Duración", "every": "Cada", "frequency": "Frecuencia", "nextScheduled": "Siguiente Programado", "on": "En", "startDate": "Fecha de Inicio", "startTime": "Hora de Inicio", "viewSchedule": "<PERSON><PERSON>", "repeatEvery": "Repetir cada", "showPlannedSchedule": "Mostrar Horario Planificado", "scheduleNotFound": "Este horario no fue encontrado.", "timeZone": "Zona Horaria", "addRepeatingSchedule": "<PERSON><PERSON><PERSON><PERSON>", "repeatingSchedules": "<PERSON><PERSON><PERSON>", "viewRepeatingSchedule": "<PERSON>er Ho<PERSON>", "viewRepeatingSchedules": "<PERSON>er Ho<PERSON>ios <PERSON>", "editSchedule": "<PERSON><PERSON>", "endLocationTimeZone": "Fin (Zona horaria de la ubicación)", "from": "<PERSON><PERSON>", "invalidDateRange": "Rango de fechas inválido", "invalidDuration": "Duración inválida", "invalidRecurringRange": "<PERSON><PERSON> recurrente inválido", "invalidTimeOfDay": "Hora del día inválida", "originalLocationTimeZone": "Original (Zona horaria de la ubicación)", "scheduledLocalTimeZone": "Programado (Zona horaria actual)", "scheduledLocationTimeZone": "Programado (Zona horaria de la ubicación)", "selectAtLeastOneDayOfTheWeek": "Seleccione al menos un día de la semana.", "startLocationTimeZone": "Inicio (Zona horaria de la ubicación)", "startTimeLocationTimeZone": "Hora de Inicio (Zona horaria de la ubicación)", "to": "<PERSON><PERSON>", "schedulegeneratorInvalidRecurringDailyfreqOccurseveryfreqId": "Este horario tiene un Recurring_DailyFreq_OccursEveryFreq_ID inválido.", "schedulegeneratorInvalidRecurringFrequencyId": "Este horario tiene un Recurring_Frequency_ID inválido.", "schedulegeneratorIsNotRecurringNoOnetimeOccurson": "Este horario no es recurrente y no tiene un valor de OneTime_OccursOn.", "schedulegeneratorIsRecurringNoRecurringDailyfreqOccursonce": "Este horario es recurrente y no tiene un valor de Recurring_DailyFreq_OccursOnce.", "schedulegeneratorNotRecurringDailyfreqOccursonceNoRecurringDailyfreqStartOrEnd": "Este horario no es recurrente diario una vez y no tiene un valor de Recurring_DailyFreq_StartingOn o Recurring_DailyFreq_EndingOn.", "schedulegeneratorRecurringFrequencyDayNoRecurringFrequencyEverynfreq": "Este horario es recurrente diario y no tiene un valor de Recurring_Frequency_EveryNFreq.", "schedulegeneratorRecurringFrequencyMonthNoRecurringFrequencyMonthlyThedayoftheweekId": "Este horario es recurrente mensual y no tiene un valor de Recurring_Frequency_Monthly_TheDayOfTheWeek_ID.", "schedulegeneratorRecurringFrequencyMonthNoRecurringFrequencyMonthlyTheweekId": "Este horario es recurrente mensual y no tiene un valor de Recurring_Frequency_Monthly_TheWeek_ID.", "userpermissionCanSendChatMessage": "<PERSON><PERSON><PERSON>", "userpermissionCanSendChatMessageDescription": "Permite al usuario enviar mensajes de chat.", "userpermissionCanViewChatMessage": "<PERSON><PERSON><PERSON> Ver Mensajes de <PERSON>", "userpermissionCanViewChatMessageDescription": "Permite al usuario ver chats con otros usuarios.", "message": "Men<PERSON><PERSON>", "recentChatMessages": "Mensajes Recientes de Chat", "send": "Enviar", "sendChatMessageTo": "Enviar mensaje de chat a", "punchCards": "Tarjetas de Fichaje", "viewPunchCards": "Ver Tarjetas de Fichaje", "clockedInBy": "<PERSON><PERSON><PERSON> por", "clockedOutBy": "Fichado <PERSON> por", "editPunchCard": "<PERSON>ar <PERSON>", "punchCardId": "ID de Tarjeta de Fichaje", "punchCardNotFound": "Esta tarjeta de fichaje no fue encontrada.", "clockOutBeforeClockedIn": "La salida debe ser después de la entrada.", "clockedIn": "Fichado <PERSON>", "christmasHoliday": "Vacaciones de Navidad", "fourthOfJulyHoliday": "Vacaciones del 4 de Julio", "newYearsHoliday": "Vacaciones de Año Nuevo", "thanksgivingHoliday": "Vacaciones de Acción de Gracias", "userpermissionCanEditNotes": "<PERSON><PERSON><PERSON>", "userpermissionCanViewNotes": "<PERSON><PERSON><PERSON>", "noteRequired": "Se requiere texto en la nota.", "addNote": "<PERSON><PERSON><PERSON><PERSON>", "editNote": "<PERSON><PERSON>", "noteNotFound": "Esta nota no fue encontrada.", "changedBy": "Modificado por", "clear": "Limpiar", "chooseEmployee": "Elegir Empleado", "employeeOrLocationRequired": "Se debe seleccionar un empleado o una ubicación.", "userpermissionCanEditChecklists": "Puede Editar Listas de Verificación", "userpermissionCanEditChecklistsDescription": "Permite al usuario editar listas de verificación.", "userpermissionCanViewChecklists": "Puede Ver Listas de Verificación", "userpermissionCanViewChecklistsDescription": "Permite al usuario ver listas de verificación.", "addChecklist": "Agregar Lista de Verificación", "checklist": "Lista de Verificación", "checklists": "Listas de Verificación", "addChecklistItem": "Agregar <PERSON>", "checklistNotFound": "Esta lista de verificación no fue encontrada.", "editChecklist": "Editar Lista de Verificación", "editChecklistItem": "<PERSON><PERSON>", "items": "Elementos", "close": "<PERSON><PERSON><PERSON>", "endLocalTimeZone": "Fin (Zona horaria local)", "startLocalTimeZone": "Inicio (Zona horaria local)", "createdBy": "<PERSON><PERSON>o por", "note": "<PERSON>a", "error": "Error", "calendar": "Calendario", "alerts": "<PERSON><PERSON><PERSON>", "allPunchCards": "Todas las Tarjetas de Fichaje", "currentPayPeriod": "Período de Pago Actual", "customTimePeriod": "Período de Tiempo Personalizado", "hours": "<PERSON><PERSON>", "recentPunchCards": "Tarjetas de Fichaje Recientes", "addPunchCard": "Agregar Tarjeta de Fichaje", "timePeriod": "Período de Tiempo", "endDateRequired": "La fecha de finalización es requerida.", "employeeRequired": "El empleado es requerido.", "startDateRequired": "La fecha de inicio es requerida.", "startDateCannotBeAfterEndDate": "La fecha de inicio no puede ser después de la fecha de finalización.", "locationRequired": "La ubicación es requerida.", "selectLocationLoadSchedules": "Seleccione una ubicación para cargar los horarios.", "takeNote": "<PERSON><PERSON>", "endTime": "Hora de Finalización", "endTimeLocationTimeZone": "Hora de Finalización (Zona horaria de la ubicación)", "recurring": "<PERSON><PERSON><PERSON>", "endsBy": "<PERSON><PERSON><PERSON>", "invalidEndDate": "Fecha de finalización inválida", "noEndDate": "Sin Fecha de Finalización", "nextLocation": "Siguiente Ubicación", "travelTime": "Tiempo de Viaje", "localTime": "Hora Local", "scheduledTime": "Hora Programada", "scheduleDetailsFor": "Detalles del Horario para", "days": "Días", "weeks": "Semanas", "months": "Meses", "dayOfMonth": "Día del Mes", "onSpecificDay": "En Día Específico", "onSpecificWeek": "En Semana Específica", "editRepeatingSchedule": "<PERSON><PERSON>", "april": "Abril", "august": "Agosto", "december": "Diciembre", "february": "<PERSON><PERSON><PERSON>", "january": "<PERSON><PERSON>", "july": "<PERSON>", "june": "<PERSON><PERSON>", "march": "<PERSON><PERSON>", "may": "Mayo", "november": "Noviembre", "october": "Octubre", "september": "Septiembre", "onSpecificMonth": "En Mes Específico", "endTimeRequired": "La hora de finalización es requerida.", "noDescription": "Sin Descripción", "scheduletemplateInvalidRecurringFrequencyId": "Este horario tiene una selección inválida de Día, Semana, Mes.", "scheduletemplateRecurringFrequencyMonthNoRecurringFrequencyEverynfreq": "Este horario es recurrente mensual y no tiene un valor de Recurring_Frequency_EveryNFreq.", "scheduletemplateRecurringFrequencyWeekNoRecurringFrequencyEverynfreq": "Este horario es recurrente semanal y no tiene un valor de Recurring_Frequency_EveryNFreq.", "scheduletemplateRecurringFrequencyWeekNoWeekdaySelected": "Este horario es recurrente semanal y no se seleccionaron días de la semana.", "startTimeCannotBeAfterEndTime": "La hora de inicio no puede ser posterior a la hora de finalización.", "startTimeRequired": "La hora de inicio es requerida.", "scheduletemplateRecurringFrequencyInvalidMonthDay": "Este horario es recurrente mensual en un día específico del mes y el día no es válido.", "scheduletemplateRecurringFrequencyNoMonthlyOnMonth": "Este horario es recurrente mensual en un mes específico y no tiene un valor de mes.", "scheduletemplateRecurringFrequencyNoMonthlyWeek": "Este horario es recurrente mensual en una semana específica del mes y no tiene un valor de ocurrencia semanal.", "scheduletemplateRecurringFrequencyNoMonthlyWeekday": "Este horario es recurrente mensual en una semana específica del mes y no tiene un valor de día de la semana.", "scheduletemplateRecurringFrequencyNoMonthDay": "Este horario es recurrente mensual en un día específico del mes y no tiene un valor de día.", "areYouSureEditSubscriptionTemplateWillRemoveFutureSchedules": "¿Está seguro de que desea editar este horario recurrente? Esto eliminará cualquier horario futuro ya creado.", "addNewSchedule": "Agregar Nuevo Horario", "editThisSchedule": "<PERSON><PERSON>", "viewThisSchedule": "Ver Este <PERSON>", "of0": "De", "onThe": "El", "nd": "º\n    Como el 2º del mes.", "rd": "º\n    Como el 3º del mes.", "st": "º\n    Como el 1º del mes.", "th": "º\n    Como el 4º del mes.", "year": "<PERSON><PERSON>", "years": "<PERSON><PERSON><PERSON>", "in0": "En", "admin": "Administrador", "adminDescription": "Administrativo", "locationDescription": "Trabajo no programado", "scheduleDescription": "Trabajo programado", "travelTimeDescription": "Tiempo de viaje", "invalidJobType": "Tipo de trabajo inválido", "jobType": "Tipo de Trabajo", "userpermissionCanViewJobTypes": "Puede Ver Tipos de Trabajo", "userpermissionCanViewJobTypeDescription": "Permite al usuario ver tipos de trabajo.", "addJobType": "Agregar Tipo de Trabajo", "editJobType": "Editar Tipo de Trabajo", "jobTypes": "Tipos de Trabajo", "jobTypeActions": "Acciones del Tipo de Trabajo", "jobTypeNotFound": "Este tipo de trabajo no fue encontrado.", "userpermissionCanEditJobTypes": "<PERSON>uede Editar Tipos de Trabajo", "userpermissionCanEditJobTypesDescription": "Permite al usuario editar tipos de trabajo.", "viewJobTypes": "Ver Tipos de Trabajo", "scheduletemplateCouldNotBeCreated": "No se pudo crear una nueva plantilla de horario. Inténtelo de nuevo.", "userpermissionCanViewAlerts": "<PERSON><PERSON><PERSON>", "userpermissionCanViewReports": "Puede Ver Informes", "userpermissionCanViewReportsDescription": "Permite al usuario ver informes.", "reportUserSessions": "Sesiones de Usuario", "reportUserSessionsDescription": "Muestra los tiempos de inicio/cierre de sesión y las razones de expulsión de las sesiones de usuario.", "administration": "Administración", "reports": "Informes", "reportName": "Nombre del Informe", "run": "<PERSON><PERSON><PERSON><PERSON>", "reportNotFound": "Este informe no fue encontrado.", "configSettingAlertClockInLimitMinMvc": "Límite de Alerta de Fichaje (Minutos)", "configSettingAlertClockInLimitMinMvcDescription": "Cantidad de tiempo en minutos después de que comienza un horario en el que se genera una alerta si el empleado no ficha entrada.", "report": "Informe", "setReportParameters": "E<PERSON>cer <PERSON>met<PERSON> del Informe", "fillInOptionsClickRunReport": "Complete las opciones, luego haga clic en Ejecutar Informe.", "adobeAcrobat": "Adobe Acrobat", "commaSeparatedValues": "Valores Separados por Comas"}