import '../base_data.dart';
import '../data/schedule_template.dart';
import '../hive_db.dart';

class ScheduleTemplateModel extends BaseDataModel<ScheduleTemplate> {
  @override
  Future<Iterable<ScheduleTemplate>> get all async => (await HiveDb.database).scheduleTemplates.values;

  @override
  Future<void> save(Iterable<ScheduleTemplate> t) async {
    final db = await HiveDb.database;
    await db.scheduleTemplates.putAll({for (final e in t) e.id: e});
    notifyListeners();
  }

  Future<void> remove(Iterable<String> ids) async {
    final db = await HiveDb.database;
    await db.scheduleTemplates.deleteAll(ids);
    notifyListeners();
  }

}
