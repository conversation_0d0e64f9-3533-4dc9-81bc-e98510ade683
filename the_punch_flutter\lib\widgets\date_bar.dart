import 'dart:async';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../helpers/color_helper.dart';
import '../helpers/screen_helper.dart';
import '../misc/closest_date.dart';
import '../misc/extensions.dart';

class DateBar extends StatefulWidget {
  final Map<DateTime, int>? pipsByDate;
  final DateTime initialDate;
  final ValueNotifier<DateTime> selectedDate;

  DateBar(
      {super.key,
      required this.initialDate,
      required this.selectedDate,
      Map<DateTime, int>? pipsByDate})
      : pipsByDate = pipsByDate?.map<DateTime, int>(
            (key, value) => MapEntry(key.forceLocal, value));

  @override
  State<DateBar> createState() => _DateBarState();
}

class _DateBarState extends State<DateBar> {
  static const initialPage = 1073741824;
  final pageController = PageController(initialPage: initialPage);

  @override
  void initState() {
    super.initState();
    widget.selectedDate.addListener(_selectedDateListener);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      pageController.jumpToPage(initialPage + calculateSelectedDateOffset());
    });
  }

  void _selectedDateListener() {
    if (pageController.hasClients) {
      final newPage = initialPage + calculateSelectedDateOffset();
      unawaited(pageController.animateToPage(newPage,
          duration: const Duration(milliseconds: 500), curve: Curves.ease));
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorOnPrimary = theme.colorScheme.onPrimary;

    return Padding(
      padding: EdgeInsets.only(
        top: ScreenHelper.screenHeightPercentage(context, 2),
      ),
      child: Container(
        color: ColorHelper.thePunchRed(),
        width: double.maxFinite,
        child: ConstrainedBox(
          constraints:
              const BoxConstraints(maxWidth: 500, minHeight: 80, maxHeight: 80),
          child: Padding(
            padding: const EdgeInsets.only(top: 8),
            child: LayoutBuilder(
              builder: (context, constraints) => Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  buildTodayButton(context, colorOnPrimary),
                  ConstrainedBox(
                    constraints: const BoxConstraints(maxWidth: 500),
                    child: SizedBox(
                      width: constraints.maxWidth - 96,
                      height: 80,
                      child: buildPageView(),
                    ),
                  ),
                  buildCalendarButton(context, colorOnPrimary),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  PageView buildPageView() => PageView.builder(
        controller: pageController,
        itemBuilder: (context, index) {
          final offset = index - initialPage;
          final newDate = widget.initialDate.addDays(7 * offset);
          
          final dates = [for (var i = 0; i < 7; i += 1) newDate.addDays(i)];

          return Flex(
            direction: Axis.horizontal,
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              for (final date in dates)
                Flexible(
                  child: _DateBarTile(
                    date: date,
                    selectedDate: widget.selectedDate,
                    isEnabled: (widget.pipsByDate?.containsKey(date) ?? true),
                    pips: widget.pipsByDate?[date] ?? 0,
                  ),
                ),
            ],
          );
        },
      );

  IconButton buildTodayButton(BuildContext context, Color colorOnPrimary) =>
      IconButton(
          onPressed: () {
            final closestDate =
                closestDateToToday(widget.pipsByDate?.keys ?? []);
            if (widget.selectedDate.value == closestDate) {
              _selectedDateListener();
            } else {
              widget.selectedDate.value = closestDate;
            }
          },
          icon: Stack(alignment: Alignment.center, children: [
            Icon(
              Icons.calendar_today,
              color: colorOnPrimary,
            ),
            Padding(
              padding: const EdgeInsets.only(top: 5),
              child: Text(
                DateTime.now().toLocal().day.toString(),
                style:
                    TextStyle(color: colorOnPrimary, fontSize: 10, height: 1),
              ),
            ),
          ]));

  IconButton buildCalendarButton(BuildContext context, Color colorOnPrimary) =>
      IconButton(
          onPressed: () async {
            final pickedDate = await showDatePicker(
              context: context,
              builder: (context, child) => Theme(
                data: ThemeData.light().copyWith(
                  // Set the accent color of the app
                  colorScheme: ColorScheme.light(
                    primary: ColorHelper.thePunchRed(),
                  ),
                ),
                child: child!,
              ),
              initialDate: widget.selectedDate.value,
              firstDate: DateTime.utc(DateTime.now().year - 20),
              lastDate: DateTime.utc(DateTime.now().year + 20),
              selectableDayPredicate: (day) {
                if (widget.pipsByDate == null) return true;
                return widget.pipsByDate!.containsKey(day);
              },
            );
            if (pickedDate != null) {
              widget.selectedDate.value = pickedDate.dateOnly;
            }
          },
          icon: Icon(
            Icons.calendar_today,
            color: colorOnPrimary,
          ));

  int calculateSelectedDateOffset() {
    final initialWeekStart = widget.initialDate;
    final selectedWeekStart = widget.selectedDate.value.startOfWeek;
    final differenceInDays =
        selectedWeekStart.difference(initialWeekStart).inDays;
    final weekOffset = differenceInDays / 7.0;
    if (weekOffset > 0) return weekOffset.ceil();
    return weekOffset.floor();
  }
}

class _DateBarTile extends StatelessWidget {
  final bool isEnabled;
  final DateTime date;
  final ValueNotifier<DateTime> selectedDate;
  final int pips;

  const _DateBarTile({
    required this.date,
    required this.selectedDate,
    this.pips = 0,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onPrimary = theme.colorScheme.onPrimary;

    return GestureDetector(
      onTap: () {
        if (isEnabled) selectedDate.value = date;
      },
      child: ConstrainedBox(
          constraints: const BoxConstraints(minWidth: 25),
          child: Column(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(25),
                child: ValueListenableBuilder<DateTime>(
                  valueListenable: selectedDate,
                  builder: (context, selectedDate, child) {
                    final isSelected = selectedDate == date;
                    final onPrimaryDisabled = onPrimary.withOpacity(0.5);
                    final color = isSelected
                        ? onPrimary
                        : ColorHelper.thePunchLightGray();

                    final Color textColor;
                    if (isSelected) {
                      textColor = ColorHelper.thePunchGray();
                    } else if (isEnabled) {
                      textColor = Colors.white;
                    } else {
                      textColor = onPrimaryDisabled;
                    }
                    final textStyle = theme.textTheme.bodySmall
                        ?.copyWith(color: textColor, height: 1);

                    final languageCode =
                        Localizations.localeOf(context).toLanguageTag();
                    final dow =
                        DateFormat(DateFormat.ABBR_WEEKDAY, languageCode)
                            .format(date);
                    final day =
                        DateFormat(DateFormat.DAY, languageCode).format(date);
                    final month =
                        DateFormat(DateFormat.ABBR_MONTH, languageCode)
                            .format(date);

                    return ColoredBox(
                      color: color,
                      child: Padding(
                        padding: const EdgeInsets.only(
                            top: 8, bottom: 8, left: 4, right: 4),
                        child: IntrinsicWidth(
                          child: Flex(
                              direction: Axis.vertical,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(dow, style: textStyle),
                                Text(day, style: textStyle),
                                Text(month, style: textStyle),
                              ]),
                        ),
                      ),
                    );
                  },
                ),
              ),
              _Pips(pips: pips),
            ],
          )),
    );
  }
}

class _Pips extends StatelessWidget {
  const _Pips({required this.pips});

  final int pips;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final onPrimary = theme.colorScheme.onPrimary;
    final caption = theme.textTheme.bodySmall
        ?.copyWith(color: onPrimary, fontSize: 10, height: .75);

    final Widget pipsWidget;
    if (pips <= 5) {
      pipsWidget = Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          for (var i = 0; i < pips && i < 5; i++)
            Icon(Icons.circle, size: 5, color: onPrimary)
        ],
      );
    } else {
      pipsWidget = Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Icon(Icons.more_horiz, size: 5, color: onPrimary),
          Text(pips.toString(), style: caption),
          Icon(Icons.more_horiz, size: 5, color: onPrimary),
        ],
      );
    }
    return ConstrainedBox(
      constraints: const BoxConstraints(minHeight: 10),
      child: Padding(
        padding: const EdgeInsets.only(top: 3),
        child: pipsWidget,
      ),
    );
  }
}
