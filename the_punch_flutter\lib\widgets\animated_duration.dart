import 'package:flutter/material.dart';
import '../misc/extensions.dart';
import '../state/server_time_state.dart';

class AnimatedFlashCard extends StatefulWidget {
  final DateTime start;
  final Widget child;
  final Color color1;
  final Color color2;

  const AnimatedFlashCard({
    required this.start,
    required this.child,
    required this.color1,
    required this.color2,
    Key? key,
  }) : super(key: key);

  @override
  _AnimatedFlashCardState createState() => _AnimatedFlashCardState();
}

class _AnimatedFlashCardState extends State<AnimatedFlashCard> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Color?> _colorTween;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(duration: const Duration(milliseconds: 900), vsync: this);
    final curve = CurvedAnimation(parent: _animationController, curve: Curves.easeInCubic);
    _colorTween = ColorTween(begin: widget.color1, end: widget.color2).animate(curve);
    _animationController.addListener(() {
      setState(() {});
    });
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) _animationController.reverse();
      if (status == AnimationStatus.dismissed) _animationController.forward();
    });
  }

  void startAnimation() {
    _animationController.forward();
  }

  void stopAnimation() {
    _animationController.stop();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          margin: const EdgeInsets.only(top: 40),
          decoration: BoxDecoration(
            color: _colorTween.value, // Background color changes
            borderRadius: BorderRadius.circular(12),
          ),
          child: child,
        );
      },
      child: widget.child,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}

class AnimatedDuration extends StatelessWidget {
  final DateTime start;
  final Color color1;
  final Color color2;

  const AnimatedDuration(this.start, this.color1, this.color2, {super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final body1 = theme.textTheme.bodyLarge;

    return StreamBuilder<Duration>(
      stream: startsInStream(start, ServerTimeState()),
      builder: (context, snapshot) {
        if (snapshot.data == null) return Container();
        final duration = snapshot.data!;
        if (duration.isNegative) {
          return _AnimatedDuration(duration, color1, color2, onTimeUp: () {
            // Start the animation when the timer reaches 0:00
            final flashCard = context.findAncestorStateOfType<_AnimatedFlashCardState>();
            flashCard?.startAnimation();
          });
        }
        return Text(duration.toAgoWithSeconds, style: body1?.apply(color: Colors.white));
      },
    );
  }

  Stream<Duration> startsInStream(DateTime start, ServerTimeState serverTimeModel) async* {
    while (true) {
      yield start.difference(serverTimeModel.utcTime);
      await Future.delayed(const Duration(seconds: 1));
    }
  }
}

class _AnimatedDuration extends StatelessWidget {
  final Duration duration;
  final Color color1;
  final Color color2;
  final VoidCallback onTimeUp;

  const _AnimatedDuration(this.duration, this.color1, this.color2, {required this.onTimeUp, super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final body1 = theme.textTheme.bodyMedium;

    if (duration.inSeconds == 0) {
      onTimeUp();
    }

    return Text(duration.toAgoWithSeconds, style: body1?.apply(color: Colors.white));
  }
}