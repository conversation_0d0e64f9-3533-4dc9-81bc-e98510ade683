import 'dart:async';

import 'package:flutter/material.dart';
import '../../dataModel/data/job_type.dart';
import '../../dataModel/data_model.dart';
import '../constrained_dialog.dart';
import '../../misc/change_notification_builder.dart';
import '../../pages/view_model_mixin.dart';
import '../../widgets/padded_card.dart';

class JobTypeOrderDialog extends StatelessWidget {
  const JobTypeOrderDialog({super.key});

  @override
  Widget build(BuildContext context) => ChangeNotifierBuilder<_ViewModel>(
        create: (context) => _ViewModel(),
        builder: (context, viewModel, child) {
          if (viewModel.jobTypes.isEmpty) return Container();
          final jobTypes = viewModel.jobTypes;
          return ConstrainedDialog(
              title: 'Job Type Order',
              child: Flexible(
                child: ReorderableList(
                  shrinkWrap: true,
                  onReorder: (oldIndex, newIndex) => viewModel.setOrder(oldIndex, newIndex),
                  itemCount: jobTypes.length,
                  itemBuilder: (context, index) {
                    final jobType = jobTypes[index];
                    return PaddedCard(
                      key: Key(jobType.id),
                      child: Center(
                          child: Stack(
                        children: [
                          Align(
                            child: Text(
                              jobType.name,
                              textAlign: TextAlign.center,
                            ),
                          ),
                          Align(alignment: Alignment.centerRight, child: ReorderableDragStartListener(index: index, child: const Icon(Icons.drag_handle))),
                        ],
                      )),
                    );
                  },
                ),
              ));
        },
      );
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  List<JobType> jobTypes = [];

  _ViewModel() {
    addListenables([
      DataModel().jobTypeModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    jobTypes = (await DataModel().jobTypeModel.activeVisible).toList();
    jobTypes.sort((a, b) => a.order.compareTo(b.order));
    notifyListeners();
  }

  void setOrder(int oldIndex, int newIndex) {
    final jobType = jobTypes[oldIndex];

    if (newIndex < oldIndex) {
      for (var i = newIndex; i < jobTypes.length; i++) {
        jobTypes[i].order = i + 2;
      }
      jobType.order = newIndex + 1;
    } else {
      for (var i = oldIndex; i < newIndex; i++) {
        jobTypes[i].order = i;
      }
      jobType.order = newIndex;
    }

    jobTypes.sort((a, b) => a.order.compareTo(b.order));
    unawaited(DataModel().jobTypeModel.saveDirty(jobTypes));
    notifyListeners();
  }
}
