import 'dart:async';
import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../../api/api_model.dart';
import '../../../dataModel/data/schedule.dart';
import '../../../dataModel/models/schedule_model.dart';
import '../../../dataModel/data_model.dart';
import '../../../dataModel/hive_db.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/extensions.dart';
import '../../view_model_mixin.dart';

class TableEventsExample extends StatefulWidget {
  const TableEventsExample({super.key});

  @override
  TableEventsExampleState createState() => TableEventsExampleState();
}

class TableEventsExampleState extends State<TableEventsExample> {
  late final ValueNotifier<List<Event>> _selectedEvents;
  RangeSelectionMode _rangeSelectionMode = RangeSelectionMode.toggledOff;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  DateTime? _rangeStart;
  DateTime? _rangeEnd;
  ValueNotifier<bool> _isLoading = ValueNotifier(false);  // Loading state indicator

  @override
  void initState() {
    super.initState();
    _selectedDay = _focusedDay;
    _selectedEvents = ValueNotifier(_getEventsForDay(_selectedDay!));
  }

  @override
  void dispose() {
    _selectedEvents.dispose();
    super.dispose();
  }

  List<Event> _getEventsForDay(DateTime day) => kEvents[day] ?? [];

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    if (!isSameDay(_selectedDay, selectedDay)) {
      setState(() {
        _selectedDay = selectedDay;
        _focusedDay = focusedDay;
        _rangeStart = null;
        _rangeEnd = null;
        _rangeSelectionMode = RangeSelectionMode.toggledOff;
      });

      _selectedEvents.value = _getEventsForDay(selectedDay);
    }
    context.go('/schedules', extra: selectedDay);
  }

  // void _fetchAndUpdateCalendar() async {
  //   final monthYear = "${_focusedDay.year}-${_focusedDay.month.toString().padLeft(2, '0')}";

  //   // Check if the month is already loaded
  //   final scheduleModel = Provider.of<ScheduleModel>(context, listen: false);
  //   if (await scheduleModel.isMonthLoaded(monthYear)) { 
  //     print("Data for month $monthYear is already loaded.");
  //     return; // Skip loading if data is already loaded
  //   }

  //   setState(() => _isLoading = true); // Show loading spinner

  //   try {
  //     List<Schedule> schedules = await ApiModel().getSchedulesForMonth(_focusedDay.month, _focusedDay.year);
  //     await scheduleModel.save(schedules);
  //     await scheduleModel.markMonthAsLoaded(monthYear); // Mark this month as loaded
  //     setState(() => _isLoading = false); // Hide loading spinner after data is loaded
  //   } catch (e) {
  //     print('Failed to load data: $e');
  //     setState(() => _isLoading = false); // Ensure loading spinner is hidden on failure
  //   }
  // }

// More efficient solution using a Set to track loaded months
// This avoids redundant API calls for months that are already loaded.


final Set<String> _loadedMonths = {};
  void _fetchAndUpdateCalendar() async {
    final monthYear = "${_focusedDay.year}-${_focusedDay.month.toString().padLeft(2, '0')}";

    if (_loadedMonths.contains(monthYear)) {
      print("Data for month $monthYear is already loaded."); // Skip loading if data is already loaded, removed the need for async I/O processing for this
      return;
    }    

    _isLoading.value = true; // Changed is loading to valueNotifier since this is actually a bit more efficient

    try {
      final schedules = await ApiModel().getSchedulesForMonth(_focusedDay.month, _focusedDay.year);
      final scheduleModel = Provider.of<ScheduleModel>(context, listen: false);
      if (!mounted) return;
      await scheduleModel.save(schedules);
      _loadedMonths.add(monthYear); // Quick and In-Memory operation, using sets rather then DB I/O operations
    } catch (e) {
      print('Failed to load data: $e');
      
    } finally {
      _isLoading.value = false;
    }

  }
  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
    create: (_) => _ViewModel(),
    child: Stack(
      children: [
        _buildCalendar(context),
        if (_isLoading.value)
          Align(
            alignment: Alignment.topCenter,
            child: Padding(
              padding: const EdgeInsets.only(top: 16.0), // Adjust the top padding as needed
              child: CircularProgressIndicator(
                color: ColorHelper.thePunchRed(),
              ),
            ),
          ),
      ],
    ),
  );

  Widget _buildCalendar(BuildContext context) => Card(
      
      elevation: 0,
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(0),
          border: Border.all(color: Colors.grey),
        ),
        child: Consumer<_ViewModel>(
          builder: (context, viewModel, child) => Column(
            children: [
              TableCalendar<Event>(
                calendarBuilders: CalendarBuilders(
                  markerBuilder: (context, datetime, events) => Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: ColorHelper.thePunchAccentRed(),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  alignment: Alignment(0, 0.3), // Move text a bit lower
                  child: Text(
                    viewModel.getEventsByDate(datetime.dateOnly).toString(),
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: Colors.white),
                  ),
                  ),
                ),
                firstDay: kFirstDay,
                lastDay: kLastDay,
                focusedDay: _focusedDay,
                selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
                rangeStartDay: _rangeStart,
                rangeEndDay: _rangeEnd,
                headerStyle: const HeaderStyle(
                  formatButtonVisible: false,
                  leftChevronIcon: Icon(Icons.arrow_back, color: Colors.black),
                  rightChevronIcon: Icon(Icons.arrow_forward, color: Colors.black),
                  titleCentered: true,
                ),
                eventLoader: (day) => List.generate(
                    viewModel.getEventsByDate(day.dateOnly),
                    (index) => Event('Event $day | ${index + 1}'),
                  ),
                   startingDayOfWeek: StartingDayOfWeek.monday,
                calendarStyle: CalendarStyle(
                  outsideDaysVisible: false,
                  selectedDecoration: BoxDecoration(
                    color: ColorHelper.thePunchRed(),
                    shape: BoxShape.circle,
                  ),
                ),
                onDaySelected: _onDaySelected,
                onPageChanged: (focusedDay) { 
                  setState(() {
                    _focusedDay = focusedDay;
                    _fetchAndUpdateCalendar();
                  });
                },
              ),
              SizedBox(height: 10,)
            ],
          ),
        ),
      ),
    );
}

class Event {
  final String title;

  const Event(this.title);

  @override
  String toString() => title;
}

/// Example events.
///
/// Using a [LinkedHashMap] is highly recommended if you decide to use a map.
final kEvents = LinkedHashMap<DateTime, List<Event>>(
  equals: isSameDay,
  hashCode: getHashCode,
)..addAll(_kEventSource);

final _kEventSource = { 
  for (var item in List.generate(50, (index) => index)) 
    DateTime.utc(kFirstDay.year, kFirstDay.month, item * 5) : 
      List.generate(item % 4 + 1, (index) => Event('Event $item | ${index + 1}'))
}..

addAll({
    kToday: [
      const Event('Today\'s Event 1'),
      const Event('Today\'s Event 2'),
    ],
});

int getHashCode(DateTime key) => key.day * 1000000 + key.month * 10000 + key.year;

/// Returns a list of [DateTime] objects from [first] to [last], inclusive.
List<DateTime> daysInRange(DateTime first, DateTime last) {
  final dayCount = last.difference(first).inDays + 1;
  return List.generate(
    dayCount,
    (index) => DateTime.utc(first.year, first.month, first.day + index),
  );
}

final kToday = DateTime.now();
final kFirstDay = DateTime(kToday.year, kToday.month - 3, kToday.day);
final kLastDay = DateTime(kToday.year, kToday.month + 3, kToday.day);

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  Map<DateTime, int> countByDates = {};

  _ViewModel() {
    addListenables([
      DataModel().scheduleModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    final schedules = await DataModel().scheduleModel.getAllStarts();
    countByDates = _getCountByDates(schedules);
    notifyListeners();
  }

  Map<DateTime, int> _getCountByDates(Iterable<DateTime> dates) {
    final dateCountMap = <DateTime, int>{};

    for (final date in dates) {
      dateCountMap[date.dateOnly] = (dateCountMap[date.dateOnly] ?? 0) + 1;
    }
    return dateCountMap;
  }

  int getEventsByDate(DateTime date) => countByDates[date.dateOnly] ?? 0;
}
