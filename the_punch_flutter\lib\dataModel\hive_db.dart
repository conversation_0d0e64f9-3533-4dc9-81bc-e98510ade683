import 'dart:async';

import 'package:hive_flutter/hive_flutter.dart';
import '../misc/my_platform.dart';
import '../state/app_state.dart';

import 'data/alert.dart';
import 'data/group_member.dart';
import 'data/language.dart';
import 'data/location_notes.dart';
import 'data/message_group.dart';
import 'data/schedule_template.dart';
import 'data/travel_pings.dart';
import 'data/user_type.dart';
import 'data/geo_location.dart';
import 'data/inspection.dart';
import 'data/inspection_template.dart';
import 'data/job_type.dart';
import 'data/location.dart';
import 'data/location_contact.dart';
import 'data/message.dart';
import 'data/note.dart';
import 'data/permission.dart';
import 'data/punch_card.dart';
import 'data/schedule.dart';
import 'data/user_type_permission.dart';
import 'data/user.dart';

class HiveDb {
  late Box<Alert> alerts;
  late Box<GeoLocation> geoLocations;
  late Box<Inspection> inspections;
  late Box<InspectionArea> inspectionAreas;
  late Box<InspectionItem> inspectionItems;
  late Box<InspectionImage> inspectionImages;
  late Box<InspectionTemplate> inspectionTemplates;
  late Box<InspectionTemplateArea> inspectionTemplateAreas;
  late Box<InspectionTemplateItem> inspectionTemplateItems;
  late Box<MessageGroup> messageGroups;
  late Box<GroupMember> groupMembers;
  late Box<JobType> jobTypes;
  late Box<Location> locations;
  late Box<LocationNote> locationNotes;
  late Box<LocationContact> locationContacts;
  late Box<Message> messages;
  late Box<Note> notes;
  late Box<Permission> permissions;
  late Box<PunchCard> punchCards;
  late Box<Schedule> schedules;
  late Box<ScheduleIdsByMonth> scheduleIdsByMonth;
  late Box<ScheduleTemplate> scheduleTemplates;
  late Box<Permission> userPermissions;
  late Box<TravelPing> travelPings;
  late Box<User> users;
  late Box<UserType> userTypes;
  late Box<UserTypePermission> userTypePermissions;
  late Box<Language> languages;

  static final Completer _initializing = Completer();

  HiveDb._();
  static HiveDb? _singleton;
  static Future<HiveDb> get database async {
    if (_singleton == null) {
      _singleton = HiveDb._();
      if (MyPlatform.isWeb) {
        await Hive.initFlutter();
      } else {
        final dbDirectory = AppState().applicationSupportDirectory;
        await Hive.initFlutter(dbDirectory.path);
      }
      await _singleton!._initialize();
      _initializing.complete();
    }
    await _initializing.future;
    return _singleton!;
  }

  Future<void> _initialize() async {
    if (!Hive.isAdapterRegistered(99)) {
      Hive.registerAdapter(DurationAdapter());
      Hive.registerAdapter(AlertAdapter());
      Hive.registerAdapter(GeoLocationAdapter());
      Hive.registerAdapter(InspectionAdapter());
      Hive.registerAdapter(InspectionAreaAdapter());
      Hive.registerAdapter(InspectionItemAdapter());
      Hive.registerAdapter(InspectionImageAdapter());
      Hive.registerAdapter(InspectionTemplateAdapter());
      Hive.registerAdapter(InspectionTemplateAreaAdapter());
      Hive.registerAdapter(InspectionTemplateItemAdapter());
      Hive.registerAdapter(JobTypeAdapter());
      Hive.registerAdapter(LocationAdapter());
      Hive.registerAdapter(LocationNoteAdapter());
      Hive.registerAdapter(LocationContactAdapter());
      Hive.registerAdapter(MessageAdapter());
      Hive.registerAdapter(MessageGroupAdapter());
      Hive.registerAdapter(GroupMemberAdapter());
      Hive.registerAdapter(NoteAdapter());
      Hive.registerAdapter(PermissionAdapter());
      Hive.registerAdapter(PunchCardAdapter());
      Hive.registerAdapter(ScheduleAdapter());
      Hive.registerAdapter(ScheduleIdsByMonthAdapter());
      Hive.registerAdapter(ScheduleTemplateAdapter());
      Hive.registerAdapter(TravelPingAdapter());
      Hive.registerAdapter(UserAdapter());
      Hive.registerAdapter(UserTypeAdapter());
      Hive.registerAdapter(UserTypePermissionAdapter());
      Hive.registerAdapter(LanguageAdapter());
    }
    await _createTables();
  }

  Future<void> _createTables() async {
    _singleton!.alerts = await Hive.openBox<Alert>('alerts');
    _singleton!.geoLocations = await Hive.openBox<GeoLocation>('geoLocations');
    _singleton!.inspections = await Hive.openBox<Inspection>('inspections');
    _singleton!.inspectionAreas = await Hive.openBox<InspectionArea>('inspectionAreas');
    _singleton!.inspectionItems = await Hive.openBox<InspectionItem>('inspectionItems');
    _singleton!.inspectionImages = await Hive.openBox<InspectionImage>('inspectionImages');
    _singleton!.inspectionTemplates = await Hive.openBox<InspectionTemplate>('inspectionTemplates');
    _singleton!.inspectionTemplateAreas = await Hive.openBox<InspectionTemplateArea>('inspectionTemplateAreas');
    _singleton!.inspectionTemplateItems = await Hive.openBox<InspectionTemplateItem>('inspectionTemplateItems');
    _singleton!.jobTypes = await Hive.openBox<JobType>('jobTypes');
    _singleton!.locations = await Hive.openBox<Location>('locations');
    _singleton!.locationNotes = await Hive.openBox<LocationNote>('locationNotes');
    _singleton!.locationContacts = await Hive.openBox<LocationContact>('locationContacts');
    _singleton!.messages = await Hive.openBox<Message>('messages');
    _singleton!.messageGroups = await Hive.openBox<MessageGroup>('messageGroup');
    _singleton!.groupMembers = await Hive.openBox<GroupMember>('groupMember');    
    _singleton!.notes = await Hive.openBox<Note>('notes');
    _singleton!.permissions = await Hive.openBox<Permission>('permissions');
    _singleton!.punchCards = await Hive.openBox<PunchCard>('punchCards');
    _singleton!.schedules = await Hive.openBox<Schedule>('schedules');
    _singleton!.scheduleIdsByMonth = await Hive.openBox<ScheduleIdsByMonth>('scheduleIdsByMonth');
    _singleton!.scheduleTemplates = await Hive.openBox<ScheduleTemplate>('scheduleTemplates');
    _singleton!.travelPings = await Hive.openBox<TravelPing>('travelPings');
    _singleton!.users = await Hive.openBox<User>('users');
    _singleton!.userPermissions = await Hive.openBox<Permission>('userPermissions');
    _singleton!.userTypes = await Hive.openBox<UserType>('userTypes');
    _singleton!.userTypePermissions = await Hive.openBox<UserTypePermission>('userTypePermissions');
    _singleton!.languages = await Hive.openBox<Language>('languages');
  }

  Future<void> delete() async {
    await alerts.clear();
    await geoLocations.clear();
    await inspections.clear();
    await inspectionAreas.clear();
    await inspectionItems.clear();
    await inspectionImages.clear();
    await inspectionTemplates.clear();
    await inspectionTemplateAreas.clear();
    await inspectionTemplateItems.clear();
    await jobTypes.clear();
    await locations.clear();
    await locationContacts.clear();
    await messages.clear();
    await messageGroups.clear();
    await groupMembers.clear();
    await notes.clear();
    await permissions.clear();
    await punchCards.clear();
    await schedules.clear();
    await scheduleIdsByMonth.clear();
    await scheduleTemplates.clear();
    await travelPings.clear();
    await users.clear();
    await userPermissions.clear();
    await userTypes.clear();
    await userTypePermissions.clear();
    await languages.clear();
    await Hive.close();
    await _createTables();
  }
}

class DurationAdapter extends TypeAdapter<Duration> {
  @override
  final typeId = 99;

  @override
  void write(BinaryWriter writer, Duration obj) => writer.writeInt(obj.inMicroseconds);

  @override
  Duration read(BinaryReader reader) => Duration(microseconds: reader.readInt());
}
