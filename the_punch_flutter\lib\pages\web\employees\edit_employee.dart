import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../dataModel/data/language.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data/user_type.dart';
import '../../../dataModel/data_model.dart';
import '../../../helpers/color_helper.dart';
import '../../../misc/app_localization.dart';
import '../../../widgets/custom_dropdown.dart';
import '../../../widgets/active_toggle.dart';
import '../../../widgets/decorated_text_field.dart';
import '../../../misc/extensions.dart';
import '../my_scaffold.dart';

class EditEmployeePage extends StatelessWidget {
  final String employeeId;
  final String anything;

  EditEmployeePage(Map<String, String> queryParms, {super.key})
      : employeeId = queryParms['id'] ?? '',
        anything = queryParms['anything'] ?? '';

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
      create: (context) => _ViewModel(employeeId),
      child: MyScaffold(
        title: anything == '1'
            ? AppLocalization.of(context).addEmployee
            : AppLocalization.of(context).editEmployee,
        body: _Body(),
      ));
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width >= 768;
    return Align(
      alignment: Alignment.centerLeft,
      child: ScrollConfiguration(
        behavior: CustomScrollBehavior(),
        child: SingleChildScrollView(
          child: SizedBox(
            width: isDesktop ? 1300 : 500,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _BodyHeader(),
                const SizedBox(height: 16),
                _Section(title: "Employee Details", child: _Row1()),
                const SizedBox(height: 25),
                _Section(title: "Contact Info", child: _Row2()),
                const SizedBox(height: 25),
                _Section(title: "Account Info", child: _Row3()),
                const SizedBox(height: 25),
                _Section(title: "Payment Info", child: _Row4()),
                const SizedBox(height: 25),
                _Section(title: "Managed By", child: _Row5()),
                const SizedBox(height: 50),
                Consumer<_ViewModel>(
                  builder: (context, viewModel, child) => ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        vertical: 20, horizontal: 40),
                      backgroundColor: ColorHelper.thePunchRed(),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                    ),
                    onPressed: !(viewModel.canSave)
                        ? null
                        : () async {
                            Navigator.of(context).pop();
                            await viewModel.save();
                          },
                    child: Text(
                      'Save',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ),
                ),
                const SizedBox(height: 75),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class CustomScrollBehavior extends ScrollBehavior {}

class _Section extends StatelessWidget {
  final String title;
  final Widget child;

  const _Section({required this.title, required this.child});

  IconData _getIconForSection(String title) {
    switch (title) {
      case 'Employee Details':
        return Icons.person_outline;
      case 'Contact Info':
        return Icons.contact_phone_outlined;
      case 'Account Info':
        return Icons.account_circle_outlined;
      case 'Payment Info':
        return Icons.payments_outlined;
      case 'Managed By':
        return Icons.supervisor_account_outlined;
      default:
        return Icons.info_outline;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title with icon
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),

          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _getIconForSection(title),
                size: 20,
                color: Colors.black87,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                      fontSize: 18,
                      color: Colors.black87,
                    ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        // Section content
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[50], // Light gray background
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.all(16),
          child: isMobile
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [child],
                )
              : child,
        ),
      ],
    );
  }
}

class _BodyHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final headline6 = Theme.of(context)
        .textTheme
        .titleLarge
        ?.copyWith(fontWeight: FontWeight.bold, color: ColorHelper.thePunchBlue());
    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final employee = viewModel.employee;
        if (employee == null) return Container();

        final title = employee.name.isEmpty
            ? AppLocalization.of(context).employee
            : employee.name;

        return Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  title,
                  style: headline6,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
              if (employee.id != User.primaryUserId)
                Padding(
                  padding: const EdgeInsets.all(8),
                  child: ActiveSwitch(
                    value: employee.isActive,
                    onChanged: (value) => viewModel.setActive(value),
                    enableDialog: false,
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}

// ==================== SECTION: _Row1 (Employee Details) =====================
class _Row1 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    final textSize = isMobile ? 12.0 : 16.0;

    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final employee = viewModel.employee;
        if (employee == null) return Container();

        return isMobile
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildFirstNameField(context, viewModel, textSize),
                  const SizedBox(height: 12),
                  _buildLastNameField(context, viewModel, textSize),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildUserTypeDropdown(context, viewModel, textSize),
                      ),
                      const SizedBox(width: 12),
                    ],
                  ),
                ],
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildFirstNameField(context, viewModel, textSize)),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildLastNameField(context, viewModel, textSize)),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildUserTypeDropdown(context, viewModel, textSize),
                      ),
                    ],
                  ),
                ],
              );
      },
    );
  }

  Widget _buildFirstNameField(
    BuildContext context,
    _ViewModel viewModel,
    double textSize,
  ) =>
      DecoratedTextField(
        padding: const EdgeInsets.symmetric(vertical: 8),
        initialValue: viewModel.employee?.firstName,
        labelText: AppLocalization.of(context).firstName,
        textStyle: TextStyle(fontSize: textSize),
        onChanged: viewModel.setFirstName,
      );

  Widget _buildLastNameField(
    BuildContext context,
    _ViewModel viewModel,
    double textSize,
  ) =>
      DecoratedTextField(
        padding: const EdgeInsets.symmetric(vertical: 8),
        initialValue: viewModel.employee?.lastName,
        labelText: AppLocalization.of(context).lastName,
        textStyle: TextStyle(fontSize: textSize),
        onChanged: viewModel.setLastName,
      );

  Widget _buildUserTypeDropdown(
    BuildContext context,
    _ViewModel viewModel,
    double textSize,
  ) =>
      Container(
        decoration: BoxDecoration(
          color: const Color(0xFF091F30),
          borderRadius: BorderRadius.circular(8),
        ),
        child: DropdownButtonFormField<String>(
          value: viewModel.employee?.userTypeId.isNotEmpty == true
              ? viewModel.employee!.userTypeId
              : null,
          decoration: InputDecoration(
            //labelText: AppLocalization.of(context).employeeType,
            prefixIcon: const Icon(Icons.work_outline_outlined, color: Colors.white),
            labelStyle: const TextStyle(
              color: Colors.white,
            ),
            floatingLabelStyle: const TextStyle(
              color: Colors.white,
            ),
            filled: true,
            fillColor: const Color(0xFF091F30),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
          ),
          icon: const Icon(
            Icons.arrow_drop_down_outlined,
            color: Colors.white,
          ),
          iconSize: 30,
          isExpanded: true,
          items: viewModel.userTypes
              .map((userType) => DropdownMenuItem<String>(
                    value: userType.id,
                    child: Text(
                      userType.name,
                      style: TextStyle(fontSize: textSize),
                    ),
                  ))
              .toList(),
          onChanged: (value) {
            if (value != null) {
              viewModel.setUserTypeId(value);
            }
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return AppLocalization.of(context).employeeType;
            }
            return null;
          },
          dropdownColor: const Color(0xFF091F30),
          style: TextStyle(
            fontSize: textSize,
            color: Colors.white,
          ),
        ),
      );
}

// ==================== SECTION: _Row2 (Contact Info) =========================
class _Row2 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    final textSize = isMobile ? 12.0 : 16.0;
    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final employee = viewModel.employee;
        if (employee == null) return Container();
        return isMobile
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildPhoneField(context, viewModel, textSize),
                  const SizedBox(height: 12),
                  _buildEmailField(context, viewModel, textSize),
                  const SizedBox(height: 12),
                  _buildLanguageDropdown(context, viewModel, textSize),
                ],
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    children: [
                      Expanded(child: _buildPhoneField(context, viewModel, textSize)),
                      const SizedBox(width: 12),
                      Expanded(child: _buildEmailField(context, viewModel, textSize)),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildLanguageDropdown(context, viewModel, textSize),
                ],
              );
      },
    );
  }

  Widget _buildPhoneField(BuildContext context, _ViewModel viewModel, double textSize) =>
      DecoratedTextField(
        padding: const EdgeInsets.symmetric(vertical: 8),
        initialValue: viewModel.employee?.phone,
        labelText: AppLocalization.of(context).phone,
        textStyle: TextStyle(fontSize: textSize),
        onChanged: viewModel.setPhone,
      );

  Widget _buildEmailField(BuildContext context, _ViewModel viewModel, double textSize) =>
      DecoratedTextField(
        padding: const EdgeInsets.symmetric(vertical: 8),
        initialValue: viewModel.employee?.emailAddress,
        labelText: AppLocalization.of(context).emailAddress,
        textStyle: TextStyle(fontSize: textSize),
        onChanged: viewModel.setEmail,
      );

  Widget _buildLanguageDropdown(BuildContext context, _ViewModel viewModel, double textSize) =>
      CustomDropdown(
        value: viewModel.employee?.languageKey.isNotEmpty == true
            ? viewModel.employee!.languageKey
            : null,
        label: AppLocalization.of(context).language,
        icon: Icons.language,
        items: viewModel.languages
            .map((language) => DropdownMenuItem<String>(
                  value: language.key,
                  child: Text(
                    language.name,
                    style: TextStyle(fontSize: textSize),
                  ),
                ))
            .toList(),
        onChanged: (value) {
          if (value != null) {
            viewModel.setLanguage(value);
          }
        },
        validator: (value) {
          if (value == null || value.isEmpty) {
            return AppLocalization.of(context).language;
          }
          return null;
        },
      );
}

// ==================== SECTION: _Row3 (Account Info) ==========================
class _Row3 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    final textSize = isMobile ? 12.0 : 16.0;
    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final employee = viewModel.employee;
        if (employee == null) return Container();
        return isMobile
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildUsernameField(context, viewModel, textSize),
                  const SizedBox(height: 12),
                  _buildPasswordField(context, viewModel, textSize),
                  const SizedBox(height: 12),
                  _buildConfirmPasswordField(context, viewModel, textSize),
                ],
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildUsernameField(context, viewModel, textSize),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(child: _buildPasswordField(context, viewModel, textSize)),
                      const SizedBox(width: 12),
                      Expanded(child: _buildConfirmPasswordField(context, viewModel, textSize)),
                    ],
                  ),
                ],
              );
      },
    );
  }

  Widget _buildUsernameField(BuildContext context, _ViewModel viewModel, double textSize) =>
      DecoratedTextField(
        padding: const EdgeInsets.symmetric(vertical: 8),
        initialValue: viewModel.employee?.username,
        labelText: AppLocalization.of(context).username,
        textStyle: TextStyle(fontSize: textSize),
        onChanged: viewModel.setUsername,
      );

  Widget _buildPasswordField(BuildContext context, _ViewModel viewModel, double textSize) =>
      DecoratedTextField(
        padding: const EdgeInsets.symmetric(vertical: 8),
        initialValue: viewModel.employee?.password,
        labelText: AppLocalization.of(context).password,
        textStyle: TextStyle(fontSize: textSize),
        onChanged: viewModel.setPassword,
        obscureText: true,
      );

  Widget _buildConfirmPasswordField(BuildContext context, _ViewModel viewModel, double textSize) =>
      DecoratedTextField(
        padding: const EdgeInsets.symmetric(vertical: 8),
        initialValue: viewModel.employee?.confirmPassword,
        labelText: AppLocalization.of(context).confirmPassword,
        textStyle: TextStyle(fontSize: textSize),
        onChanged: viewModel.setConfirmPassword,
        obscureText: true,
      );
}

// ==================== SECTION: _Row4 (Payment Info) ==========================
class _Row4 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;
    final textSize = isMobile ? 12.0 : 16.0;
    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final employee = viewModel.employee;
        if (employee == null) return Container();
        return isMobile
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildPayRateField(context, viewModel, textSize),
                  const SizedBox(height: 12),
                  _buildPayrollIdField(context, viewModel, textSize),
                ],
              )
            : Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Row(
                    children: [
                      Expanded(child: _buildPayRateField(context, viewModel, textSize)),
                      const SizedBox(width: 12),
                      Expanded(child: _buildPayrollIdField(context, viewModel, textSize)),
                    ],
                  ),
                ],
              );
      },
    );
  }

  Widget _buildPayRateField(BuildContext context, _ViewModel viewModel, double textSize) =>
      DecoratedTextField(
        padding: const EdgeInsets.symmetric(vertical: 8),
        initialValue: viewModel.employee?.payRate?.toString(),
        labelText: AppLocalization.of(context).payRate,
        textStyle: TextStyle(fontSize: textSize),
        onChanged: (value) => viewModel.setPayrate(double.tryParse(value)),
      );

  Widget _buildPayrollIdField(BuildContext context, _ViewModel viewModel, double textSize) =>
      DecoratedTextField(
        padding: const EdgeInsets.symmetric(vertical: 8),
        initialValue: viewModel.employee?.payrollId,
        labelText: AppLocalization.of(context).payrollId,
        textStyle: TextStyle(fontSize: textSize),
        onChanged: (value) => viewModel.setPayrollId(value),
      );
}

// ==================== SECTION: _Row5 (Managed By) ============================
class _Row5 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<_ViewModel>(
      builder: (context, viewModel, child) {
        final employee = viewModel.employee;
        if (employee == null) return const SizedBox();

        final managers = viewModel.managersAndAdmins.toList();

        // A fixed grid of 4 columns that shrinks to fit its content height,
        // and does not introduce its own scrollbar.
        return GridView.count(
          crossAxisCount: 5,                    // number of columns
          crossAxisSpacing: 8,                 // space between columns
          mainAxisSpacing: 8,                  // space between rows
          childAspectRatio: 3,                 // adjust as needed for chip shape
          shrinkWrap: true,                     // so it only takes up necessary height
          physics: const NeverScrollableScrollPhysics(),  // turn off GridView’s own scrolling
          children: managers.map((manager) {
            final isSelected = viewModel.hasManager(manager.id);
            return FilterChip(
              label: Text(manager.name),
              selected: isSelected,
              selectedColor: ColorHelper.thePunchAccentRed(),
              onSelected: (selected) =>
                  viewModel.toggleManager(manager.id, selected),
            );
          }).toList(),
        );
      },
    );
  }
}



  // ==================== VIEWMODEL =============================================
  class _ViewModel extends ChangeNotifier {
    final String employeeId;
    User? employee;
    Iterable<User> users = [];
    var userTypes = <UserType>[];
    var userTypeMap = <String, UserType>{};
    Iterable<Language> languages = [];

    _ViewModel(this.employeeId) {
      refresh();
    }

    Future<void> refresh() async {
      if (employeeId.isEmpty) {
        employee = User.createEmployee();
      } else {
        employee = await DataModel().userModel.getById(employeeId);
        if (employee != null) {
          employee = User.from(employee!);
        }
      }
      users = await DataModel().userModel.all;
      userTypes = (await DataModel().userTypeModel.activeEmployees).toList();
      userTypeMap = {for (final e in userTypes) e.id: e};
      languages = [
        Language(key: 'en-US', name: 'English', createdOn: DateTime.now(), id: '1', isDefault: true),
        Language(key: 'es',    name: 'Spanish', createdOn: DateTime.now(), id: '2', isDefault: false),
        Language(key: 'fr',    name: 'French',  createdOn: DateTime.now(), id: '3', isDefault: false),
      ];
      notifyListeners();
    }

    void toggleManager(String managerId, bool selected) {
      if (employee == null) return;

      final currentList = (employee!.managedByIds ?? '')
          .split(',')
          .where((e) => e.isNotEmpty)
          .toList();

      if (selected && !currentList.contains(managerId)) {
        currentList.add(managerId);
      } else if (!selected && currentList.contains(managerId)) {
        currentList.remove(managerId);
      }

      employee!.managedByIds = currentList.join(',');
      employee!.isDirty = true;

      notifyListeners();
    }

    bool hasManager(String managerId) {
      if (employee == null || (employee!.managedByIds ?? '').isEmpty) return false;
      final list = employee!.managedByIds!.split(',');
      return list.contains(managerId);
    }

    // IDs from your user_type.dart
    // userType syopId = UserType.sysopId, etc.
    // We'll just read them from there for isManagerOrAdmin:

    // isManagerOrAdmin helper
    bool isManagerOrAdmin(String userTypeId) {
      final lower = userTypeId.toLowerCase();
      // compare against the static fields in UserType:
      return lower == UserType.sysopId ||
          lower == UserType.administratorId ||
          lower == UserType.managerId ||
          lower == '40cc63eb-5a05-42c8-985d-b4804bd12472'; // Supervisor (if needed)
    }

    // List of all possible manager/admin users
    Iterable<User> get managersAndAdmins =>
        users.where((u) => isManagerOrAdmin(u.userTypeId));

    static const int maxFirstNameLength = 50;
    static const int maxLastNameLength = 50;
    static const int maxUsernameLength = 30;
    static const String phoneNumberPattern = r'^\+?[0-9]{10,15}$';

    bool get validateUsername {
      if (employee!.username == null || employee!.username!.isEmpty) return false;
      if (employee!.username!.length > maxUsernameLength) return false;
      return true;
    }

    bool get validateFirstName {
      if (employee!.firstName.isEmpty) return false;
      if (employee!.firstName.length > maxFirstNameLength) return false;
      return true;
    }

    bool get validateLastName {
      if (employee!.lastName.isEmpty) return false;
      if (employee!.lastName.length > maxLastNameLength) return false;
      return true;
    }

    bool get validatePhone {
      if (employee!.phone == null || employee!.phone!.isEmpty) return false;
      final regExp = RegExp(phoneNumberPattern);
      return regExp.hasMatch(employee!.phone!);
    }

    bool get validatePassword {
      if (!validateRequiredPassword) return false;
      return true;
    }

    bool get validateConfirmPassword {
      if (!validateRequiredConfirmPassword) return false;
      if (!confirmPasswordMatches) return false;
      return true;
    }

    bool get usernameExists {
      if (employee!.username == null || employee!.username!.isEmpty) return false;
      return users
          .where((e) => e.id != employee!.id)
          .map((e) => e.username)
          .contains(employee!.username);
    }

    bool get requiresPassword => employeeId.isEmpty;

    bool get validateRequiredPassword {
      if (requiresPassword && (employee!.password == null || employee!.password!.isEmpty)) {
        return false;
      }
      return true;
    }

    bool get validateRequiredConfirmPassword {
      if (requiresPassword &&
          (employee!.confirmPassword == null || employee!.confirmPassword!.isEmpty)) {
        return false;
      }
      return true;
    }

    bool get confirmPasswordMatches =>
        employee!.password == employee!.confirmPassword;

    void setPassword(String value) {
      if (employee == null) return;
      employee!.password = value;
      employee!.isDirty = true;
      notifyListeners();
    }

    void setConfirmPassword(String value) {
      if (employee == null) return;
      employee!.confirmPassword = value;
      employee!.isDirty = true;
      notifyListeners();
    }

    void setFirstName(String value) {
      if (employee == null) return;
      employee!.firstName = value;
      employee!.isDirty = true;
      notifyListeners();
    }

    void setLastName(String value) {
      if (employee == null) return;
      employee!.lastName = value;
      employee!.isDirty = true;
      notifyListeners();
    }

    void setUsername(String value) {
      if (employee == null) return;
      employee!.username = value;
      employee!.isDirty = true;
      notifyListeners();
    }

    void setEmail(String value) {
      if (employee == null) return;
      employee!.emailAddress = value;
      employee!.isDirty = true;
      notifyListeners();
    }

    void setPhone(String value) {
      if (employee == null) return;
      employee!.phone = value;
      employee!.isDirty = true;
      notifyListeners();
    }

    void setUserTypeId(String value) {
      if (employee == null) return;
      employee!.userTypeId = value;
      employee!.isDirty = true;
      notifyListeners();
    }

    void setLanguage(String value) {
      if (employee == null) return;
      employee!.languageKey = value;
      employee!.isDirty = true;
      notifyListeners();
    }

    void setPayrate(double? value) {
      if (employee == null) return;
      employee!.payRate = value?.toCurrency.parseCurrency;
      employee!.isDirty = true;
      notifyListeners();
    }

    void setPayrollId(String? value) {
      if (employee == null) return;
      employee!.payrollId = value;
      employee!.isDirty = true;
      notifyListeners();
    }

    void setPayRateFrequency(int value) {
      if (employee == null) return;
      employee!.payRateFrequency = value;
      employee!.isDirty = true;
      notifyListeners();
    }

    void setActive(bool value) {
      if (employee == null) return;
      employee!.isActive = value;
      employee!.isDirty = true;
      notifyListeners();
    }

    Future<void> save() async {
      if (employee == null) return;
      if (employee!.isDirty) {
        await DataModel().userModel.saveDirty([employee!]);
      }
    }


    bool get canSave {
      if (!validateFirstName) return false;
      if (!validateLastName) return false;
      if (!validatePhone) return false;
      if (!validateUsername) return false;
      if (!validatePassword) return false;
      if (!validateConfirmPassword) return false;
      return true;
    }
}
