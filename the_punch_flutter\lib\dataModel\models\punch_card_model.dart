import '../../api/api_model.dart';
import '../base_data.dart';
import '../data/punch_card.dart';
import '../hive_db.dart';
import '../../misc/extensions.dart';

class PunchCardModel extends BaseDataModel<PunchCard> {
  @override
  Future<Iterable<PunchCard>> get all async => (await HiveDb.database).punchCards.values;

  @override
  Future<void> save(Iterable<PunchCard> t) async {
    await (await HiveDb.database).punchCards.putAll({for (final e in t) e.id: e});
    notifyListeners();
  }

  Future<Iterable<PunchCard>> getByEmployeeId(String id) async => (await all).where((e) => e.userId == id);

  Future<Iterable<PunchCard>> getBetween(DateTime start, DateTime end) async => (await all).where((e) => e.clockedIn >= start && e.clockedIn < end);

  Future<Iterable<PunchCard>> getBetweenByEmployeeId(String id, DateTime start, DateTime end) async => (await all).where((e) => e.userId == id).where((e) => e.clockedIn >= start && e.clockedIn < end);

  Future<PunchCard?> getPunchedIn(String employeeId) async => (await all).where((e) => e.userId == employeeId && e.clockedOut == null).firstOrNull;

  Future<Iterable<PunchCard>> getAllPunchedIn() async => (await active).where((e) => e.clockedOut == null);

  Future<PunchCard?> getByScheduleId(String id) async => (await all).where((e) => e.scheduleId == id).firstOrNull;

  Future<Iterable<PunchCard>> getByScheduleIds(Iterable<String> ids) async => (await all).where((e) => ids.contains(e.scheduleId));

  Future<Iterable<DateTime>> getAllStarts() async => (await all).map((e) => e.clockedIn);

  Future<Iterable<DateTime>> getStartsByEmployeeId(String id) async => (await getByEmployeeId(id)).map((e) => e.clockedIn);

  Future<void> syncUpdate(Iterable<PunchCard> punchCards) async {
    if (punchCards.isEmpty) return;

    // get all existing that match the ids of the incoming
    final ids = punchCards.map((e) => e.id);
    final existingPunchCards = (await all).where((e) => ids.contains(e.id));
    final existingPunchCardMap = {for (final e in existingPunchCards) e.id: e};

    // cull out the ones that have changed more recently than the incoming
    final updatePunchCards = punchCards.where((e) => existingPunchCardMap[e.id] == null || e.lastChangedOn! > existingPunchCardMap[e.id]!.lastChangedOn!);

    // update the ones that can be updated
    await (await HiveDb.database).punchCards.putAll({for (final e in updatePunchCards) e.id: e});

    // tell everyone there's new data
    notifyListeners();
  }

  Future<Iterable<PunchCard>> getDirty({required int limit}) async {
    var allRecords = await all;
    var dirtyRecords = allRecords.where((e) => e.isDirty);
    if (dirtyRecords.length > limit) {
      return dirtyRecords.take(limit);
    }
    return dirtyRecords;
  }

  Future<PunchCard?> getLastCompletedJobByEmployeeId(String employeeId) async {
    // Retrieve punch cards for the employee and filter out those with null clockedOut and the Travel Time job type
    final punchCards = (await getByEmployeeId(employeeId))
        .where((e) => e.clockedOut != null && e.jobTypeId != '2EBCDAA1-7DE7-4106-BC26-D99CA120C820')
        .toList();

    if (punchCards.isEmpty) return null;

    // Sort punch cards by clockedOut time in descending order
    punchCards.sort((a, b) => b.clockedOut!.compareTo(a.clockedOut!));

    // Return the most recent punch card
    return punchCards.firstOrNull;
  }

  Future<DateTime?> getFirstPunchCardClockedInTime(String linkId) async {
    final punchCards = await getAllByLinkId(linkId);
    if (punchCards.isEmpty) return null;

    // Sort by ClockedIn time in ascending order
    final sortedPunchCards = punchCards.toList()
      ..sort((a, b) => a.clockedIn.compareTo(b.clockedIn));

    // Return the ClockedIn time of the first punch card
    return sortedPunchCards.first.clockedIn;
  }

  // New method: getAllByLinkId
  Future<Iterable<PunchCard>> getAllByLinkId(String linkId) async {
    return (await all).where((e) => e.punchCardLinkId == linkId);
  }

Future<void> savePunchCard(PunchCard punchCard) async {
  // We do NOT set punchCard.isDirty or trigger sync here.
  // We just call the standard save(...) to store the data locally in Hive.
  await save([punchCard]);
  await ApiModel().createPunchCard(punchCard);
}


}
