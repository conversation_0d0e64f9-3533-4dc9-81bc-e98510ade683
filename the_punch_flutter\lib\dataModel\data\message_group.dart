import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:uuid/uuid.dart';
import '../../misc/json_conversion.dart';
import '../../state/login_state.dart';
import '../../state/server_time_state.dart';
import '../base_data.dart';

part 'message_group.g.dart';

@HiveType(typeId: 28)
@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class MessageGroup extends BaseData {
  @override
  @HiveField(6) // again, offset for the subclass
  @JsonKey(fromJson: idFromJson)
  String id;

  @HiveField(7)
  String name;

  MessageGroup({
    required this.id,
    required this.name,

    // Required by BaseData:
    bool isDirty = false,
    bool isActive = true,
    required String createdByUserId,
    required DateTime createdOn,
    DateTime? lastChangedOn,
    String? lastChangedByUserId,
  }) : super(
          isDirty: isDirty,
          isActive: isActive,
          createdByUserId: createdByUserId,
          createdOn: createdOn,
          lastChangedOn: lastChangedOn,
          lastChangedByUserId: lastChangedByUserId,
        );

  factory MessageGroup.fromJson(Map<String, dynamic> json) =>
      _$MessageGroupFromJson(json);

  Map<String, dynamic> toJson() => _$MessageGroupToJson(this);

  /// Example convenience factory that sets defaults
  factory MessageGroup.create(String? suggestedName) => MessageGroup(
      id: const Uuid().v4(),
      name: suggestedName ?? 'New Group',
      createdByUserId: LoginState.userId,
      createdOn: ServerTimeState().utcTime,
    );
}
