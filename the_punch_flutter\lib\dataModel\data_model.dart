//import 'dart:async';
import 'package:flutter/foundation.dart';
import 'models/alert_model.dart';
import 'models/group_member_model.dart';
import 'models/location_notes_model.dart';
import 'models/message_group_model.dart';
import 'models/travel_ping_model.dart';
import 'models/travel_time_model.dart';
import 'models/user_type_model.dart';
import 'models/geo_location_model.dart';
import 'models/inspection_model.dart';
import 'models/inspection_template_model.dart';
import 'models/job_type_model.dart';
import 'models/location_model.dart';
import 'models/message_model.dart';
import 'models/note_model.dart';
import 'models/permission_model.dart';
import 'models/punch_card_model.dart';
import 'models/schedule_model.dart';
import 'hive_db.dart';
import 'models/language_model.dart';
import 'models/location_contact_model.dart';
import 'models/schedule_template_model.dart';
import 'models/user_model.dart';
import 'models/user_type_permission_model.dart';

class DataModel extends ChangeNotifier {
  final alertModel = AlertModel();
  final geoLocationModel = GeoLocationModel();
  final inspectionModel = InspectionModel();
  final inspectionAreaModel = InspectionAreaModel();
  final inspectionItemModel = InspectionItemModel();
  final inspectionImageModel = InspectionImageModel();
  final inspectionTemplateModel = InspectionTemplateModel();
  final inspectionTemplateAreaModel = InspectionTemplateAreaModel();
  final inspectionTemplateItemModel = InspectionTemplateItemModel();
  final jobTypeModel = JobTypeModel();
  final locationModel = LocationModel();
  final locationNoteModel = LocationNoteModel();
  final locationContactModel = LocationContactModel();
  final messageModel = MessageModel();
  final groupMemberModel = GroupMemberModel();
  final messageGroupModel = MessageGroupModel();
  final noteModel = NoteModel();
  final permissionModel = PermissionModel();
  final punchCardModel = PunchCardModel();
  final scheduleModel = ScheduleModel();
  final scheduleTemplateModel = ScheduleTemplateModel();
  final travelPingModel = TravelPingModel();
  final userModel = UserModel();
  final userTypeModel = UserTypeModel();
  final userPermissionModel = PermissionModel();
  final userTypePermissionModel = UserTypePermissionModel();
  final languageModel = LanguageModel();
  final travelTimesModel = TravelTimesModel();
  factory DataModel() {
    _singleton ??= DataModel._();
    return _singleton!;
  }

  DataModel._();

  static DataModel? _singleton;

  Future<void> delete() async => (await HiveDb.database).delete();
}
