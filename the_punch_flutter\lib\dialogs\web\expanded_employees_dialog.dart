import 'dart:async';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import '../../dataModel/data/user.dart';
import '../../dataModel/data_model.dart';
import '../constrained_search_dialog.dart';
import '../../misc/change_notification_builder.dart';
import '../../pages/view_model_mixin.dart';
import '../../widgets/padded_card.dart';

class ExpandedEmployeesDialog extends StatelessWidget {
  final Iterable<String> ignoreEmployeeIds;
  final Function(String) onSelection;
  final search = ValueNotifier<String>('');

  ExpandedEmployeesDialog({super.key, required this.onSelection, this.ignoreEmployeeIds = const []});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final headline6 = theme.textTheme.titleLarge;

    return ChangeNotifierBuilder<_ViewModel>(
      create: (context) => _ViewModel(ignoreEmployees: ignoreEmployeeIds),
      builder: (context, viewModel, child) {
        if (viewModel.employees.isEmpty) return Container();
        return ConstrainedSearchDialog(
          autofocus: true,
          title: Text(AppLocalizations.of(context)!.employees, style: headline6),
          builder: (search) {
            final visibleEmployees = viewModel.employees.where((e) => _employeeMatches(e, search));
            return visibleEmployees.map((e) => _Tile(employee: e, employeeSelected: onSelection));
          },
        );
      },
    );
  }

  bool _employeeMatches(User employee, String search) {
    if (employee.name.toLowerCase().contains(search.toLowerCase())) return true;
    if (employee.phone != null && employee.phone!.contains(search)) return true;
    if (employee.emailAddress != null && employee.emailAddress!.toLowerCase().contains(search.toLowerCase())) {
      return true;
    }
    return false;
  }
}

class _Tile extends StatelessWidget {
  const _Tile({required this.employeeSelected, required this.employee});

  final User employee;
  final Function(String) employeeSelected;

  @override
  Widget build(BuildContext context) => GestureDetector(
        onTap: () {
          Navigator.of(context).pop();
          employeeSelected(employee.id);
        },
        child: PaddedCard(
          child: Center(
              child: Column(
            children: [
              Text(employee.name),
              if (employee.phone != null) Text(employee.phone!),
              if (employee.emailAddress != null) Text(employee.emailAddress!),
            ],
          )),
        ),
      );
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  Iterable<User> employees = [];

  Iterable<String> ignoreEmployees;
  _ViewModel({required this.ignoreEmployees}) {
    addListenables([DataModel().userModel]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    employees = (await DataModel().userModel.activeEmployees).where((e) => !ignoreEmployees.contains(e.id));
    notifyListeners();
  }
}
