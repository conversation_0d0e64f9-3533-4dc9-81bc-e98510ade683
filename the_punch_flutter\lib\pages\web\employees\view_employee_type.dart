import 'dart:async';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart'; // Added for custom fonts
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../../dataModel/data/permission.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data/user_type.dart';
import '../../../dataModel/data_model.dart';
import '../../../misc/app_localization.dart';
import '../../view_model_mixin.dart';
import '../home/<USER>';
import '../my_scaffold.dart';
import 'employee_type_mixin.dart';
import '../../../misc/extensions.dart';
import '../../../state/permissions_state.dart';
import '../../../widgets/decorated_text_field.dart';

class ViewEmployeeTypePage extends StatelessWidget {
  final String userTypeId;

  ViewEmployeeTypePage(Map<String, String> queryParms, {super.key})
      : userTypeId = queryParms['id'] ?? '';

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
        create: (context) => _ViewModel(userTypeId),
        child: MyScaffold(
          title: AppLocalization.of(context).employeeType,
          body: _Body(),
        ),
      );
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // Use LayoutBuilder to determine the available width
    return LayoutBuilder(
      builder: (context, constraints) {
        // Define breakpoints for responsiveness
        if (constraints.maxWidth < 600) {
          return _MobileBody();
        } else if (constraints.maxWidth < 1200) {
          return _TabletBody();
        } else {
          return _DesktopBody();
        }
      },
    );
  }
}

class _MobileBody extends StatelessWidget {
  @override
  Widget build(BuildContext context) => SingleChildScrollView(
        child: Column(
          children: [
            _BodyHeader(),
            Padding(
              padding: const EdgeInsets.all(16),
              child: _Row1(),
            ),
            SizedBox(height: 16),
            _PermissionsTab(isMobile: true),
          ],
        ),
      );
}

class _TabletBody extends StatelessWidget {
  @override
  Widget build(BuildContext context) => SingleChildScrollView(
        child: Column(
          children: [
            _BodyHeader(),
            Padding(
              padding: const EdgeInsets.all(24),
              child: _Row1(),
            ),
            SizedBox(height: 24),
            _PermissionsTab(isMobile: false),
          ],
        ),
      );
}

class _DesktopBody extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1300),
          child: SingleChildScrollView(
            child: Column(
              children: [
                _BodyHeader(),
                Padding(
                  padding: const EdgeInsets.all(32),
                  child: _Row1(),
                ),
                SizedBox(height: 32),
                _PermissionsTab(isMobile: false),
              ],
            ),
          ),
        ),
      );
}

class _BodyHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final userType = context.select<_ViewModel, UserType?>((viewModel) => viewModel.userType);

    if (userType == null) return Container();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
      // decoration: BoxDecoration(
      //   gradient: LinearGradient(
      //     colors: [theme.colorScheme.primary, theme.colorScheme.secondary],
      //     begin: Alignment.topLeft,
      //     end: Alignment.bottomRight,
      //   ),
      //),
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              userType.name,
              style: theme.textTheme.headlineMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontFamily: GoogleFonts.lato().fontFamily,
              ),
            ),
            SizedBox(height: 8),
            Text(
              userType.description,
              style: theme.textTheme.titleMedium?.copyWith(
                color: Colors.white70,
                fontFamily: GoogleFonts.lato().fontFamily,
              ),
            ),
            SizedBox(height: 16),
            Row(
              children: [
                if (PermissionsState().editEmployeeTypes &&
                    userType.id != UserType.sysopId &&
                    userType.id != UserType.administratorId)
                  ElevatedButton.icon(
                    onPressed: () async => context.pushNamed(
                      '/employeeTypes/edit',
                      queryParameters: {'id': userType.id},
                    ),
                    icon: Icon(Icons.edit),
                    label: Text(AppLocalization.of(context).editEmployeeType),
                    style: ElevatedButton.styleFrom(
                      foregroundColor: theme.colorScheme.primary, backgroundColor: Colors.white,
                    ),
                  ),
                Spacer(),
                // Activation Toggle
                Row(
                  children: [
                    Text(
                      AppLocalization.of(context).activate,
                      style: TextStyle(color: Colors.white),
                    ),
                    Switch(
                      value: userType.isActive,
                      onChanged: null, // Non-interactive in view mode
                      activeColor: Colors.white,
                      inactiveThumbColor: Colors.grey,
                      inactiveTrackColor: Colors.grey.shade400,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _Row1 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final userType = context.select<_ViewModel, UserType?>((viewModel) => viewModel.userType);
    if (userType == null) return Container();

    bool isMobile = MediaQuery.of(context).size.width < 600;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: isMobile
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDetailItem(context, AppLocalization.of(context).employeeTypeName, userType.name),
                _buildDetailItem(context, AppLocalization.of(context).description, userType.description),
              ],
            )
          : Row(
              children: [
                Expanded(
                  child: _buildDetailItem(context, AppLocalization.of(context).employeeTypeName, userType.name),
                ),
                Expanded(
                  child: _buildDetailItem(context, AppLocalization.of(context).description, userType.description),
                ),
              ],
            ),
    );
  }

  Widget _buildDetailItem(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: Theme.of(context).textTheme.bodySmall),
          SizedBox(height: 4),
          Text(value, style: Theme.of(context).textTheme.bodyLarge),
        ],
      ),
    );
  }
}

class _PermissionsTab extends StatelessWidget {
  final bool isMobile;

  _PermissionsTab({required this.isMobile});

  @override
  Widget build(BuildContext context) {
    final viewModel = context.watch<_ViewModel>();
    if (viewModel.permissions.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          AppLocalization.of(context).permissionNotFound,
          style: Theme.of(context).textTheme.bodyLarge,
          textAlign: TextAlign.center,
        ),
      );
    }

    return Card(
      margin: const EdgeInsets.all(16.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: isMobile
            ? _PermissionsList(isMobile: true, viewModel: viewModel)
            : _PermissionsList(isMobile: false, viewModel: viewModel),
      ),
    );
  }
}

class _PermissionsList extends StatelessWidget {
  final bool isMobile;
  final _ViewModel viewModel;

  _PermissionsList({required this.isMobile, required this.viewModel});

  @override
  Widget build(BuildContext context) {
    if (isMobile) {
      return ListView.separated(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: viewModel.permissions.length,
        separatorBuilder: (context, index) => Divider(),
        itemBuilder: (context, index) {
          final permission = viewModel.permissions[index];
          return ListTile(
            leading: Icon(Icons.security, color: Theme.of(context).colorScheme.primary),
            title: Text(permission.name, style: TextStyle(fontWeight: FontWeight.bold)),
            subtitle: Text(permission.description),
            trailing: Icon(Icons.chevron_right),
            onTap: () {
              // Handle permission tap if needed
            },
          );
        },
      );
    } else {
      return DataTable(
        headingRowColor: MaterialStateColor.resolveWith((states) => Theme.of(context).highlightColor),
        columns: [
          DataColumn(
            label: Text(AppLocalization.of(context).permission),
          ),
          DataColumn(
            label: Text(AppLocalization.of(context).description),
          ),
        ],
        rows: [
          for (final permission in viewModel.permissions)
            DataRow(cells: [
              DataCell(Text(permission.name)),
              DataCell(Text(permission.description)),
            ]),
        ],
      );
    }
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin, EmployeeTypeMixin {
  final String userTypeId;
  UserType? userType;
  int get permissionCount => permissions.length;
  int get employeeCount => employees.length;

  List<Permission> permissions = [];
  List<User> employees = [];

  _ViewModel(this.userTypeId) {
    addListenables([
      DataModel().userTypeModel,
      DataModel().userModel,
      DataModel().userTypePermissionModel,
      DataModel().permissionModel,
    ]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    userType = await DataModel().userTypeModel.getById(userTypeId);
    if (userType == null) return;
    employees = (await DataModel().userModel.getByUserTypeId(userTypeId)).toList();
    if (userTypeId == UserType.administratorId) {
      permissions = (await DataModel().permissionModel.active).toList();
    } else {
      final permissionIds = (await DataModel()
              .userTypePermissionModel
              .getActiveByUserTypeId(userTypeId))
          .map((e) => e.permissionId);
      permissions = (await DataModel().permissionModel.getByIds(permissionIds)).toList();
    }

    notifyListeners();
  }
}
                                                                               