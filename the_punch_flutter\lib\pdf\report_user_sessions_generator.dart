// import 'dart:math';
// import 'dart:typed_data';

// import 'package:flutter/widgets.dart';
// import 'package:pdf/pdf.dart' as pw;
// import 'package:pdf/widgets.dart' as pw;
// import 'package:printing/printing.dart' as pw;
// import 'package:the_punch_flutter/api/requests/reportUserSessions.dart';
// import 'package:the_punch_flutter/dataModel/data/userSession.dart';
// import 'package:the_punch_flutter/dataModel/dataModel.dart';
// import 'package:the_punch_flutter/pdf/pdfGenerator.dart';
// import 'package:the_punch_flutter/misc/extensions.dart';
// import 'package:basics/basics.dart';

// class ReportUserSessionsEntityConverter {
//   Future<List<List<String>>> convert(BuildContext context, List<ReportUserSessionsEntity> entities) async {
//     var userTypeIds = entities.map((e) => e.userTypeId).toSet();
//     var userTypes = await DataModel().userTypeModel.getByIds(userTypeIds);
//     var userTypeMap = {for (var e in userTypes) e.id: e};
//     return entities
//         .map((e) => [
//               e.name,
//               userTypeMap[e.userTypeId]?.name ?? '',
//               e.signedInOn.toFormattedDateTimeWithYear(context),
//               e.signedOutOn?.toFormattedDateTimeWithYear(context) ?? '',
//               UserSession.name(context, e.signOutTypeId ?? ''),
//               e.forcedIPAddress ?? ''
//             ])
//         .toList();
//   }
// }

// class ReportUserSessionsGenerator implements PdfGenerator {
//   final String title;
//   final String dateRange;
//   final List<List<String>> tableRows;

//   ReportUserSessionsGenerator({required this.title, required this.dateRange, required this.tableRows});

//   final tableHeaders = ['Name', 'User Type', 'Signed In On', 'Signed Out On', 'Signed Out Reason', 'IP Address'];

//   final headerHeight = 25.0;
//   final cellHeight = 20.0;

//   @override
//   Future<Uint8List> buildPdf(pw.PdfPageFormat pageFormat) async {
//     final openSansRegular = await pw.fontFromAssetBundle('assets/OpenSans-Regular.ttf');
//     final openSansBold = await pw.fontFromAssetBundle('assets/OpenSans-Bold.ttf');

//     final themeData = pw.ThemeData.withFont(base: openSansRegular, bold: openSansBold).copyWith(
//       defaultTextStyle: pw.TextStyle(fontSize: 10, color: pw.PdfColors.black),
//       header5: pw.TextStyle(fontSize: 12, color: pw.PdfColors.black, fontWeight: pw.FontWeight.bold),
//     );

//     final tableRowsPerPages = <List<List<String>>>[];

//     final rowsPerPage = (pageFormat.height - pageFormat.marginTop - pageFormat.marginBottom - pageFormat.height / headerHeight - 30) ~/ cellHeight;

//     for (var i = 0; i < tableRows.length / rowsPerPage; i++) {
//       tableRowsPerPages.add(tableRows.slice(start: i * rowsPerPage, end: min((i + 1) * rowsPerPage, tableRows.length)));
//     }

//     final doc = pw.Document();
//     for (final rows in tableRowsPerPages) {
//       doc.addPage(pw.Page(
//         theme: themeData,
//         pageFormat: pageFormat,
//         build: (context) => pw.Column(
//           crossAxisAlignment: pw.CrossAxisAlignment.center,
//           mainAxisAlignment: pw.MainAxisAlignment.start,
//           children: [
//             pw.Text(title, style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold)),
//             pw.Align(alignment: pw.Alignment.centerLeft, child: pw.Text(dateRange)),
//             _buildRows(rows, openSansRegular, openSansBold),
//           ],
//         ),
//       ));
//       // doc.addPage(pw.Page(pageFormat: pageFormat, build: (pw.Context context) => pw.Center(child: buildRows(rows))));
//     } // Page

//     return doc.save();
//   }

//   pw.Table _buildRows(List<List<String>> rows, pw.Font? font, pw.Font? fontBold) {
//     final headerStyle = pw.TextStyle(fontSize: 10, color: pw.PdfColors.black, fontWeight: pw.FontWeight.bold);
//     final cellStyle = pw.TextStyle(fontSize: 8, color: pw.PdfColors.black);
//     final oddRowDecoration = pw.BoxDecoration(color: pw.PdfColorGrey(0.90));
//     final headerDecoration = pw.BoxDecoration(borderRadius: const pw.BorderRadius.all(pw.Radius.circular(2)), color: pw.PdfColors.lightBlue);
//     final tableBorder = pw.TableBorder.all(color: pw.PdfColorGrey(0.90));

//     return pw.Table.fromTextArray(
//         border: tableBorder,
//         headerHeight: headerHeight,
//         cellHeight: cellHeight,
//         cellPadding: pw.EdgeInsets.all(2.0),
//         headerStyle: headerStyle,
//         cellStyle: cellStyle,
//         headerDecoration: headerDecoration,
//         oddRowDecoration: oddRowDecoration,
//         headerAlignments: {
//           0: pw.Alignment.center,
//           1: pw.Alignment.center,
//           2: pw.Alignment.center,
//           3: pw.Alignment.center,
//           4: pw.Alignment.center,
//           5: pw.Alignment.center,
//         },
//         cellAlignments: {
//           0: pw.Alignment.centerLeft,
//           1: pw.Alignment.center,
//           2: pw.Alignment.centerRight,
//           3: pw.Alignment.centerRight,
//           4: pw.Alignment.center,
//           5: pw.Alignment.center,
//         },
//         columnWidths: {
//           0: pw.FractionColumnWidth(1),
//           1: pw.FractionColumnWidth(1),
//           2: pw.FractionColumnWidth(.9),
//           3: pw.FractionColumnWidth(.9),
//           4: pw.FractionColumnWidth(.8),
//           5: pw.FractionColumnWidth(.8),
//         },
//         // rowDecoration: pw.BoxDecoration(border: pw.Border(bottom: pw.BorderSide(width: .5, color: PdfColors.black))),
//         headers: List<String>.generate(tableHeaders.length, (col) => tableHeaders[col]),
//         data: List<List<String>>.generate(
//           rows.length,
//           (row) => List<String>.generate(
//             tableHeaders.length,
//             (col) => rows[row][col],
//           ),
//         ));
//   }
// }
