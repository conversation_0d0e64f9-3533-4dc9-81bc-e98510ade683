// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'edit_group_members_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EditGroupMembersRequest _$EditGroupMembersRequestFromJson(
        Map<String, dynamic> json) =>
    EditGroupMembersRequest(
      groupId: json['GroupId'] as String,
      addUserIds: (json['AddUserIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      removeUserIds: (json['RemoveUserIds'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$EditGroupMembersRequestToJson(
        EditGroupMembersRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'GroupId': instance.groupId,
      'AddUserIds': instance.addUserIds,
      'RemoveUserIds': instance.removeUserIds,
    };

EditGroupMembersResponse _$EditGroupMembersResponseFromJson(
        Map<String, dynamic> json) =>
    EditGroupMembersResponse(
      successMessage: json['SuccessMessage'] as String?,
      addedCount: (json['AddedCount'] as num?)?.toInt(),
      removedCount: (json['RemovedCount'] as num?)?.toInt(),
      errorCode: json['errorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['serverTime']),
    );

Map<String, dynamic> _$EditGroupMembersResponseToJson(
    EditGroupMembersResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('errorCode', instance.errorCode);
  writeNotNull('serverTime', nullableDateTimeToJson(instance.serverTime));
  writeNotNull('SuccessMessage', instance.successMessage);
  writeNotNull('AddedCount', instance.addedCount);
  writeNotNull('RemovedCount', instance.removedCount);
  return val;
}
