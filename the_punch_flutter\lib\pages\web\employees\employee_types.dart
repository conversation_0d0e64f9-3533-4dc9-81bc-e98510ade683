import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:collection/collection.dart';
import '../../../dataModel/data/user_type.dart';
import '../../../dataModel/data_model.dart';
import '../../../helpers/text_style_helper.dart';
import '../../../misc/app_localization.dart';
import '../../../misc/extensions.dart';
import '../../view_model_mixin.dart';
import '../my_scaffold.dart';
import 'employee_type_mixin.dart';
import '../../../state/permissions_state.dart';
import '../../../widgets/active_toggle.dart';
import '../../../widgets/search_text_field.dart';
import '../../../widgets/value_listenable_builder.dart';
import '../../../widgets/tables_global.dart'; // Import the global custom table

class EmployeeTypesPage extends StatelessWidget {
  const EmployeeTypesPage({super.key});

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_ViewModel>(
        create: (context) => _ViewModel(),
        child: MyScaffold(
          title: AppLocalization.of(context).employeeTypes,
          body: _Body(),
        ),
      );
}

class _Body extends StatelessWidget {
  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, constraints) {
          final isMobile = constraints.maxWidth < 600;

          return Align(
            alignment: Alignment.centerLeft,
            child: ConstrainedBox(
              constraints:
                  BoxConstraints(maxWidth: isMobile ? double.infinity : 1300),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: _TableHeader(),
                  ),
                  const SizedBox(height: 10), // Space between header and table
                  Expanded(
                    child: _EmployeeTypesTable(),
                  ),
                ],
              ),
            ),
          );
        },
      );
}

class _TableHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, constraints) {
          final isMobile = constraints.maxWidth < 768;

          return Consumer<_ViewModel>(
            builder: (context, viewModel, child) => Card(
              color: Colors.transparent,
              elevation: 0,
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: isMobile
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          _buildSearchField(viewModel),
                          const SizedBox(height: 10),
                          // _buildActiveToggle(viewModel),
                          const SizedBox(height: 10),
                          // if (PermissionsState().editEmployeeTypes)
                          //   _buildAddEmployeeTypeButton(context),
                        ],
                      )
                    : Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Flexible(child: _buildSearchField(viewModel)),
                          const SizedBox(width: 20),
                          // _buildActiveToggle(viewModel),
                          const SizedBox(width: 20),
                          // if (PermissionsState().editEmployeeTypes)
                          //   _buildAddEmployeeTypeButton(context),
                        ],
                      ),
              ),
            ),
          );
        },
      );

  // Search field widget with a max width of 300px
  Widget _buildSearchField(_ViewModel viewModel) => ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 300),
        child: SearchTextField(notifier: viewModel.searchNotifier),
      );

  // Active toggle widget
  Widget _buildActiveToggle(_ViewModel viewModel) =>
      ActiveToggle(viewModel.activeNotifier);

  // Add employee type button widget
  Widget _buildAddEmployeeTypeButton(BuildContext context) =>
      ElevatedButton.icon(
        onPressed: () => context.go('/employeeTypes/edit'),
        icon: const Icon(Icons.add),
        label: Text(AppLocalization.of(context).addEmployeeType),
      );
}

class _EmployeeTypesTable extends StatelessWidget {
  @override
  Widget build(BuildContext context) => Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
          final rows = _getRows(viewModel, context);
          final columns = _getColumns(context);

          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: CustomTable(
              columns: columns,
              rows: rows,
              onRowClick: (DataRow row) {
                final userTypeName = (row.cells[0].child as Text).data ?? '';
                final selectedUserType = viewModel.userTypes
                    .firstWhere((userType) => userType.name == userTypeName);

                viewModel.goToDetail(selectedUserType.id, context);
              },
              headerHeight: 60,
              rowHeight: 40,
              mobileTableTitle: "Employee Types",
              stickyHeader: true,
              headerFontSize: 14,
              cellFontSize: 12,
              columnWidths: const {
                0: 0.2, // Employee Type Name
                1: 0.2, // Description
                2: 0.2, // Employee Count
                3: 0.2, // Permission Count

                5: 0.1, // Actions
              },
            ),
          );
        },
      );

  // Define columns for CustomTable
  List<DataColumn> _getColumns(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;

    if (isMobile) {
      return [
        DataColumn(
          label: Text(AppLocalization.of(context).employeeTypeName),
        ),
        DataColumn(
          label: Text(AppLocalization.of(context).activate),
        ),
      ];
    } else {
      return [
        DataColumn(
          label: Text(AppLocalization.of(context).employeeTypeName),
        ),
        DataColumn(
          label: Text(AppLocalization.of(context).description),
        ),
        DataColumn(
          label: Text(AppLocalization.of(context).employeeCount),
        ),
        DataColumn(
          label: Text(AppLocalization.of(context).permissionCount),
        ),
        // DataColumn(
        //   label: Text(AppLocalization.of(context).activate),
        // ),
        DataColumn(label: Expanded(child: Center(child: const Text('Actions')))),
      ];
    }
  }

  // Define rows for CustomTable
  List<DataRow> _getRows(_ViewModel viewModel, BuildContext context) {
    final userTypes = viewModel.getFilteredUserTypes(context);
    final isMobile = MediaQuery.of(context).size.width < 600;

    return userTypes
        .map((userType) => DataRow(
              cells: isMobile
                  ? [
                      DataCell(Text(userType.name, style: const TextStyle(fontWeight: FontWeight.bold))   ),
                      DataCell(Text(userType.isActive.toActive(context))),
                    ]
                  : [
                      DataCell(Text(userType.name, style: const TextStyle(fontWeight: FontWeight.bold)) ),
                      DataCell(Text(userType.description)),
                      DataCell(
                        Text('${viewModel.employeeCountMap[userType.id] ?? 0}'),
                      ),
                      DataCell(
                        Text(
                            '${viewModel.permissionCountMap[userType.id] ?? 0}'),
                      ),
                      //DataCell(Text(userType.isActive.toActive(context))),
                      DataCell(
                        Center(
                          child: SizedBox(
                            width: 60,
                            child: ElevatedButton(
                              onPressed: () =>
                                  viewModel.goToDetail(userType.id, context),
                                style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8.0, vertical: 4.0),
                                textStyle: const TextStyle(fontSize: 12),
                                foregroundColor: Colors.blue,
                                backgroundColor: Colors.white,
                                side: const BorderSide(color: Colors.blue),
                                ),
                              child: const Text('View'),
                            ),
                          ),
                        ),
                      ),
                    ],
            ))
        .toList();
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin, EmployeeTypeMixin {
  List<UserType> userTypes = [];
  Map<String, int> employeeCountMap = {};
  Map<String, int> permissionCountMap = {};
  ValueNotifier<ActiveToggleState> activeNotifier =
      ValueNotifier(ActiveToggleState.active);
  ValueNotifier<String> searchNotifier = ValueNotifier('');

  _ViewModel() {
    addListenables([
      DataModel().userTypeModel,
      DataModel().userTypePermissionModel,
      DataModel().permissionModel,
    ]);
    unawaited(refresh());

    // Add listeners to refresh table when active state or search changes
    activeNotifier.addListener(notifyListeners);
    searchNotifier.addListener(notifyListeners);
  }

  Future<void> refresh() async {
    userTypes = (await DataModel().userTypeModel.allEmployees).toList();
    employeeCountMap = await _getEmployeeCountMap();
    permissionCountMap = await _getPermissionCountMap();
    notifyListeners();
  }

  Future<Map<String, int>> _getEmployeeCountMap() async {
    final employees = await DataModel().userModel.allEmployees;
    return employees
        .groupListsBy((e) => e.userTypeId)
        .map((key, value) => MapEntry(key, value.length));
  }

  Future<Map<String, int>> _getPermissionCountMap() async {
    final activePermissions = await DataModel().userTypePermissionModel.active;
    final adminPermissions = await DataModel().permissionModel.active;
    final permissionMap = activePermissions
        .groupListsBy((e) => e.userTypeId)
        .map((key, value) => MapEntry(key, value.length));
    permissionMap[UserType.administratorId] = adminPermissions.length;
    return permissionMap;
  }

  // Filter user types based on search and active state
  List<UserType> getFilteredUserTypes(BuildContext context) {
    var filtered = userTypes;
    final search = searchNotifier.value.toLowerCase();
    final isActiveFilter = activeNotifier.value;

    if (isActiveFilter != ActiveToggleState.all) {
      final isActive = isActiveFilter == ActiveToggleState.active;
      filtered = filtered.where((e) => e.isActive == isActive).toList();
    }

    if (search.isNotEmpty) {
      filtered = filtered.where((e) {
        return e.name.toLowerCase().contains(search) ||
            e.description.toLowerCase().contains(search);
      }).toList();
    }

    return filtered;
  }

  void goToDetail(String id, BuildContext context) {
    context.pushNamed('/employeeTypes/view', queryParameters: {'id': id});
  }
}
