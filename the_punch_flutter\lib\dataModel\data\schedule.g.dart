// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ScheduleAdapter extends TypeAdapter<Schedule> {
  @override
  final int typeId = 18;

  @override
  Schedule read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Schedule(
      isDirty: fields[0] as bool,
      isActive: fields[1] as bool,
      createdOn: fields[2] as DateTime,
      createdByUserId: fields[3] as String?,
      lastChangedOn: fields[4] as DateTime?,
      lastChangedByUserId: fields[5] as String?,
      id: fields[100] as String,
      locationId: fields[103] as String,
      startDateUtc: fields[101] as DateTime,
      endDateUtc: fields[102] as DateTime,
      userId: fields[104] as String,
      scheduleTemplateId: fields[105] as String,
      travelTime: fields[106] as Duration?,
      startDateLocal: fields[107] as DateTime,
      endDateLocal: fields[108] as DateTime,
      originalStartDateLocal: fields[109] as DateTime,
      nextLocationId: fields[110] as String?,
      notification1sent: fields[111] as bool,
      notification2sent: fields[112] as bool,
      notification3sent: fields[113] as bool,
      notification4sent: fields[114] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, Schedule obj) {
    writer
      ..writeByte(21)
      ..writeByte(100)
      ..write(obj.id)
      ..writeByte(101)
      ..write(obj.startDateUtc)
      ..writeByte(102)
      ..write(obj.endDateUtc)
      ..writeByte(103)
      ..write(obj.locationId)
      ..writeByte(104)
      ..write(obj.userId)
      ..writeByte(105)
      ..write(obj.scheduleTemplateId)
      ..writeByte(106)
      ..write(obj.travelTime)
      ..writeByte(107)
      ..write(obj.startDateLocal)
      ..writeByte(108)
      ..write(obj.endDateLocal)
      ..writeByte(109)
      ..write(obj.originalStartDateLocal)
      ..writeByte(110)
      ..write(obj.nextLocationId)
      ..writeByte(111)
      ..write(obj.notification1sent)
      ..writeByte(112)
      ..write(obj.notification2sent)
      ..writeByte(113)
      ..write(obj.notification3sent)
      ..writeByte(114)
      ..write(obj.notification4sent)
      ..writeByte(0)
      ..write(obj.isDirty)
      ..writeByte(1)
      ..write(obj.isActive)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdByUserId)
      ..writeByte(4)
      ..write(obj.lastChangedOn)
      ..writeByte(5)
      ..write(obj.lastChangedByUserId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ScheduleAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ScheduleIdsByMonthAdapter extends TypeAdapter<ScheduleIdsByMonth> {
  @override
  final int typeId = 24;

  @override
  ScheduleIdsByMonth read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ScheduleIdsByMonth()..ids = (fields[100] as List).cast<String>();
  }

  @override
  void write(BinaryWriter writer, ScheduleIdsByMonth obj) {
    writer
      ..writeByte(1)
      ..writeByte(100)
      ..write(obj.ids);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ScheduleIdsByMonthAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Schedule _$ScheduleFromJson(Map<String, dynamic> json) => Schedule(
      isActive: json['IsActive'] as bool? ?? true,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
      id: idFromJson(json['Id']),
      locationId: idFromJson(json['LocationId']),
      startDateUtc: dateTimeFromJson(json['StartDateUTC']),
      endDateUtc: dateTimeFromJson(json['EndDateUTC']),
      userId: idFromJson(json['UserId']),
      scheduleTemplateId: idFromJson(json['ScheduleTemplateId']),
      travelTime: nullableDurationFromJson(json['TravelTime']),
      startDateLocal: dateTimeFromJson(json['StartDateLocal']),
      endDateLocal: dateTimeFromJson(json['EndDateLocal']),
      originalStartDateLocal: dateTimeFromJson(json['OriginalStartDateLocal']),
      nextLocationId: nullableIdFromJson(json['NextLocationId']),
      notification1sent: json['notification1sent'] as bool? ?? false,
      notification2sent: json['notification2sent'] as bool? ?? false,
      notification3sent: json['notification3sent'] as bool? ?? false,
      notification4sent: json['notification4sent'] as bool? ?? false,
    );

Map<String, dynamic> _$ScheduleToJson(Schedule instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['StartDateUTC'] = dateTimeToJson(instance.startDateUtc);
  val['EndDateUTC'] = dateTimeToJson(instance.endDateUtc);
  val['LocationId'] = instance.locationId;
  val['UserId'] = instance.userId;
  val['ScheduleTemplateId'] = instance.scheduleTemplateId;
  writeNotNull('TravelTime', nullableDurationToJson(instance.travelTime));
  val['StartDateLocal'] = dateTimeToJson(instance.startDateLocal);
  val['EndDateLocal'] = dateTimeToJson(instance.endDateLocal);
  val['OriginalStartDateLocal'] =
      dateTimeToJson(instance.originalStartDateLocal);
  writeNotNull('NextLocationId', instance.nextLocationId);
  val['notification1sent'] = instance.notification1sent;
  val['notification2sent'] = instance.notification2sent;
  val['notification3sent'] = instance.notification3sent;
  val['notification4sent'] = instance.notification4sent;
  return val;
}
