<!DOCTYPE html>
<html>
<head>
  <base href="$FLUTTER_BASE_HREF">
  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- Your version meta tag -->
  <meta name="flutter-web-build" content="1.1.8+113" />

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="the_punch_flutter">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>The Punch</title>
  <link rel="manifest" href="manifest.json">

  <!-- Google Maps -->
  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBVO_QhmNJNr2ih4kBewpVCOrlEJ72fPXw"></script>

  <!-- Firebase -->
  <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>

  <!-- reCAPTCHA v2 -->
  <script src="https://www.google.com/recaptcha/api.js?render=explicit" async defer></script>

  <!-- Flutter initialization script -->
  <script src="flutter.js" defer></script>
</head>
<body>

  <!-- Optional PDF.js, etc... -->
  <script src="//cdnjs.cloudflare.com/ajax/libs/pdf.js/2.7.570/pdf.min.js"></script>
  <script>
    pdfjsLib.GlobalWorkerOptions.workerSrc =
      "//cdnjs.cloudflare.com/ajax/libs/pdf.js/2.7.570/pdf.worker.min.js";
  </script>

  <script>
    // You could read the meta tag from the HTML to keep things DRY:
    var versionMeta = document.querySelector('meta[name="flutter-web-build"]');
    var appVersion = versionMeta ? versionMeta.content : "dev";

    // Now override how Flutter loads main.dart.js, appending ?v=appVersion
    window.addEventListener('load', function() {
      _flutter.loader
        .loadEntrypoint({
          serviceWorker: {
            // If you are using a service worker, also set its version
            serviceWorkerVersion: appVersion
          },
          // Pass your version here:
          entrypointUrl: 'main.dart.js?v=' + appVersion
        })
        .then(function(engineInitializer) {
          return engineInitializer.initializeEngine();
        })
        .then(function(appRunner) {
          return appRunner.runApp();
        });
    });
  </script>
</body>
</html>
