import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import 'package:collection/collection.dart';

// Import your data-model classes
import '../../dataModel/data/message_group.dart';
import '../../dataModel/data/group_member.dart';
import '../../dataModel/data/user.dart';
import '../../dataModel/data_model.dart';

// Import your custom widgets and mixins
import '../../helpers/color_helper.dart';
import '../../widgets/chat_widget.dart';
import '../view_model_mixin.dart';
import '../web/my_scaffold.dart';
import '../../state/login_state.dart'; // For current user ID

/// A page to display details of a specific chat group.
class ChatDetailsPage extends StatelessWidget {
  ChatDetailsPage(Map<String, String> queryParms, {Key? key})
      : groupId = queryParms['id'] ?? '',
        super(key: key);

  final String groupId;

  @override
  Widget build(BuildContext context) {
    const bool showTitle = true;

    return ChangeNotifierProvider<_ViewModel>(
      create: (_) => _ViewModel(groupId),
      child: Consumer<_ViewModel>(
        builder: (context, viewModel, child) {
final groupName =
             viewModel.otherUserName?? viewModel.group?.name;
          final memberCount = viewModel.groupMemberUserIds.length;
         print('${viewModel.otherUserName}');
          return MyScaffold(
            title: groupName ?? '',
            showTitle: false,          
            //totalMembers:memberCount,
            titleWidget: InkWell(
              onTap: () async {
                // Refresh group members
                await viewModel.refresh();
                // Then open the dialog
                showDialog(
                  context: context,
                  builder: (_) => _GroupMembersDialog(group: viewModel.group),
                );
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
              Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
             children:[Text(
                groupName ?? '',
                style: const TextStyle(fontSize: 20),
                
              ),
              if(memberCount>2)
              RichText(
                  text: TextSpan(
                    children: [
                      const TextSpan(
                        text: "Total Members",
                        style: TextStyle(fontSize: 10,color:Colors.black),
                        
                      ),
                      TextSpan(
                        text: '  ${memberCount} ',
                        style: TextStyle(fontSize: 10, color: Colors.red),
                      ),
                    ],
                  ),
                )

              ]
              ),
                  // If it's NOT a 2-person group, show "Total Members"
               
              ],
              ),
            ),
            //showTitle: showTitle,
            showBackButton: true,
            body: Center(
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: _Body(
                  groupId: groupId,
                  groupName: groupName ?? '',
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

class _Body extends StatelessWidget {
  final String groupId;
  final String groupName;

  const _Body({
    required this.groupId,
    required this.groupName,
  });

  @override
  Widget build(BuildContext context) {
    // Access the ViewModel from Provider
    final viewModel = context.watch<_ViewModel>();

    return SizedBox.expand(
      child: ChatWidget(
        groupId: groupId,
        groupName: groupName,
        groupMemberUserIds: viewModel.groupMemberUserIds,
      ),
    );
  }
}

class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String groupId;
  MessageGroup? group;
  
  // Store the active userIds in the group
  Set<String> groupMemberUserIds = {};

  // NEW: store the other person's name if there are exactly 2 members
  String? otherUserName;

  _ViewModel(this.groupId) {
    addListenables([DataModel().messageGroupModel]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    try {
      // 1. Get the group
      group = await DataModel().messageGroupModel.getById(groupId);

      // 2. Load group members
      final memberList =
          await DataModel().groupMemberModel.getByGroupId(groupId);
      groupMemberUserIds = memberList.map((m) => m.userId).toSet();

      // 3. If exactly 2 members, find the "other" user and fetch their name
      if (groupMemberUserIds.length == 2) {
        final myUserId = LoginState.userId;
        final otherId = groupMemberUserIds.firstWhere(
          (uid) => uid != myUserId,
          orElse: () => '',
        );
        if (otherId.isNotEmpty) {
          final otherUser = await DataModel().userModel.getById(otherId);
          otherUserName = otherUser?.name;  // or fullName
        }
      } else {
        // If more or fewer than 2, set this to null
        otherUserName = null;
      }
    } catch (e, st) {
      debugPrint('Error fetching group: $e\n$st');
    }

    notifyListeners();
  }
}

/// A dialog that lists all members of the group.
/// If the current user is the group creator, it shows "Edit" mode with two tabs:
///  - Add: lists all users NOT in the group
///  - Remove: lists all users in the group (excluding yourself)
/// The user can check members to add or remove, then press "Done" to confirm.
class _GroupMembersDialog extends StatefulWidget {
  final MessageGroup? group;

  const _GroupMembersDialog({Key? key, this.group}) : super(key: key);

  @override
  State<_GroupMembersDialog> createState() => _GroupMembersDialogState();
}

class _GroupMembersDialogState extends State<_GroupMembersDialog> {
  /// Whether we’re currently in “Edit” mode
  bool _isEditMode = false;

  /// Which tab is selected in “Edit” mode? 0 = Add, 1 = Remove
  int _editTabIndex = 0;

  /// Once we’ve fetched GroupMember + User info, store it here.
  /// This is the *current* membership list for the group.
  List<_MemberWithUser> _memberWithUserList = [];

  /// Full list of all users (so we can show those who are NOT in the group in the Add tab).
  List<User> _allUsers = [];

  /// Track which userIds should be added/removed
  final Set<String> _selectedToAdd = {};
  final Set<String> _selectedToRemove = {};

  /// A Future we can await in a single [FutureBuilder]
  late Future<void> _futureLoad;

  /// Whether we’re currently sending changes to the backend
  bool _isBusy = false;

  @override
  void initState() {
    super.initState();
    _futureLoad = _loadData();
  }

  /// Loads:
  ///  - The group’s current members
  ///  - All users (to know who’s *not* in the group)
  Future<void> _loadData() async {
    final group = widget.group;
    if (group == null) return;

    // 1. Get the group's members
    final members = await DataModel().groupMemberModel.getByGroupId(group.id);

    // 2. Fetch the User object for each member
    final memberTasks = members.map((member) async {
      final user = await DataModel().userModel.getById(member.userId);
      return _MemberWithUser(member, user);
    });
    _memberWithUserList = await Future.wait(memberTasks);

    // 3. Fetch *all* users (so we can filter out group members to show the “Add” list)
    _allUsers = await DataModel().userModel.getAllUsers();
  }

  @override
  Widget build(BuildContext context) {
    final group = widget.group;
    if (group == null) {
      return AlertDialog(
        title: const Text('Chat'),
        content: const Text('Group not found.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      );
    }

    // If the current user is the group creator, we allow “Edit” and “Delete”
    final isCreator = (LoginState.userId == group.createdByUserId);

    return AlertDialog(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // First row: Group title, plus either "Edit" & "Delete" or "Done/Cancel"
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                group.name,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey,
                ),
              ),
              if (isCreator)
                if (!_isEditMode)
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                                            // <<<<<< NEW "DELETE" BUTTON >>>>>>
                      TextButton(
                        onPressed: _onDeleteGroup,
                        child: const Text(
                          'Delete',
                          style: TextStyle(
                            color: Colors.red,
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      TextButton(
                        onPressed: () {
                          setState(() => _isEditMode = true);
                        },
                        child: Text(
                          'Edit',
                          style: TextStyle(
                            color: ColorHelper.thePunchLightGray(),
                          ),
                        ),
                      ),                      
                    ],
                  )
                else
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextButton(
                        onPressed: _isBusy ||
                                (_selectedToAdd.isEmpty &&
                                    _selectedToRemove.isEmpty)
                            ? null
                            : _onDonePressed,
                        child: const Text('Done'),
                      ),
                      TextButton(
                        onPressed: _isBusy
                            ? null
                            : () {
                                setState(() {
                                  _isEditMode = false;
                                  _selectedToAdd.clear();
                                  _selectedToRemove.clear();
                                });
                              },
                        child: const Text('Cancel'),
                      ),
                    ],
                  ),
            ],
          ),
          const SizedBox(height: 8),
          // Second row: The Add / Remove tabs (only in edit mode)
          if (_isEditMode)
            Row(
              children: [
                _buildTabButton(
                  label: 'Add',
                  index: 0,
                ),
                _buildTabButton(
                  label: 'Remove',
                  index: 1,
                ),
              ],
            ),
        ],
      ),
      content: SizedBox(
        width: 350,
        height: 420,
        child: _isBusy
            ? Center(
                child: CircularProgressIndicator(
                  color: ColorHelper.thePunchAccentRed(),
                ),
              )
            : FutureBuilder<void>(
                future: _futureLoad,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return Center(
                      child: CircularProgressIndicator(
                        color: ColorHelper.thePunchAccentRed(),
                      ),
                    );
                  }
                  if (snapshot.hasError) {
                    return Text('Error: ${snapshot.error}');
                  }

                  // If loaded, build the main content
                  return _buildContent(context);
                },
              ),
      ),
      actions: [
        // If not in edit mode, we show "Close"
        // If in edit mode, we show Done/Cancel above in the title
        if (!_isEditMode)
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
      ],
    );
  }

  Widget _buildContent(BuildContext context) {
    if (_memberWithUserList.isEmpty) {
      return const Center(child: Text('No members in this group.'));
    }

    if (!_isEditMode) {
      // Read-only mode: just show the current members
      return ListView.builder(
        itemCount: _memberWithUserList.length,
        itemBuilder: (context, index) {
          final memberAndUser = _memberWithUserList[index];
          final user = memberAndUser.user;
          final displayName = user?.name ?? 'Unknown user';

          return ListTile(
            leading: const Icon(Icons.person),
            title: Text(displayName),
          );
        },
      );
    } else {
      // Edit mode: show either Add or Remove list
      if (_editTabIndex == 0) {
        // "Add" tab: show all users who are NOT in the group
        final currentIds =
            _memberWithUserList.map((mu) => mu.member.userId).toSet();

        final addCandidates =
            _allUsers.where((u) => !currentIds.contains(u.id)).toList();

        if (addCandidates.isEmpty) {
          return const Center(child: Text('No users available to add.'));
        }

        return ListView.builder(
          itemCount: addCandidates.length,
          itemBuilder: (context, index) {
            final user = addCandidates[index];
            final isSelected = _selectedToAdd.contains(user.id);

            return CheckboxListTile(
              title: Text(user.name),
              value: isSelected,
              activeColor: ColorHelper.thePunchAccentRed(),
              onChanged: (bool? checked) {
                setState(() {
                  if (checked == true) {
                    _selectedToAdd.add(user.id);
                  } else {
                    _selectedToAdd.remove(user.id);
                  }
                });
              },
            );
          },
        );
      } else {
        // "Remove" tab: show current group members (excluding yourself, if desired)
        final myUserId = LoginState.userId;
        final removeCandidates = _memberWithUserList
            .where((m) => m.member.userId != myUserId)
            .toList();

        if (removeCandidates.isEmpty) {
          return const Center(child: Text('No other members to remove.'));
        }

        return ListView.builder(
          itemCount: removeCandidates.length,
          itemBuilder: (context, index) {
            final memberAndUser = removeCandidates[index];
            final member = memberAndUser.member;
            final user = memberAndUser.user;

            final displayName = user?.name ?? 'Unknown user';
            final isSelected = _selectedToRemove.contains(member.userId);

            return CheckboxListTile(
              title: Text(displayName),
              value: isSelected,
              activeColor: ColorHelper.thePunchAccentRed(),
              onChanged: (bool? value) {
                setState(() {
                  if (value == true) {
                    _selectedToRemove.add(member.userId);
                  } else {
                    _selectedToRemove.remove(member.userId);
                  }
                });
              },
            );
          },
        );
      }
    }
  }

  /// Renders a simple tab-style button to switch between "Add" and "Remove".
  Widget _buildTabButton({
    required String label,
    required int index,
  }) {
    final bool isSelected = (_editTabIndex == index);
    return Expanded(
      child: TextButton(
        onPressed: () {
          setState(() {
            _editTabIndex = index;
          });
        },
        style: TextButton.styleFrom(
          backgroundColor: isSelected ? Colors.grey[200] : null,
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.black : Colors.blueGrey,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  /// Called when the user taps “Done” in edit mode.
  /// Send both add & remove lists to the backend in one call.
  Future<void> _onDonePressed() async {
    if (widget.group == null) return;
    if (_selectedToAdd.isEmpty && _selectedToRemove.isEmpty) {
      // No changes => just close
      setState(() => _isEditMode = false);
      return;
    }

    setState(() => _isBusy = true);

    try {
      await DataModel().groupMemberModel.editGroup(
        widget.group!.id,
        _selectedToAdd.toList(),
        _selectedToRemove.toList(),
      );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Group updated: Added ${_selectedToAdd.length}, '
            'Removed ${_selectedToRemove.length}.',
          ),
        ),
      );

      Navigator.of(context).pop(); // close the dialog
    } catch (e) {
      debugPrint('Failed to edit group members: $e');
      // Show error if needed
    } finally {
      setState(() => _isBusy = false);
    }
  }

  /// Called when user clicks "Delete" in the read-only mode (icon next to Edit).
  void _onDeleteGroup() {
    // 1) Confirm first
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('Delete Chat'),
        content: const Text('Are you sure you want to delete this chat?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(), // close the dialog
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop(); // close confirmation dialog
              // 2) Make the API call
              await _doDeleteGroup();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _doDeleteGroup() async {
    if (widget.group == null) return;

    setState(() => _isBusy = true);
    try {
      // If you already have a method in your DataModel or an API call:
      await DataModel().messageGroupModel.deleteGroup(widget.group!.id);
      // Return to the previous page entirely if you want
      if (!mounted) return;
      Navigator.of(context).pop(); // closes the group-members dialog
      Navigator.of(context).pop(); // closes the entire chat detail page
    } catch (e) {
      debugPrint('Failed to delete group: $e');
      // Optionally show an error
    } finally {
      if (mounted) setState(() => _isBusy = false);
    }
  }
}

/// Convenience class to hold both the [GroupMember] and its corresponding [User].
class _MemberWithUser {
  final GroupMember member;
  final User? user;
  _MemberWithUser(this.member, this.user);
}
