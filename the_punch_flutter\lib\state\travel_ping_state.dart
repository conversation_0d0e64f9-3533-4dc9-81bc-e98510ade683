import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class TravelPingState {
  static const _userIdKey = 'userId';
  static const _punchCardIdKey = 'punchCardId';
  static const _lastPingLatKey = 'lastPingLat';
  static const _lastPingLongKey = 'lastPingLong';
  static const _lastPingEndTimeKey = 'lastPingEndTime';
  static const _currentPunchTravelPingsKey = 'currentPunchTravelPings';
  static const _stashedPingsKey = 'stashedPings';

  Future<void> clearAllTravelPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userIdKey);
    await prefs.remove(_punchCardIdKey);
    await prefs.remove(_lastPingLatKey);
    await prefs.remove(_lastPingLongKey);
    await prefs.remove(_lastPingEndTimeKey);
    await prefs.remove(_currentPunchTravelPingsKey);
    await prefs.remove(_stashedPingsKey);
  }  

  Future<void> saveUserId(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userIdKey, userId);
  }

  Future<String?> getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userIdKey);
  }

  Future<void> savePunchCardId(String punchCardId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_punchCardIdKey, punchCardId);
  }

  Future<String?> getPunchCardId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_punchCardIdKey);
  }

  Future<void> saveLastPingLocation(String? latitude, String? longitude) async {
    final prefs = await SharedPreferences.getInstance();
    if (latitude != null && longitude != null) {
      await prefs.setString(_lastPingLatKey, latitude);
      await prefs.setString(_lastPingLongKey, longitude);
    } else {
      await prefs.remove(_lastPingLatKey);
      await prefs.remove(_lastPingLongKey);
    }
  }

  Future<Map<String, String?>> getLastPingLocation() async {
    final prefs = await SharedPreferences.getInstance();
    String? latitude = prefs.getString(_lastPingLatKey);
    String? longitude = prefs.getString(_lastPingLongKey);
    return {
      "latitude": latitude,
      "longitude": longitude
    };
  }

  Future<void> saveLastPingEndTime(DateTime endTime) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastPingEndTimeKey, endTime.toIso8601String());
  }

  Future<DateTime?> getLastPingEndTime() async {
    final prefs = await SharedPreferences.getInstance();
    String? endTimeString = prefs.getString(_lastPingEndTimeKey);
    if (endTimeString != null) {
      return DateTime.tryParse(endTimeString);
    }
    return null;
  }

  Future<void> saveCurrentPunchTravelPings(List<Map<String, dynamic>> pings) async {
    final prefs = await SharedPreferences.getInstance();
    String encodedData = jsonEncode(pings);
    await prefs.setString(_currentPunchTravelPingsKey, encodedData);
  }

  Future<List<Map<String, dynamic>>> getCurrentPunchTravelPings() async {
    final prefs = await SharedPreferences.getInstance();
    String? encodedData = prefs.getString(_currentPunchTravelPingsKey);
    return encodedData == null ? [] : List<Map<String, dynamic>>.from(jsonDecode(encodedData));
  }

  Future<void> saveStashedPings(List<Map<String, dynamic>> pings) async {
    final prefs = await SharedPreferences.getInstance();
    String encodedData = jsonEncode(pings);
    await prefs.setString(_stashedPingsKey, encodedData);
  }

  Future<List<Map<String, dynamic>>> getStashedPings() async {
    final prefs = await SharedPreferences.getInstance();
    String? encodedData = prefs.getString(_stashedPingsKey);
    return encodedData == null ? [] : List<Map<String, dynamic>>.from(jsonDecode(encodedData));
  }
}
