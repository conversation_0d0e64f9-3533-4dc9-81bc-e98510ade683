import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';

// If you already have a common "SystemRequest" and "SystemResponse" that use
// @JsonSerializable, import them here. For example:
import '../../misc/json_conversion.dart';
import 'system.dart';

// This line is critical for json_serializable; 
// it must match your file name minus the .dart extension.
part 'remove_group_members_request.g.dart';

/// --------------------------------------------------------
/// REQUEST CLASS
/// --------------------------------------------------------
@JsonSerializable(includeIfNull: false, fieldRename: FieldRename.none)
class RemoveGroupMembersRequest extends SystemRequest {
  /// Matches the C# property name 'GroupId'
  @JsonKey(name: 'GroupId')
  final String groupId;

  /// Matches the C# property name 'UserIds'
  @JsonKey(name: 'UserIds')
  final List<String> userIds;

  // If your SystemRequest has serverIP, databaseName, sessionId fields,
  // simply pass them up to the superclass below.

  RemoveGroupMembersRequest({
    required this.groupId,
    required this.userIds,

    // These come from the SystemRequest base class
    required String serverIP,
    required String databaseName,
    required String sessionId,
  }) : super(
          serverIP: serverIP,
          databaseName: databaseName,
          sessionId: sessionId,
        );

  /// Creates a request with system details from [SystemRequest.create()].
  static Future<RemoveGroupMembersRequest> create(
    String groupId,
    List<String> userIds,
  ) async {
    final systemReq = await SystemRequest.create(); // however you do it
    return RemoveGroupMembersRequest(
      groupId: groupId,
      userIds: userIds,
      serverIP: systemReq.serverIP,
      databaseName: systemReq.databaseName,
      sessionId: systemReq.sessionId,
    );
  }

  /// Factory constructor for decoding JSON
  factory RemoveGroupMembersRequest.fromJson(Map<String, dynamic> json) =>
      _$RemoveGroupMembersRequestFromJson(json);

  /// Converts this object to a JSON map
  @override
  Map<String, dynamic> toJson() => _$RemoveGroupMembersRequestToJson(this);
}

/// --------------------------------------------------------
/// RESPONSE CLASS
/// --------------------------------------------------------
@JsonSerializable(includeIfNull: false, fieldRename: FieldRename.none)
class RemoveGroupMembersResponse extends SystemResponse {
  /// Example: a success message from the server, e.g. "Successfully removed 2 users..."
  /// This might come from your .NET as "ErrorMessage" or some other field.
  @JsonKey(name: 'SuccessMessage')
  final String? successMessage;

  /// If you want to track how many were removed,
  /// your .NET could add a field, e.g. "RemovedCount", which we parse here.
  @JsonKey(name: 'RemovedCount')
  final int? removedCount;

  /// Additional fields as needed

  /// Remember `SystemResponse` might already have `errorCode` and `serverTime`.
  RemoveGroupMembersResponse({
    this.successMessage,
    this.removedCount,

    // From SystemResponse base class
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  /// Factory constructor for decoding JSON
  factory RemoveGroupMembersResponse.fromJson(Map<String, dynamic> json) =>
      _$RemoveGroupMembersResponseFromJson(json);

  /// Converts this object to a JSON map
  @override
  Map<String, dynamic> toJson() => _$RemoveGroupMembersResponseToJson(this);
}