import 'package:flutter/material.dart';

class CalendarBodyDay extends StatelessWidget {
  final List<String> dates;
  final List<String> locations;
  final List<String> employees;
  final List<String> timeSlots;

  const CalendarBodyDay({
    Key? key,
    this.dates = const [
      'Sunday, February 2, 2025',
      'Monday, February 3, 2025', 
      'Tuesday, February 4, 2025'
    ],
    this.locations = const ['Tampa Suncoast Spotless'],
    this.employees = const [
      '<PERSON>, Alien',
      'Fuentes, Clarissa',
      'Ramirez Luna, Yordanka'
    ],
    this.timeSlots = const [
      '12am', '3am', '6am', '9am', '12pm', '3pm', '6pm', '9pm'
    ],
  }) : super(key: key);

  @override
  Widget build(BuildContext context) => LayoutBuilder(
      builder: (context, constraints) => Container(
          width: constraints.maxWidth,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(32),
            border: Border.all(
              color: const Color(0xFFEBF6FF),
              width: 1.5,
            ),
          ),
          child: Column(
            children: [
              
              Expanded(
                child: SingleChildScrollView(
                  child:Column(children: [
                    _buildCalendarGrid()
                  ],)
                ),
              ),
            ],
          ),
        )
    );



 
  Widget _buildTimeBlock(String time) => Container(
      width: 40,
      child: Text(
        time,
        textAlign: TextAlign.center,
        style: const TextStyle(
          fontFamily: 'Poppins',
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: Color(0xFF091F30),
        ),
      ),
    );

  Widget _buildCalendarGrid() => Table(
      border: TableBorder.all(
        color: const Color(0xFFEBF6FF),
        width: 1,
      ),
      columnWidths: const {
        0: FixedColumnWidth(170),
      },
      children: [
        ...locations.map((location) => _buildLocationRow(location)),
        ...employees.map((employee) => _buildEmployeeRow(employee)),
      ],
    );

  TableRow _buildLocationRow(String location) => TableRow(
      decoration: const BoxDecoration(
        color: Color(0xFFEBF6FF),
      ),
      children: [
        Container(
          height: 40,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 4),
          child: Text(
            location,
            style: const TextStyle(
              fontFamily: 'Poppins',
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: Color(0xFF091F30),
            ),
          ),
        ),
        ...List.generate(dates.length - 1, (_) => Container(height: 40)),
      ],
    );

  TableRow _buildEmployeeRow(String name) => TableRow(
      children: [
        Container(
          height: 40,
          padding: const EdgeInsets.only(left: 24),
          alignment: Alignment.centerLeft,
          child: Text(
            name,
            style: const TextStyle(
              fontFamily: 'Poppins',
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Color(0xFF091F30),
            ),
          ),
        ),
        ...List.generate(
          dates.length - 1,
          (_) => Container(
            height: 40,
            child: Row(
              children: List.generate(
                timeSlots.length,
                (_) => Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: const Color(0xFFEBF6FF),
                        width: 0.5,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
}

class CalendarBodyWeek extends CalendarBodyDay {
  const CalendarBodyWeek({
    Key? key,
    List<String> dates = const [
      'Sunday, February 2, 2025',
      'Monday, February 3, 2025',
      'Tuesday, February 4, 2025',
      'Wednesday, February 5, 2025',
      'Thursday, February 6, 2025',
      'Friday, February 7, 2025',
      'Saturday, February 8, 2025'
    ],
    List<String> locations = const ['Tampa Suncoast Spotless C'],
    List<String> employees = const [
      'Alvarez Valdez, Alien',
      'Fuentes, Clarissa',
      'Ramirez Luna, Yordanka'
    ],
    List<String> timeSlots = const [
      '12am', '3am', '6am', '9am', '12pm', '3pm', '6pm', '9pm'
    ],
  }) : super(
          key: key,
          dates: dates,
          locations: locations,
          employees: employees,
          timeSlots: timeSlots,
        );
}

class CalendarBodyMonth extends CalendarBodyDay {
  const CalendarBodyMonth({
    Key? key,
    List<String> dates = const [
      'Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5'
    ],
    List<String> locations = const ['Tampa Suncoast SpotlessB'],
    List<String> employees = const [
      'Alvarez Valdez, Alien',
      'Fuentes, Clarissa',
      'Ramirez Luna, Yordanka'
    ],
    List<String> timeSlots = const [
      'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'
    ],
  }) : super(
          key: key,
          dates: dates,
          locations: locations,
          employees: employees,
          timeSlots: timeSlots,
        );
}