// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_schedules_for_user_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetSchedulesForUserRequest _$GetSchedulesForUserRequestFromJson(
        Map<String, dynamic> json) =>
    GetSchedulesForUserRequest(
      targetUserId: json['TargetUserId'] as String,
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$GetSchedulesForUserRequestToJson(
        GetSchedulesForUserRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'TargetUserId': instance.targetUserId,
    };

GetSchedulesForUserResponse _$GetSchedulesForUserResponseFromJson(
        Map<String, dynamic> json) =>
    GetSchedulesForUserResponse(
      schedules: (json['Schedules'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList(),
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$GetSchedulesForUserResponseToJson(
    GetSchedulesForUserResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  writeNotNull('Schedules', instance.schedules);
  return val;
}
