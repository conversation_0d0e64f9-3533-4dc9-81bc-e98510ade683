// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule_template.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ScheduleTemplateAdapter extends TypeAdapter<ScheduleTemplate> {
  @override
  final int typeId = 22;

  @override
  ScheduleTemplate read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ScheduleTemplate(
      isDirty: fields[0] as bool,
      isActive: fields[1] as bool,
      createdOn: fields[2] as DateTime,
      createdByUserId: fields[3] as String?,
      lastChangedOn: fields[4] as DateTime?,
      lastChangedByUserId: fields[5] as String?,
      id: fields[100] as String,
      recurringFrequencyId: fields[101] as int,
      recurringFrequencyEveryNFreq: fields[102] as int,
      recurringFrequencyWeeklyOnMonday: fields[103] as bool,
      recurringFrequencyWeeklyOnTuesday: fields[104] as bool,
      recurringFrequencyWeeklyOnWednesday: fields[105] as bool,
      recurringFrequencyWeeklyOnThursday: fields[106] as bool,
      recurringFrequencyWeeklyOnFriday: fields[107] as bool,
      recurringFrequencyWeeklyOnSaturday: fields[108] as bool,
      recurringFrequencyWeeklyOnSunday: fields[109] as bool,
      recurringFrequencyMonthlyOccursOnSpecificMonth: fields[111] as bool,
      recurringFrequencyMonthlyOnMonth: fields[112] as int,
      recurringFrequencyMonthlyOccursOnSpecificDay: fields[113] as bool,
      recurringFrequencyMonthlyOnDay: fields[114] as int,
      recurringFrequencyMonthlyTheWeekId: fields[115] as int,
      recurringFrequencyMonthlyTheDayOfTheWeekId: fields[116] as int,
      userId: fields[117] as String,
      locationId: fields[118] as String,
      duration: fields[119] as Duration,
      startDateLocal: fields[120] as DateTime,
      endDateLocal: fields[121] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, ScheduleTemplate obj) {
    writer
      ..writeByte(27)
      ..writeByte(100)
      ..write(obj.id)
      ..writeByte(101)
      ..write(obj.recurringFrequencyId)
      ..writeByte(102)
      ..write(obj.recurringFrequencyEveryNFreq)
      ..writeByte(103)
      ..write(obj.recurringFrequencyWeeklyOnMonday)
      ..writeByte(104)
      ..write(obj.recurringFrequencyWeeklyOnTuesday)
      ..writeByte(105)
      ..write(obj.recurringFrequencyWeeklyOnWednesday)
      ..writeByte(106)
      ..write(obj.recurringFrequencyWeeklyOnThursday)
      ..writeByte(107)
      ..write(obj.recurringFrequencyWeeklyOnFriday)
      ..writeByte(108)
      ..write(obj.recurringFrequencyWeeklyOnSaturday)
      ..writeByte(109)
      ..write(obj.recurringFrequencyWeeklyOnSunday)
      ..writeByte(111)
      ..write(obj.recurringFrequencyMonthlyOccursOnSpecificMonth)
      ..writeByte(112)
      ..write(obj.recurringFrequencyMonthlyOnMonth)
      ..writeByte(113)
      ..write(obj.recurringFrequencyMonthlyOccursOnSpecificDay)
      ..writeByte(114)
      ..write(obj.recurringFrequencyMonthlyOnDay)
      ..writeByte(115)
      ..write(obj.recurringFrequencyMonthlyTheWeekId)
      ..writeByte(116)
      ..write(obj.recurringFrequencyMonthlyTheDayOfTheWeekId)
      ..writeByte(117)
      ..write(obj.userId)
      ..writeByte(118)
      ..write(obj.locationId)
      ..writeByte(119)
      ..write(obj.duration)
      ..writeByte(120)
      ..write(obj.startDateLocal)
      ..writeByte(121)
      ..write(obj.endDateLocal)
      ..writeByte(0)
      ..write(obj.isDirty)
      ..writeByte(1)
      ..write(obj.isActive)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdByUserId)
      ..writeByte(4)
      ..write(obj.lastChangedOn)
      ..writeByte(5)
      ..write(obj.lastChangedByUserId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ScheduleTemplateAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ScheduleTemplate _$ScheduleTemplateFromJson(Map<String, dynamic> json) =>
    ScheduleTemplate(
      isActive: json['IsActive'] as bool? ?? true,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      createdByUserId: json['CreatedByUserId'] as String?,
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
      id: idFromJson(json['Id']),
      recurringFrequencyId: (json['Recurring_Frequency_ID'] as num).toInt(),
      recurringFrequencyEveryNFreq:
          (json['Recurring_Frequency_EveryNFreq'] as num).toInt(),
      recurringFrequencyWeeklyOnMonday:
          json['Recurring_Frequency_Weekly_OnMonday'] as bool,
      recurringFrequencyWeeklyOnTuesday:
          json['Recurring_Frequency_Weekly_OnTuesday'] as bool,
      recurringFrequencyWeeklyOnWednesday:
          json['Recurring_Frequency_Weekly_OnWednesday'] as bool,
      recurringFrequencyWeeklyOnThursday:
          json['Recurring_Frequency_Weekly_OnThursday'] as bool,
      recurringFrequencyWeeklyOnFriday:
          json['Recurring_Frequency_Weekly_OnFriday'] as bool,
      recurringFrequencyWeeklyOnSaturday:
          json['Recurring_Frequency_Weekly_OnSaturday'] as bool,
      recurringFrequencyWeeklyOnSunday:
          json['Recurring_Frequency_Weekly_OnSunday'] as bool,
      recurringFrequencyMonthlyOccursOnSpecificMonth:
          json['Recurring_Frequency_Monthly_OccursOnSpecificMonth'] as bool,
      recurringFrequencyMonthlyOnMonth:
          (json['Recurring_Frequency_Monthly_OnMonth'] as num).toInt(),
      recurringFrequencyMonthlyOccursOnSpecificDay:
          json['Recurring_Frequency_Monthly_OccursOnSpecificDay'] as bool,
      recurringFrequencyMonthlyOnDay:
          (json['Recurring_Frequency_Monthly_OnDay'] as num).toInt(),
      recurringFrequencyMonthlyTheWeekId:
          (json['Recurring_Frequency_Monthly_TheWeek_ID'] as num).toInt(),
      recurringFrequencyMonthlyTheDayOfTheWeekId:
          (json['Recurring_Frequency_Monthly_TheDayOfTheWeek_ID'] as num)
              .toInt(),
      userId: idFromJson(json['UserId']),
      locationId: idFromJson(json['LocationId']),
      duration: durationFromJson(json['Duration']),
      startDateLocal: dateTimeFromJson(json['StartDateLocal']),
      endDateLocal: nullableDateTimeFromJson(json['EndDateLocal']),
    );

Map<String, dynamic> _$ScheduleTemplateToJson(ScheduleTemplate instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['Recurring_Frequency_ID'] = instance.recurringFrequencyId;
  val['Recurring_Frequency_EveryNFreq'] = instance.recurringFrequencyEveryNFreq;
  val['Recurring_Frequency_Weekly_OnMonday'] =
      instance.recurringFrequencyWeeklyOnMonday;
  val['Recurring_Frequency_Weekly_OnTuesday'] =
      instance.recurringFrequencyWeeklyOnTuesday;
  val['Recurring_Frequency_Weekly_OnWednesday'] =
      instance.recurringFrequencyWeeklyOnWednesday;
  val['Recurring_Frequency_Weekly_OnThursday'] =
      instance.recurringFrequencyWeeklyOnThursday;
  val['Recurring_Frequency_Weekly_OnFriday'] =
      instance.recurringFrequencyWeeklyOnFriday;
  val['Recurring_Frequency_Weekly_OnSaturday'] =
      instance.recurringFrequencyWeeklyOnSaturday;
  val['Recurring_Frequency_Weekly_OnSunday'] =
      instance.recurringFrequencyWeeklyOnSunday;
  val['Recurring_Frequency_Monthly_OccursOnSpecificMonth'] =
      instance.recurringFrequencyMonthlyOccursOnSpecificMonth;
  val['Recurring_Frequency_Monthly_OnMonth'] =
      instance.recurringFrequencyMonthlyOnMonth;
  val['Recurring_Frequency_Monthly_OccursOnSpecificDay'] =
      instance.recurringFrequencyMonthlyOccursOnSpecificDay;
  val['Recurring_Frequency_Monthly_OnDay'] =
      instance.recurringFrequencyMonthlyOnDay;
  val['Recurring_Frequency_Monthly_TheWeek_ID'] =
      instance.recurringFrequencyMonthlyTheWeekId;
  val['Recurring_Frequency_Monthly_TheDayOfTheWeek_ID'] =
      instance.recurringFrequencyMonthlyTheDayOfTheWeekId;
  val['UserId'] = instance.userId;
  val['LocationId'] = instance.locationId;
  writeNotNull('Duration', durationToJson(instance.duration));
  val['StartDateLocal'] = dateTimeToJson(instance.startDateLocal);
  writeNotNull('EndDateLocal', nullableDateTimeToJson(instance.endDateLocal));
  return val;
}
