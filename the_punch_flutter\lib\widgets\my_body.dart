import 'package:flutter/material.dart';

class MyRoundedBody extends StatelessWidget {
  final Widget header;
  final Widget body;

  const MyRoundedBody({super.key, required this.header, required this.body});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final surface = theme.colorScheme.surface;

    return ColoredBox(
      color: Colors.red,
      child: Column(
        children: [
          header,
          Expanded(
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20), topRight: Radius.circular(20)),
              child: ColoredBox(
                color: surface,
                child: body,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class MyBody extends StatelessWidget {
  final Widget? menu;
  final Widget? header;
  final Widget body;

  const MyBody({super.key, this.menu, this.header, required this.body});

  @override
  Widget build(BuildContext context) => Column(
        children: [
          Material(
            elevation: 5,
            color: Theme.of(context).primaryColor,
            child: Column(
              children: [
                if (menu != null) menu!,
                if (header != null) header!,
              ],
            ),
          ),
          Expanded(
            child: Container(
              child: body,
            ),
          ),
        ],
      );
}
