import '../base_data.dart';
import '../data/permission.dart';
import '../hive_db.dart';
import 'package:collection/collection.dart';

class PermissionModel extends BaseDataModel<Permission> {
  @override
  Future<Iterable<Permission>> get all async => (await HiveDb.database).permissions.values;

  @override
  Future<void> save(Iterable<Permission> t) async {
    await (await HiveDb.database).permissions.putAll({for (final e in t) e.id: e});
    notifyListeners();
  }

  Future<Permission?> getByKey(String key) async => (await all).firstWhereOrNull((e) => e.key == key);
}
