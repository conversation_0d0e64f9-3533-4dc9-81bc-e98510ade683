// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fetch_messages_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FetchMessagesRequest _$FetchMessagesRequestFromJson(
        Map<String, dynamic> json) =>
    FetchMessagesRequest(
      targetUserId: json['TargetUserId'] as String,
      serverIP: json['Request_ServerIP'] as String,
      databaseName: json['Request_DatabaseName'] as String,
      sessionId: json['Request_SessionID'] as String,
    );

Map<String, dynamic> _$FetchMessagesRequestToJson(
        FetchMessagesRequest instance) =>
    <String, dynamic>{
      'TargetUserId': instance.targetUserId,
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
    };

FetchMessagesResponse _$FetchMessagesResponseFromJson(
        Map<String, dynamic> json) =>
    FetchMessagesResponse(
      messages: (json['Messages'] as List<dynamic>)
          .map((e) => Message.fromJson(e as Map<String, dynamic>))
          .toList(),
      messageGroups: (json['MessageGroups'] as List<dynamic>?)
          ?.map((e) => MessageGroup.fromJson(e as Map<String, dynamic>))
          .toList(),
      groupMembers: (json['GroupMembers'] as List<dynamic>?)
          ?.map((e) => GroupMember.fromJson(e as Map<String, dynamic>))
          .toList(),
      errorCode: json['errorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['serverTime']),
    );

Map<String, dynamic> _$FetchMessagesResponseToJson(
    FetchMessagesResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('errorCode', instance.errorCode);
  writeNotNull('serverTime', nullableDateTimeToJson(instance.serverTime));
  val['Messages'] = instance.messages;
  writeNotNull('MessageGroups', instance.messageGroups);
  writeNotNull('GroupMembers', instance.groupMembers);
  return val;
}
