import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../dataModel/data/schedule_template.dart';

import 'system.dart';

part 'schedule_template.g.dart';

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class AddScheduleTemplateRequest extends SystemRequest {
  final ScheduleTemplate scheduleTemplate;

  AddScheduleTemplateRequest({
    required this.scheduleTemplate,
    required super.serverIP,
    required super.databaseName,
    required super.sessionId,
  });

  static Future<AddScheduleTemplateRequest> create(
      ScheduleTemplate scheduleTemplate) async {
    final systemRequest = await SystemRequest.create();

    return AddScheduleTemplateRequest(
      scheduleTemplate: scheduleTemplate,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory AddScheduleTemplateRequest.fromJson(Map<String, dynamic> json) {
    try {
      return _$AddScheduleTemplateRequestFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }

  @override
  Map<String, dynamic> toJson() => _$AddScheduleTemplateRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class EditScheduleTemplateRequest extends SystemRequest {
  final ScheduleTemplate scheduleTemplate;

  EditScheduleTemplateRequest({
    required this.scheduleTemplate,
    required super.serverIP,
    required super.databaseName,
    required super.sessionId,
  });

  static Future<EditScheduleTemplateRequest> create(
      ScheduleTemplate scheduleTemplate) async {
    final systemRequest = await SystemRequest.create();

    return EditScheduleTemplateRequest(
      scheduleTemplate: scheduleTemplate,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory EditScheduleTemplateRequest.fromJson(Map<String, dynamic> json) {
    try {
      return _$EditScheduleTemplateRequestFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }

  @override
  Map<String, dynamic> toJson() => _$EditScheduleTemplateRequestToJson(this);
}

//
// ADD THIS CLASS FOR DELETING A SCHEDULE TEMPLATE
//

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class DeleteScheduleTemplateRequest extends SystemRequest {
  /// The unique ID of the schedule template you want to delete.
  final String scheduleTemplateId;

  DeleteScheduleTemplateRequest({
    required this.scheduleTemplateId,
    required super.serverIP,
    required super.databaseName,
    required super.sessionId,
  });

  /// Static create method to mirror the style of the other requests.
  static Future<DeleteScheduleTemplateRequest> create(
    String scheduleTemplateId,
  ) async {
    final systemRequest = await SystemRequest.create();

    return DeleteScheduleTemplateRequest(
      scheduleTemplateId: scheduleTemplateId,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  /// Factory constructor to handle JSON -> object conversion
  factory DeleteScheduleTemplateRequest.fromJson(Map<String, dynamic> json) {
    try {
      return _$DeleteScheduleTemplateRequestFromJson(json);
    } catch (e) {
      if (kDebugMode) print('$e\n${StackTrace.current}');
      rethrow;
    }
  }

  /// toJson override for object -> JSON conversion
  @override
  Map<String, dynamic> toJson() =>
      _$DeleteScheduleTemplateRequestToJson(this);
}
