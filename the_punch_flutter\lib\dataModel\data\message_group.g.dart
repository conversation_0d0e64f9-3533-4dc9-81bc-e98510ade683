// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message_group.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MessageGroupAdapter extends TypeAdapter<MessageGroup> {
  @override
  final int typeId = 28;

  @override
  MessageGroup read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MessageGroup(
      id: fields[6] as String,
      name: fields[7] as String,
      isDirty: fields[0] as bool,
      isActive: fields[1] as bool,
      createdByUserId: fields[3] as String,
      createdOn: fields[2] as DateTime,
      lastChangedOn: fields[4] as DateTime?,
      lastChangedByUserId: fields[5] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, MessageGroup obj) {
    writer
      ..writeByte(8)
      ..writeByte(6)
      ..write(obj.id)
      ..writeByte(7)
      ..write(obj.name)
      ..writeByte(0)
      ..write(obj.isDirty)
      ..writeByte(1)
      ..write(obj.isActive)
      ..writeByte(2)
      ..write(obj.createdOn)
      ..writeByte(3)
      ..write(obj.createdByUserId)
      ..writeByte(4)
      ..write(obj.lastChangedOn)
      ..writeByte(5)
      ..write(obj.lastChangedByUserId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MessageGroupAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MessageGroup _$MessageGroupFromJson(Map<String, dynamic> json) => MessageGroup(
      id: idFromJson(json['Id']),
      name: json['Name'] as String,
      isActive: json['IsActive'] as bool? ?? true,
      createdByUserId: json['CreatedByUserId'] as String,
      createdOn: dateTimeFromJson(json['CreatedOn']),
      lastChangedOn: nullableDateTimeFromJson(json['LastChangedOn']),
      lastChangedByUserId: json['LastChangedByUserId'] as String?,
    );

Map<String, dynamic> _$MessageGroupToJson(MessageGroup instance) {
  final val = <String, dynamic>{
    'IsActive': instance.isActive,
    'CreatedOn': dateTimeToJson(instance.createdOn),
  };

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('CreatedByUserId', instance.createdByUserId);
  writeNotNull('LastChangedOn', nullableDateTimeToJson(instance.lastChangedOn));
  writeNotNull('LastChangedByUserId', instance.lastChangedByUserId);
  val['Id'] = instance.id;
  val['Name'] = instance.name;
  return val;
}
