import 'dart:io';

import 'package:flutter/widgets.dart';
import 'package:path_provider/path_provider.dart';
import '../api/sync_model.dart';
import '../misc/my_platform.dart';
import 'fcm_model.dart';
import 'permissions_state.dart';
import 'session_timeout.dart';

class AppState extends ChangeNotifier {
  final sessionTimeout = SessionTimeout();
  late final Directory applicationSupportDirectory;
  late final Directory temporaryDirectory;
  static String? appVersion;
  static AppState? _singleton;
  static bool _isInitialized = false; // Track initialization status

  factory AppState() {
    if (_singleton == null) throw Exception('AppState not initialized');
    return _singleton!;
  }

  AppState._();

  static Future<AppState> initialize() async {
    if (_singleton == null) {
      _singleton = AppState._();
      await _singleton!._initialize();
      _isInitialized = true; // Set to true after successful initialization
    }
    return _singleton!;
  }

  // Publicly accessible getter to check initialization status
  static bool get isInitialized => _isInitialized;

  Future<void> _initialize() async {
    if (!MyPlatform.isWeb) {
      applicationSupportDirectory = (await getApplicationSupportDirectory());
      temporaryDirectory = (await getTemporaryDirectory());
    }

    //FCMModel.startListener(SyncModel());
  }
}

