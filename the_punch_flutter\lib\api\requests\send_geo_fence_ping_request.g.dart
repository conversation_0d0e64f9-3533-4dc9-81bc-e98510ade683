// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'send_geo_fence_ping_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SendGeoFencePingRequest _$SendGeoFencePingRequestFromJson(
        Map<String, dynamic> json) =>
    SendGeoFencePingRequest(
      location: GeoLocation.fromJson(json['Location'] as Map<String, dynamic>),
      serverIP: json['Request_ServerIP'] as String? ?? '',
      databaseName: json['Request_DatabaseName'] as String? ?? '',
      sessionId: json['Request_SessionID'] as String? ?? '',
    );

Map<String, dynamic> _$SendGeoFencePingRequestToJson(
        SendGeoFencePingRequest instance) =>
    <String, dynamic>{
      'Request_ServerIP': instance.serverIP,
      'Request_DatabaseName': instance.databaseName,
      'Request_SessionID': instance.sessionId,
      'Location': instance.location,
    };

SendGeoFencePingResponse _$SendGeoFencePingResponseFromJson(
        Map<String, dynamic> json) =>
    SendGeoFencePingResponse(
      success: json['Success'] as bool,
      errorCode: json['ErrorCode'] as String?,
      serverTime: nullableDateTimeFromJson(json['ServerTime']),
    );

Map<String, dynamic> _$SendGeoFencePingResponseToJson(
    SendGeoFencePingResponse instance) {
  final val = <String, dynamic>{};

  void writeNotNull(String key, dynamic value) {
    if (value != null) {
      val[key] = value;
    }
  }

  writeNotNull('ErrorCode', instance.errorCode);
  writeNotNull('ServerTime', nullableDateTimeToJson(instance.serverTime));
  val['Success'] = instance.success;
  return val;
}
