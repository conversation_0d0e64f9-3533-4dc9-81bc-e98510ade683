import 'dart:math' show atan2, cos, sin, sqrt;
import 'package:vector_math/vector_math.dart';

import 'package:intl/intl.dart';

double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
  // reference: https://stackoverflow.com/a/65250976
  const earthRadius = 6371000;
  final dLat = radians(lat1 - lat2);
  final dLon = radians(lon1 - lon2);
  final a = sin(dLat / 2) * sin(dLat / 2) + cos(radians(lat2)) * cos(radians(lat1)) * sin(dLon / 2) * sin(dLon / 2);
  final c = 2 * atan2(sqrt(a), sqrt(1 - a));
  final d = earthRadius * c;
  return d; //d is the distance in meters
}

extension DistanceExtension on double {
  static final format = NumberFormat('##0.0');
  String get metersToMiles => '${format.format(this * 0.000621)} mi';

  String get metersToKilometers => '${format.format(this * 0.001)} k';
}
