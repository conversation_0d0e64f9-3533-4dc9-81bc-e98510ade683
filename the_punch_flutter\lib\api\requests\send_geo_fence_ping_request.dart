import 'package:json_annotation/json_annotation.dart';
import '../../dataModel/data/geo_location.dart';
import '../../misc/json_conversion.dart';
import 'system.dart';

part 'send_geo_fence_ping_request.g.dart';  // This will generate JSON serializable code.

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class SendGeoFencePingRequest extends SystemRequest {
  final GeoLocation location;

  SendGeoFencePingRequest({
    required this.location,
    required String serverIP,
    required String databaseName,
    required String sessionId,
  }) : super(serverIP: serverIP, databaseName: databaseName, sessionId: sessionId);

  static Future<SendGeoFencePingRequest> create(GeoLocation location) async {
    final systemRequest = await SystemRequest.create();
    return SendGeoFencePingRequest(
      location: location,
      serverIP: systemRequest.serverIP,
      databaseName: systemRequest.databaseName,
      sessionId: systemRequest.sessionId,
    );
  }

  factory SendGeoFencePingRequest.fromJson(Map<String, dynamic> json) =>
      _$SendGeoFencePingRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$SendGeoFencePingRequestToJson(this);
}

@JsonSerializable(fieldRename: FieldRename.pascal, includeIfNull: false)
class SendGeoFencePingResponse extends SystemResponse {
  final bool success;

  SendGeoFencePingResponse({
    required this.success,
    String? errorCode,
    DateTime? serverTime,
  }) : super(errorCode, serverTime);

  factory SendGeoFencePingResponse.fromJson(Map<String, dynamic> json) =>
      _$SendGeoFencePingResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SendGeoFencePingResponseToJson(this);
}