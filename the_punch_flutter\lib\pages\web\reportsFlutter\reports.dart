// import 'package:flutter/material.dart';
// import 'package:the_punch_flutter/menus/myAppBar.dart';
// import 'package:the_punch_flutter/misc/appLocalization.dart';
// import 'package:the_punch_flutter/pages/transitionMixin.dart';
// import 'package:the_punch_flutter/state/appState.dart';

// class ReportsPage extends MaterialPage with NoTransitionMixin {
//   ReportsPage() : super(key: ValueKey('ReportsPage'), child: _Scaffold());
// }

// class _Scaffold extends StatelessWidget {
//   const _Scaffold();

//   @override
//   Widget build(BuildContext context) {
//     return MyScaffold(
//       appBar: MyAppBar(
//         AppLocalization.of(context).reports,
//         breadcrumbs: {
//           AppLocalization.of(context).reports: () => AppState().replaceStates([PageState.reports()]),
//           AppLocalization.of(context).reports: null,
//         },
//       ).appBar,
//       drawer: MyDrawer().drawer,
//       body: _Body(),
//     );
//   }
// }

// class _Body extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return Center(
//       child: ConstrainedBox(
//         constraints: BoxConstraints(maxWidth: 1300),
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             Padding(
//                 padding: const EdgeInsets.all(8.0),
//                 child: ElevatedButton(
//                   onPressed: () => AppState().pushState(PageState.reportUserSessions()),
//                   child: Text(AppLocalization.of(context).reportUserSessions),
//                 )),
//             Padding(
//                 padding: const EdgeInsets.all(8.0),
//                 child: ElevatedButton(
//                   onPressed: () => AppState().pushState(PageState.reportEmployeeHours()),
//                   child: Text(AppLocalization.of(context).reportEmployeesHoursWorked),
//                 )),
//             Padding(
//                 padding: const EdgeInsets.all(8.0),
//                 child: ElevatedButton(
//                   onPressed: () => AppState().pushState(PageState.reportLocationHours()),
//                   child: Text(AppLocalization.of(context).reportLocationsHoursWorked),
//                 ))
//           ],
//         ),
//       ),
//     );
//   }
// }
