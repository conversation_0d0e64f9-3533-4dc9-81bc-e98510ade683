import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../dataModel/data/group_member.dart';
import '../../dataModel/data/message_group.dart';
import '../../dataModel/data/user.dart';
import '../../dataModel/data_model.dart';
import '../../helpers/color_helper.dart';
import '../../state/login_state.dart';
import '../view_model_mixin.dart';
import '../web/my_scaffold.dart';

class EditChatPage extends StatefulWidget {
  final String groupId;

  const EditChatPage({Key? key, required this.groupId}) : super(key: key);

  @override
  _EditChatPageState createState() => _EditChatPageState();
}

class _EditChatPageState extends State<EditChatPage> {
bool _isEditMode = false;
 int _editTabIndex = 0;

  /// Once we’ve fetched GroupMember + User info, store it here.
  /// This is the *current* membership list for the group.
  List<_MemberWithUser> _memberWithUserList = [];

  /// Full list of all users (so we can show those who are NOT in the group in the Add tab).
  List<User> _allUsers = [];

  /// Track which userIds should be added/removed
  final Set<String> _selectedToAdd = {};
  final Set<String> _selectedToRemove = {};

  /// A Future we can await in a single [FutureBuilder]
  late Future<void> _futureLoad;

  /// Whether we’re currently sending changes to the backend
  bool _isBusy = false;


  @override
  void initState() {
    super.initState();
    _futureLoad = _loadData();
  }

  /// Loads:
  ///  - The group’s current members
  ///  - All users (to know who’s *not* in the group)
  Future<void> _loadData() async {
    // final group = widget.group;
    // if (group == null) return;

    // 1. Get the group's members
    final members = await DataModel().groupMemberModel.getByGroupId(widget.groupId);

    // 2. Fetch the User object for each member
    final memberTasks = members.map((member) async {
      final user = await DataModel().userModel.getById(member.userId);
      return _MemberWithUser(member, user);
    });
    _memberWithUserList = await Future.wait(memberTasks);

    // 3. Fetch *all* users (so we can filter out group members to show the “Add” list)
    _allUsers = await DataModel().userModel.getAllUsers();
  }
  Future<void> _doDeleteGroup() async {
    if (widget.groupId == null) return;

    setState(() => _isBusy = true);
    try {
      // If you already have a method in your DataModel or an API call:
      await DataModel().messageGroupModel.deleteGroup(widget.groupId);
          
      // Return to the previous page entirely if you want
      if (!mounted) return;
      Navigator.of(context).pop(); // closes the group-members dialog
      Navigator.of(context).pop(); // closes the entire chat detail page
      // Notify the DataModel that it has been updated
      
    } catch (e) {
      debugPrint('Failed to delete group: $e');
      // Optionally show an error
    } finally {
      if (mounted) setState(() => _isBusy = false);
    }
  }
  void _onDeleteGroup() {
    // 1) Confirm first
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('Delete Chat'),
        content: const Text('Are you sure you want to delete this chat?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(), // close the dialog
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop(); // close confirmation dialog
              // 2) Make the API call
              await _doDeleteGroup();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<_EditChatViewModel>(
        create: (_) => _EditChatViewModel(widget.groupId),
        child: Consumer<_EditChatViewModel>(
          builder: (context, viewModel, child) => MyScaffold(
            title: 'Edit Chat',
            showBackButton: true,
            body: Column(
              children: [
                const SizedBox(height: 10),
                const Text('Edit Chat',
                    style: TextStyle(
                        fontSize: 25,
                        color: Colors.black,
                        fontWeight: FontWeight.bold)),
                const SizedBox(height: 10),
                Container(
                  // decoration: BoxDecoration(
                  //   gradient: LinearGradient(
                  //     colors: [Colors.grey[200]!, Colors.white],
                  //     begin: Alignment.topCenter,
                  //     end: Alignment.bottomCenter,
                  //   ),
                  // ),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(20),
                        child: Center(
                          child: SizedBox(
                            height: 50,
                            width: MediaQuery.of(context).size.width * 0.85,
                            child: TextField(
                              onChanged: viewModel.updateSearchQuery,
                              decoration: InputDecoration(
                                prefixIcon: const Icon(Icons.search),
                                hintText: 'Search by name',
                                filled: true,
                                fillColor: Colors.grey[200],
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(50.0),
                                  borderSide: BorderSide.none,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: FutureBuilder<void>(
                    future: viewModel.futureLoad,
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const Center(child: CircularProgressIndicator());
                      }
                      if (snapshot.hasError) {
                        return Center(child: Text('Error: ${snapshot.error}'));
                      }

                      final filteredUsers = viewModel.filteredUsers;

                        final sortedUsers = [...filteredUsers.where((user) => user.id != LoginState.userId)];
                        sortedUsers.sort((a, b) {
                        final aIsMember = viewModel.groupMemberUserIds.contains(a.id);
                        final bIsMember = viewModel.groupMemberUserIds.contains(b.id);
                        if (aIsMember && !bIsMember) return -1;
                        if (!aIsMember && bIsMember) return 1;
                        return a.name.toLowerCase().compareTo(b.name.toLowerCase());
                        });

                        return ListView.builder(
                        itemCount: sortedUsers.length + 2, // Add 2 for headers
                        itemBuilder: (context, index) {
                            if (index == 0) {
                            return Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Center(
                              child: Text(
                                'Group Members',
                                style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                                ),
                              ),
                              ),
                            );
                            } else if (index == sortedUsers.indexWhere((user) => !viewModel.groupMemberUserIds.contains(user.id)) + 1) {
                            return Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Center(
                              child: Text(
                                'Add Members',
                                style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                                ),
                              ),
                              ),
                            );
                            }

                          final adjustedIndex = index - 1; // Adjust for header
                          final user = sortedUsers[adjustedIndex];
                          final isMember =
                          viewModel.groupMemberUserIds.contains(user.id);

                          return Container(
                          decoration: BoxDecoration(
                          border: Border(
                          top: BorderSide(
                            color: Colors.grey[200]!, width: 1.0),
                          ),
                          ),
                          child: Center(
                          child: Container(
                          width:
                            MediaQuery.of(context).size.width * 0.85,
                          child: ListTile(
                            title: Text(user.name,
                            style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.black)),
                            trailing: Icon(
                            isMember
                            ? (_selectedToRemove.contains(user.id)
                            ? Icons.remove_circle
                            : Icons.check_circle)
                            : (_selectedToAdd.contains(user.id)
                            ? Icons.add_circle
                            : Icons.add_circle_outline),
                            color: isMember
                            ? (_selectedToRemove.contains(user.id)
                            ? ColorHelper.thePunchRed()
                            : Colors.blue)
                            : (_selectedToAdd.contains(user.id)
                            ? Colors.blue
                            : Colors.grey[500]),
                            ),
                            onTap: () {
                            setState(() {
                              final myUserId = LoginState.userId;
                              if (user.id == myUserId) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                content: Text("You cannot remove yourself from the group."),
                                ),
                              );
                              return;
                              }

                              if (isMember) {
                              if (_selectedToRemove.contains(user.id)) {
                                _selectedToRemove.remove(user.id);
                              } else {
                                _selectedToRemove.add(user.id);
                              }
                              } else {
                              if (_selectedToAdd.contains(user.id)) {
                                _selectedToAdd.remove(user.id);
                              } else {
                                _selectedToAdd.add(user.id);
                              }
                              }
                            });
                            }
                          ),
                          ),
                          ),
                          );
                        },
                        );
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      SizedBox(
                        width: MediaQuery.of(context).size.width * 0.35,
                        child: OutlinedButton(
                          onPressed: () {
                            _onDeleteGroup();
                          },
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: Colors.blue),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20.0),
                            ),
                          ),
                          child: const Text('Delete',
                              style: TextStyle(
                                  color: Colors.blue,
                                  fontWeight: FontWeight.bold)),
                        ),
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width * 0.35,
                        child: TextButton(
                          onPressed: () async {
                                if (_selectedToAdd.isEmpty && _selectedToRemove.isEmpty) {
      // No changes => just close
                              setState(() => _isEditMode = false);
                              return;
                            }

                            setState(() => _isBusy = true);

                            try {
                              await DataModel().groupMemberModel.editGroup(
                                widget.groupId,
                                _selectedToAdd.toList(),
                                _selectedToRemove.toList(),
                              );

                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Group updated: Added ${_selectedToAdd.length}, '
                                    'Removed ${_selectedToRemove.length}.',
                                  ),
                                ),
                              );

                              Navigator.of(context).pop(); // close the dialog
                            } catch (e) {
                              debugPrint('Failed to edit group members: $e');
                              // Show error if needed
                            } finally {
                              setState(() => _isBusy = false);
                            }
                          },
                          style: TextButton.styleFrom(
                            backgroundColor: Colors.blue,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20.0),
                            ),
                          ),
                          child: const Text('Save & Close',
                              style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold)),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );
}

class _EditChatViewModel extends ChangeNotifier with ViewModelMixin {
  final String groupId;
  Set<String> groupMemberUserIds = {};
  List<User> allUsers = [];
  String searchQuery = '';
  late Future<void> futureLoad;

  _EditChatViewModel(this.groupId) {
    addListenables([DataModel().groupMemberModel, DataModel().userModel]);
    futureLoad = _loadData();
  }

  Future<void> _loadData() async {
    try {
      final members = await DataModel().groupMemberModel.getByGroupId(groupId);
      groupMemberUserIds = members.map((m) => m.userId).toSet();
      allUsers = await DataModel().userModel.getAllUsers();
    } catch (e) {
      debugPrint('Error loading data: $e');
    }
    notifyListeners();
  }

  List<User> get filteredUsers {
    if (searchQuery.isEmpty) return allUsers;
    return allUsers
        .where((user) => user.name.toLowerCase().contains(searchQuery.toLowerCase()))
        .toList();
  }

  void updateSearchQuery(String query) {
    searchQuery = query;
    notifyListeners();
  }

  void addUserToGroup(String userId) {
    groupMemberUserIds.add(userId);
    notifyListeners();
  }

  void removeUserFromGroup(String userId) {
    groupMemberUserIds.remove(userId);
    notifyListeners();
  }

  @override
  Future<void> refresh() async {
    await _loadData();
  }
}

/// A dialog that lists all members of the group.
/// If the current user is the group creator, it shows "Edit" mode with two tabs:
///  - Add: lists all users NOT in the group
///  - Remove: lists all users in the group (excluding yourself)
/// The user can check members to add or remove, then press "Done" to confirm.

/// Convenience class to hold both the [GroupMember] and its corresponding [User].
class _MemberWithUser {
  final GroupMember member;
  final User? user;
  _MemberWithUser(this.member, this.user);
}
class _ViewModel extends ChangeNotifier with ViewModelMixin {
  final String groupId;
  MessageGroup? group;
  
  // Store the active userIds in the group
  Set<String> groupMemberUserIds = {};

  // NEW: store the other person's name if there are exactly 2 members
  String? otherUserName;

  _ViewModel(this.groupId) {
    addListenables([DataModel().messageGroupModel]);
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    try {
      // 1. Get the group
      group = await DataModel().messageGroupModel.getById(groupId);

      // 2. Load group members
      final memberList =
          await DataModel().groupMemberModel.getByGroupId(groupId);
      groupMemberUserIds = memberList.map((m) => m.userId).toSet();

      // 3. If exactly 2 members, find the "other" user and fetch their name
      if (groupMemberUserIds.length == 2) {
        final myUserId = LoginState.userId;
        final otherId = groupMemberUserIds.firstWhere(
          (uid) => uid != myUserId,
          orElse: () => '',
        );
        if (otherId.isNotEmpty) {
          final otherUser = await DataModel().userModel.getById(otherId);
          otherUserName = otherUser?.name;  // or fullName
        }
      } else {
        // If more or fewer than 2, set this to null
        otherUserName = null;
      }
    } catch (e, st) {
      debugPrint('Error fetching group: $e\n$st');
    }

    notifyListeners();
  }
}
